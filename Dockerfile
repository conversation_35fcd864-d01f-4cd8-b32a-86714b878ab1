FROM php:7.4-apache

# Install dependencies
RUN apt-get update && apt-get install -y \
    libzip-dev \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libgmp-dev \
    libldap2-dev \
    libicu-dev \
    libxml2-dev \
    libxslt1-dev \
    libonig-dev \
    git \
    curl \
    zip \
    unzip

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-configure ldap \
    && docker-php-ext-install -j$(nproc) \
        bcmath \
        mysqli \
        pdo_mysql \
        zip \
        gd \
        soap \
        intl \
        ldap \
        xsl \
        xml \
        mbstring \
        json

# Install IMAP extension
RUN apt-get install -y libc-client-dev libkrb5-dev \
    && docker-php-ext-configure imap --with-kerberos --with-imap-ssl \
    && docker-php-ext-install imap

# Install GMP extension
RUN docker-php-ext-install gmp

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configure Apache
RUN a2enmod rewrite

# Set working directory
WORKDIR /var/www/html

# Copy application files
#COPY . .

# Run composer install
#RUN composer install --no-interaction --optimize-autoloader

# Set permissions
#RUN chown -R www-data:www-data /var/www/html
