<h1>{$title}</h1>
{if $subtitle}<h2>{$subtitle|escape}</h2>{/if}

<table border="0" cellpadding="0" cellspacing="0">
  {if $action eq 'filter'}
    {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=documents&amp;documents=filter&amp;{if $generate_system_task}generate_system_task={$generate_system_task}&amp;{/if}{if $smarty.get.autocomplete_filter}autocomplete_filter=session&amp;{/if}{if $smarty.request.uniqid}uniqid={$smarty.request.uniqid}&amp;{/if}page={/capture}

  {else}
    <tr>
      <td class="pagemenu">
        {capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$controller}={$action}{if $type && is_numeric($type)}&amp;type={$type}{/if}&amp;page={/capture}
        {include file="`$theme->templatesDir`pagination.html"
          found=$pagination.found
          total=$pagination.total
          rpp=$pagination.rpp
          page=$pagination.page
          pages=$pagination.pages
          link=$link
          hide_stats=1
        }
      </td>
    </tr>
  {/if}
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="documents" action="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}{/if}" method="post" enctype="multipart/form-data">
      {if $action eq 'filter' && $smarty.request.autocomplete_filter}
        {assign var='uniqid' value=$smarty.request.uniqid}
        {assign var='autocomplete_params' value=$smarty.session.autocomplete_params.$uniqid}
        {json assign='autocomplete_params_json' encode=$autocomplete_params}
        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="{$autocomplete_params_json|escape}" />
      {/if}
      <style type="text/css">

      </style>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <th class="t_caption t_border t_checkall">
            {if $action eq 'filter'}
              {if !$autocomplete_params || $autocomplete_params.select_multiple}
                {include file="`$theme->templatesDir`_select_items.html"
                  pages=$pagination.pages
                  total=$pagination.total
                  session_param=$session_param|default:$pagination.session_param
                }
              {else}
                {assign var='hide_selection_stats' value=true}
              {/if}
            {else}
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
            {/if}
          </th>
          <th class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></th>
          <th class="t_caption t_border {$sort.full_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.full_num.link}">{$basic_vars_labels.full_num|default:#documents_full_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__request_faq.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__request_faq.link} onclick="{$sort.a__request_faq.link}"{/if}>{$add_vars_labels.96.request_faq}</div></th>
          <th class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{$basic_vars_labels.customer|default:#documents_customer#|escape}</div></th>
          <th class="t_caption t_border {$sort.status.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.status.link}">{if 'documents' == 'projects'}{#documents_status_phase#|escape}{else}{$basic_vars_labels.status|default:#documents_status#|escape}{/if}</div></th>
          <th class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{$basic_vars_labels.added|default:#added#|escape}</div></th>
          <th class="t_caption t_border {$sort.tags.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.tags.link} onclick="{$sort.tags.link}"{/if}>{$basic_vars_labels.tags|default:#documents_tags#|escape}</div></th>
          <th class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{$basic_vars_labels.name|default:#documents_name#|escape}</div></th>
          <th class="t_caption t_border {$sort.custom_num.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.custom_num.link}">{$basic_vars_labels.custom_num|default:#documents_custom_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.decision.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.decision.link} onclick="{$sort.decision.link}"{/if}>{$basic_vars_labels.decision|default:#documents_decision#|escape}</div></th>
          <th class="t_caption t_border {$sort.group.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.group.link}">{#documents_group#|escape}</div></th>
          <th class="t_caption t_border {$sort.added_by.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.added_by.link} onclick="{$sort.added_by.link}"{/if}>{$basic_vars_labels.added_by|default:#documents_added_by#|escape}</div></th>
          <th class="t_caption t_border {$sort.emails.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.emails.link} onclick="{$sort.emails.link}"{/if}>{$basic_vars_labels.emails|default:#documents_emails#|escape}</div></th>
          <th class="t_caption t_border {$sort.name_full_num.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.name_full_num.link} onclick="{$sort.name_full_num.link}"{/if}>{$basic_vars_labels.name_full_num|default:#documents_name_full_num#|escape}</div></th>
          <th class="t_caption t_border {$sort.timesheet_time.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.timesheet_time.link} onclick="{$sort.timesheet_time.link}"{/if}>{$basic_vars_labels.timesheet_time|default:#documents_timesheet_time#|escape}</div></th>
          <th class="t_caption t_border {$sort.owner.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.owner.link} onclick="{$sort.owner.link}"{/if}>{$basic_vars_labels.owner|default:#documents_owner#|escape}</div></th>
          <th class="t_caption t_border {$sort.customer.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.customer.link}">{$basic_vars_labels.customer_name_code|default:#documents_customer_name_code#|escape}</div></th>
          <th class="t_caption t_border {$sort.project.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.project.link}">{$basic_vars_labels.project_name_code|default:#documents_project_name_code#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__problem_module.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__problem_module.link} onclick="{$sort.a__problem_module.link}"{/if}>{$add_vars_labels.96.problem_module}</div></th>
          <th class="t_caption t_border {$sort.observer.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.observer.link} onclick="{$sort.observer.link}"{/if}>{$basic_vars_labels.observer|default:#documents_observer#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__new_func_desc.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__new_func_desc.link} onclick="{$sort.a__new_func_desc.link}"{/if}>{$add_vars_labels.96.new_func_desc}</div></th>
          <th class="t_caption t_border {$sort.responsible.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.responsible.link} onclick="{$sort.responsible.link}"{/if}>{$basic_vars_labels.responsible|default:#documents_responsible#|escape}</div></th>
          <th class="t_caption t_border {$sort.project.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.project.link}">{$basic_vars_labels.project|default:#documents_project#|escape}</div></th>
          <th class="t_caption t_border {$sort.relatives_parent.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.relatives_parent.link} onclick="{$sort.relatives_parent.link}"{/if}>{$basic_vars_labels.relatives_parent|default:#documents_relatives_parent#|escape}</div></th>
          <th class="t_caption t_border {$sort.a__bug_or_func.class}" nowrap="nowrap"><div class="t_caption_title"{if $sort.a__bug_or_func.link} onclick="{$sort.a__bug_or_func.link}"{/if}>{$add_vars_labels.96.bug_or_func}</div></th>

          <th class="t_caption">&nbsp;</th>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {array assign='background_colors'}
      {foreach name='i' from=$documents item='single'}
      {strip}
      {capture assign='info'}
        <strong><u>{$basic_vars_labels.full_num|default:#documents_full_num#|escape}:</u></strong> {$single->get('full_num')|escape|numerate:$single->get('direction')}<br />
        <strong>{$basic_vars_labels.name|default:#documents_name#|escape}:</strong> {$single->get('name')|escape}<br />
        <strong>{$basic_vars_labels.type|default:#documents_type#|escape}:</strong> {$single->get('type_name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$single->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$single->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('modified_by_name')|escape}<br />
        <strong>{#status_modified#|escape}:</strong> {$single->get('status_modified')|date_format:#date_mid#|escape} {#by#|escape} {$single->get('status_modified_by_name')|escape}<br />
        {if $single->isDeleted()}<strong>{#deleted#|escape}:</strong> {$single->get('deleted')|date_format:#date_mid#|escape}{if $single->get('deleted_by_name')} {#by#|escape} {$single->get('deleted_by_name')|escape}{/if}<br />{/if}
        {if $single->get('archived_by')}<strong>{#archived#|escape}:</strong> {$single->get('archived')|date_format:#date_mid#|escape}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$single->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $single->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span><br />
      {/capture}
      {capture assign='document_status'}
        {if $single->get('status') eq 'opened'}
          {#help_documents_status_opened#}
        {elseif $single->get('status') eq 'locked'}
          {#help_documents_status_locked#}
        {elseif $single->get('status') eq 'closed'}
          {#help_documents_status_closed#}
        {/if}
        {if $single->get('substatus_name')}
          <br />
          {#help_documents_substatus#}{$single->get('substatus_name')}
        {/if}
      {/capture}
      {/strip}
      {include file="`$theme->templatesDir`row_link_action.html" object=$single assign='row_link'}
      {capture assign='row_link_class}{if $row_link}pointer{/if}{/capture}
      <div id="rf{$single->get('id')}" style="display: none">{$single->get('full_num')|numerate:$single->get('direction')} {$single->get('name')|escape|default:"&nbsp;"}</div>
      {if $single->modelName != 'Event' && !$single->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$single->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">
            {counter name='item_counter' print=true}
          </td>
          <td colspan="24" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td>
            {include file=`$theme->templatesDir`single_actions_list.html object=$single disabled='edit,delete,view'}
          </td>
        </tr>
      {else}
        <tr {if !$background_style}class="{cycle values='t_odd,t_even'}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('archived_by')} attention{/if}{if $single->get('deleted_by')} t_deleted{/if}{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} {if $single->modelName eq 'Contract'}strike{else}t_strike{/if}{/if}{if $single->get('severity')} {$single->get('severity')}{/if}"{else}class="t_row{if $single->get('annulled_by') || $single->get('subtype_status') == 'failed'} t_strike{/if}{if $single->isDefined('active') && !$single->get('active')} t_inactive{/if}{if $single->get('deleted_by')} t_deleted{/if}"{if $single->isDefined('active') && $single->get('active') || !$single->isDefined('active')} {$background_style}{/if}{/if}>
          <td class="t_border">
            {if $action eq 'filter'}
              {if $autocomplete_params && !$autocomplete_params.select_multiple}
                <input type="checkbox" name='items[]' value="{$single->get('id')}" title="{#check_to_include#|escape}" onclick="return clickOnce(this);" />
              {else}
                <input type="checkbox"
                       onclick="setCheckAllBox(params = {ldelim}
                                                the_element: this,
                                                module: '{$module}',
                                                controller: '{$controller}',
                                                action: '{$action}',
                                                button_id: '{$module}_{$controller}_{$action}_checkall_1'
                                               {rdelim});"
                       name='items[]'
                       value="{$single->get('id')}{if $module eq 'customers' && $relation}_{if $single->get('is_company')}company{else}person{/if}{/if}"
                       title="{#check_to_include#|escape}" />
              {/if}
            {else}
              <input onclick="sendIds(params = {ldelim}
                                              the_element: this,
                                              module: '{$module}',
                                              controller: '{$controller}',
                                              action: '{$action}',
                                              session_param: '{$session_param|default:$pagination.session_param}',
                                              total: {$pagination.total}
                                             {rdelim});"
                     type="checkbox"
                     name='items[]'
                     value="{$single->get('id')}"
                     title="{#check_to_include#|escape}"
                     {if @in_array($single->get('id'), $selected_items.ids) ||
                         (@$selected_items.select_all eq 1 && @!in_array($single->get('id'), $selected_items.ignore_ids))}
                       checked="checked"
                     {/if} />
            {/if}
          </td>
          <td class="t_border hright" nowrap="nowrap">
            {if $single->get('files_count')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module != $controller}&amp;{$controller_param}={$controller}&amp;{$controller}{else}&amp;{$module}{/if}=attachments&amp;attachments={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}">
               <img border="0" src="{$theme->imagesUrl}attachments.png" alt=""
                     onmouseover="showFiles(this, '{$module}', '{$controller}', {$single->get('id')}{if $single->get('archived_by')}, '', 1{/if})"
                     onmouseout="mclosetime()" />
              </a>
            {/if}
            {counter name='item_counter' print=true}
          </td>
          <td class="t_border {$sort.full_num.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}">{$single->get('full_num')|numerate:$single->get('direction')}</a></td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'request_faq')}request_faq{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('request_faq', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.96.request_faq}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}autocompleter{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_request_faq', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_request_faq', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_request_faq_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_request_faq_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__request_faq.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
          <td class="t_border {$sort.customer.isSorted}">{if $single->get('customer')}<a href="{$smarty.server.PHP_SELF}?{$module_param}=customers&amp;customers=view&amp;view={$single->get('customer')}" title="{#view#|escape}: {$single->get('customer_name')|escape}">{$single->get('customer_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
          <td class="t_border {$sort.status.isSorted}" nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$document_status|escape|default:'&nbsp;' caption=#help_documents_status#|escape width=250}{if $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'documents')" style="cursor:pointer;"{/if}
          {/capture}
          {capture assign='document_expired'}
            {if $single->get('status') != 'closed' && $single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {#documents_expired_legend#}: <strong>{$single->get('deadline')|date_format:#date_mid#}</strong>!
            {/if}
            {if $single->get('status') != 'closed' && $single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {$documents_expired} {#documents_expired_validity_legend#}: <strong>{$single->get('validity_term')|date_format:#date_mid#}</strong>!
            {/if}
          {/capture}
          {if $single->get('status') != 'closed' && (($single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#) || ($single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
            <img src="{$theme->imagesUrl}warning.png" width="16" height="16" border="0" alt="" {popup text=$document_expired|escape caption=#documents_expired#|escape width=250} />
          {/if}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              <img src="{$smarty.const.PH_DOCUMENTS_STATUSES_URL}{$single->get('icon_name')}" border="0" alt="" title="" {$popup_and_onclick} />
            {else}
              <img src="{$theme->imagesUrl}documents_{$single->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {/if}
            <span {$popup_and_onclick}>{$single->get('substatus_name')}</span>
          {else}
            <img src="{$theme->imagesUrl}documents_{$single->get('status')}.png" width="16" height="16" border="0" alt="" title="" {$popup_and_onclick} />
            {capture assign='status_param'}documents_status_{$single->get('status')}{/capture}
            <span {$popup_and_onclick}>{$smarty.config.$status_param}</span>
          {/if}
          </td>
          <td class="t_border {$sort.added.isSorted} {$row_link_class}" nowrap="nowrap"{$row_link}>{$single->get('added')|date_format:#date_short#|escape}</td>
          {strip}
            {if preg_match('#^Finance_.*$#i', $single->modelName)}
              {assign var='_module' value='finance'}
              {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
            {else}
              {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
              {capture assign='_controller'}{/capture}
            {/if}
          {/strip}
          <td class="t_border {$sort.tags.isSorted}" {if $single->getModelTags() && $single->get('available_tags_count') gt 0 && $single->checkPermissions('tags_view') && $single->checkPermissions('tags_edit')} onclick="changeTags({$single->get('id')}, '{$_module}', '{$_controller}'{if $redirect_to_url && $update_target}, '{$redirect_to_url}' + ($$('#{$update_target} .page_menu_current_page').length ? $$('#{$update_target} .page_menu_current_page')[0].innerHTML : 1){/if})" style="cursor: pointer;" title="{#tags_change#|escape}"{/if}>
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                <span class="{$tag->get('color')}_pushpin" title="{$tag->get('description')|escape}">{$tag->get('name')|escape}</span>{if !$smarty.foreach.ti.last}<br />{/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td class="t_border {$sort.name.isSorted} {$row_link_class}"{$row_link}>{$single->get('name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.custom_num.isSorted} {$row_link_class}"{$row_link}>{$single->get('custom_num')|default:"&nbsp;"}</td>
        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td class="t_border"{if !(is_array($single->get('assignments_decision')) && $single->get('assignments_decision')|@count gt 3)}{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_decision'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer;" title="{$smarty.config.$title_label}"{/if}{/if}>
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('assignments_decision')}
              {foreach from=$single->get('assignments_decision') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {if is_array($single->get('assignments_decision')) && $single->get('assignments_decision')|@count gt 3}
                <div id="assignments_decision_part_{$smarty.foreach.i.iteration}">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_decision'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$short_text}
                  </div>
                  {capture assign='show_all_label'}{$_module}_show_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_down.png" border="0" alt="{$smarty.config.$show_all_label|escape}" title="{$smarty.config.$show_all_label|escape}" onclick="toggleContent('assignments_decision', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
                <div id="assignments_decision_full_{$smarty.foreach.i.iteration}" style="display: none;">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_decision'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$long_text}
                  </div>
                  {capture assign='hide_all_label'}{$_module}_hide_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_up.png" border="0" alt="{$smarty.config.$hide_all_label|escape}" title="{$smarty.config.$hide_all_label|escape}" onclick="toggleContent('assignments_decision', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
              {else}
                {$short_text}
              {/if}
            {else}
              &nbsp;
            {/if}
        </td>
          <td class="t_border {$sort.group.isSorted} {$row_link_class}"{$row_link}>{$single->get('group_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.added_by.isSorted} {$row_link_class}"{$row_link}>{$single->get('added_by_name')|escape|default:"&nbsp;"}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td class="t_border {$sort.emails.isSorted}">
            <div class="t_occupy_cell has_inline_add hright emails emails_{$single->modelName|lower}_{$single->get('id')}{if $single->get('emails') gt 0 && $single->checkPermissions('emails')} pointer" onmouseenter="showCommunicationsInfo(this, 'emails', '{$_module}', '{$_controller}', {$single->get('id')}, '{$single->get('archived_by')}')" onmouseleave="mclosetime()" onclick="openHref('{$smarty.server.SCRIPT_NAME}?{$module_param}={$_module}{if $_controller ne $_module}&amp;controller={$_controller}{/if}&amp;{$_controller}=communications&amp;communications={$single->get('id')}&amp;communication_type=emails{if $single->get('archived_by')}&amp;archive=1{/if}', '_self', event){/if}">
              <span class="emails_total">{if $single->get('emails')}{$single->get('emails')}{/if}</span>
            </div>
            {if $single->checkPermissions('emails_add')}
            <div class="inline_add" onmouseover="Event.stop(event)">
              <a href="#" onclick="return loadInlineAddCommunication(event, 'email', '{$_module}', '{$_controller}', {$single->get('id')});" title="{#add_email#|escape}"></a>
            </div>
            {/if}
          </td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{if $module ne $controller}&amp;{$controller_param}={$controller}{/if}&amp;{$action_param}=view&amp;view={$single->get('id')}">&#91;{$single->get('full_num')|escape|default:"&nbsp;"}&#93; {$single->get('name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.timesheet_time.isSorted}">
            {if $single->get('generate_system_task')}
              {assign var='timesheet_time' value=$single->getTimesheetTime()}
              <div class="t_occupy_cell has_inline_add timesheet_time timesheet_time_{$single->modelName|lower}_{$single->get('id')}{if $single->get('timesheet_time') gt 0 && $single->checkPermissions('viewtimesheets')} pointer" onmouseenter="showTimesheetsInfo(this, '{$single->getModule()}', '{$single->getController()}', {$single->get('id')}, '{$single->get('archived_by')}')" onmouseleave="mclosetime()" onclick="openHref('{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=timesheets&amp;timesheets={$single->get('id')}{if $single->get('archived_by')}&amp;archive=1{/if}', '_self', event){/if}">
                <span class="timesheet_time_total">{$single->get('timesheet_time_formatted')|escape}</span>
              </div>
              {if $single->get('system_task_id') && $single->checkPermissions('addtimesheet')}
              <div class="inline_add" onmouseover="Event.stop(event)">
                {capture assign='label_param'}{$module}_add_timesheet{/capture}
                <a href="#" onclick="addTimesheet('', '', '{$single->get('system_task_id')}'); return false;" title="{$smarty.config.$label_param|escape}"></a>
              </div>
              {/if}
            {else}
              &nbsp;
            {/if}
          </td>
        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td class="t_border"{if !(is_array($single->get('assignments_owner')) && $single->get('assignments_owner')|@count gt 3)}{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_owner'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer;" title="{$smarty.config.$title_label}"{/if}{/if}>
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('assignments_owner')}
              {foreach from=$single->get('assignments_owner') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {if is_array($single->get('assignments_owner')) && $single->get('assignments_owner')|@count gt 3}
                <div id="assignments_owner_part_{$smarty.foreach.i.iteration}">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_owner'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$short_text}
                  </div>
                  {capture assign='show_all_label'}{$_module}_show_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_down.png" border="0" alt="{$smarty.config.$show_all_label|escape}" title="{$smarty.config.$show_all_label|escape}" onclick="toggleContent('assignments_owner', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
                <div id="assignments_owner_full_{$smarty.foreach.i.iteration}" style="display: none;">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_owner'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$long_text}
                  </div>
                  {capture assign='hide_all_label'}{$_module}_hide_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_up.png" border="0" alt="{$smarty.config.$hide_all_label|escape}" title="{$smarty.config.$hide_all_label|escape}" onclick="toggleContent('assignments_owner', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
              {else}
                {$short_text}
              {/if}
            {else}
              &nbsp;
            {/if}
        </td>
          <td class="t_border {$sort.customer.isSorted}">{if $single->get('customer')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$single->get('customer')}" title="{#view#|escape}: {$single->get('customer_name')|escape}">&#91;{$single->get('customer_code')|escape|default:"&nbsp;"}&#93; {$single->get('customer_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
          <td class="t_border {$sort.project.isSorted}">{if $single->get('project')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$single->get('project')}" title="{#view#|escape}: {$single->get('project_name')|escape}">&#91;{$single->get('project_code')|escape|default:"&nbsp;"}&#93; {$single->get('project_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>
          {assign var='var_values' value=$single->getSelectedAddVarCheckboxLabels('problem_module')}
          {capture assign='var_back_label'}{$add_vars_back_labels.96.problem_module}{/capture}
          <td class="t_border {$row_link_class}"{$row_link}>
            {foreach from=$var_values item='checkbox_value' name='cb_val'}
              {if trim($checkbox_value) !== ''}
                {$checkbox_value|nl2br|url2href|default:"&nbsp;"}
              {else}
                  <img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="" style="margin-left: 5px;" />
              {/if}
              {if !$smarty.foreach.cb_val.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
            {if $var_values}{include file="_back_label.html" back_label=$var_back_label}{/if}
          </td>
        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td class="t_border"{if !(is_array($single->get('assignments_observer')) && $single->get('assignments_observer')|@count gt 3)}{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_observer'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer;" title="{$smarty.config.$title_label}"{/if}{/if}>
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('assignments_observer')}
              {foreach from=$single->get('assignments_observer') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {if is_array($single->get('assignments_observer')) && $single->get('assignments_observer')|@count gt 3}
                <div id="assignments_observer_part_{$smarty.foreach.i.iteration}">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_observer'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$short_text}
                  </div>
                  {capture assign='show_all_label'}{$_module}_show_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_down.png" border="0" alt="{$smarty.config.$show_all_label|escape}" title="{$smarty.config.$show_all_label|escape}" onclick="toggleContent('assignments_observer', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
                <div id="assignments_observer_full_{$smarty.foreach.i.iteration}" style="display: none;">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_observer'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$long_text}
                  </div>
                  {capture assign='hide_all_label'}{$_module}_hide_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_up.png" border="0" alt="{$smarty.config.$hide_all_label|escape}" title="{$smarty.config.$hide_all_label|escape}" onclick="toggleContent('assignments_observer', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
              {else}
                {$short_text}
              {/if}
            {else}
              &nbsp;
            {/if}
        </td>
          {capture assign='asterisk_contact'}{if $use_asterisk && preg_match('#^asteriskcall_(fax|phone|gsm).*$#', 'new_func_desc')}new_func_desc{/if}{/capture}
          {capture assign='var_value'}{$single->getVarValue('new_func_desc', 0)}{/capture}
          {capture assign='var_back_label'}{$add_vars_back_labels.96.new_func_desc}{/capture}
          {capture assign='var_row_link'}{if !$asterisk_contact && !preg_match(str_replace(' ', '', '#(< a\s|onclick=)#'), $var_value)} {$row_link}{/if}{/capture}
          {capture assign='content'}{if $asterisk_contact}{if $var_value}{include file=`$theme->templatesDir`_asterisk_contact.html contact_type=$asterisk_contact|regex_replace:'/^asteriskcall_(fax|phone|gsm).*$/':'$1' number=$var_value}{/if}{else}{$var_value}{/if}{/capture}
          {capture assign='content'}{if $content !== ''}{$content}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          {assign var='long_content' value=false}
          {capture assign='var_type'}textarea{/capture}
          {if $var_type eq 'textarea' &&  $content|mb_count_characters:true gt 130}
            {assign var='long_content' value=true}
            {assign var='single_id' value=$single->get('id')}
            {strip}
            {capture assign='show_full'}
              <img src="{$theme->imagesUrl}small/arrow_down.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#full_text#|escape}" title="{#full_text#|escape}" onclick="toggleContent('add_var_new_func_desc', {$single_id});" />
            {/capture}
            {capture assign='show_part'}
              <img src="{$theme->imagesUrl}small/arrow_up.png" width="12" height="12" border="0" style="cursor: pointer;" alt="{#part_text#|escape}" title="{#part_text#|escape}" onclick="toggleContent('add_var_new_func_desc', {$single_id});" />
            {/capture}
            {/strip}
            {capture assign='content'}<div id="add_var_new_func_desc_part_{$single_id}"><span{$var_row_link}>{$content|mb_html_substr:130:"..."|nl2br|url2href}</span>{$show_full}</div><div id="add_var_new_func_desc_full_{$single_id}" style="display: none;"><span{$var_row_link}>{$content|nl2br|url2href}</span>{$show_part}</div>{/capture}
          {else}
            {capture assign='content'}{$content|nl2br|url2href}{/capture}
          {/if}
          <td class="t_border {if is_numeric($var_value)} hright{/if} {$sort.a__new_func_desc.isSorted}{if $var_row_link} {$row_link_class}{/if}" {if !$long_content}{$var_row_link}{/if}>{$content}</td>
        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td class="t_border"{if !(is_array($single->get('assignments_responsible')) && $single->get('assignments_responsible')|@count gt 3)}{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_responsible'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer;" title="{$smarty.config.$title_label}"{/if}{/if}>
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('assignments_responsible')}
              {foreach from=$single->get('assignments_responsible') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}<br />{/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {if is_array($single->get('assignments_responsible')) && $single->get('assignments_responsible')|@count gt 3}
                <div id="assignments_responsible_part_{$smarty.foreach.i.iteration}">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_responsible'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$short_text}
                  </div>
                  {capture assign='show_all_label'}{$_module}_show_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_down.png" border="0" alt="{$smarty.config.$show_all_label|escape}" title="{$smarty.config.$show_all_label|escape}" onclick="toggleContent('assignments_responsible', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
                <div id="assignments_responsible_full_{$smarty.foreach.i.iteration}" style="display: none;">
                  <div{if $single->checkPermissions('assign') && !$single->get('archived_by')} onclick="changeAssignments({$single->get('id')}, '{$_module}', 'assignments_responsible'{if $_controller}, '{$_controller}'{/if})" style="cursor:pointer; padding: 3px 0 3px 0;" title="{$smarty.config.$title_label}"{/if}>
                  {$long_text}
                  </div>
                  {capture assign='hide_all_label'}{$_module}_hide_full_assignments_list{/capture}
                  <img src="{$theme->imagesUrl}small/arrow_up.png" border="0" alt="{$smarty.config.$hide_all_label|escape}" title="{$smarty.config.$hide_all_label|escape}" onclick="toggleContent('assignments_responsible', {$smarty.foreach.i.iteration});" align="right" class="pointer" />
                </div>
              {else}
                {$short_text}
              {/if}
            {else}
              &nbsp;
            {/if}
        </td>
          <td class="t_border {$sort.project.isSorted}">{if $single->get('project')}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=projects&amp;projects=view&amp;view={$single->get('project')}" title="{#view#|escape}: {$single->get('project_name')|escape}">{$single->get('project_name')|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}</td>          <td class="t_border nopadding" nowrap="nowrap">
            <table border="0" cellspacing="0" cellpadding="0" width="100%">
              <tr>
                <td style="border: none!important;">
                  <div class="collapsed" id="parents_{$single->get('id')}" style="max-height: 36px; overflow: hidden;">
                    {assign var='parents' value=$single->getFirstLevelRelatedDocuments('parent')}
                    {foreach name='pd' from=$parents item='parent'}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=view&amp;view={$parent.id}{if $parent.archived_by}&amp;archive=1{/if}" {popup text=$parent.name|escape|default:"&nbsp;" caption=#documents_relative_document_name#|escape width=250}>{$parent.full_num|numerate:$parent.direction}</a>
                      {if !$smarty.foreach.pd.last}
                        <br />
                      {/if}
                    {foreachelse}
                      &nbsp;
                    {/foreach}
                  </div>
                </td>
                <td class="hright" style="border: none!important;">{if is_array($parents) && $parents|@count gt 3}<img alt="" class="pointer" title="{#expand#|escape}" src="{$theme->imagesUrl}expand1.png" onclick="toggleDocumentsListRelatives(this, 'parents_{$single->get('id')}');" />{else}&nbsp;{/if}
                </td>
              </tr>
            </table>
          </td>
          {assign var='var_values' value=$single->getSelectedAddVarCheckboxLabels('bug_or_func')}
          {capture assign='var_back_label'}{$add_vars_back_labels.96.bug_or_func}{/capture}
          <td class="t_border {$row_link_class}"{$row_link}>
            {foreach from=$var_values item='checkbox_value' name='cb_val'}
              {if trim($checkbox_value) !== ''}
                {$checkbox_value|nl2br|url2href|default:"&nbsp;"}
              {else}
                  <img src="{$theme->imagesUrl}small/check_yes.png" border="0" alt="" style="margin-left: 5px;" />
              {/if}
              {if !$smarty.foreach.cb_val.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
            {if $var_values}{include file="_back_label.html" back_label=$var_back_label}{/if}
          </td>

          <td class="hcenter" nowrap="nowrap">
            {if $action eq 'filter'}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single exclude="edit, view, delete"}
            {else}
              {include file=`$theme->templatesDir`single_actions_list.html object=$single}
            {/if}
          </td>
        </tr>
      {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="27">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="27"></td>
        </tr>
      </table>
      {if $action eq 'filter'}
        <br />
        {strip}
          {if $smarty.request.autocomplete_filter}
  {if $autocomplete_params.select_multiple}
    <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: false{rdelim});">{#select#|escape}</button>
  {/if}
  <button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete({ldelim}close_window: true{rdelim});">{#select#|escape} &amp; {#close#|escape}</button>
{else}
  <button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 0);{rdelim}, this, '{#confirm_link_documents#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_documents#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape}</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, 'items')) {ldelim}return confirmAction('link', function(el) {ldelim}updateReferers(el.form, 1);{rdelim}, this, '{#confirm_link_documents#|escape:'quotes'|escape}');{rdelim}else{ldelim}alert('{#alert_link_documents#|escape:'quotes'|escape}'); return false;{rdelim}">{#select#|escape} &amp; {#close#|escape}</button>
{/if}

        {/strip}
      {else}
        {if ('')}
          {include file="`$theme->templatesDir`_severity_legend.html" prefix=''}
        {/if}
        <br />
        {include file=`$theme->templatesDir`multiple_actions_list.html exclude='' include='purge,multistatus,multiprint,tags' session_param=$session_param|default:$pagination.session_param}
      {/if}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
  {if $background_colors}
    {include file="`$theme->templatesDir`_invoices_reasons_legend.html"}
  {/if}
</table>
