    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.full_num|default:#documents_full_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.custom_num|default:#documents_custom_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#documents_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.customer|default:#documents_customer#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#documents_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.department|default:#documents_department#|escape}</th>
          <th nowrap="nowrap">{if 'documents' == 'projects'}{#documents_status_phase#|escape}{else}{$basic_vars_labels.status|default:#documents_status#|escape}{/if}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#documents_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.notes|default:#documents_notes#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.comments|default:#documents_comments#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.emails|default:#documents_emails#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.decision|default:#documents_decision#|escape}</th>

        </tr>
      {foreach name='i' from=$documents item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="16-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$single->get('full_num')|numerate:$single->get('direction')}</td>
          <td style="mso-number-format: \@;">{$single->get('custom_num')|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{if $single->get('customer')}{$single->get('customer_name')|escape|default:"&nbsp;"}{else}&nbsp;{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('department_name')|escape|default:"&nbsp;"}</td>
          <td nowrap="nowrap">
          {capture assign='popup_and_onclick'}
            {popup text=$document_status|escape|default:'&nbsp;' caption=#help_documents_status#|escape width=250}{if $single->checkPermissions('setstatus')} onclick="changeStatus({$single->get('id')}, 'documents')" style="cursor:pointer;"{/if}
          {/capture}
          {capture assign='document_expired'}
            {if $single->get('status') != 'closed' && $single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {#documents_expired_legend#}: {$single->get('deadline')|date_format:#date_mid#}!
            {/if}
            {if $single->get('status') != 'closed' && $single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#}
              {$documents_expired} {#documents_expired_validity_legend#}: {$single->get('validity_term')|date_format:#date_mid#}!
            {/if}
          {/capture}
          {if $single->get('status') != 'closed' && (($single->get('deadline') && $single->get('deadline')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#) || ($single->get('validity_term') && $single->get('validity_term')|date_format:#date_iso# < $smarty.now|date_format:#date_iso#))}
            
          {/if}
          {if $single->get('substatus_name')}
            {if $single->get('icon_name')}
              
            {else}
              
            {/if}
            {$single->get('substatus_name')}
          {else}
            
            {capture assign='status_param'}documents_status_{$single->get('status')}{/capture}
            {$smarty.config.$status_param}
          {/if}
          </td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          <td style="mso-number-format: \@;">{$single->get('notes')|escape|nl2br|url2href|default:"&nbsp;"}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td style="mso-number-format: \@;">
            
              {if $single->get('comments')}{$single->get('comments')}{/if}
            
            {if $single->checkPermissions('comments_add')}
            
              
            
            {/if}
          </td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td style="mso-number-format: \@;">
            
              {if $single->get('emails')}{$single->get('emails')}{/if}
            
            {if $single->checkPermissions('emails_add')}
            
              
            
            {/if}
          </td>
        {if preg_match('#^Finance_.*$#i', $single->modelName)}
          {assign var='_module' value='finance'}
          {capture assign='_controller'}{$single->modelName|regex_replace:'#^Finance_(.*)$#i':'$1'|mb_lower}s{/capture}
        {else}
          {capture assign='_module'}{$single->modelName|mb_lower}s{/capture}
        {/if}
        {capture assign='title_label'}{$_module}_assign_change{/capture}
        <td style="mso-number-format: \@;">
        {assign var='long_text' value=''}
        {assign var='short_text' value=''}
        {if $single->get('assignments_decision')}
              {foreach from=$single->get('assignments_decision') item='assignment' name='assignees'}
                {capture assign='long_text'}
                  {$long_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                {/capture}
                {if $smarty.foreach.assignees.iteration le 3}
                  {capture assign='short_text'}
                    {$short_text}{$assignment.assigned_to_name|escape|default:'&nbsp;'}{if !$smarty.foreach.assignees.last}, {/if}
                  {/capture}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
              {$long_text}
            {else}
              &nbsp;
            {/if}
        </td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="16">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
