<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:59
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/after_actions_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/after_actions_box.html', 6, false),)), $this); ?>
<?php if ($this->_tpl_vars['available_after_actions']): ?>
<div id="after_actions_container">
  <div id="after_actions_details"<?php if ($_COOKIE['after_actions_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
    <div id="after_actions_definitions">
      <fieldset>
        <legend><?php echo ((is_array($_tmp=$this->_config[0]['vars']['available_after_actions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</legend>
<?php $_from = $this->_tpl_vars['available_after_actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_after_action']):
?>
        <input type="radio" name="after_action" id="after_action_<?php echo $this->_tpl_vars['available_after_action']['name']; ?>
" value="<?php echo $this->_tpl_vars['available_after_action']['name']; ?>
" title="<?php echo $this->_tpl_vars['available_after_action']['label']; ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if ($this->_tpl_vars['available_after_action']['selected']): ?> checked="checked"<?php endif; ?> onclick="toggleAfterActionOptions(this)" /><label for="after_action_<?php echo $this->_tpl_vars['available_after_action']['name']; ?>
"><?php echo $this->_tpl_vars['available_after_action']['label']; ?>
</label><br />
<?php endforeach; endif; unset($_from); ?>
      </fieldset>
    </div>
    <div id="available_after_options_container">
<?php $_from = $this->_tpl_vars['available_after_actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_after_action']):
?>
  <?php if ($this->_tpl_vars['available_after_action']['options']): ?>
        <fieldset class="available_after_options" id="after_options_<?php echo $this->_tpl_vars['available_after_action']['name']; ?>
" <?php if (! $this->_tpl_vars['available_after_action']['selected']): ?>style="display: none;"<?php endif; ?>>
          <legend style="margin-left: 3px;"><?php echo $this->_tpl_vars['available_after_action']['label']; ?>
 <?php echo ((is_array($_tmp=$this->_config[0]['vars']['options'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</legend>
          <table border="0" cellpadding="3" cellspacing="3">
          <?php $_from = $this->_tpl_vars['available_after_action']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['i']['iteration']++;
?>
            <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['option']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['option']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['option']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['available_after_action']['selected']): ?><?php echo ''; ?><?php $this->assign('disabled', 0); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('disabled', 1); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

            <?php if ($this->_tpl_vars['option']['type']): ?>
                            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['option']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['option'],'standalone' => false,'name' => $this->_tpl_vars['option']['name'],'custom_id' => $this->_tpl_vars['option']['custom_id'],'label' => $this->_tpl_vars['option']['label'],'help' => $this->_tpl_vars['option']['help'],'value' => $this->_tpl_vars['option']['value'],'options' => $this->_tpl_vars['option']['options'],'optgroups' => $this->_tpl_vars['option']['optgroups'],'option_value' => $this->_tpl_vars['option']['option_value'],'first_option_label' => $this->_tpl_vars['option']['first_option_label'],'onclick' => $this->_tpl_vars['option']['onclick'],'on_change' => $this->_tpl_vars['option']['on_change'],'onchange' => $this->_tpl_vars['option']['onchange'],'sequences' => $this->_tpl_vars['option']['sequences'],'check' => $this->_tpl_vars['option']['check'],'scrollable' => $this->_tpl_vars['option']['scrollable'],'readonly' => $this->_tpl_vars['option']['readonly'],'hidden' => $this->_tpl_vars['option']['hidden'],'required' => $this->_tpl_vars['option']['required'],'really_required' => $this->_tpl_vars['option']['really_required'],'disabled' => $this->_tpl_vars['disabled'],'disallow_date_after' => $this->_tpl_vars['option']['disallow_date_after'],'disallow_date_before' => $this->_tpl_vars['option']['disallow_date_before'],'options_align' => $this->_tpl_vars['option']['options_align'],'js_methods' => $this->_tpl_vars['option']['js_methods'],'restrict' => $this->_tpl_vars['option']['js_filter'],'custom_class' => $this->_tpl_vars['option']['custom_class'],'show_placeholder' => $this->_tpl_vars['option']['show_placeholder'],'text_align' => $this->_tpl_vars['option']['text_align'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
          </table>
        </fieldset>
  <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
    </div>
  </div>
  <div class="clear"></div>
  <div class="t_footer" id="after_actions_switch" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['available_after_actions_show'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><div class="<?php if ($_COOKIE['after_actions_box'] == 'off'): ?>switch_down<?php else: ?>switch_up<?php endif; ?>"></div></div>
</div>
<?php endif; ?>