<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_minitasks_edit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_minitasks_edit.html', 3, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_minitasks_edit.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_minitasks_edit.html', 53, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_minitasks_edit.html', 9, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_minitasks_edit.html', 168, false),)), $this); ?>
<?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['status_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('status_modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('status_modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>


<?php echo smarty_function_math(array('assign' => 'index','equation' => 'x+1','x' => $this->_tpl_vars['row_index']), $this);?>


  <?php if ($this->_tpl_vars['real_module'] == 'minitasks' && $this->_tpl_vars['real_action'] != 'dashlet'): ?>
  <td class="t_border">
    &nbsp;
      </td>
  <?php endif; ?>
  <td class="t_border"<?php if ($this->_tpl_vars['real_module'] != 'minitasks' || ( $this->_tpl_vars['real_action'] == 'dashlet' && ! in_array ( 'for_record' , $this->_tpl_vars['columns'] ) )): ?> style="display: none"<?php endif; ?> id="model_id_cell_<?php echo $this->_tpl_vars['index']; ?>
">
    <a id="error_model_id_<?php echo $this->_tpl_vars['row_index']; ?>
"></a>
    <?php if ($this->_tpl_vars['real_module'] == 'minitasks' && ! ( $this->_tpl_vars['minitask']->get('id') && $this->_tpl_vars['minitask']->get('model_id') )): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('index' => $this->_tpl_vars['index'],'name' => 'record_model_type','value' => '','optgroups' => $this->_tpl_vars['records_model_types'],'onchange' => "changeMinitaskRecordType(this)",'width' => 214,'standalone' => true,'required' => 1,'really_required' => 1,'first_option_label' => $this->_config[0]['vars']['minitasks_no_record'],'label' => $this->_config[0]['vars']['minitasks_for_record'],'help' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <div id="model_id_div_<?php echo $this->_tpl_vars['index']; ?>
" class="model_id_div" style="display: none;"></div>
    <?php else: ?>
      <?php if ($this->_tpl_vars['real_module'] == 'minitasks' && $this->_tpl_vars['minitask']->get('model_id')): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['minitask']->get('module'); ?>
&amp;<?php if ($this->_tpl_vars['minitask']->get('controller')): ?><?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['minitask']->get('controller'); ?>
&amp;<?php echo $this->_tpl_vars['minitask']->get('controller'); ?>
<?php else: ?><?php echo $this->_tpl_vars['minitask']->get('module'); ?>
<?php endif; ?>=view&amp;view=<?php echo $this->_tpl_vars['minitask']->get('model_id'); ?>
<?php if ($this->_tpl_vars['minitask']->get('archive')): ?>&amp;archive=1<?php endif; ?>" title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('record_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
"><?php if ($this->_tpl_vars['minitask']->get('record_num')): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('record_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php elseif ($this->_tpl_vars['minitask']->get('model') == 'Contract'): ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['minitasks_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?> <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('record_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
      <?php endif; ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_hidden.html', 'smarty_include_vars' => array('name' => 'model_id','index' => $this->_tpl_vars['index'],'standalone' => true,'value' => $this->_tpl_vars['minitask']->get('model_id'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_hidden.html', 'smarty_include_vars' => array('name' => 'model','index' => $this->_tpl_vars['index'],'standalone' => true,'value' => $this->_tpl_vars['minitask']->get('model'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
  <td class="t_border"<?php if ($this->_tpl_vars['real_module'] != 'minitasks' || ( $this->_tpl_vars['real_action'] == 'dashlet' && ! in_array ( 'customer' , $this->_tpl_vars['columns'] ) )): ?> style="display: none"<?php endif; ?>>
    <a id="error_customer_<?php echo $this->_tpl_vars['row_index']; ?>
"></a>
    <?php if ($this->_tpl_vars['real_module'] == 'minitasks' && ! ( $this->_tpl_vars['minitask']->get('id') && $this->_tpl_vars['minitask']->get('model_id') )): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('name' => 'customer','index' => $this->_tpl_vars['index'],'autocomplete' => $this->_tpl_vars['customer_autocomplete'],'filters_array' => $this->_tpl_vars['autocomplete_filters'],'autocomplete_type' => 'customers','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'clear','value' => $this->_tpl_vars['minitask']->get('customer'),'value_autocomplete' => $this->_tpl_vars['minitask']->get('customer_name'),'execute_after' => 'clearMinitaskModelIDAutocompleter','width' => 200,'standalone' => true,'label' => $this->_config[0]['vars']['minitasks_customer'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php else: ?>
      <?php if ($this->_tpl_vars['real_module'] == 'minitasks' && $this->_tpl_vars['minitask']->get('customer')): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['minitask']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
      <?php endif; ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_hidden.html', 'smarty_include_vars' => array('name' => 'customer','index' => $this->_tpl_vars['index'],'standalone' => true,'value' => $this->_tpl_vars['minitask']->get('customer'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
  </td>
  <td class="t_border">
    <a id="error_description_<?php echo $this->_tpl_vars['row_index']; ?>
"></a>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_textarea.html', 'smarty_include_vars' => array('required' => 1,'name' => 'description','index' => $this->_tpl_vars['index'],'value' => $this->_tpl_vars['minitask']->get('description'),'standalone' => true,'label' => $this->_config[0]['vars']['minitasks_description'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </td>
  <td class="t_border"<?php if ($this->_tpl_vars['real_module'] == 'minitasks' && $this->_tpl_vars['real_action'] == 'dashlet' && ! in_array ( 'deadline' , $this->_tpl_vars['columns'] )): ?> style="display: none;"<?php endif; ?>>
    <a id="error_deadline_<?php echo $this->_tpl_vars['row_index']; ?>
"></a>
    <?php if ($this->_tpl_vars['minitask']->get('id')): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_date.html', 'smarty_include_vars' => array('name' => 'deadline','index' => $this->_tpl_vars['index'],'value' => $this->_tpl_vars['minitask']->get('deadline'),'width' => 70,'standalone' => true,'label' => $this->_config[0]['vars']['minitasks_deadline'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php else: ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_dropdown.html', 'smarty_include_vars' => array('name' => 'deadline','index' => $this->_tpl_vars['index'],'value' => '','options' => $this->_tpl_vars['predefined_deadlines'],'width' => 70,'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['minitasks_deadline'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
  </td>
  <td class="t_border"<?php if ($this->_tpl_vars['real_module'] == 'minitasks' && $this->_tpl_vars['real_action'] == 'dashlet' && ! in_array ( 'assigned_to' , $this->_tpl_vars['columns'] )): ?> style="display: none;"<?php endif; ?>>
    <a id="error_assigned_to_<?php echo $this->_tpl_vars['row_index']; ?>
"></a>
    <?php if ($this->_tpl_vars['currentUser']->checkRights('minitasks','assign')): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_autocompleter.html', 'smarty_include_vars' => array('name' => 'assigned_to','index' => $this->_tpl_vars['index'],'autocomplete' => $this->_tpl_vars['user_autocomplete'],'autocomplete_type' => 'users','autocomplete_var_type' => 'basic','autocomplete_buttons' => 'clear','value' => $this->_tpl_vars['minitask']->get('assigned_to'),'value_autocomplete' => $this->_tpl_vars['minitask']->get('assigned_to_name'),'width' => 148,'standalone' => true,'required' => 1,'label' => $this->_config[0]['vars']['minitasks_assigned_to'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php else: ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_hidden.html', 'smarty_include_vars' => array('name' => 'assigned_to','index' => $this->_tpl_vars['index'],'standalone' => true,'value' => $this->_tpl_vars['minitask']->get('assigned_to'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <div>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('assigned_to_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </div>
    <?php endif; ?>
  </td>
  <td class="hcenter">
    <?php if ($this->_tpl_vars['minitask']->get('id')): ?>
    <?php if ($this->_tpl_vars['minitask']->checkPermissions('edit')): ?>
    <input type="image" id="img_edit_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_save[<?php echo $this->_tpl_vars['row_index']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" class="pointer" border="0" alt="" title="<?php echo $this->_config[0]['vars']['save']; ?>
" onclick="manageMinitask(this.form, '<?php echo $this->_tpl_vars['real_module']; ?>
', '<?php echo $this->_tpl_vars['real_action']; ?>
', 'save', this, <?php echo $this->_tpl_vars['minitask']->get('id'); ?>
); return false;" />
    <?php else: ?>
    <img id="img_edit_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_edit[<?php echo $this->_tpl_vars['row_index']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" class="pointer dimmed" border="0" alt="" title="<?php echo $this->_config[0]['vars']['edit']; ?>
" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['minitask']->checkPermissions('setstatus')): ?>
    <input type="image" id="img_finished_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_finished[<?php echo $this->_tpl_vars['row_index']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
minitasks_finished.png" class="img_finished_<?php echo $this->_tpl_vars['row_index']; ?>
<?php if ($this->_tpl_vars['dashlet_id']): ?>_<?php echo $this->_tpl_vars['dashlet_id']; ?>
<?php endif; ?>" border="0" alt="" title="<?php echo $this->_config[0]['vars']['minitasks_finish']; ?>
" onclick="changeMinitaskStatus(this.className, '<?php echo $this->_tpl_vars['real_module']; ?>
', '<?php echo $this->_tpl_vars['real_action']; ?>
', <?php echo $this->_tpl_vars['minitask']->get('id'); ?>
, 'finished'); return false;" />
    <input type="image" id="img_failed_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_failed[<?php echo $this->_tpl_vars['row_index']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
minitasks_failed.png" class="img_failed_<?php echo $this->_tpl_vars['row_index']; ?>
<?php if ($this->_tpl_vars['dashlet_id']): ?>_<?php echo $this->_tpl_vars['dashlet_id']; ?>
<?php endif; ?>" border="0" alt="" title="<?php echo $this->_config[0]['vars']['minitasks_cancel']; ?>
" onclick="changeMinitaskStatus(this.className, '<?php echo $this->_tpl_vars['real_module']; ?>
', '<?php echo $this->_tpl_vars['real_action']; ?>
', <?php echo $this->_tpl_vars['minitask']->get('id'); ?>
, 'failed'); return false;" />
    <?php else: ?>
    <img id="img_finished_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_finished[<?php echo $this->_tpl_vars['row_index']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
minitasks_finished.png" class="pointer dimmed" border="0" alt="" title="<?php echo $this->_config[0]['vars']['minitasks_finish']; ?>
" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_changestatus_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" />
    <img id="img_failed_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_failed[<?php echo $this->_tpl_vars['row_index']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
minitasks_failed.png" class="pointer dimmed" border="0" alt="" title="<?php echo $this->_config[0]['vars']['minitasks_cancel']; ?>
" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_changestatus_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" />
    <?php endif; ?>
    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
info.png" width="16" height="16" border="0" alt="" class="help" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
    <?php else: ?>
    <input type="image" id="img_save_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_save[<?php echo $this->_tpl_vars['row_index']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" class="pointer" border="0" alt="" title="<?php echo $this->_config[0]['vars']['save']; ?>
" onclick="manageMinitask(this.form, '<?php echo $this->_tpl_vars['real_module']; ?>
', '<?php echo $this->_tpl_vars['real_action']; ?>
', 'save', this); return false;" />
    <?php endif; ?>
  </td>