<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:59
         compiled from _table_buttons.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'json', '_table_buttons.html', 14, false),array('function', 'help', '_table_buttons.html', 15, false),array('modifier', 'escape', '_table_buttons.html', 15, false),)), $this); ?>
<?php if (empty ( $this->_tpl_vars['values_count'] ) || ! is_numeric ( $this->_tpl_vars['values_count'] )): ?><?php $this->assign('values_count', 0); ?><?php endif; ?>
<?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['floating_buttons'] )): ?>_floating<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('button_id_suffix', ob_get_contents());ob_end_clean(); ?>

<div class="t_buttons<?php if ($this->_tpl_vars['floating_buttons']): ?> t_floating_buttons invisible" onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);<?php endif; ?>">
  <?php if (empty ( $this->_tpl_vars['var']['hide_multiple_rows_buttons'] ) && ! empty ( $this->_tpl_vars['var']['show_import_button'] )): ?>
    <?php echo smarty_function_json(array('assign' => 'query_params','encode' => $this->_tpl_vars['var']['show_import_button']), $this);?>
 
    <div onclick="importTableShowForm('var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
', <?php echo ((is_array($_tmp=$this->_tpl_vars['query_params'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
);" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['import'],'popup_only' => 1), $this);?>
>
      <div class="t_import" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['import_table'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"></div>
    </div>
  <?php endif; ?>
    <?php if (empty ( $this->_tpl_vars['var']['hide_multiple_rows_buttons'] ) && ! empty ( $this->_tpl_vars['var']['show_select_buttons'] )): ?>
    <?php $_from = $this->_tpl_vars['var']['show_select_buttons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?>
      <?php if (in_array ( $this->_tpl_vars['button'] , array ( 'nomenclatures' , 'customers' , 'documents' , 'projects' , 'tasks' , 'contracts' , 'users' , 'departments' ) )): ?>
                <?php ob_start(); ?>autocomplete_search_<?php echo $this->_tpl_vars['button']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('filter_items', ob_get_contents());ob_end_clean(); ?>
        <div id="<?php echo $this->_tpl_vars['button']; ?>
_filterButton<?php echo $this->_tpl_vars['button_id_suffix']; ?>
_<?php echo $this->_tpl_vars['var']['grouping']; ?>
" onclick="filterAutocompleteItems({table: 'var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
', type: '<?php echo $this->_tpl_vars['button']; ?>
'});" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['autocomplete_add_rows'],'text_content' => $this->_config[0]['vars'][$this->_tpl_vars['filter_items']],'popup_only' => 1), $this);?>
><div class="t_filter_<?php echo $this->_tpl_vars['button']; ?>
"></div></div>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
  <?php if (! empty ( $this->_tpl_vars['var']['show_refresh_buttons'] )): ?>
    <?php $_from = $this->_tpl_vars['var']['show_refresh_buttons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?>
      <?php if (in_array ( $this->_tpl_vars['button'] , array ( 'nomenclatures' , 'customers' , 'documents' , 'projects' , 'tasks' , 'contracts' , 'users' , 'departments' ) )): ?>
                <?php ob_start(); ?>autocomplete_refresh_<?php echo $this->_tpl_vars['button']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('refresh_items', ob_get_contents());ob_end_clean(); ?>
        <div id="<?php echo $this->_tpl_vars['button']; ?>
_refreshButton<?php echo $this->_tpl_vars['button_id_suffix']; ?>
_<?php echo $this->_tpl_vars['var']['grouping']; ?>
" onclick="refreshAutocompleteItems({table: 'var_group_<?php echo $this->_tpl_vars['var']['grouping']; ?>
', type: '<?php echo $this->_tpl_vars['button']; ?>
'});" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['autocomplete_refresh_items'],'text_content' => $this->_config[0]['vars'][$this->_tpl_vars['refresh_items']],'popup_only' => 1), $this);?>
><div class="t_refresh_<?php echo $this->_tpl_vars['button']; ?>
"></div></div>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
  <?php if (empty ( $this->_tpl_vars['var']['hide_multiple_rows_buttons'] )): ?>
    <?php echo '<div id="var_group_'; ?><?php echo $this->_tpl_vars['var']['grouping']; ?><?php echo '_plusButton'; ?><?php echo $this->_tpl_vars['button_id_suffix']; ?><?php echo '"'; ?><?php if ($this->_tpl_vars['values_count'] >= @PH_MAX_GROUP_ROWS): ?><?php echo ' class="disabled"'; ?><?php endif; ?><?php echo ' onclick="addField(\'var_group_'; ?><?php echo $this->_tpl_vars['var']['grouping']; ?><?php echo '\''; ?><?php if ($this->_tpl_vars['var']['type'] == 'grouping' && $this->_tpl_vars['var']['dont_copy_values']): ?><?php echo ',\'\',\'\',\''; ?><?php echo $this->_tpl_vars['var']['dont_copy_values']; ?><?php echo '\''; ?><?php endif; ?><?php echo ')'; ?><?php if ($this->_tpl_vars['var']['upon_table_rows_update']): ?><?php echo ';'; ?><?php echo $this->_tpl_vars['var']['upon_table_rows_update']; ?><?php echo ''; ?><?php endif; ?><?php echo '" '; ?><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?><?php echo '><div class="t_plus"></div></div><div id="var_group_'; ?><?php echo $this->_tpl_vars['var']['grouping']; ?><?php echo '_minusButton'; ?><?php echo $this->_tpl_vars['button_id_suffix']; ?><?php echo '"'; ?><?php if ($this->_tpl_vars['values_count'] <= 1): ?><?php echo ' class="disabled"'; ?><?php endif; ?><?php echo ' onclick="removeField(\'var_group_'; ?><?php echo $this->_tpl_vars['var']['grouping']; ?><?php echo '\')'; ?><?php if ($this->_tpl_vars['var']['upon_table_rows_update']): ?><?php echo ';'; ?><?php echo $this->_tpl_vars['var']['upon_table_rows_update']; ?><?php echo ''; ?><?php endif; ?><?php echo '" '; ?><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?><?php echo '><div class="t_minus"></div></div>'; ?>

  <?php endif; ?>
</div>