<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html', 7, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html', 17, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html', 24, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html', 25, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html', 23, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html', 25, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_legend.html', 29, false),)), $this); ?>
<?php if ($this->_tpl_vars['projects']): ?>
<table class="t_table" cellspacing="0" cellpadding="3" border="0" style="width: 100%;">
  <tr>
    <?php if ($this->_tpl_vars['filters']['project']): ?><td class="t_panel_caption t_panel_caption_title" style="width: 100px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_project'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td><?php endif; ?>
    <td class="t_panel_caption t_panel_caption_title t_border"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_task'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
</table>
<div class="t_table1" style="overflow: auto; width: 298px; max-height: 400px; border-top: 0px none;">
  <table class="t_table" cellspacing="0" cellpadding="3" border="0" style="border-color: transparent; width: 100%;">
    <?php $_from = $this->_tpl_vars['projects']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pid'] => $this->_tpl_vars['project']):
?>
    <?php $_from = $this->_tpl_vars['project']['tasks']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tid']):
        $this->_foreach['ti']['iteration']++;
?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo $this->_config[0]['vars']['tasks_status']; ?><?php echo ':</strong> <span class=\'tasks_status '; ?><?php echo $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['status']; ?><?php echo '\'>'; ?><?php echo $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['status_name']; ?><?php echo ''; ?><?php if ($this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['substatus_name']): ?><?php echo ' &raquo; '; ?><?php echo $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['substatus_name']; ?><?php echo ''; ?><?php endif; ?><?php echo '</span><br /><strong>'; ?><?php echo $this->_config[0]['vars']['tasks_planned_start_date']; ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['planned_start_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?><?php echo '<br /><strong>'; ?><?php echo $this->_config[0]['vars']['tasks_planned_finish_date']; ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['planned_finish_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?><?php echo '<br /><strong>'; ?><?php echo $this->_config[0]['vars']['tasks_planned_time']; ?><?php echo ':</strong> '; ?><?php echo $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['planned_time_formatted']; ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['status'] == 'progress'): ?><?php echo '<strong>'; ?><?php echo $this->_config[0]['vars']['tasks_timesheet_time']; ?><?php echo ':</strong> '; ?><?php echo $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['timesheet_time_formatted']; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo $this->_config[0]['vars']['tasks_assign_title']; ?><?php echo ':</strong> '; ?><?php $_from = $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['user_permissions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['up']):
?><?php echo ''; ?><?php if (isset ( $this->_tpl_vars['dashlet_data']['users'][$this->_tpl_vars['up']] )): ?><?php echo '<label class=\'user_'; ?><?php if ($this->_tpl_vars['dashlet_data']['users'][$this->_tpl_vars['up']]['is_portal']): ?><?php echo 'portal'; ?><?php else: ?><?php echo 'normal'; ?><?php endif; ?><?php echo '\'>'; ?><?php echo $this->_tpl_vars['dashlet_data']['users'][$this->_tpl_vars['up']]['label']; ?><?php echo '</label> '; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo '<br />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <tr id="project_<?php echo $this->_tpl_vars['pid']; ?>
" class="<?php echo smarty_function_cycle(array('values' => 't_odd2,t_even2'), $this);?>
 project_<?php echo $this->_tpl_vars['pid']; ?>
">
        <?php if (($this->_foreach['ti']['iteration'] <= 1) && $this->_tpl_vars['filters']['project']): ?><td class="t_border" rowspan="<?php if (is_array ( $this->_tpl_vars['project']['tasks'] )): ?><?php echo count($this->_tpl_vars['project']['tasks']); ?>
<?php else: ?>1<?php endif; ?>" style="width: 100px;"><?php echo ((is_array($_tmp=$this->_tpl_vars['project']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td><?php endif; ?>
        <td id="task_<?php echo $this->_tpl_vars['tid']; ?>
" class="task_<?php echo $this->_tpl_vars['tid']; ?>
 <?php echo $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['severity']; ?>
" style="background-color: #<?php echo ((is_array($_tmp=@$this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['background_color'])) ? $this->_run_mod_handler('default', true, $_tmp, 'cccccc') : smarty_modifier_default($_tmp, 'cccccc')); ?>
;" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['info'],'caption' => ((is_array($_tmp=@$this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['type_name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['task']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['task'])),'width' => 250), $this);?>
>
                      <?php if ($this->_tpl_vars['currentUser']->checkRights('tasks','edit')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
edit.png" class="pointer floatr" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="plannedTime.hideOverlib(); ajaxSaveTask('ajax_edit', '<?php echo $this->_tpl_vars['tid']; ?>
', 'dashlet_messages_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
', plannedTime.reloadTasks.bind(plannedTime));" /><?php endif; ?>
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=tasks&amp;tasks=view&amp;view=<?php echo $this->_tpl_vars['tid']; ?>
" target="_blank"><?php echo ((is_array($_tmp=$this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
            <span class="remaining_time" style="color: #000000;">(<?php if ($this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['planned_time']): ?><span class="hours"><?php echo smarty_function_math(array('equation' => 'floor(d/60)','d' => ((is_array($_tmp=@$this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['remaining_time'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0))), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['hours_short'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <span class="minutes"><?php echo $this->_tpl_vars['tasks'][$this->_tpl_vars['tid']]['remaining_time']%60; ?>
</span> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['minutes_short'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?>-<?php endif; ?>)</span>
                  </td>
      </tr>
    <?php endforeach; endif; unset($_from); ?>
    <?php endforeach; endif; unset($_from); ?>
  </table>
</div>
<?php endif; ?>