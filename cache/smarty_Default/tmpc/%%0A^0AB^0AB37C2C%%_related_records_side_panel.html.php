<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:52
         compiled from _related_records_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'math', '_related_records_side_panel.html', 6, false),array('function', 'cycle', '_related_records_side_panel.html', 8, false),array('modifier', 'count', '_related_records_side_panel.html', 12, false),array('modifier', 'escape', '_related_records_side_panel.html', 14, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<?php $this->assign('cnum', 10); ?>
<?php $this->assign('cwidth', 60); ?>
<table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
<?php $_from = $this->_tpl_vars['related_records']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['rr'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['rr']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['record']):
        $this->_foreach['rr']['iteration']++;
?>
<?php echo smarty_function_math(array('equation' => "a%b",'a' => $this->_foreach['rr']['iteration'],'b' => $this->_tpl_vars['cnum'],'assign' => 'cell'), $this);?>

<?php if ($this->_tpl_vars['cell'] == '1'): ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
<?php endif; ?>
    <?php ob_start(); ?><?php if (preg_match ( '/^referent_/' , $this->_tpl_vars['record']['name'] )): ?>referent_records<?php else: ?><?php echo $this->_tpl_vars['record']['name']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('img_name', ob_get_contents());ob_end_clean(); ?>
    <td style="white-space: nowrap; max-width: <?php echo $this->_tpl_vars['cwidth']; ?>
px; padding-right: 0px; padding-left: 0px;">
      <?php if (count($this->_tpl_vars['record']['ids']) != 0): ?>
      <a href="<?php echo $this->_tpl_vars['record']['link']; ?>
"<?php if (preg_match ( '/^#related_subpanel/' , $this->_tpl_vars['record']['link'] )): ?>onclick="toggleRelatedTabs($('tab_<?php echo $this->_tpl_vars['record']['name']; ?>
')); $('rel_type').value='<?php echo $this->_tpl_vars['record']['name']; ?>
';"<?php endif; ?>>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['img_name']; ?>
.png" alt="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="border: 0px; margin-left: 5px;" class="floatl" />
        <span style="padding: 2px 0 2px 3px;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="strong floatl"><?php echo count($this->_tpl_vars['record']['ids']); ?>
</span>
      </a>
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['img_name']; ?>
.png" alt="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="border: 0px; margin-left: 5px;" class="dimmed floatl" />
        <span style="padding: 2px 0 2px 3px;" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['record']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="floatl">-</span>
      <?php endif; ?>
    </td>
<?php if ($this->_tpl_vars['cell'] == '0'): ?>
  </tr>
<?php elseif (($this->_foreach['rr']['iteration'] == $this->_foreach['rr']['total'])): ?>
    <?php echo smarty_function_math(array('assign' => 'colspan','equation' => 'y-x%y','x' => $this->_tpl_vars['cell'],'y' => $this->_tpl_vars['cnum']), $this);?>

    <td colspan="<?php echo $this->_tpl_vars['colspan']; ?>
" style="width: <?php echo smarty_function_math(array('equation' => 'y*x','x' => $this->_tpl_vars['cwidth'],'y' => $this->_tpl_vars['colspan']), $this);?>
px;">&nbsp;</td>
  </tr>
<?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</table>
<?php endif; ?>