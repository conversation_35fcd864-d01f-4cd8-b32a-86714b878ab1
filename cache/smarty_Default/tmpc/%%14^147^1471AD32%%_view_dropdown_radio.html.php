<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from _view_dropdown_radio.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_view_dropdown_radio.html', 8, false),array('modifier', 'default', '_view_dropdown_radio.html', 8, false),array('modifier', 'regex_replace', '_view_dropdown_radio.html', 13, false),)), $this); ?>
<?php if ($this->_tpl_vars['extended_value'] || $this->_tpl_vars['extended_value'] === '0'): ?>
        <?php echo $this->_tpl_vars['extended_value']; ?>

<?php elseif ($this->_tpl_vars['var']['options']): ?>
    <?php $_from = $this->_tpl_vars['var']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
        <?php if (( is_array ( $this->_tpl_vars['value'] ) && @ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] ) ) || ( ! is_array ( $this->_tpl_vars['value'] ) && $this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['value'] )): ?>
            <?php echo ''; ?><?php if ($this->_tpl_vars['var']['view_mode'] == 'link' && $this->_tpl_vars['var']['view_mode_url']): ?><?php echo '<a target="_blank" href="'; ?><?php echo $this->_tpl_vars['var']['view_mode_url']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['value']; ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ': '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?><?php echo '">'; ?><?php endif; ?><?php echo ''; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?><?php echo '<span class="inactive_option" title="'; ?><?php echo $this->_config[0]['vars']['inactive_option']; ?><?php echo '">*'; ?><?php endif; ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "/&nbsp;/", ' ') : smarty_modifier_regex_replace($_tmp, "/&nbsp;/", ' ')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?><?php echo '</span>'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['view_mode'] == 'link' && $this->_tpl_vars['var']['view_mode_url']): ?><?php echo '</a>'; ?><?php endif; ?><?php echo ''; ?>

        <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
<?php elseif ($this->_tpl_vars['var']['optgroups']): ?>
    <?php $_from = $this->_tpl_vars['var']['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optgroup_name'] => $this->_tpl_vars['optgroup']):
?>
        <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
            <?php if (( is_array ( $this->_tpl_vars['value'] ) && @ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] ) ) || ( ! is_array ( $this->_tpl_vars['value'] ) && $this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['value'] )): ?>
                <?php echo ''; ?><?php if ($this->_tpl_vars['var']['view_mode'] == 'link' && $this->_tpl_vars['var']['view_mode_url']): ?><?php echo '<a target="_blank" href="'; ?><?php echo $this->_tpl_vars['var']['view_mode_url']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['value']; ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ': '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?><?php echo '">'; ?><?php endif; ?><?php echo ''; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?><?php echo '<span class="inactive_option" title="'; ?><?php echo $this->_config[0]['vars']['inactive_option']; ?><?php echo '">*'; ?><?php endif; ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "/&nbsp;/", ' ') : smarty_modifier_regex_replace($_tmp, "/&nbsp;/", ' ')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?><?php echo '</span>'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['view_mode'] == 'link' && $this->_tpl_vars['var']['view_mode_url']): ?><?php echo '</a>'; ?><?php endif; ?><?php echo ''; ?>

            <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
    <?php endforeach; endif; unset($_from); ?>
<?php endif; ?>