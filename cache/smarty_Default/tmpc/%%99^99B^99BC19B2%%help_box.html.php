<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/help_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/help_box.html', 6, false),)), $this); ?>
<?php if (( $this->_tpl_vars['help_text'] )): ?>
  <div id="help_container">
    <div id="help_details"<?php if ($_COOKIE['help_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
      <table cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
          <td class="t_caption" colspan="3"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['help_information_panel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
        </tr>
        <tr>
          <td class="help_information_panel" colspan="3"><?php echo $this->_tpl_vars['help_text']->get('content'); ?>
</td>
        </tr>
      </table>
    </div>
    <div class="clear"></div>
    <div class="t_footer" id="help_switch" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['help_show'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><div class="<?php if ($_COOKIE['help_box'] == 'off'): ?>switch_down<?php else: ?>switch_up<?php endif; ?>"></div></div>
  </div>
<?php else: ?>
  <div class="t_footer help_footer"></div>
<?php endif; ?>