<?php /* Smarty version 2.6.33, created on 2025-05-21 16:13:40
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_customers_contacts_data.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_customers_contacts_data.html', 1, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_customers_contacts_data.html', 4, false),)), $this); ?>
                      <?php echo smarty_function_counter(array('name' => 'k','start' => 0,'print' => false), $this);?>

                      <?php if ($this->_tpl_vars['customers_info']->get('email')): ?>
                        <?php $_from = $this->_tpl_vars['customers_info']->get('email'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['emails']):
        $this->_foreach['i']['iteration']++;
?>
                          <a href="mailto:<?php echo $this->_tpl_vars['emails']; ?>
" target="_self"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
email.png" alt="EMAIL:" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /> <?php echo $this->_tpl_vars['emails']; ?>
</a><br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['customers_info']->get('web')): ?>
                        <?php $_from = $this->_tpl_vars['customers_info']->get('web'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['web_link']):
        $this->_foreach['i']['iteration']++;
?>
                          <a href="http://<?php echo $this->_tpl_vars['web_link']; ?>
" target="_blank"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
website.png" alt="WEB SITE:" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_web'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /> <?php echo $this->_tpl_vars['web_link']; ?>
</a><br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['customers_info']->get('phone')): ?>
                        <?php $_from = $this->_tpl_vars['customers_info']->get('phone'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['phones']):
        $this->_foreach['i']['iteration']++;
?>
                          <?php if ($this->_tpl_vars['use_asterisk']): ?>
                            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => 'phone','number' => $this->_tpl_vars['phones'],'label' => 'customers_phone')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                          <?php else: ?>
                            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
phone.png" alt="PHONE:" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_phone'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /> <?php echo $this->_tpl_vars['phones']; ?>
<br />
                          <?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['customers_info']->get('fax')): ?>
                        <?php $_from = $this->_tpl_vars['customers_info']->get('fax'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['faxes']):
        $this->_foreach['i']['iteration']++;
?>
                          <?php if ($this->_tpl_vars['use_asterisk']): ?>
                            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => 'fax','number' => $this->_tpl_vars['faxes'],'label' => 'customers_fax')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                          <?php else: ?>
                            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
fax.png" alt="FAX:" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_fax'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /> <?php echo $this->_tpl_vars['faxes']; ?>
<br />
                          <?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['customers_info']->get('gsm')): ?>
                        <?php $_from = $this->_tpl_vars['customers_info']->get('gsm'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['gsms']):
        $this->_foreach['i']['iteration']++;
?>
                          <?php if ($this->_tpl_vars['use_asterisk']): ?>
                            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => 'gsm','number' => $this->_tpl_vars['gsms'],'label' => 'customers_gsm')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                          <?php else: ?>
                            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
gsm.png" alt="GSM:" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_gsm'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /> <?php echo $this->_tpl_vars['gsms']; ?>
<br />
                          <?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['customers_info']->get('skype')): ?>
                        <?php $_from = $this->_tpl_vars['customers_info']->get('skype'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['skypes']):
        $this->_foreach['i']['iteration']++;
?>
                          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
skype.png" alt="SKYPE: " title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_skype'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /> <?php echo $this->_tpl_vars['skypes']; ?>
<br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['customers_info']->get('othercontact')): ?>
                        <?php $_from = $this->_tpl_vars['customers_info']->get('othercontact'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['othercontacts']):
        $this->_foreach['i']['iteration']++;
?>
                          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
othercontact.png" alt="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_othercontact'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /> <?php echo $this->_tpl_vars['othercontacts']; ?>
<br />
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>