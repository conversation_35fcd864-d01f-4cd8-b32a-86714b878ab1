<?php /* Smarty version 2.6.33, created on 2025-05-21 16:06:01
         compiled from _communication_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_communication_panel.html', 5, false),array('modifier', 'default', '_communication_panel.html', 5, false),)), $this); ?>
              <table border="0" cellspacing="0" cellpadding="0" width="100%">
                <tr>
                  <td align="center" style="font-weight: bold; color:#888888;">
                    <?php $_from = $this->_tpl_vars['allowed_actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['aa'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['aa']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['allowed_action']):
        $this->_foreach['aa']['iteration']++;
?>
                      <a onclick="manageCommunicationTabs(this, '<?php echo $this->_tpl_vars['current_model']->get('id'); ?>
'<?php if ($this->_tpl_vars['current_model']->get('archived_by')): ?>, 1<?php endif; ?>)" id="communication_tab_<?php echo $this->_tpl_vars['allowed_action']['name']; ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['allowed_action']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="font-size:13px; " class="pointer communication_panel_tabs<?php if ($this->_tpl_vars['allowed_action']['selected']): ?> communication_tab_selected<?php endif; ?>"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['allowed_action']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                      <?php if (! ($this->_foreach['aa']['iteration'] == $this->_foreach['aa']['total'])): ?>
                        | 
                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                  </td>
                </tr>
                <tr>
                  <td>
                    <div id="<?php echo $this->_tpl_vars['communications_session_param']; ?>
">
                      <?php if ($this->_tpl_vars['communication_type'] == 'minitasks'): ?>
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_communication_minitasks_list_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php else: ?>
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_communication_list_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php endif; ?>
                    </div>
                    <div id="communications_errors"></div>
                  </td>
                </tr>
                <tr>
                  <td nowrap="nowrap" class="m_header_menu t_table" style="border-bottom: 1px solid #DDDDDD; padding: 0;">
                    <ul>
                      <?php $_from = $this->_tpl_vars['allowed_actions_add']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['aa1'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['aa1']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['allowed_action']):
        $this->_foreach['aa1']['iteration']++;
?>
                        <?php if (( $this->_tpl_vars['allowed_action'] == 'comments' )): ?>
                          <li<?php if (! $this->_tpl_vars['active_add_tab']): ?> style="display: none;"<?php endif; ?>><span<?php if ($this->_tpl_vars['allowed_action'] == $this->_tpl_vars['active_add_tab']): ?> class="selected"<?php endif; ?>><a onclick="manageCommunicationAddPanels('comment', 'add', '<?php echo $this->_tpl_vars['current_model']->get('id'); ?>
')" id="comment_tab" class="pointer"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
comments.png" width="16" height="16" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_comments_add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_comments_add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" style="vertical-align: middle;" /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_comments_add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
                        <?php elseif (( $this->_tpl_vars['allowed_action'] == 'emails' )): ?>
                          <li<?php if (! $this->_tpl_vars['active_add_tab']): ?> style="display: none;"<?php endif; ?>><span<?php if ($this->_tpl_vars['allowed_action'] == $this->_tpl_vars['active_add_tab']): ?> class="selected"<?php endif; ?>><a onclick="manageCommunicationAddPanels('email', 'add', '<?php echo $this->_tpl_vars['current_model']->get('id'); ?>
')" id="email_tab" class="pointer"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
email.png" width="16" height="16" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_emails_add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_emails_add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" style="vertical-align: middle;" /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_emails_add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span></li>
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    </ul>
                  </td>
                </tr>
                <tr>
                  <td class="nopadding">
                    <div id="add_panel">
                      <?php if ($this->_tpl_vars['active_add_tab'] == 'comments'): ?>
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_communication_comments_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php elseif ($this->_tpl_vars['active_add_tab'] == 'emails'): ?>
                        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_communication_emails_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
              </table>