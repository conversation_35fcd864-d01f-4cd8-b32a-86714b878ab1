<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from view_file_upload.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'view_file_upload.html', 2, false),array('function', 'getimagesize', 'view_file_upload.html', 9, false),array('modifier', 'encrypt', 'view_file_upload.html', 10, false),array('modifier', 'escape', 'view_file_upload.html', 10, false),)), $this); ?>
        <tr<?php if ($this->_tpl_vars['var']['hidden']): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['var']['help']), $this);?>
</td>
          <td class="required"><?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap" style="vertical-align: top;">
            <?php $this->assign('file_info', $this->_tpl_vars['var']['value']); ?>
            <?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?>
              <?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?>
                <?php if ($this->_tpl_vars['var']['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?>
                  <?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?>

                  <img src="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=files&amp;files=viewfile&amp;viewfile=<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?><?php if ($this->_tpl_vars['var']['thumb_width']): ?>&amp;maxwidth=<?php echo $this->_tpl_vars['var']['thumb_width']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['var']['thumb_height']): ?>&amp;maxheight=<?php echo $this->_tpl_vars['var']['thumb_height']; ?>
<?php endif; ?>" onclick="showFullLBImage('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', '<?php echo $this->_tpl_vars['var']['label']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['width']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['height']; ?>
')" alt="" />
                <?php else: ?>
                  <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>" target="_blank"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                  <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                <?php endif; ?>
              <?php else: ?>
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
              <?php endif; ?>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>

                        <?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('back_label' => $this->_tpl_vars['var']['back_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
          </td>
        </tr>