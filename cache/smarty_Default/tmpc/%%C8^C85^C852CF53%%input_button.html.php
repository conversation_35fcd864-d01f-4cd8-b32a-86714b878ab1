<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:59
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/input_button.html */ ?>
<?php echo ''; ?><?php echo ''; ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone'] && $this->_tpl_vars['width']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['width']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo 'px'; ?><?php elseif ($this->_tpl_vars['height']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if (! $this->_tpl_vars['standalone']): ?><?php echo '<tr'; ?><?php if ($this->_tpl_vars['hidden']): ?><?php echo ' style="display: none"'; ?><?php endif; ?><?php echo '>'; ?><?php echo '<td class="labelbox">&nbsp;</td>'; ?><?php echo '<td'; ?><?php if ($this->_tpl_vars['required']): ?><?php echo ' class="required">'; ?><?php echo $this->_config[0]['vars']['required']; ?><?php echo ''; ?><?php else: ?><?php echo ' class="unrequired">&nbsp;'; ?><?php endif; ?><?php echo '</td>'; ?><?php echo '<td nowrap="nowrap">'; ?><?php endif; ?><?php echo ''; ?><?php echo '<button type="button" name="'; ?><?php echo $this->_tpl_vars['name']; ?><?php echo '" id="'; ?><?php echo $this->_tpl_vars['name']; ?><?php echo '"'; ?><?php if ($this->_tpl_vars['source']['onclick']): ?><?php echo ' onclick="'; ?><?php echo $this->_tpl_vars['source']['onclick']; ?><?php echo '"'; ?><?php endif; ?><?php echo ' class="button'; ?><?php if ($this->_tpl_vars['custom_class']): ?><?php echo ' '; ?><?php echo $this->_tpl_vars['custom_class']; ?><?php echo ''; ?><?php endif; ?><?php echo '" style="'; ?><?php if ($this->_tpl_vars['hidden']): ?><?php echo 'display: none;'; ?><?php elseif ($this->_tpl_vars['width']): ?><?php echo 'width: '; ?><?php echo $this->_tpl_vars['width']; ?><?php echo ';'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['height']): ?><?php echo 'height: '; ?><?php echo $this->_tpl_vars['height']; ?><?php echo ';'; ?><?php endif; ?><?php echo '" '; ?><?php if ($this->_tpl_vars['disabled']): ?><?php echo ' disabled="disabled"'; ?><?php endif; ?><?php echo '>'; ?><?php echo $this->_tpl_vars['label']; ?><?php echo '</button>'; ?><?php if (! $this->_tpl_vars['standalone']): ?><?php echo '</td></tr>'; ?><?php endif; ?><?php echo ''; ?>