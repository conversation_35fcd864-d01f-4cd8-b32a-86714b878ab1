<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/templates/_view_vars.html */ ?>
          <?php $this->assign('layout_id', $this->_tpl_vars['layout']['id']); ?>
          <?php $this->assign('vars', $this->_tpl_vars['layouts_vars'][$this->_tpl_vars['layout_id']]); ?>
          <?php if ($this->_tpl_vars['layout']['id']): ?>
          <tr id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_box"<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
            <td class="nopadding" colspan="3">
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
          <?php endif; ?>
          <?php $_from = $this->_tpl_vars['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var']):
        $this->_foreach['j']['iteration']++;
?>
            <?php if ($this->_tpl_vars['var']['type']): ?>
              <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

              <?php if ($this->_tpl_vars['var']['type'] == 'gt2'): ?>
                <tr>
                  <td colspan="3">
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_view.html", 'smarty_include_vars' => array('table' => $this->_tpl_vars['var'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  </td>
                </tr>
              <?php else: ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "view_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php endif; ?>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
          <?php if ($this->_tpl_vars['layout']['id']): ?>
              </table>
            </td>
          </tr>
          <?php endif; ?>