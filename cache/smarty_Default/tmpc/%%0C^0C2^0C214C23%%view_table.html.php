<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from view_table.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'view_table.html', 6, false),array('function', 'getimagesize', 'view_table.html', 45, false),array('modifier', 'escape', 'view_table.html', 15, false),array('modifier', 'url2href', 'view_table.html', 15, false),array('modifier', 'nl2br', 'view_table.html', 15, false),array('modifier', 'default', 'view_table.html', 15, false),array('modifier', 'date_format', 'view_table.html', 29, false),array('modifier', 'encrypt', 'view_table.html', 46, false),)), $this); ?>
<tr><td colspan="3"><?php if (trim ( $this->_tpl_vars['var']['label'] )): ?><div class="t_caption2_title"><?php echo $this->_tpl_vars['var']['label']; ?>
</div><?php endif; ?>
<table class="t_grouping_table<?php if ($this->_tpl_vars['var']['borderless']): ?> t_borderless<?php endif; ?>"<?php if ($this->_tpl_vars['var']['t_width']): ?> width="<?php echo $this->_tpl_vars['var']['t_width']; ?>
"<?php endif; ?>><tr>
<?php $_from = $this->_tpl_vars['var']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['label']):
        $this->_foreach['i']['iteration']++;
?>
<?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?>
  <?php ob_start(); ?><?php if ($this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['help'][$this->_tpl_vars['key']]; ?>
<?php else: ?><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
  <th<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]): ?> width="<?php echo $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]; ?>
"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['info']), $this);?>
<?php if ($this->_tpl_vars['var']['required'][$this->_tpl_vars['key']]): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></th>
<?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</tr>
<tr>
<?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
?>
<?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']]): ?>
  <td style="<?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align']): ?>text-align: <?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['text_align']; ?>
;<?php endif; ?>">
  <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'text' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'textarea'): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter'): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']],'value_id' => $this->_tpl_vars['var'][$this->_tpl_vars['name']]['value_id'],'view_mode_url' => $this->_tpl_vars['var']['autocomplete'][$this->_tpl_vars['name']]['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'formula'): ?>
    <?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['values'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

    <?php if ($this->_tpl_vars['var']['formula'][$this->_tpl_vars['key']]): ?>
      <?php $this->assign('formula_index', $this->_tpl_vars['var']['formula'][$this->_tpl_vars['key']]['value']); ?>
      (<?php echo $this->_tpl_vars['formulas'][$this->_tpl_vars['formula_index']]['label']; ?>
)
    <?php endif; ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'index'): ?>
    <?php $this->assign('formula_index', $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']]); ?>
    <?php echo $this->_tpl_vars['indexes'][$this->_tpl_vars['formula_index']]['label']; ?>
 - <?php if ($this->_tpl_vars['var']['index'][$this->_tpl_vars['key']]['date'] != '0000-00-00'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var']['index'][$this->_tpl_vars['key']]['date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>
<?php endif; ?>
    <?php if ($this->_tpl_vars['var']['index'][$this->_tpl_vars['key']]['formula']): ?>
      <?php $this->assign('formula_index', $this->_tpl_vars['var']['index'][$this->_tpl_vars['key']]['formula']); ?>
      (<?php echo $this->_tpl_vars['formulas'][$this->_tpl_vars['formula_index']]['label']; ?>
)
    <?php endif; ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'datetime' && $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] != 0): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'time' && $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] !== 0 && $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] !== ''): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'date' && $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] != 0): ?>
    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['values'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'file_upload'): ?>
    <?php $this->assign('file_info', $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']]); ?>
    <?php if (! empty ( $this->_tpl_vars['file_info'] ) && is_object ( $this->_tpl_vars['file_info'] ) && ! $this->_tpl_vars['file_info']->get('deleted_by')): ?>
      <?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?>
        <?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?>
          <?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?>

          <img src="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=files&amp;files=viewfile&amp;viewfile=<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']): ?>&amp;maxwidth=<?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_width']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']): ?>&amp;maxheight=<?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['thumb_height']; ?>
<?php endif; ?>" onclick="showFullLBImage('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>', '<?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['width']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['height']; ?>
')" alt="" />
        <?php else: ?>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
<?php if ($this->_tpl_vars['file_info']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>" target="_blank"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
        <?php endif; ?>
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
      <?php endif; ?>
    <?php else: ?>
      &nbsp;
    <?php endif; ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'dropdown' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'radio'): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_dropdown_radio.html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'][$this->_tpl_vars['name']],'value' => $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'checkbox_group'): ?>
    <?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] ) > 16): ?><div class="scroll_box" style="width:100%!important;"><?php endif; ?>
    <?php if ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']): ?>
      <?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
        <?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] )): ?>
          <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?><span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*<?php endif; ?>
          <?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] ) && $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] == 'horizontal'): ?>&nbsp;<?php else: ?><br /><?php endif; ?><?php else: ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" /><?php endif; ?>
          <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?></span><?php endif; ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php elseif ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups']): ?>
      <?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optgroup_name'] => $this->_tpl_vars['optgroup']):
?>
        <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
          <?php if (@ in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] )): ?>
            <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?><span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*<?php endif; ?>
            <?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] ) && $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options_align'] == 'horizontal'): ?>&nbsp;<?php else: ?><br /><?php endif; ?><?php else: ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" /><?php endif; ?>
            <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?></span><?php endif; ?>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
    <?php if (is_array ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] ) && count ( $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] ) > 16): ?></div><?php endif; ?>
  <?php endif; ?>

    <?php if (( $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] || $this->_tpl_vars['var']['values'][$this->_tpl_vars['key']] === '0' ) && $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label']): ?><?php echo $this->_tpl_vars['var'][$this->_tpl_vars['name']]['back_label']; ?>
<?php endif; ?>
  </td>
<?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</tr>
</table>
</td></tr>