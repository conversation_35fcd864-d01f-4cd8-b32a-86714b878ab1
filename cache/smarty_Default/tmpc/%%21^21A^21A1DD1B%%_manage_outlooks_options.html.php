<?php /* Smarty version 2.6.33, created on 2023-11-15 17:44:53
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_manage_outlooks_options.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_manage_outlooks_options.html', 1, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_manage_outlooks_options.html', 10, false),)), $this); ?>
                  <form name="outlooks" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=outlooks&amp;outlooks=manage_outlook" method="post"<?php if ($this->_tpl_vars['redirect_to_url'] && $this->_tpl_vars['update_target']): ?> onsubmit="ajaxUpdater({link: this.action, target: '<?php echo $this->_tpl_vars['update_target']; ?>
', parameters: Form.serialize(this) + '&use_ajax=1&redirect_to_url=<?php echo ((is_array($_tmp=$this->_tpl_vars['redirect_to_url'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
' + ($$('#<?php echo $this->_tpl_vars['update_target']; ?>
 .page_menu_current_page').length ? $$('#<?php echo $this->_tpl_vars['update_target']; ?>
 .page_menu_current_page')[0].innerHTML : 1)}); cancelOutlooksEdition(null, 1); return false;"<?php endif; ?>>
                    <div id="outlook_options" class="outlook_options">
                      <div id="outlook_options_title" class="t_caption3 strong ">
                        <div style="float: right" class="drag">
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/move.png" alt="" width="10" height="10" title="<?php echo $this->_config[0]['vars']['draggable']; ?>
" />
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete2.png" width="10" height="10" alt="" onclick="cancelOutlooksEdition(this)" title="<?php echo $this->_config[0]['vars']['close']; ?>
" style="cursor: default" />
                        </div>

                        <div class="drag">
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" alt="" style="padding-right: 1px;cursor: help" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['outlooks_personal_settings'],'text_content' => $this->_config[0]['vars']['help_outlooks_personal_settings'],'popup_only' => 1), $this);?>
 /> <?php echo $this->_config[0]['vars']['outlooks_personal_settings']; ?>

                        </div>

                      </div>
                      <div id="outlook_all_options" class="outlook_all_options"></div>
                      <div id="outlook_options_buttons" class="outlook_options_buttons t_caption3_reverted">
                        <div id="outlook_restore_defaults" class="hidden" style="padding-bottom: 3px;">
                          <input type="checkbox" id="restore_defaults" name="restore_defaults" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['load_defaults'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <label for="restore_defaults"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['load_defaults'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                        </div>
                        <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><button type="button" name="cancel" class="button" onclick="cancelOutlooksEdition(this)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
                      </div>
                    </div>
                  </form>
                  <script type="text/javascript">
                      new Draggable('outlook_options', {handle: 'outlook_options_title'});
                  </script>