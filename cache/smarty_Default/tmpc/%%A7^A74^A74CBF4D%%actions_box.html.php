<?php /* Smarty version 2.6.33, created on 2023-11-15 17:44:53
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/actions_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/actions_box.html', 4, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/actions_box.html', 21, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/actions_box.html', 25, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/actions_box.html', 143, false),)), $this); ?>
<?php if ($this->_tpl_vars['available_actions']): ?>
  <div style="position: relative;">
    <div class="action_tabs" id="action_tabs">
      <ul id="model_actions_<?php echo smarty_function_counter(array('start' => 1,'name' => 'menu_counter','print' => true,'assign' => 'model_action_menu_num'), $this);?>
" class="zpHideOnLoad">
        <?php $_from = $this->_tpl_vars['available_actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
          <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
            </ul>
            <script type="text/javascript">
              new Zapatec.Menu({source: 'model_actions_<?php echo $this->_tpl_vars['model_action_menu_num']; ?>
',
                                hideDelay: 100,
                                theme: 'nzoom'});
            </script>
            <div class="clear"></div>
              <ul id="model_actions_<?php echo smarty_function_counter(array('name' => 'menu_counter','print' => true,'assign' => 'model_action_menu_num'), $this);?>
" class="zpHideOnLoad">
          <?php else: ?>
            <?php if ($this->_tpl_vars['available_action']['drop_menu']): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_drop_menu_item.html", 'smarty_include_vars' => array('action_options' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <li class="<?php if ($this->_tpl_vars['available_action']['selected']): ?>menu-path<?php endif; ?><?php if (! $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?> tab_no_label<?php endif; ?>" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['available_action']['img']): ?><?php echo $this->_tpl_vars['available_action']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['name']; ?>
<?php endif; ?>.png" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" border="0" />
                <a <?php if ($this->_tpl_vars['available_action']['options']): ?>href="#"<?php else: ?>href="<?php echo $this->_tpl_vars['available_action']['url']; ?>
"<?php if ($this->_tpl_vars['available_action']['target']): ?> target="<?php echo $this->_tpl_vars['available_action']['target']; ?>
"<?php endif; ?><?php endif; ?> title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"><?php if ($this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['available_action']['label'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20, '...', true) : smarty_modifier_mb_truncate($_tmp, 20, '...', true)); ?>
<?php endif; ?></a>
                                <span style="display: none;"
                <?php if ($this->_tpl_vars['available_action']['options']): ?> onclick="if (null === $('<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go')) if ('<?php echo $this->_tpl_vars['available_action']['ajax_no']; ?>
' != '1') {getActionOptions('td_<?php echo $this->_tpl_vars['available_action']['name']; ?>
_options', '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', '<?php echo $this->_tpl_vars['available_action']['name']; ?>
', <?php if ($this->_tpl_vars['model'] && $this->_tpl_vars['model']->get('id')): ?>'<?php echo $this->_tpl_vars['model']->get('id'); ?>
'<?php else: ?>0<?php endif; ?>, {});}toggleActionOptions(this); return false;"
                <?php elseif ($this->_tpl_vars['available_action']['confirm']): ?> onclick="return confirmAction('<?php echo $this->_tpl_vars['available_action']['name']; ?>
', function(el) { window.open('<?php echo $this->_tpl_vars['available_action']['url']; ?>
', '<?php echo ((is_array($_tmp=@$this->_tpl_vars['available_action']['target'])) ? $this->_run_mod_handler('default', true, $_tmp, '_self') : smarty_modifier_default($_tmp, '_self')); ?>
'); }, this, i18n['messages']['confirm_<?php echo ((is_array($_tmp=@$this->_tpl_vars['available_action']['confirm_label'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['available_action']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['available_action']['name'])); ?>
']);"
                <?php elseif ($this->_tpl_vars['available_action']['onclick']): ?> onclick="<?php echo $this->_tpl_vars['available_action']['onclick']; ?>
"<?php endif; ?> id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_action"></span>
              </li>
            <?php endif; ?>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      </ul>
      <script type="text/javascript">
          new Zapatec.Menu({source: 'model_actions_<?php echo $this->_tpl_vars['model_action_menu_num']; ?>
',
                            hideDelay: 100,
                            theme: 'nzoom'});
      </script>
    </div>

    <?php if (! empty ( $this->_tpl_vars['available_actions_upper_right'] )): ?>
      <div class="action_tabs action_tabs_submenu_upper_right">
        <ul id="model_actions_right_<?php echo smarty_function_counter(array('start' => 1,'name' => 'menu_counter_upper_right','assign' => 'model_action_upper_right_menu_num','print' => true), $this);?>
" class="zpHideOnLoad">
          <?php $_from = $this->_tpl_vars['available_actions_upper_right']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
            <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
              </ul>
              <script type="text/javascript">
                  new Zapatec.Menu({source: 'model_actions_right_<?php echo $this->_tpl_vars['model_action_upper_right_menu_num']; ?>
',
                                    hideDelay: 100,
                                    theme: 'nzoom'});
              </script>
              <div class="clear"></div>
                <ul id="model_actions_<?php echo smarty_function_counter(array('name' => 'menu_counter_upper_right','print' => true,'assign' => 'model_action_upper_right_menu_num'), $this);?>
" class="zpHideOnLoad">
            <?php elseif ($this->_tpl_vars['available_action']['drop_menu']): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_drop_menu_item.html", 'smarty_include_vars' => array('action_options' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif ($this->_tpl_vars['available_action']['action'] == 'manage_outlooks'): ?>
              <?php $this->assign('show_manage_outlook_form', '1'); ?>
              <li title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['available_action']['img']): ?><?php echo $this->_tpl_vars['available_action']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['name']; ?>
<?php endif; ?>.png" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" border="0" onclick="showAvailableOutlookOptions('<?php echo $this->_tpl_vars['available_action']['module']; ?>
|<?php if ($this->_tpl_vars['available_action']['controller']): ?><?php echo $this->_tpl_vars['available_action']['controller']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['module']; ?>
<?php endif; ?>', '<?php echo $this->_tpl_vars['custom_template_options']['name']; ?>
', '<?php echo $this->_tpl_vars['custom_template_options']['value']; ?>
')" style="cursor:pointer;" />
              </li>
            <?php elseif ($this->_tpl_vars['available_action']['action'] != 'manage_outlooks'): ?>
              <li title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" class="tab_no_label"><a href="<?php echo $this->_tpl_vars['available_action']['url']; ?>
"<?php if ($this->_tpl_vars['available_action']['target']): ?> target="<?php echo $this->_tpl_vars['available_action']['target']; ?>
"<?php endif; ?> id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_action" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['available_action']['img']): ?><?php echo $this->_tpl_vars['available_action']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['name']; ?>
<?php endif; ?>.png" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" border="0" /></a>
              </li>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        </ul>
        <?php if ($this->_tpl_vars['show_manage_outlook_form']): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_manage_outlooks_options.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
        <script type="text/javascript">
            new Zapatec.Menu({source: 'model_actions_right_<?php echo $this->_tpl_vars['model_action_upper_right_menu_num']; ?>
',
                              hideDelay: 100,
                              theme: 'nzoom'});
        </script>
      </div>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['action'] == 'add' && $this->_tpl_vars['module'] != 'customers'): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layouts_index.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>

    <div class="clear"></div>
    <?php if ($this->_tpl_vars['available_actions_filter']): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_filter_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <div class="clear"></div>
    <?php endif; ?>
  </div>
  <div id="available_options_container">
    <?php $_from = $this->_tpl_vars['available_actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
      <?php if ($this->_tpl_vars['available_action']['options']): ?>
        <?php if ($this->_tpl_vars['available_action']['name'] == 'filter'): ?>
          <script type="text/javascript">
          Event.observe(window, 'load', getSearchOptionsExclusively);
          function getSearchOptionsExclusively() {
            getActionOptions('td_<?php echo $this->_tpl_vars['available_action']['name']; ?>
_options', '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', '<?php echo $this->_tpl_vars['available_action']['name']; ?>
', <?php if ($this->_tpl_vars['model'] && $this->_tpl_vars['model']->get('id')): ?>'<?php echo $this->_tpl_vars['model']->get('id'); ?>
'<?php else: ?>0<?php endif; ?>, {<?php if ($_GET['autocomplete_filter']): ?>autocomplete_filter: '<?php echo $_GET['autocomplete_filter']; ?>
'<?php endif; ?>});
            scalePopup();
            }
          </script>
        <?php endif; ?>
        <div class="available_options" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_options" style="display: <?php if ($this->_tpl_vars['available_action']['expanded']): ?>block;<?php else: ?>none;<?php endif; ?>">
          <div>
          <form method="<?php echo ((is_array($_tmp=@$this->_tpl_vars['available_action']['options']['form_method'])) ? $this->_run_mod_handler('default', true, $_tmp, 'get') : smarty_modifier_default($_tmp, 'get')); ?>
" action="<?php echo $_SERVER['PHP_SELF']; ?>
" enctype="multipart/form-data">
            <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['module_param']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" />
            <?php if ($this->_tpl_vars['available_action']['controller']): ?>
              <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['controller_param']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['controller']; ?>
" />
            <?php endif; ?>
            <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['action_param']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" />
            <?php if ($this->_tpl_vars['available_action']['model_id']): ?>
              <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['model_id']; ?>
" />
            <?php endif; ?>
            <?php if ($this->_tpl_vars['available_action']['model_lang']): ?>
              <input type="hidden" name="model_lang" value="<?php echo $this->_tpl_vars['available_action']['model_lang']; ?>
" />
            <?php endif; ?>
            <?php if ($this->_tpl_vars['available_action']['name'] == 'search' || $this->_tpl_vars['available_action']['name'] == 'filter'): ?>
              <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['session_param']; ?>
" value="1" />
              <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_module" value="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" />
              <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_controller" value="<?php echo $this->_tpl_vars['available_action']['controller']; ?>
" />
              <?php if ($this->_tpl_vars['event'] && ! is_object ( $this->_tpl_vars['event'] )): ?>
              <input type="hidden" name="event" value="<?php echo $this->_tpl_vars['event']; ?>
" />
              <?php endif; ?>
              <?php if ($this->_tpl_vars['relation']): ?>
              <input type="hidden" name="relation" value="<?php echo $this->_tpl_vars['relation']; ?>
" />
              <?php endif; ?>
              <?php if ($this->_tpl_vars['group_table']): ?>
              <input type="hidden" name="group_table" value="<?php echo $this->_tpl_vars['group_table']; ?>
" />
              <?php endif; ?>
              <?php if ($this->_tpl_vars['mynzoom_settings_table']): ?>
              <input type="hidden" name="mynzoom_settings_table" value="<?php echo $this->_tpl_vars['mynzoom_settings_table']; ?>
" />
              <?php endif; ?>
              <?php if ($this->_tpl_vars['form_name']): ?>
              <input type="hidden" name="form_name" value="<?php echo $this->_tpl_vars['form_name']; ?>
" />
              <?php endif; ?>
              <?php if ($_REQUEST['autocomplete_filter']): ?>
                <input type="hidden" name="autocomplete_filter" id="autocomplete_filter" value="session" />
              <?php endif; ?>
              <?php if ($_REQUEST['uniqid']): ?>
                <input type="hidden" name="uniqid" id="uniqid" value="<?php echo $_REQUEST['uniqid']; ?>
" />
              <?php endif; ?>
              <?php if ($this->_tpl_vars['session_param']): ?>
                <input type="hidden" name="session_param" value="<?php echo $this->_tpl_vars['session_param']; ?>
" />
              <?php endif; ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['hidden_fields']): ?><?php echo $this->_tpl_vars['hidden_fields']; ?>
<?php endif; ?>
            <table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
              <tr>
                <td class="t_caption"><div class="t_caption_title"><?php echo $this->_tpl_vars['available_action']['label']; ?>
 <?php echo ((is_array($_tmp=$this->_config[0]['vars']['options'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
              </tr>
              <tr>
                <td id="td_<?php echo $this->_tpl_vars['available_action']['name']; ?>
_options">
                  <?php if ($this->_tpl_vars['available_action']['show_notice']): ?>
                    <span style="color: #0000FF"><?php echo $this->_tpl_vars['available_action']['show_notice']; ?>
</span>
                  <?php endif; ?>
                  <?php if ($this->_tpl_vars['available_action']['ajax_no'] == '1'): ?>
                    <?php if ($this->_tpl_vars['available_action']['template']): ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['available_action']['template'], 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php else: ?>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => '_action_common_options.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    <?php endif; ?>
                  <?php endif; ?>
                </td>
              </tr>
            </table>
          </form>
          </div>
        </div>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  </div>
<?php endif; ?>
<div class="clear"></div>
<?php if ($this->_tpl_vars['action'] == 'search'): ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_user_friendly_filters.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>