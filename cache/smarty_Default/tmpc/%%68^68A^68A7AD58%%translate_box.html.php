<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/translate_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/translate_box.html', 8, false),)), $this); ?>
<?php if ($this->_tpl_vars['translations']): ?>
<div id="translate_container">
  <div class="t_footer" id="translate_switch"><div class="<?php if ($_COOKIE['translate_box'] == 'off'): ?>switch_up<?php else: ?>switch_down<?php endif; ?>"></div></div>
  <div id="translate_flags"<?php if ($_COOKIE['translate_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
    <table>
      <tr>
        <td>
          <div><?php if ($this->_tpl_vars['action_param'] == 'users' && $this->_tpl_vars['action'] == 'profile'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['posible_translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['available_translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>:</div>
        </td>
          <?php $_from = $this->_tpl_vars['translations']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?>
            <td>
              <?php if ($this->_tpl_vars['trans']['lang']): ?>
                <?php if ($this->_tpl_vars['trans']['selected']): ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['trans']['lang']; ?>
.png" alt="<?php echo $this->_tpl_vars['trans']['lang_name']; ?>
" title="<?php echo $this->_tpl_vars['trans']['lang_name']; ?>
" border="0" class="selected" />
                <?php else: ?>
                  <a href="<?php echo $this->_tpl_vars['trans']['url']; ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['trans']['lang']; ?>
.png" alt="<?php echo $this->_tpl_vars['trans']['lang_name']; ?>
" title="<?php echo $this->_tpl_vars['trans']['lang_name']; ?>
" border="0" /></a>
                <?php endif; ?>
              <?php else: ?>
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['lang']; ?>
.png" alt="<?php echo $this->_tpl_vars['lang']; ?>
" border="0" class="selected" />
              <?php endif; ?>
            </td>
          <?php endforeach; else: ?>
            <td>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['lang']; ?>
.png" alt="<?php echo $this->_tpl_vars['lang']; ?>
" border="0" class="selected" />
            </td>
          <?php endif; unset($_from); ?>
        <?php if ($this->_tpl_vars['make_translations'] && $this->_tpl_vars['model']->checkPermissions('translate')): ?>
          <td>
            &nbsp;&nbsp;
          </td>
          <td style="border-left: 1px solid #CCCCCC;">
            <div class="make_translation_div"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['make_translation'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</div>
          </td>
          <?php $_from = $this->_tpl_vars['make_translations']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['mk_trans']):
?>
            <td>
              <?php if ($this->_tpl_vars['mk_trans']['lang']): ?>
                <?php if ($this->_tpl_vars['mk_trans']['lang'] == $this->_tpl_vars['model']->get('model_lang')): ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['mk_trans']['lang']; ?>
.png" alt="<?php echo $this->_tpl_vars['mk_trans']['lang_name']; ?>
" title="<?php echo $this->_tpl_vars['mk_trans']['lang_name']; ?>
" border="0" class="selected" />
                <?php else: ?>
                  <a href="<?php echo $this->_tpl_vars['mk_trans']['url']; ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['mk_trans']['lang']; ?>
.png" alt="<?php echo $this->_tpl_vars['mk_trans']['lang_name']; ?>
" title="<?php echo $this->_tpl_vars['mk_trans']['lang_name']; ?>
" border="0" /></a>
                <?php endif; ?>
              <?php else: ?>
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags/<?php echo $this->_tpl_vars['lang']; ?>
.png" alt="<?php echo $this->_tpl_vars['lang']; ?>
" border="0" class="selected" />
              <?php endif; ?>
            </td>
          <?php endforeach; endif; unset($_from); ?>
        <?php endif; ?>
      </tr>
    </table>
  </div>
</div>
<?php endif; ?>