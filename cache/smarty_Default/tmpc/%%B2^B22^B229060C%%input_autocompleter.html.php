<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:59
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 65, false),array('modifier', 'replace', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 112, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 233, false),array('modifier', 'strip_tags', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 234, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 67, false),array('function', 'array', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 113, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 162, false),array('function', 'uniqid', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 220, false),array('function', 'json', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/input_autocompleter.html', 292, false),)), $this); ?>

<?php if ($this->_tpl_vars['index']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo 'px'; ?><?php elseif ($this->_tpl_vars['height']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td>
<?php endif; ?>

        <?php if (! $this->_tpl_vars['value_autocomplete'] && ( $this->_tpl_vars['value_code'] || $this->_tpl_vars['value_name'] )): ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['value_code']): ?>[<?php echo $this->_tpl_vars['value_code']; ?>
] <?php endif; ?><?php if ($this->_tpl_vars['value_name']): ?><?php echo $this->_tpl_vars['value_name']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('value_autocomplete', ob_get_contents());ob_end_clean(); ?>
    <?php endif; ?>

        <?php $this->assign('var_name', ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name']))); ?>

        <?php if ($this->_tpl_vars['autocomplete_var_type'] == 'basic' && $this->_tpl_vars['basic_vars_additional_settings'][$this->_tpl_vars['var_name']]['autocomplete']): ?>
      <?php $_from = $this->_tpl_vars['basic_vars_additional_settings'][$this->_tpl_vars['var_name']]; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['setting_key'] => $this->_tpl_vars['setting_value']):
?>
        <?php if (in_array ( $this->_tpl_vars['setting_key'] , array ( 'autocomplete' , 'readonly' , 'hidden' , 'width' , 'show_placeholder' , 'custom_class' , 'text_align' ) ) && $this->_tpl_vars['setting_value'] !== '' && $this->_tpl_vars['setting_value'] !== null): ?>
          <?php $this->assign($this->_tpl_vars['setting_key'], $this->_tpl_vars['setting_value']); ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>

    <?php if ($this->_tpl_vars['autocomplete_var_type'] == 'basic' || ( $this->_tpl_vars['autocomplete_var_type'] == 'searchable' && $this->_tpl_vars['autocomplete']['search_by_id'] )): ?>
      <input type="hidden"
             name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
             id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
             value="<?php echo $this->_tpl_vars['value']; ?>
" />

      <?php ob_start(); ?><?php echo $this->_tpl_vars['name']; ?>
_autocomplete<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_name', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_autocomplete<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_id', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['value_autocomplete']): ?><?php echo $this->_tpl_vars['value_autocomplete']; ?>
<?php elseif ($this->_tpl_vars['value']): ?><?php echo $this->_tpl_vars['value']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_value', ob_get_contents());ob_end_clean(); ?>
      <?php $this->assign('value_id', $this->_tpl_vars['value']); ?>

      <?php if (! $this->_tpl_vars['autocomplete'] && $this->_tpl_vars['autocomplete_type']): ?>
        <?php if (preg_match ( '#^([^_]*)_(.*)#' , $this->_tpl_vars['autocomplete_type'] , $this->_tpl_vars['act_matches'] )): ?>
          <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['act_matches'][1]; ?>
&<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['act_matches'][2]; ?>
&<?php echo $this->_tpl_vars['act_matches'][2]; ?>
=ajax_select<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('autocomplete_url', ob_get_contents());ob_end_clean(); ?>
        <?php else: ?>
          <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['autocomplete_type']; ?>
&<?php echo $this->_tpl_vars['autocomplete_type']; ?>
=ajax_select<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('autocomplete_url', ob_get_contents());ob_end_clean(); ?>
        <?php endif; ?>
        <?php if (! $this->_tpl_vars['view_mode']): ?><?php $this->assign('view_mode', 'link'); ?><?php endif; ?>
        <?php ob_start(); ?><?php if ($this->_tpl_vars['view_mode'] == 'link'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['autocomplete_url'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'ajax_select', 'view&view=') : smarty_modifier_replace($_tmp, 'ajax_select', 'view&view=')); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('view_mode_url', ob_get_contents());ob_end_clean(); ?>
        <?php echo smarty_function_array(array('assign' => 'autocomplete','type' => $this->_tpl_vars['autocomplete_type'],'url' => $this->_tpl_vars['autocomplete_url'],'min_chars' => $this->_tpl_vars['min_chars'],'buttons' => $this->_tpl_vars['autocomplete_buttons'],'buttons_hide' => $this->_tpl_vars['autocomplete_buttons_hide'],'execute_after' => $this->_tpl_vars['execute_after'],'filters' => $this->_tpl_vars['filters_array'],'addquick_type' => $this->_tpl_vars['addquick_type'],'stop_customer_details' => $this->_tpl_vars['stop_customer_details'],'suggestions' => $this->_tpl_vars['autocomplete_suggestions'],'fill_options' => $this->_tpl_vars['autocomplete_fill_options'],'view_mode' => $this->_tpl_vars['view_mode'],'view_mode_url' => $this->_tpl_vars['view_mode_url']), $this);?>

      <?php else: ?>
        <?php if ($this->_tpl_vars['autocomplete_var_type'] == 'searchable'): ?>
          <?php echo smarty_function_array(array('assign' => 'autocomplete','fill_options' => ''), $this);?>

        <?php elseif ($this->_tpl_vars['filters_array']): ?>
          <?php echo smarty_function_array(array('assign' => 'autocomplete','filters' => $this->_tpl_vars['filters_array']), $this);?>

        <?php endif; ?>
      <?php endif; ?>
            <?php if ($this->_tpl_vars['autocomplete_var_type'] == 'basic'): ?>
                <?php echo smarty_function_array(array('assign' => 'autocomplete','var_type' => $this->_tpl_vars['autocomplete_var_type']), $this);?>

      <?php endif; ?>
    <?php else: ?>
      <?php ob_start(); ?><?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_name', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_id', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['value_autocomplete']): ?><?php echo $this->_tpl_vars['value_autocomplete']; ?>
<?php elseif ($this->_tpl_vars['value']): ?><?php echo $this->_tpl_vars['value']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_value', ob_get_contents());ob_end_clean(); ?>
    <?php endif; ?>

    <?php $this->assign('buttons_count', 0); ?>
    <?php $this->assign('include_clear_button', 0); ?>
    <?php $this->assign('include_add_button', 0); ?>
    <?php $this->assign('include_search_button', 0); ?>
    <?php $this->assign('include_refresh_button', 0); ?>
    <?php $this->assign('include_report_button', 0); ?>
    <?php $this->assign('include_edit_button', 0); ?>
    <?php if ($this->_tpl_vars['autocomplete_var_type'] != 'searchable' && ! $this->_tpl_vars['readonly']): ?>
      <?php if (( preg_match ( '/combobox/' , $this->_tpl_vars['autocomplete']['buttons'] ) || $this->_tpl_vars['autocomplete']['combobox'] ) && ! preg_match ( '/combobox/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/combobox/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php $this->assign('include_combobox_button', 1); ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['available_action']['action'] == 'filter' || $this->_tpl_vars['available_action']['action'] == 'search' || ( preg_match ( '/clear/' , $this->_tpl_vars['autocomplete']['buttons'] ) || $this->_tpl_vars['autocomplete']['clear'] ) && ! preg_match ( '/clear/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/clear/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php $this->assign('include_clear_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if (( $this->_tpl_vars['available_action']['action'] != 'filter' && $this->_tpl_vars['available_action']['action'] != 'search' ) && ( preg_match ( '/search/' , $this->_tpl_vars['autocomplete']['buttons'] ) || ! $this->_tpl_vars['autocomplete']['buttons'] ) && ! preg_match ( '/search/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/search/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php $this->assign('include_search_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if (( preg_match ( '/refresh/' , $this->_tpl_vars['autocomplete']['buttons'] ) || $this->_tpl_vars['autocomplete']['refresh'] ) && ! preg_match ( '/refresh/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/refresh/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php $this->assign('include_refresh_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if ($this->_tpl_vars['autocomplete']['report'] && ! preg_match ( '/report/' , $this->_tpl_vars['autocomplete']['buttons_hide'] )): ?>
        <?php $this->assign('include_report_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if (( $this->_tpl_vars['available_action']['action'] != 'filter' && $this->_tpl_vars['available_action']['action'] != 'search' ) && ( $this->_tpl_vars['autocomplete']['type'] == 'customers' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<contactpersons>' , $this->_tpl_vars['autocomplete']['filters'] ) ) || $this->_tpl_vars['autocomplete']['type'] == 'projects' || $this->_tpl_vars['autocomplete']['type'] == 'nomenclatures' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<customer_trademark>' , $this->_tpl_vars['autocomplete']['filters'] ) ) ) && ( preg_match ( '/add/' , $this->_tpl_vars['autocomplete']['buttons'] ) || $this->_tpl_vars['autocomplete']['add'] ) && ! preg_match ( '/add/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/add/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['autocomplete']['type'],'add')): ?>
          <?php $this->assign('include_add_button', 1); ?>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_add_button'] || $this->_tpl_vars['autocomplete_var_type'] == 'basic'): ?>
          <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

        <?php endif; ?>
      <?php endif; ?>
      <?php if (( $this->_tpl_vars['available_action']['action'] != 'filter' && $this->_tpl_vars['available_action']['action'] != 'search' ) && ( $this->_tpl_vars['autocomplete']['type'] == 'customers' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<contactpersons>' , $this->_tpl_vars['autocomplete']['filters'] ) ) || $this->_tpl_vars['autocomplete']['type'] == 'nomenclatures' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<customer_trademark>' , $this->_tpl_vars['autocomplete']['filters'] ) ) ) && ( preg_match ( '/edit/' , $this->_tpl_vars['autocomplete']['buttons'] ) || $this->_tpl_vars['autocomplete']['edit'] ) && ! preg_match ( '/edit/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/edit/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['autocomplete']['type'],'edit')): ?>
          <?php $this->assign('include_edit_button', 1); ?>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_edit_button'] || $this->_tpl_vars['autocomplete_var_type'] == 'basic'): ?>
          <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

        <?php endif; ?>
      <?php endif; ?>
    <?php endif; ?>

    <?php if ($this->_tpl_vars['autocomplete']['button_menu'] && $this->_tpl_vars['buttons_count'] > 0 && ( $this->_tpl_vars['available_action']['action'] != 'filter' && $this->_tpl_vars['available_action']['action'] != 'search' )): ?>
      <?php $this->assign('button_menu', 1); ?>
      <?php $this->assign('buttons_count', 1); ?>
      <?php if ($this->_tpl_vars['autocomplete_var_type'] == 'basic' && $this->_tpl_vars['standalone'] && ! ( $this->_tpl_vars['width'] && preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] ) ) && empty ( $this->_tpl_vars['basic_vars_additional_settings'][$this->_tpl_vars['var_name']]['width'] )): ?>
        <?php $this->assign('width', 222); ?>
      <?php endif; ?>
    <?php else: ?>
      <?php $this->assign('button_menu', 0); ?>
    <?php endif; ?>

    <?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone']): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] )): ?><?php echo '100%'; ?><?php else: ?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => "x - (y*z)",'x' => $this->_tpl_vars['width'],'y' => $this->_tpl_vars['buttons_count'],'z' => 22), $this);?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>

        <?php echo smarty_function_uniqid(array('assign' => 'uniqID','more_entropy' => true), $this);?>

        <?php $this->assign('uniqID', ((is_array($_tmp=$this->_tpl_vars['uniqID'])) ? $this->_run_mod_handler('replace', true, $_tmp, '.', '') : smarty_modifier_replace($_tmp, '.', ''))); ?>

    <input
       type="text"
       class="txtbox autocompletebox<?php if (! $this->_tpl_vars['readonly'] && ! $this->_tpl_vars['hidden']): ?> autocomplete_<?php echo $this->_tpl_vars['autocomplete']['type']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['readonly']): ?> readonly<?php if ($this->_tpl_vars['autocomplete']['view_mode'] == 'link' && $this->_tpl_vars['autocomplete']['view_mode_url']): ?> hidden<?php endif; ?><?php endif; ?><?php if ($this->_tpl_vars['include_combobox_button']): ?> combobox<?php endif; ?><?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
       name="<?php echo $this->_tpl_vars['visible_name']; ?>
"
       id="<?php echo $this->_tpl_vars['visible_id']; ?>
"
       value="<?php echo ((is_array($_tmp=$this->_tpl_vars['visible_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
       title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
       style="<?php if ($this->_tpl_vars['hidden']): ?>display: none;<?php elseif (( $this->_tpl_vars['width'] )): ?>width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
       <?php if ($this->_tpl_vars['show_placeholder']): ?>
         placeholder="<?php if ($this->_tpl_vars['show_placeholder'] === 'label'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php elseif ($this->_tpl_vars['show_placeholder'] === 'help'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['help'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_tpl_vars['show_placeholder'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>"
       <?php endif; ?>
       <?php if ($this->_tpl_vars['onkeydown'] || ! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?>
         onkeydown="<?php if ($this->_tpl_vars['onkeydown']): ?><?php echo $this->_tpl_vars['onkeydown']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeydown']; ?>
;<?php endif; ?>"
       <?php endif; ?>
       <?php if ($this->_tpl_vars['restrict']): ?>
         onkeypress="return changeKey(this, event, <?php echo $this->_tpl_vars['restrict']; ?>
);"
       <?php elseif ($this->_tpl_vars['onkeypress'] || ! empty ( $this->_tpl_vars['js_methods']['onkeypress'] )): ?>
         onkeypress="<?php if ($this->_tpl_vars['onkeypress']): ?><?php echo $this->_tpl_vars['onkeypress']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeypress'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeypress']; ?>
;<?php endif; ?>"
       <?php endif; ?>
       <?php if ($this->_tpl_vars['onkeyup'] || ! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?>
         onkeyup="<?php if ($this->_tpl_vars['onkeyup']): ?><?php echo $this->_tpl_vars['onkeyup']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeyup']; ?>
;<?php endif; ?>"
       <?php endif; ?>
       onfocus="highlight(this);<?php if (! empty ( $this->_tpl_vars['js_methods']['onfocus'] )): ?><?php echo $this->_tpl_vars['js_methods']['onfocus']; ?>
;<?php endif; ?>"
       onblur="unhighlight(this);<?php if (! empty ( $this->_tpl_vars['js_methods']['onblur'] )): ?><?php echo $this->_tpl_vars['js_methods']['onblur']; ?>
;<?php endif; ?><?php if (! $this->_tpl_vars['readonly']): ?>cancelAutocompleter(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);<?php endif; ?>"
       onclick="<?php if ($this->_tpl_vars['include_combobox_button']): ?>toggleAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onclick'] )): ?><?php echo $this->_tpl_vars['js_methods']['onclick']; ?>
;<?php endif; ?>"
       oncontextmenu="return false;"
       ondrop="return false;"
       <?php $_from = $this->_tpl_vars['js_methods']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['method'] => $this->_tpl_vars['func']):
?>
         <?php if ($this->_tpl_vars['func'] && $this->_tpl_vars['method'] && $this->_tpl_vars['method'] != 'onkeydown' && $this->_tpl_vars['method'] != 'onkeypress' && $this->_tpl_vars['method'] != 'onkeyup' && $this->_tpl_vars['method'] != 'onfocus' && $this->_tpl_vars['method'] != 'onblur' && $this->_tpl_vars['method'] != 'onclick'): ?>
           <?php echo $this->_tpl_vars['method']; ?>
="<?php echo $this->_tpl_vars['func']; ?>
"
         <?php endif; ?>
       <?php endforeach; endif; unset($_from); ?>
       <?php if ($this->_tpl_vars['readonly']): ?> readonly="readonly"<?php endif; ?>
       <?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?>
       uniqid="<?php echo $this->_tpl_vars['uniqID']; ?>
"/>

    <?php if (! $this->_tpl_vars['readonly']): ?>
      <?php if (! $this->_tpl_vars['exclude_oldvalues']): ?>
        <input type="hidden"
               name="<?php echo $this->_tpl_vars['name']; ?>
_oldvalue<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
               id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_oldvalue<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
               value="<?php echo ((is_array($_tmp=$this->_tpl_vars['visible_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
               disabled="disabled" />
      <?php endif; ?>

      <div id="suggestions_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="autocompletebox" style="display: none;"></div>

            
      <script type="text/javascript">
        <?php echo smarty_function_array(array('assign' => 'autocomplete','uniqid' => $this->_tpl_vars['uniqID']), $this);?>

        params_<?php echo $this->_tpl_vars['uniqID']; ?>
 = <?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))), $this);?>
;
        initAutocompleter(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);
      </script>
    <?php elseif ($this->_tpl_vars['autocomplete']['view_mode'] == 'link' && $this->_tpl_vars['autocomplete']['view_mode_url']): ?>
      <?php ob_start(); ?><?php echo $this->_tpl_vars['uniqID']; ?>
 id_var-<?php if ($this->_tpl_vars['autocomplete_var_type'] == 'basic' || ( $this->_tpl_vars['autocomplete_var_type'] == 'searchable' && $this->_tpl_vars['autocomplete']['search_by_id'] )): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php else: ?><?php echo $this->_tpl_vars['autocomplete']['id_var']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_class', ob_get_contents());ob_end_clean(); ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => ((is_array($_tmp=@$this->_tpl_vars['value_name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['visible_value']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['visible_value'])),'value_id' => $this->_tpl_vars['value_id'],'view_mode_url' => $this->_tpl_vars['autocomplete']['view_mode_url'],'ac_class' => $this->_tpl_vars['ac_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>

    <?php if ($this->_tpl_vars['autocomplete_var_type'] != 'searchable' && ! $this->_tpl_vars['readonly']): ?>
        <?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<customer_trademark>' , $this->_tpl_vars['autocomplete']['filters'] )): ?>trademarks<?php elseif (! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<contactpersons>' , $this->_tpl_vars['autocomplete']['filters'] )): ?>contactpersons<?php else: ?><?php echo $this->_tpl_vars['autocomplete']['type']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('autocomplete_type', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_clear_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('clear_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_search_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('search_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_add_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('add_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_refresh_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('refresh_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_edit_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('edit_param', ob_get_contents());ob_end_clean(); ?>

        <?php if ($this->_tpl_vars['include_combobox_button']): ?>
          <a href="javascript:void(0);"
             onclick="toggleAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/combobox.png"
                 class="combobox_button"
                 alt="" />
          </a>
        <?php endif; ?>
      <?php if ($this->_tpl_vars['button_menu']): ?>
    <div class="button_menu">
      <a href="javascript:void(0);"
         onmouseover="mopen('button_menu_<?php echo $this->_tpl_vars['uniqID']; ?>
')"
         onmouseout="mclosetime()">
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/more.png"
             class="icon_button"
             alt="..." />
      </a>
      <div id="button_menu_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="autocompletebox" style="visibility: hidden;" onmouseover="mcancelclosetime()" onmouseout="mclosetime()">
        <ul>
      <?php endif; ?>
        <?php if ($this->_tpl_vars['include_clear_button']): ?>
          <?php if ($this->_tpl_vars['button_menu']): ?><li><?php endif; ?>
          <a href="javascript:void(0);"
             onclick="clearAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);"
             <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['autocomplete_clear_items'],'text_content' => $this->_config[0]['vars'][$this->_tpl_vars['clear_param']],'popup_only' => 1), $this);?>
>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/clear.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            <?php if ($this->_tpl_vars['button_menu']): ?><?php echo $this->_config[0]['vars']['autocomplete_clear_items']; ?>
<?php endif; ?>
          </a>
          <?php if ($this->_tpl_vars['button_menu']): ?></li><?php endif; ?>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_search_button']): ?>
          <?php if ($this->_tpl_vars['button_menu']): ?><li><?php endif; ?>
          <a href="javascript:void(0);"
             onclick="filterAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);"
             <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['autocomplete_search_items'],'text_content' => $this->_config[0]['vars'][$this->_tpl_vars['search_param']],'popup_only' => 1), $this);?>
>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/<?php echo $this->_tpl_vars['autocomplete_type']; ?>
.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            <?php if ($this->_tpl_vars['button_menu']): ?><?php echo $this->_config[0]['vars']['autocomplete_search_items']; ?>
<?php endif; ?>
          </a>
          <?php if ($this->_tpl_vars['button_menu']): ?></li><?php endif; ?>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_report_button']): ?>
          <?php if ($this->_tpl_vars['button_menu']): ?><li><?php endif; ?>
          <a href="javascript:void(0);"
             onclick="reportAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);"
             <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['autocomplete_search_items'],'text_content' => $this->_config[0]['vars']['autocomplete_search_report'],'popup_only' => 1), $this);?>
>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/reports.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            <?php if ($this->_tpl_vars['button_menu']): ?><?php echo $this->_config[0]['vars']['autocomplete_search_items']; ?>
<?php endif; ?>
          </a>
          <?php if ($this->_tpl_vars['button_menu']): ?></li><?php endif; ?>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_refresh_button']): ?>
          <?php if ($this->_tpl_vars['button_menu']): ?><li><?php endif; ?>
          <a href="javascript:void(0);"
             onclick="refreshAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);"
             <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['autocomplete_refresh_items'],'text_content' => $this->_config[0]['vars'][$this->_tpl_vars['refresh_param']],'popup_only' => 1), $this);?>
>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/refresh.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0"
                 style="vertical-align: middle;" />
            <?php if ($this->_tpl_vars['button_menu']): ?><?php echo $this->_config[0]['vars']['autocomplete_refresh_items']; ?>
<?php endif; ?>
          </a>
          <?php if ($this->_tpl_vars['button_menu']): ?></li><?php endif; ?>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_add_button']): ?>
          <?php if ($this->_tpl_vars['button_menu']): ?><li><?php endif; ?>
          <a href="javascript:void(0);"
             onclick="addAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);"
             <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add'],'text_content' => $this->_config[0]['vars'][$this->_tpl_vars['add_param']],'popup_only' => 1), $this);?>
>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/<?php echo $this->_tpl_vars['autocomplete']['type']; ?>
_add.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0" />
            <?php if ($this->_tpl_vars['button_menu']): ?><?php echo $this->_config[0]['vars']['add']; ?>
<?php endif; ?>
          </a>
          <?php if ($this->_tpl_vars['button_menu']): ?></li><?php endif; ?>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_edit_button']): ?>
          <?php if ($this->_tpl_vars['button_menu']): ?><li><?php endif; ?>
          <a href="javascript:void(0);"
             onclick="editAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);"
             <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['edit'],'text_content' => $this->_config[0]['vars'][$this->_tpl_vars['edit_param']],'popup_only' => 1), $this);?>
>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/<?php echo $this->_tpl_vars['autocomplete']['type']; ?>
_edit.png"
                 class="icon_button"
                 width="14"
                 height="14"
                 alt=""
                 border="0" />
            <?php if ($this->_tpl_vars['button_menu']): ?><?php echo $this->_config[0]['vars']['edit']; ?>
<?php endif; ?>
          </a>
          <?php if ($this->_tpl_vars['button_menu']): ?></li><?php endif; ?>
        <?php endif; ?>
      <?php if ($this->_tpl_vars['button_menu']): ?>
        </ul>
      </div>
    </div>
      <?php endif; ?>
    <?php endif; ?>

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => $this->_tpl_vars['custom_id'],'name' => $this->_tpl_vars['name'],'back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>