<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from _history_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_history_side_panel.html', 4, false),array('modifier', 'default', '_history_side_panel.html', 11, false),array('modifier', 'date_format', '_history_side_panel.html', 12, false),array('function', 'cycle', '_history_side_panel.html', 9, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 100px;"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['history_event_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['history_event_text'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 80px;"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
<?php $_from = $this->_tpl_vars['history']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['event']):
        $this->_foreach['i']['iteration']++;
?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 vtop">
    <td class="t_border"><?php echo $this->_tpl_vars['event']['action_type_name']; ?>
</td>
    <td class="t_border"><?php echo ((is_array($_tmp=@$this->_tpl_vars['event']['data'])) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
    <td class=""><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['event']['h_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
<?php endforeach; else: ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <td class="error" colspan="3"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
<?php endif; unset($_from); ?>
</table>
<?php endif; ?>