<?php /* Smarty version 2.6.33, created on 2025-05-21 16:13:53
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/communications.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/communications.html', 18, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
<div id="form_container">

  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
    <tr>
      <td class="nopadding">
        <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info_header.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </table>
      </td>
    </tr>
    <?php ob_start(); ?>contracts_<?php if ($this->_tpl_vars['communication_type']): ?><?php echo $this->_tpl_vars['communication_type']; ?>
<?php else: ?>communications<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_communication_list', ob_get_contents());ob_end_clean(); ?>
    <tr>
      <td class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="contracts_communications_switch"><div class="switch_<?php if ($_COOKIE['contracts_communications_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title" id="title_section_communications"><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['current_communication_list']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    </tr>
    <tr id="contracts_communications"<?php if ($_COOKIE['contracts_communications_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
      <td class="nopadding">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_communication_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  </table>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>