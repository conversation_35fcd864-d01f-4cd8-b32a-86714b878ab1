<?php $_config_vars = array (
  'customers_counters' => 'Броячи за контрагенти',
  'customers_counters_name' => 'Име',
  'customers_counters_formula' => 'Формула',
  'customers_counters_description' => 'Описание',
  'customers_counters_next_number' => 'Следващ номер',
  'customers_counters_count_customers' => 'Брой контрагенти',
  'customers_counters_types_used' => 'Използван в типове',
  'customers_counters_status' => 'Статус',
  'customers_counters_status_active' => 'Активен',
  'customers_counters_status_inactive' => 'Неактивен',
  'customers_counters_added_by' => 'Добавен от',
  'customers_counters_modified_by' => 'Променен от',
  'customers_counters_added' => 'Добавен на',
  'customers_counters_modified' => 'Променен на',
  'customers_counters_add' => 'Добавяне на брояч за контрагенти',
  'customers_counters_edit' => 'Редакция на брояч за контрагенти',
  'customers_counters_view' => 'Разглеждане на брояч за контрагенти',
  'customers_counters_translate' => 'Превод на брояч за контрагенти',
  'message_customers_counters_add_success' => 'Данните за брояч бяха добавени успешно!',
  'message_customers_counters_edit_success' => 'Данните за брояч бяха редактирани успешно!',
  'message_customers_counters_translate_success' => 'Броячът беше успешно преведен!',
  'error_customers_counters_edit_failed' => 'Данните за брояч не бяха редактирани успешно:',
  'error_customers_counters_add_failed' => 'Данните за брояч не бяха добавени:',
  'error_customers_counters_translate_failed' => 'Броячът не беше успешно преведен:',
  'error_no_such_customer_counter' => 'Нямате възможност да прегледате този запис!',
  'error_no_counter_name_specified' => 'Не сте въвели име!',
  'error_no_counter_formula_specified' => 'Не сте въвели формула!',
  'error_invalid_next_number' => 'Моля, въведете следващ номер за брояча, състоящ се само от цифри, и със стойност по-голяма от 0!',
  'error_no_types_used' => 'не е използван в нито един тип контрагент',
  'customers_counters_formula_delimiter' => 'Разделител',
  'customers_counters_empty_delimiter' => 'без разделител',
  'customers_counters_formula_leading_zeroes' => 'Брой водещи нули',
  'customers_counters_formula_date_format' => 'формат',
  'customers_counters_formula_date_delimiter' => 'с разделител',
  'customers_counters_formula_date_format_year' => 'гггг',
  'customers_counters_formula_date_format_year_short' => 'гг',
  'customers_counters_formula_date_format_month' => 'мм',
  'customers_counters_formula_date_format_day' => 'дд',
  'customers_counters_formula_date_format1' => 'гггг',
  'customers_counters_formula_date_format2' => 'мм/гггг',
  'customers_counters_formula_date_format3' => 'мм/гг',
  'customers_counters_formula_date_format4' => 'гггг/мм',
  'customers_counters_formula_date_format5' => 'гг/мм',
  'customers_counters_formula_date_format6' => 'дд/мм/гггг',
  'customers_counters_formula_date_format7' => 'дд/мм/гг',
  'customers_counters_formula_date_format8' => 'мм/дд/гггг',
  'customers_counters_formula_date_format9' => 'мм/дд/гг',
  'customers_counters_formula_date_format10' => 'гггг/дд/мм',
  'customers_counters_formula_date_format11' => 'гг/дд/мм',
  'customers_counters_formula_date_format12' => 'гггг/мм/дд',
  'customers_counters_formula_date_format13' => 'гг/мм/дд',
  'customers_counters_formula_date_format14' => 'гг',
  'customers_counters_formula_date_format15' => 'ггг/мм',
  'customers_counters_formula_legend' => 'Легенда за попълването на формулата на брояча',
  'customers_counters_formula_prefix' => 'Префикс',
  'customers_counters_formula_num' => 'Номер на контрагент',
  'customers_counters_formula_user_code' => 'Код на потребител',
  'customers_counters_formula_assigned_code' => 'Код на отговорник',
  'customers_counters_formula_added' => 'Дата на контрагент',
  'customers_counters_formula_prefix_descr' => 'попълва се директно с 2-3 букви, например за клиент CLI.',
  'customers_counters_formula_num_descr' => 'попълва поредния номер на контрагент.',
  'customers_counters_formula_user_code_descr' => 'попълва кода на потребителя, създал контрагента.',
  'customers_counters_formula_assigned_code_descr' => 'попълва код за отговорника на контрагента. Ако не е избран отговорник, код не се попълва.',
  'customers_counters_formula_added_descr' => 'дата на добавяне на контрагента.',
  'customers_counters_formula_note' => '<strong>ЗАБЕЛЕЖКА:</strong> Към формулата на брояча могат да се добавят <strong>само 5 елемента</strong>',
  'help_customers_counters_name' => '',
  'help_customers_counters_formula' => '',
  'help_customers_counters_count_customers' => '',
  'help_customers_counters_types_used' => '',
  'help_customers_counters_status' => '',
  'help_customers_counters_description' => '',
  'help_customers_counters_next_number' => 'Следващ номер. Използвайте това поле, за да зададете номер, от който да започва броячът.',
); ?>