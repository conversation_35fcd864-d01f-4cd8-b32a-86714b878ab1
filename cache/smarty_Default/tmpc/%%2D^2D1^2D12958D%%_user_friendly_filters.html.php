<?php /* Smarty version 2.6.33, created on 2025-05-21 15:13:35
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_user_friendly_filters.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_user_friendly_filters.html', 11, false),)), $this); ?>
<div id="friendly_filters_container">
  <div class="t_footer" id="friendly_filters_switch">
    <div class="<?php if ($_COOKIE['friendly_filters_box'] == 'off'): ?>switch_up<?php else: ?>switch_down<?php endif; ?>">&nbsp;</div>
  </div>
  <table id="user_friendly_filters" class="t_table" width="100%" <?php if ($_COOKIE['friendly_filters_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
    <tr>
      <td style="width:150px">
        <strong><?php echo $this->_config[0]['vars']['search_filters']; ?>
:</strong>
      </td>
      <td align="left">
        <?php echo ((is_array($_tmp=@$this->_tpl_vars['user_friendly_filters']['search'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['filters_not_defined']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['filters_not_defined'])); ?>

      </td>
    </tr>
    <tr>
      <td style="width:150px">
        <strong><?php echo $this->_config[0]['vars']['sort']; ?>
:</strong>
      </td>
      <td align="left">
        <?php echo ((is_array($_tmp=@$this->_tpl_vars['user_friendly_filters']['sort'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['sort_not_defined']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['sort_not_defined'])); ?>

      </td>
    </tr>
  </table>
</div>