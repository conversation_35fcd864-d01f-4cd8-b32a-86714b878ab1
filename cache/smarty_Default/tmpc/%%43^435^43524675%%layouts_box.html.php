<?php /* Smarty version 2.6.33, created on 2025-05-21 16:42:03
         compiled from /var/www/Nzoom-Hella/_libs/modules/users/templates/layouts_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/users/templates/layouts_box.html', 4, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/users/templates/layouts_box.html', 17, false),)), $this); ?>
<?php if ($this->_tpl_vars['layouts']): ?>
<?php $this->assign('display_action_labels', $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels',true)); ?>
<div class="action_tabs action_tabs_submenu_left" id="layouts_tabs">
    <ul id="mynzoom_actions_<?php echo smarty_function_counter(array('start' => 1,'name' => 'menu_counter','print' => true,'assign' => 'mynzoom_action_menu_num'), $this);?>
" class="zpHideOnLoad">
      <?php $_from = $this->_tpl_vars['layouts']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['l']):
?>
        <?php if ($this->_tpl_vars['l']['name'] == '|' && $this->_tpl_vars['display_action_labels']): ?>
            </ul>
            <script type="text/javascript">
              new Zapatec.Menu({source: 'mynzoom_actions_<?php echo $this->_tpl_vars['mynzoom_action_menu_num']; ?>
',
                                hideDelay: 100,
                                theme: 'nzoom'});
            </script>
            <div class="clear"></div>
              <ul id="mynzoom_actions_<?php echo smarty_function_counter(array('name' => 'menu_counter','print' => true,'assign' => 'mynzoom_action_menu_num'), $this);?>
" class="zpHideOnLoad">
        <?php elseif (( $this->_tpl_vars['l']['view'] && $this->_tpl_vars['l']['edit'] )): ?>
          <?php ob_start(); ?>users_mynzoom_settings_<?php echo $this->_tpl_vars['l']['keyword']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('label', ob_get_contents());ob_end_clean(); ?>
          <li class="<?php if ($this->_tpl_vars['l']['keyword'] == $this->_tpl_vars['layout']): ?>menu-path<?php endif; ?><?php if (! $this->_tpl_vars['display_action_labels']): ?> tab_no_label<?php endif; ?>" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['l']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['l']['keyword']; ?>
.png" width="16" height="16" alt="" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['l']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" />
            <a href="#" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['l']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
              <?php if ($this->_tpl_vars['display_action_labels']): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
            </a>
                        <span style="display: none" id="<?php echo $this->_tpl_vars['l']['keyword']; ?>
_action" onclick="mynZoomLoad(this, <?php echo $this->_tpl_vars['user']->get('id'); ?>
)" title="<?php echo $this->_tpl_vars['l']['keyword']; ?>
"></span>
          </li>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    </ul>
    <script type="text/javascript">
        new Zapatec.Menu({source: 'mynzoom_actions_<?php echo $this->_tpl_vars['mynzoom_action_menu_num']; ?>
',
                          hideDelay: 100,
                          theme: 'nzoom'});
    </script>
</div>
<div class="clear"></div>
<?php endif; ?>