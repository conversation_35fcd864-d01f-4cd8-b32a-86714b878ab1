<?php /* Smarty version 2.6.33, created on 2023-11-15 17:38:07
         compiled from /var/www/Nzoom-Hella/_libs/modules/auth/templates/login.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/auth/templates/login.html', 1, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/auth/templates/login.html', 21, false),)), $this); ?>
<h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
login.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['login'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <?php echo $this->_tpl_vars['title']; ?>
</h1>
<h2><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_login_legend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2>
<form name="loginform" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
  <table border="0" cellpadding="0" cellspacing="0" class="t_table">
    <tr>
      <td class="t_caption"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_login'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    </tr>
    <tr>
      <td>
        <table border="0" cellspacing="0" cellpadding="5">
                  <tr>
            <td class="labelbox" nowrap="nowrap"><a name="error_username"><label for="username"<?php if ($this->_tpl_vars['messages']->getErrors('username')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'username'), $this);?>
</label></a></td>
            <td><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <input type="text" class="txtbox" name="username" id="username" value="<?php echo $_POST['username']; ?>
" style="width: <?php echo $this->_tpl_vars['width']; ?>
px;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_username'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            </td>
          </tr>
          <tr>
            <td class="labelbox" nowrap="nowrap"><a name="error_password"><label for="password"<?php if ($this->_tpl_vars['messages']->getErrors('password')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'password'), $this);?>
</label></a></td>
            <td><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <input type="password" class="txtbox" name="password" id="password" style="width: <?php echo $this->_tpl_vars['width']; ?>
px;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_password'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
            </td>
          </tr>
          <?php if ($this->_tpl_vars['captcha']): ?>
          <tr>
            <td class="labelbox" nowrap="nowrap" valign="top"><a name="error_captcha"><label for="captcha"<?php if ($this->_tpl_vars['messages']->getErrors('captcha')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'captcha'), $this);?>
</label></a></td>
            <td valign="top"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <img src="<?php echo $this->_tpl_vars['captcha']; ?>
" id="captcha" alt="" /><br /><br />
              <input type="text" class="txtbox" name="captcha" id="captcha" value="" style="width: <?php echo $this->_tpl_vars['width']; ?>
px;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_captcha'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" style="float: left;" /><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
refresh.png" alt="" onclick="$('captcha').src=$('captcha').src+'#'" align="absmiddle" style="padding-left: 3px;" />
            </td>
          </tr>
          <tr>
            <td colspan="3">&nbsp;</td>
          </tr>
          <?php endif; ?>
          <tr>
            <td colspan="3" nowrap="nowrap">
              <input type="checkbox" name="rememberme" id="rememberme" value="1"<?php if ($_POST['rememberme'] == 1): ?> checked="checked"<?php endif; ?> /> <label class="labelbox" for="rememberme" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_rememberme'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_rememberme'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <br />
              <button type="submit" class="button" name="loginButton" id="loginButton" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_login_button'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
              <br />
            </td>
          </tr>
          <?php if (! $this->_tpl_vars['disable_lost_password']): ?>
          <tr>
            <td colspan="3">
              <a href="<?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=lost_password"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_lost_password'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
            </td>
          </tr>
          <?php endif; ?>
        </table>
      </td>
    </tr>
    <tr>
      <td class="t_footer"></td>
    </tr>
  </table>
</form>
<script type="text/javascript">
  document.onload = focusLogin();
</script>