<?php /* Smarty version 2.6.33, created on 2023-11-15 17:38:07
         compiled from welcome.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'welcome.html', 3, false),array('modifier', 'date_format', 'welcome.html', 8, false),array('modifier', 'default', 'welcome.html', 9, false),array('function', 'popup', 'welcome.html', 11, false),)), $this); ?>
<?php if ($this->_tpl_vars['validLogin']): ?>
  <?php ob_start(); ?>
    <strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['welcome_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</strong> <?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('firstname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
    <strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['welcome_username'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</strong> <?php echo $this->_tpl_vars['currentUser']->get('username'); ?>
<br />
    <strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['welcome_role'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</strong>
    <?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('role_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    <br />
    <strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['welcome_last_login'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</strong> <?php if ($this->_tpl_vars['currentUser']->get('last_login') != '0000-00-00 00:00:00'): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['currentUser']->get('last_login'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['welcome_no_login_yet'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?><br />
    <strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['welcome_remote_addr'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</strong> <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['currentUser']->get('remote_addr'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['welcome_no_login_yet']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['welcome_no_login_yet'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
  <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" width="11" height="11" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="help" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['welcome'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
, <?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('firstname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

<?php else: ?>
  &nbsp;
<?php endif; ?>