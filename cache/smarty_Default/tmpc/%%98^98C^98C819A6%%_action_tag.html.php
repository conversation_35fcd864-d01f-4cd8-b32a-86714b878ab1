<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from _action_tag.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_action_tag.html', 2, false),array('function', 'help', '_action_tag.html', 24, false),)), $this); ?>
<?php if ($this->_tpl_vars['available_action']['show_form']): ?>
  <form method="get" action="<?php echo $_SERVER['PHP_SELF']; ?>
"<?php if ($this->_tpl_vars['redirect_to_url'] && $this->_tpl_vars['update_target']): ?> onsubmit="ajaxUpdater({link: this.action, target: '<?php echo $this->_tpl_vars['update_target']; ?>
', parameters: Form.serialize(this) + '&use_ajax=1&redirect_to_url=<?php echo ((is_array($_tmp=$this->_tpl_vars['redirect_to_url'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
'}); lb.deactivate(); return false;"<?php endif; ?>>
    <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['module_param']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" />
    <?php if ($this->_tpl_vars['available_action']['controller_param']): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['controller_param']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['controller']; ?>
" />
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['controller']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" />
    <?php else: ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['available_action']['model_id']): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['model_id']; ?>
" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['available_action']['model_lang']): ?>
      <input type="hidden" name="model_lang" value="<?php echo $this->_tpl_vars['available_action']['model_lang']; ?>
" />
    <?php endif; ?>
<?php endif; ?>

  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td class="vtop">
        <table cellpadding="0" cellspacing="0" border="0" class="t_layout_table">
          <tr>
            <?php if (! $this->_tpl_vars['available_action']['show_form']): ?>
            <td class="labelbox"><a name="error_tags"><label<?php if ($this->_tpl_vars['messages']->getErrors('tags')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['available_action']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</label></a></td>
            <td class="unrequired">&nbsp;</td>
            <?php endif; ?>
            <td>
              <?php $this->assign('tags_mode', ''); ?>
              <?php if ($this->_tpl_vars['model']->checkPermissions('tags_view')): ?>
                <?php ob_start(); ?><?php if ($this->_tpl_vars['model']->checkPermissions('tags_edit')): ?>addedit<?php else: ?>view<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('tags_mode', ob_get_contents());ob_end_clean(); ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_tags.html", 'smarty_include_vars' => array('mode' => $this->_tpl_vars['tags_mode'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php endif; ?>
            </td>
          </tr>
          <?php if ($this->_tpl_vars['model']->get('available_tags_count') > 0 && $this->_tpl_vars['tags_mode'] == 'addedit'): ?>
          <tr>
            <td<?php if (! $this->_tpl_vars['available_action']['show_form']): ?> colspan="3"<?php endif; ?>>
              <?php if ($this->_tpl_vars['action'] == 'ajax_tag'): ?>
              <button type="button" name="saveButton1" class="button" onclick="if(lb && lb.params && typeof lb.params.saveFunction == 'function') { lb.params.saveFunction.apply(this,[lb]);} else { this.form.submit() }"><?php echo ((is_array($_tmp=$this->_tpl_vars['available_action']['options']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
              <?php else: ?>
              <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['options']['label']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['available_action']['options']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
              <?php endif; ?>
            </td>
          </tr>
          <?php endif; ?>
        </table>
      </td>
    </tr>
  </table>

<?php if ($this->_tpl_vars['available_action']['show_form']): ?>
  </form>
<?php endif; ?>