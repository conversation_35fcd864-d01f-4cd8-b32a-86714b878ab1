<?php /* Smarty version 2.6.33, created on 2023-11-16 11:25:04
         compiled from /var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'mb_upper', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html', 7, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html', 10, false),array('modifier', 'string_format', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html', 21, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html', 22, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html', 40, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html', 43, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_attachments.html', 112, false),)), $this); ?>
<?php if ($this->_tpl_vars['nomenclature']->get('attachments') || ( $this->_tpl_vars['action'] != 'view' && $this->_tpl_vars['layout']['edit'] )): ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
<?php endif; ?>
<!-- ATTACHMENTS --> 
<?php if ($this->_tpl_vars['nomenclature']->get('attachments')): ?>
  <tr>
    <td class="t_caption2" colspan="4"><div class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_files_title'])) ? $this->_run_mod_handler('mb_upper', true, $_tmp) : smarty_modifier_mb_upper($_tmp)); ?>
</div></td>
  </tr>
  <tr>
    <td class="t_caption3 t_border" width="30"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption3" width="60">&nbsp;</td>
  </tr>
  <?php $_from = $this->_tpl_vars['nomenclature']->get('attachments'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['file']):
        $this->_foreach['i']['iteration']++;
?>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['modified_files'] && array_key_exists ( $this->_tpl_vars['file']->get('id') , $this->_tpl_vars['modified_files'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('modified', ob_get_contents());ob_end_clean(); ?>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['modified']): ?><?php echo $this->_tpl_vars['file']->get('id'); ?>
<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('midx', ob_get_contents());ob_end_clean(); ?>
    <?php ob_start(); ?>
      <?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('filename'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('revision'))) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['file']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['file']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['file']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['file']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?>

    <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?>
    <?php if (! $this->_tpl_vars['file']->get('not_exist')): ?>
      <?php ob_start(); ?><?php echo ''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['nomenclature']->get('id'); ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['file']->get('id'); ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php echo 'onclick="window.open(\''; ?><?php echo $this->_tpl_vars['row_link']; ?><?php echo '\', \'_blank\')" style="cursor:pointer;"'; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_clauses', ob_get_contents());ob_end_clean(); ?>
    <?php else: ?>
      <?php ob_start(); ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_clauses', ob_get_contents());ob_end_clean(); ?>
    <?php endif; ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['erred_modified_files'] && @ in_array ( $this->_tpl_vars['file']->get('id') , $this->_tpl_vars['erred_modified_files'] )): ?> t_deleted<?php endif; ?>">
    <td<?php if ($this->_tpl_vars['erred_modified_files'] && @ in_array ( $this->_tpl_vars['file']->get('id') , $this->_tpl_vars['erred_modified_files'] )): ?> class="error"<?php endif; ?> align="right">
      <?php if ($this->_tpl_vars['file']->get('not_exist')): ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" alt="" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
      <?php endif; ?>
      <?php echo $this->_foreach['i']['iteration']; ?>
.
    </td>
    <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
      <input type="hidden" name="file_filenames[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" value="<?php echo $this->_tpl_vars['file']->get('filename'); ?>
" />
      <input type="hidden" name="file_indices[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" value="<?php echo $this->_foreach['i']['iteration']; ?>
" />
      <?php if ($this->_tpl_vars['modified']): ?>
      <div id="file_paths_value_<?php echo $this->_tpl_vars['midx']; ?>
" style="display: none">
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="" />
        <?php echo ((is_array($_tmp=$this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['filename'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </div>
      <input type="file" class="filebox" name="file_paths[<?php echo $this->_tpl_vars['midx']; ?>
]" id="file_paths_<?php echo $this->_tpl_vars['midx']; ?>
" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
      <?php else: ?>
      <div id="file_paths_value_<?php echo $this->_tpl_vars['file']->get('id'); ?>
">
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="" />
        <?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('filename'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </div>
      <input type="file" class="filebox" name="file_paths[<?php echo $this->_tpl_vars['file']->get('id'); ?>
]" id="file_paths_<?php echo $this->_tpl_vars['file']->get('id'); ?>
" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="display: none" disabled="disabled" />
      <?php endif; ?>
    </td>
    <td <?php echo $this->_tpl_vars['row_link_clauses']; ?>
>
      <?php if ($this->_tpl_vars['modified']): ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['modified_files'][$this->_tpl_vars['midx']]['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php else: ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['file']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php endif; ?>
    </td>
    <td nowrap="nowrap">
    <?php if (! $this->_tpl_vars['file']->get('not_exist')): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
" target="_blank">
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" width="16" height="16" border="0" alt="" />
      </a>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
">
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="" />
      </a>
    <?php else: ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
view.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['action'] != 'view' && $this->_tpl_vars['layout']['edit']): ?>
      <?php if ($this->_tpl_vars['currentUser']->checkRights('nomenclatures','delete_file') && $_SESSION['currentUserId'] == $this->_tpl_vars['file']->get('added_by') && ! $this->_tpl_vars['file']->get('not_exist')): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=delfile&amp;delfile=<?php echo $this->_tpl_vars['nomenclature']->get('id'); ?>
&amp;file=<?php echo $this->_tpl_vars['file']->get('id'); ?>
" onclick="return confirmAction('delete_file', function(el) { window.location.href = el.href; }, this);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
        </a>
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_delete_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" class="pointer dimmed" />
      <?php endif; ?>
    <?php endif; ?>
      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
info.png" width="16" height="16" border="0" alt="" class="help" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
    </td>
  </tr>
  <?php endforeach; else: ?>
  <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
    <td class="error" colspan="4"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
  </tr>
  <?php endif; unset($_from); ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['action'] != 'view' && $this->_tpl_vars['layout']['edit']): ?>
  <tr>
    <td class="t_caption2" colspan="4">
    <div class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_add_new'])) ? $this->_run_mod_handler('mb_upper', true, $_tmp) : smarty_modifier_mb_upper($_tmp)); ?>
</div></td>
  </tr>
  <tr>
    <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_caption3 t_border"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo $this->_config[0]['vars']['required']; ?>
</div></td>
    <td class="t_caption3 t_buttons_container" colspan="2">
      <div class="t_caption3_title floatl"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
      <div class="t_buttons">
        <div id="plusButton" onclick="addFileBrowse('a_file_paths', 'a_file_descriptions', this)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
        <div id="minusButton" class="disabled" onclick="removeFileBrowse(this)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
      </div>
    </td>
  </tr>
  <tr>
    <td class="t_border hright">1.</td>
    <td class="t_border">
      <input type="file" class="filebox" name="a_file_paths[]" id="a_file_paths_1" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    </td>
    <td>
      <input type="text" class="txtbox" name="a_file_descriptions[]" id="a_file_descriptions_1" value="" onfocus="highlight(this)" onblur="unhighlight(this)" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" style="width: 300px" />
    </td>
  </tr>
  <?php endif; ?>
<?php if ($this->_tpl_vars['nomenclature']->get('attachments') || ( $this->_tpl_vars['action'] != 'view' && $this->_tpl_vars['layout']['edit'] )): ?>
</table>
<div style="height:10px; border-top: 1px solid #CCCCCC"></div>
<?php endif; ?>