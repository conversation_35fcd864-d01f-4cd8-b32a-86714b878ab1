<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_drop_menu_item.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_drop_menu_item.html', 4, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_drop_menu_item.html', 23, false),)), $this); ?>
              <li title="<?php echo $this->_tpl_vars['action_options']['label']; ?>
" class="action_tabs_drop_menu<?php if ($this->_tpl_vars['action_options']['no_tab']): ?> no_tab<?php if ($this->_tpl_vars['action_options']['button']): ?> button<?php endif; ?><?php elseif ($this->_tpl_vars['action_options']['selected'] || ( $this->_tpl_vars['rel_type'] && ( $this->_tpl_vars['action_options']['name'] == 'referent_records' && preg_match ( '/referent_/' , $this->_tpl_vars['rel_type'] ) || $this->_tpl_vars['action_options']['name'] == 'finance' && preg_match ( '/finance_/' , $this->_tpl_vars['rel_type'] ) && $this->_tpl_vars['rel_type'] != 'finance_warehouses_documents' ) )): ?> menu-path<?php endif; ?><?php if (! $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels') || $this->_tpl_vars['action_options']['hide_label']): ?> tab_no_label<?php endif; ?>">
                <?php if (! $this->_tpl_vars['action_options']['no_img']): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['action_options']['img']): ?><?php echo $this->_tpl_vars['action_options']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['action_options']['name']; ?>
<?php endif; ?>.png" alt="" title="<?php echo $this->_tpl_vars['action_options']['label']; ?>
" border="0" /><?php endif; ?>
                <?php if ($this->_tpl_vars['action_options']['url']): ?>
                  <a href="<?php echo $this->_tpl_vars['action_options']['url']; ?>
"<?php if ($this->_tpl_vars['action_options']['target']): ?> target="<?php echo $this->_tpl_vars['action_options']['target']; ?>
"<?php endif; ?> id="<?php echo $this->_tpl_vars['action_options']['name']; ?>
_action" title="<?php echo $this->_tpl_vars['action_options']['label']; ?>
"<?php if ($this->_tpl_vars['action_options']['options']): ?> onmouseover="mopen('<?php echo $this->_tpl_vars['action_options']['name']; ?>
_additional_options');" onmouseout="mclosetime()"<?php endif; ?>><?php if ($this->_tpl_vars['action_options']['no_img'] || $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels') && ! $this->_tpl_vars['action_options']['hide_label']): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['action_options']['label'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20, '...', true) : smarty_modifier_mb_truncate($_tmp, 20, '...', true)); ?>
<?php endif; ?></a>
                <?php elseif ($this->_tpl_vars['action_options']['no_img'] || $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels') && ! $this->_tpl_vars['action_options']['hide_label']): ?>
                  <?php echo ((is_array($_tmp=$this->_tpl_vars['action_options']['label'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20, '...', true) : smarty_modifier_mb_truncate($_tmp, 20, '...', true)); ?>

                <?php endif; ?>
                <?php if ($this->_tpl_vars['action_options']['name'] == 'referent_records' || $this->_tpl_vars['action_options']['name'] == 'finance'): ?>
                                <span style="display: none" id="tab_<?php echo $this->_tpl_vars['action_options']['name']; ?>
"></span>
                <?php elseif ($this->_tpl_vars['action_options']['onclick']): ?>
                                <span style="display: none" onclick="<?php echo $this->_tpl_vars['action_options']['onclick']; ?>
" id="tab_<?php echo $this->_tpl_vars['action_options']['name']; ?>
"></span>
                <?php endif; ?>
                <?php if ($this->_tpl_vars['action_options']['options']): ?>
                  <ul>
                    <?php $_from = $this->_tpl_vars['action_options']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                      <?php if ($this->_tpl_vars['option']['hr']): ?>
                        <li><hr /></li>
                      <?php else: ?>
                        <li<?php if (( $this->_tpl_vars['option']['selected'] || ( $this->_tpl_vars['rel_type'] && $this->_tpl_vars['option']['name'] == $this->_tpl_vars['rel_type'] ) ) && ! $this->_tpl_vars['option']['annulled']): ?> class="menu-path"<?php elseif (( $this->_tpl_vars['option']['selected'] || ( $this->_tpl_vars['rel_type'] && $this->_tpl_vars['option']['name'] == $this->_tpl_vars['rel_type'] ) ) && $this->_tpl_vars['option']['annulled']): ?> class="menu-path strike"<?php elseif (! ( $this->_tpl_vars['option']['selected'] || ( $this->_tpl_vars['rel_type'] && $this->_tpl_vars['option']['name'] == $this->_tpl_vars['rel_type'] ) ) && $this->_tpl_vars['option']['annulled']): ?> class="strike"<?php endif; ?>>
                          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['option']['img']): ?><?php echo $this->_tpl_vars['option']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['option']['name']; ?>
<?php endif; ?>.png" alt="" title="<?php echo $this->_tpl_vars['option']['label']; ?>
" border="0" />
                          <a href="<?php echo $this->_tpl_vars['option']['url']; ?>
"<?php if ($this->_tpl_vars['option']['target']): ?> target="<?php echo $this->_tpl_vars['option']['target']; ?>
"<?php endif; ?> title="<?php echo $this->_tpl_vars['option']['help']; ?>
"<?php if (preg_match ( '/^#related_subpanel/' , $this->_tpl_vars['option']['url'] ) && $this->_tpl_vars['option']['session_param']): ?> id="a_tab_<?php echo $this->_tpl_vars['option']['session_param']; ?>
"<?php endif; ?><?php if ($this->_tpl_vars['option']['style']): ?> style="<?php echo $this->_tpl_vars['option']['style']; ?>
"<?php endif; ?>><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40, '...', true) : smarty_modifier_mb_truncate($_tmp, 40, '...', true)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 </a>
                          <?php if ($this->_tpl_vars['option']['url_preview']): ?>
                            <span style="vertical-align: middle;"><img onclick="window.open('<?php echo $this->_tpl_vars['option']['url_preview']; ?>
', '<?php echo $this->_tpl_vars['option']['target_preview']; ?>
');" class="menu_additional_option_button_img" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['option']['img_preview']; ?>
.png" alt="" width="14" height="14" title="<?php echo $this->_tpl_vars['option']['label_preview']; ?>
" border="0" /></span>
                          <?php endif; ?>
                          <?php if (( $this->_tpl_vars['action_options']['name'] == 'referent_records' || $this->_tpl_vars['action_options']['name'] == 'finance' ) && preg_match ( '/^#related_subpanel/' , $this->_tpl_vars['option']['url'] )): ?>
                                                        <span style="display: none" onclick="toggleRelatedTabs(this); $('rel_type').value='<?php echo $this->_tpl_vars['option']['name']; ?>
';" id="tab_<?php echo $this->_tpl_vars['option']['name']; ?>
"></span>
                          <?php elseif ($this->_tpl_vars['option']['onclick']): ?>
                                                        <span style="display: none" onclick="<?php echo $this->_tpl_vars['option']['onclick']; ?>
" id="tab_<?php echo $this->_tpl_vars['option']['name']; ?>
"></span>
                          <?php endif; ?>
                        </li>
                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                  </ul>
                <?php endif; ?>
              </li>