<?php $_config_vars = array (
  'finance_documents_types' => 'Типове финансови документи',
  'finance_documents_types_name' => 'Име',
  'finance_documents_type' => 'Тип',
  'finance_documents_types_name_plural' => 'Име (за списък и меню)',
  'finance_documents_types_code' => 'Код',
  'finance_documents_types_description' => 'Описание',
  'finance_documents_types_pattern' => 'Шаблон за печат',
  'finance_documents_types_email' => 'Шаблон за писмо',
  'finance_documents_types_all_companies' => '[За всички фирми]',
  'finance_documents_types_department' => 'Отдел',
  'finance_documents_types_group' => 'Група',
  'finance_documents_types_default_user_group' => '[Група по подразбиране на текущия потребител]',
  'finance_documents_types_parent_group' => '[Групата се наследява от родителския запис]',
  'finance_documents_types_default_user_department' => '[Отдел по подразбиране на текущия потребител]',
  'finance_documents_types_parent_department' => '[Отделът се наследява от родителския запис]',
  'finance_documents_types_fiscal_event_date' => 'Дата на данъчно събитие по подразбиране',
  'finance_documents_types_date_of_payment' => 'Падеж по подразбиране',
  'finance_documents_types_assignment_types' => 'Типове назначения',
  'finance_documents_types_related_customers_types' => 'Типове контрагенти за АК',
  'finance_documents_types_available_actions' => 'Подредба на бутони',
  'finance_documents_types_default_customer' => 'Контрагент по подразбиране',
  'finance_documents_types_company' => 'Фирма',
  'finance_documents_types_mandatory_num' => 'Изисква се номер',
  'finance_documents_types_additional_settings_of_fields' => 'Допълнителни настройки на полета',
  'finance_documents_types_validate' => 'Задължителни за попълване',
  'help_finance_documents_types_validate' => '',
  'finance_documents_types_for_model' => 'За модел',
  'finance_pattern' => 'Шаблон за печат',
  'finance_documents_types_basic_settings' => 'Основни настройки',
  'finance_documents_types_counter_settings' => 'Настройки на брояч',
  'finance_documents_default_settings' => 'Настройки по подразбиране',
  'finance_documents_types_VAT' => 'Финансово отношение',
  'finance_documents_types_no_VAT' => 'Не се начислява ДДС',
  'finance_documents_types_cstm_VAT' => 'Начислява се ДДС само при фактуриране',
  'finance_documents_types_include_VAT' => 'Начислява се ДДС',
  'finance_documents_types_default_VAT' => 'Ставка по подразбиране',
  'finance_documents_types_company_no_VAT' => 'Няма регистрация по ДДС',
  'finance_documents_types_commodity' => 'Стоково отношение',
  'finance_documents_types_add_invoice' => 'Издава фактура',
  'finance_documents_types_add_proforma' => 'Издава проформа фактура',
  'finance_documents_types_credit' => 'Дебит/Кредит',
  'finance_documents_types_payment_way' => 'Начин на плащане',
  'finance_documents_types_payments' => 'Издава плащания',
  'finance_documents_types_nopay' => 'Няма да има плащане',
  'finance_documents_types_calculated_price' => 'Цена за изчисленията',
  'finance_documents_types_gt2_price' => 'Продажна цена',
  'finance_documents_types_gt2_last_delivery_price' => 'Доставна цена',
  'finance_documents_types_list' => 'Списък на типове финансови документи',
  'finance_documents_types_search' => 'Търсене на типове финансови документи',
  'finance_documents_types_add' => 'Добавяне на тип финансов документ',
  'finance_documents_types_edit' => 'Редактиране на тип финансов документ',
  'finance_documents_types_view' => 'Разглеждане на тип финансов документ',
  'finance_documents_types_translate' => 'Превод на тип финансов документ',
  'finance_commodity_relation_none' => 'не се издават стокови документи',
  'finance_commodity_relation_incoming' => 'само приемателни протоколи',
  'finance_commodity_relation_outgoing' => 'само предавателни протоколи',
  'finance_commodity_relation_both' => 'приемателни и предавателни протоколи',
  'finance_documents_layouts_num' => 'Номер',
  'finance_documents_layouts_type' => 'Тип',
  'finance_documents_layouts_name' => 'Относно',
  'finance_documents_layouts_customer_fer' => 'Контрагент',
  'finance_documents_layouts_customer_fir' => 'Контрагент',
  'finance_documents_layouts_project' => 'Проект',
  'finance_documents_layouts_invoice_num' => 'Фактура №',
  'finance_documents_layouts_company_data' => 'Каса/Банкова сметка',
  'finance_documents_layouts_issue_date' => 'Дата на документа',
  'finance_documents_layouts_date_of_payment' => 'Падеж',
  'finance_documents_layouts_admit_VAT_credit' => 'Признаване на ДДС кредит',
  'finance_documents_layouts_allocated_status' => 'Разпределяне на разход по доставки',
  'finance_documents_layouts_invoice_status' => 'Статус на фактуриране',
  'finance_documents_layouts_description' => 'Описание',
  'finance_documents_layouts_department' => 'Отдел',
  'finance_documents_layouts_employee' => 'Служител',
  'finance_documents_types_batch_vars' => 'Пaртидни променливи',
  'message_finance_documents_types_edit_success' => 'Типът беше редактиран успешно',
  'message_finance_documents_types_add_success' => 'Типът беше добавен успешно',
  'message_finance_documents_types_translate_success' => 'Типът беше преведен успешно',
  'message_finance_documents_displaysettings_save_success' => 'Настройките за работа за тип "%s" бяха успешно запазени',
  'message_finance_documents_printsettings_save_success' => 'Настройките за печат за тип "%s" бяха успешно запазени',
  'message_finance_documents_emailsettings_save_success' => 'Настройките за писма за тип "%s" бяха успешно запазени',
  'error_finance_documents_types_edit_failed' => 'Типът не беше редактиран',
  'error_finance_documents_types_add_failed' => 'Типът не беше добавен',
  'error_finance_documents_types_translate_failed' => 'Типът не беше преведен',
  'error_finance_documents_displaysettings_save_failed' => 'Настройките за работа за тип "%s" не бяха запазени',
  'error_finance_documents_printsettings_save_failed' => 'Настройките за печат за тип "%s" не бяха запазени',
  'error_finance_documents_emailsettings_save_failed' => 'Настройките за писма за тип "%s" не бяха запазени',
  'error_no_such_finance_document_type' => 'Няма такъв тип финансов документ',
  'error_no_name_specified' => 'Моля, въведете име!',
  'error_no_typename_plural_specified' => 'Моля, въведете име (за списък и меню)!',
  'error_no_code' => 'Моля, въведете код!',
  'error_code_not_unique' => 'Този код вече се използва! Моля, въведете нов код!',
  'help_finance_documents_types_assignment_types' => '',
  'help_finance_documents_types_related_customers_types' => '',
  'help_finance_documents_types_available_actions' => 'Бутоните за действия с приключен запис могат да бъдат размествани чрез влачене на редовете до желаната позиция.<br /><br />Потребителите ще виждат само тези бутони, които са позволени според правата им, настройките за типа на документа и вече издадените документи от него.',
  'help_finance_documents_types_commodity' => 'Настройката определя дали от документа могат да се издават складови документи и с каква посока.',
  'help_finance_documents_types_add_invoice' => 'Настройката определя дали от документа могат да се издават фактури.',
  'help_finance_documents_types_add_proforma' => 'Настройката определя дали от документа могат да се издават проформа фактури.',
  'help_finance_documents_types_credit' => 'Настройката определя дали документът може да се коригира и дали от фактури от него могат да се издават кредитни и дебитни известия.',
  'help_finance_documents_types_payment_way' => '',
  'help_finance_documents_types_payments' => 'Настройката определя дали от документа могат да се издават плащания.',
  'help_finance_documents_types_nopay' => 'Настройката определя дали статусът на плащане на документа може да бъде сменен на "Няма да има плащане".',
  'finance_parent_document_settings' => 'Използват се настройките на родителския документ',
  'finance_expenses_invoice_commodity_exception' => 'Ако фактурата е вторичен документ, ще се ползват настройките на родителския!',
); ?>