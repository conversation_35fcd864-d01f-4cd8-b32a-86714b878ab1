<?php /* Smarty version 2.6.33, created on 2023-11-15 17:51:08
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit_email.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit_email.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit_email.html', 13, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit_email.html', 21, false),array('modifier', 'regex_replace', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit_email.html', 21, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit_email.html', 23, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit_email.html', 16, false),)), $this); ?>
<?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['audit']['vars'] ) && is_array ( $this->_tpl_vars['audit']['vars'] ) && count ( $this->_tpl_vars['audit']['vars'] )): ?><?php echo '<i>'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['audit_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</i><table border="1" cellpadding="5" cellspacing="0"><tr><th nowrap="nowrap" style="width: 150px;" align="left">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['var_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</th><th nowrap="nowrap" style="width: 200px;" align="left">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['old_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</th><th nowrap="nowrap" style="width: 200px;" align="left">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['var_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</th></tr>'; ?><?php $_from = $this->_tpl_vars['audit']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var']):
        $this->_foreach['i']['iteration']++;
?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['field_name'] == 'bb_delimiter'): ?><?php echo '<tr><th colspan="3">'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo '</th></tr>'; ?><?php else: ?><?php echo '<tr style="background-color: '; ?><?php echo smarty_function_cycle(array('values' => '#F8F8F8,#ECECEC'), $this);?><?php echo '; vertical-align: top;"><td>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['var_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo '</td><td>'; ?><?php if (isset ( $this->_tpl_vars['var']['old_value'] ) && $this->_tpl_vars['var']['old_value'] !== ''): ?><?php echo ''; ?><?php if (preg_match ( '/<\w.*>/' , $this->_tpl_vars['var']['old_value'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['old_value'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;") : smarty_modifier_regex_replace($_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;")); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['old_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<i>'; ?><?php echo $this->_config[0]['vars']['no_old_value']; ?><?php echo '</i>'; ?><?php endif; ?><?php echo '</td><td>'; ?><?php if (isset ( $this->_tpl_vars['var']['label'] ) && $this->_tpl_vars['var']['label'] !== ''): ?><?php echo ''; ?><?php $this->assign('new_value', $this->_tpl_vars['var']['label']); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('new_value', $this->_tpl_vars['var']['field_value']); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (preg_match ( '/<\w.*>/' , $this->_tpl_vars['new_value'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['new_value'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;") : smarty_modifier_regex_replace($_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;")); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['new_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?><?php echo ''; ?><?php endif; ?><?php echo '</td></tr>'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; else: ?><?php echo '<tr style="background-color: '; ?><?php echo smarty_function_cycle(array('values' => '#F8F8F8,#ECECEC'), $this);?><?php echo '; vertical-align: top;"><td colspan="3" style="color: red;">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</td></tr>'; ?><?php endif; unset($_from); ?><?php echo '</table>'; ?><?php endif; ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['audit']['gt2'] )): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_audit_email.html", 'smarty_include_vars' => array('gt2_audit' => $this->_tpl_vars['audit']['gt2'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>