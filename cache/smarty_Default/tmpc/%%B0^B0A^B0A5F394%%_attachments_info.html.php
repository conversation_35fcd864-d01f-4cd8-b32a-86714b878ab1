<?php /* Smarty version 2.6.33, created on 2025-05-21 15:13:52
         compiled from _attachments_info.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_attachments_info.html', 10, false),array('modifier', 'mb_truncate', '_attachments_info.html', 33, false),array('modifier', 'replace', '_attachments_info.html', 89, false),array('function', 'cycle', '_attachments_info.html', 21, false),)), $this); ?>
<?php echo ''; ?><?php if ($this->_tpl_vars['module'] && $this->_tpl_vars['controller']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['controller'] != $this->_tpl_vars['module']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('controller_action_string', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

            <?php if ($this->_tpl_vars['files']): ?>
              <table cellpadding="0" cellspacing="0" border="0" class="t_layout_table t_table attachments" style="border: 1px solid #999999; z-index: 10000; width: 200px;">
                <tr>
                  <th colspan="4" class="t_caption t_border" nowrap="nowrap">
                    <div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                  </th>
                </tr>
                <tr class="t_even hleft">
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                  <td class="t_border" nowrap="nowrap" style="font-weight:bold;background-color: #ccc"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
                </tr>
                <?php if ($this->_tpl_vars['files']['attachments']): ?>
                <?php $_from = $this->_tpl_vars['files']['attachments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['k'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['k']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['attachment']):
        $this->_foreach['k']['iteration']++;
?>
                <tr class="<?php echo smarty_function_cycle(array('name' => 'attachments','values' => 't_odd,t_even'), $this);?>
">
                  <td class="t_border" style="font-weight:normal; text-align:right; white-space: nowrap">
                  <?php echo ''; ?><?php if (! file_exists ( $this->_tpl_vars['attachment']['path'] )): ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'warning.png" width="16" height="16" border="0" alt="" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" />'; ?><?php endif; ?><?php echo '&nbsp;'; ?><?php echo $this->_foreach['k']['iteration']; ?><?php echo '.'; ?>

                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left; white-space: nowrap">
                    <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
                        <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      </a>
                    <?php else: ?>
                      <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                    <?php endif; ?>
                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left; white-space: nowrap">
                    <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
                        <span title="<?php echo $this->_tpl_vars['attachment']['filename']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['filename'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      </a>
                    <?php else: ?>
                      <span title="<?php echo $this->_tpl_vars['attachment']['filename']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['filename'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                    <?php endif; ?>
                  </td>
                  <td style="font-weight:normal; text-align:right; white-space: nowrap"><?php echo $this->_tpl_vars['attachment']['revision']; ?>
</td>
                </tr>
                <?php endforeach; endif; unset($_from); ?>
                <?php endif; ?>
                <?php if ($this->_tpl_vars['files']['generated']): ?>
                <?php $_from = $this->_tpl_vars['files']['generated']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['k'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['k']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['attachment']):
        $this->_foreach['k']['iteration']++;
?>
                <tr class="<?php echo smarty_function_cycle(array('name' => 'attachments','values' => 't_odd,t_even'), $this);?>
">
                  <td class="t_border" style="font-weight:normal; text-align:left; text-align:right; white-space: nowrap">
                  <?php echo ''; ?><?php if (! file_exists ( $this->_tpl_vars['attachment']['path'] )): ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'warning.png" width="16" height="16" border="0" alt="" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" />'; ?><?php endif; ?><?php echo '&nbsp;'; ?><?php echo $this->_foreach['k']['iteration']; ?><?php echo '.'; ?>

                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left; white-space: nowrap">
                    <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
                        <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      </a>
                    <?php else: ?>
                      <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                    <?php endif; ?>
                  </td>
                  <td class="t_border" style="font-weight:normal; text-align:left;white-space: nowrap">
                    <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
                        <span title="<?php echo $this->_tpl_vars['attachment']['filename']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['filename'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      </a>
                    <?php else: ?>
                      <span title="<?php echo $this->_tpl_vars['attachment']['filename']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['filename'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                    <?php endif; ?>
                  </td>
                  <td style="font-weight:normal; text-align:right;white-space: nowrap"><?php echo $this->_tpl_vars['attachment']['revision']; ?>
</td>
                </tr>
              <?php endforeach; endif; unset($_from); ?>
              <?php endif; ?>
              <?php if ($this->_tpl_vars['files_more'] > 0): ?>
                <tr class="legend">
                  <td colspan="4" nowrap="nowrap">
                    <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=attachments&amp;attachments=<?php echo $this->_tpl_vars['model_id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_files_more'])) ? $this->_run_mod_handler('replace', true, $_tmp, '[files_more]', $this->_tpl_vars['files_more']) : smarty_modifier_replace($_tmp, '[files_more]', $this->_tpl_vars['files_more'])); ?>

                    </a>
                  </td>
                </tr>
              <?php endif; ?>
              </table>
            <?php endif; ?>