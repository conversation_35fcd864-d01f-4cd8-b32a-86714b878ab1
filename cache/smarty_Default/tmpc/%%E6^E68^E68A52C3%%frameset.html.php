<?php /* Smarty version 2.6.33, created on 2023-11-15 17:38:07
         compiled from frameset.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'frameset.html', 2, false),array('modifier', 'regex_replace', 'frameset.html', 78, false),array('modifier', 'capitalize', 'frameset.html', 78, false),array('function', 'overlib_init', 'frameset.html', 11, false),)), $this); ?>
<?php echo '<?xml'; ?>
 version="1.0" encoding="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['charset'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php echo '?>'; ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<?php echo $_SESSION['lang']; ?>
" lang="<?php echo $_SESSION['lang']; ?>
">
<head>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'head.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</head>

<body>
<?php echo smarty_function_overlib_init(array('src' => (@PH_JAVASCRIPT_URL)."overlib/"), $this);?>

<?php ob_start(); ?>/&(?![a-zA-Z]{2,5};)/<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('amp_regex', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'confirm.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<div id="loading" class="loading" style="display: none;">
  <div id="loading_msg"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['loading'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
  <img width="220" height="19" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
loading.gif" alt="" />
</div>

<table width="100%" border="0" cellpadding="0" cellspacing="0" class="m_container">
  <tr>
    <td class="m_header" colspan="2">
    <!-- Begin Header -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'header.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <!-- End Header -->
    </td>
  </tr>
  <tr>
    <td class="m_header_menu" colspan="2">
    <!-- Begin Menu -->
    <?php if ($_COOKIE['main_menu_path']): ?>
      <?php $this->assign('cookie_value', $_COOKIE['main_menu_path']); ?>
    <?php else: ?>
      <?php $this->assign('cookie_value', ''); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => '_menu.html', 'smarty_include_vars' => array('postfix' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <script type="text/javascript">
        <?php echo '
        new Zapatec.Menu({source: \'main_menu\',
                          hideDelay: 100,
                          theme: \'nzoom\'});
        '; ?>

        fixMenu('main_menu_path', '<?php echo $this->_tpl_vars['cookie_value']; ?>
');
    </script>
    <!-- End Menu -->
    </td>
  </tr>
  <tr valign="top">
    <td class="m_main">
      <table width="100%" border="0" cellpadding="0" cellspacing="0">
        <tr class="m_title_bar">
          <td class="m_navbar">
          <!-- Begin Title Bar -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'titlebar.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <!-- End Title Bar -->
          </td>
        </tr>
        <?php if ($this->_tpl_vars['display_commercial_announcements']): ?>
        <tr>
          <td class="m_announcement_bar hidden" id="announcement_bar_container">
            <!-- announcement bar -->
            <script type="text/javascript">
            loadAnnouncementBar('announcement_bar_container');
            </script>
            <!-- end announcement bar -->
          </td>
        </tr>
        <?php endif; ?>
        <tr>
          <td class="m_body">
            <div id="messages_container">
            <!-- Begin Messages Reports -->
<?php if ($this->_tpl_vars['messages']->getErrors()): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'message.html', 'smarty_include_vars' => array('display' => 'error','items' => $this->_tpl_vars['messages']->getErrors())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php if ($this->_tpl_vars['messages']->getMessages()): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'message.html', 'smarty_include_vars' => array('display' => 'message','items' => $this->_tpl_vars['messages']->getMessages())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php if ($this->_tpl_vars['messages']->getWarnings()): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'message.html', 'smarty_include_vars' => array('display' => 'warning','items' => $this->_tpl_vars['messages']->getWarnings())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
            <!-- End Messages Reports -->
            </div>
            <!-- Begin Main Body: <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "#(.*\/|_|\.html)#", "") : smarty_modifier_regex_replace($_tmp, "#(.*\/|_|\.html)#", "")))) ? $this->_run_mod_handler('capitalize', true, $_tmp) : smarty_modifier_capitalize($_tmp)); ?>
 -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['template'], 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <!-- End Main Body: <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "#(.*\/|_|\.html)#", "") : smarty_modifier_regex_replace($_tmp, "#(.*\/|_|\.html)#", "")))) ? $this->_run_mod_handler('capitalize', true, $_tmp) : smarty_modifier_capitalize($_tmp)); ?>
 -->
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr valign="bottom">
    <td class="m_footer" colspan="2">
    <!-- Begin Footer -->
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'footer.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <!-- End Footer -->
    </td>
  </tr>
</table>
</body>
</html>