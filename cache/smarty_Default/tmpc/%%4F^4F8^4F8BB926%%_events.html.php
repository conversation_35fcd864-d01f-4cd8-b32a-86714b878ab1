<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_events.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_events.html', 16, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_events.html', 44, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_events.html', 44, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_events.html', 44, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_events.html', 24, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_events.html', 44, false),)), $this); ?>

<?php $this->assign('event_area_border_width', 1); ?>
<?php $this->assign('event_overlapping_multiplier', 1.7); ?>
<?php $this->assign('event_div_height', 16); ?>
<?php $this->assign('ecount', 0); ?>
<?php if ($this->_tpl_vars['event_ids_chunk'] && is_array ( $this->_tpl_vars['event_ids_chunk'] )): ?><?php $this->assign('ecount', count($this->_tpl_vars['event_ids_chunk'])); ?><?php endif; ?>

<?php $_from = $this->_tpl_vars['event_ids_chunk']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ei'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ei']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['eid'] => $this->_tpl_vars['event']):
        $this->_foreach['ei']['iteration']++;
?>
  <?php ob_start(); ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events_info.html", 'smarty_include_vars' => array('event' => $this->_tpl_vars['events'][$this->_tpl_vars['eid']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('event_info', ob_get_contents());ob_end_clean(); ?>

  <?php if ($this->_tpl_vars['max_count']): ?>
        <?php if ($this->_tpl_vars['ecount'] <= $this->_tpl_vars['max_count']): ?>
      <?php echo smarty_function_math(array('assign' => 'event_left','equation' => 'o+i*w/max','w' => $this->_tpl_vars['width'],'i' => $this->_foreach['ei']['iteration']-1,'o' => $this->_tpl_vars['offset'],'max' => $this->_tpl_vars['max_count']), $this);?>

      <?php echo smarty_function_math(array('assign' => 'event_width','equation' => 'w/max-4*eb','w' => $this->_tpl_vars['width'],'max' => $this->_tpl_vars['max_count'],'eb' => $this->_tpl_vars['event_area_border_width']), $this);?>

    <?php else: ?>
      <?php echo smarty_function_math(array('assign' => 'fraction','equation' => 'w/n','w' => $this->_tpl_vars['width'],'n' => $this->_tpl_vars['ecount']), $this);?>

      <?php echo smarty_function_math(array('assign' => 'event_left','equation' => 'o+i*f','i' => $this->_foreach['ei']['iteration']-1,'f' => $this->_tpl_vars['fraction'],'o' => $this->_tpl_vars['offset']), $this);?>

      <?php if (($this->_foreach['ei']['iteration'] == $this->_foreach['ei']['total'])): ?>
        <?php echo smarty_function_math(array('assign' => 'event_width','equation' => 'f-4*eb','f' => $this->_tpl_vars['fraction'],'eb' => $this->_tpl_vars['event_area_border_width']), $this);?>

      <?php else: ?>
        <?php echo smarty_function_math(array('assign' => 'event_width','equation' => 'mul*f-4*eb','f' => $this->_tpl_vars['fraction'],'mul' => $this->_tpl_vars['event_overlapping_multiplier'],'eb' => $this->_tpl_vars['event_area_border_width']), $this);?>

      <?php endif; ?>
    <?php endif; ?>
    <?php $this->assign('event_top', 0); ?>
    <?php $this->assign('event_height', $this->_tpl_vars['event_div_height']); ?>
  <?php else: ?>
        <?php echo smarty_function_math(array('assign' => 'event_top','equation' => 'ep*(eh+(ec-1)*eol)/ec-ep*eol','ec' => $this->_tpl_vars['event']['count'],'ep' => $this->_tpl_vars['event']['pos'],'eh' => $this->_tpl_vars['event_div_height'],'eol' => 2), $this);?>

    <?php echo smarty_function_math(array('assign' => 'event_left','equation' => 'o+((es-ds)*w/60)','es' => $this->_tpl_vars['event']['start'],'ds' => $this->_tpl_vars['day_start'],'w' => $this->_tpl_vars['width'],'o' => $this->_tpl_vars['offset']), $this);?>

    <?php echo smarty_function_math(array('assign' => 'event_height','equation' => '(eh+(ec-1)*eol)/ec','ec' => $this->_tpl_vars['event']['count'],'eh' => $this->_tpl_vars['event_div_height'],'eol' => 2), $this);?>

    <?php echo smarty_function_math(array('assign' => 'event_width','equation' => '(ee-es)*w/60-4*eb*((ee-es)*w/60>4)','ee' => $this->_tpl_vars['event']['end'],'es' => $this->_tpl_vars['event']['start'],'w' => $this->_tpl_vars['width'],'eb' => $this->_tpl_vars['event_area_border_width']), $this);?>

  <?php endif; ?>
  <div class="cal_event<?php if ($this->_tpl_vars['ecount'] <= $this->_tpl_vars['max_count']): ?> hcenter<?php endif; ?><?php if ($this->_tpl_vars['events'][$this->_tpl_vars['eid']]['edit_allocate']): ?> drag<?php endif; ?>" style="background-color: #<?php echo ((is_array($_tmp=@$this->_tpl_vars['events'][$this->_tpl_vars['eid']]['background_color'])) ? $this->_run_mod_handler('default', true, $_tmp, 'cccccc') : smarty_modifier_default($_tmp, 'cccccc')); ?>
;<?php if ($this->_tpl_vars['events'][$this->_tpl_vars['eid']]['allday_event'] != 1 && $this->_tpl_vars['events'][$this->_tpl_vars['eid']]['type_keyword'] != 'plannedtime'): ?> border-top-right-radius: 1em;<?php endif; ?> cursor: <?php if ($this->_tpl_vars['events'][$this->_tpl_vars['eid']]['edit_allocate']): ?>pointer<?php else: ?>default<?php endif; ?>; top: <?php echo $this->_tpl_vars['event_top']; ?>
px; left: <?php echo $this->_tpl_vars['event_left']; ?>
px; height: <?php echo $this->_tpl_vars['event_height']; ?>
px; width: <?php echo $this->_tpl_vars['event_width']; ?>
px;" id="<?php if ($this->_tpl_vars['events'][$this->_tpl_vars['eid']]['type_keyword'] == 'plannedtime'): ?>pt_<?php echo $this->_tpl_vars['eid']; ?>
<?php else: ?>evt_<?php echo $this->_tpl_vars['day']; ?>
_<?php echo $this->_tpl_vars['uid']; ?>
_<?php echo $this->_tpl_vars['eid']; ?>
<?php endif; ?>" data-task="<?php echo $this->_tpl_vars['events'][$this->_tpl_vars['eid']]['task']; ?>
" data-priority="<?php echo $this->_tpl_vars['events'][$this->_tpl_vars['eid']]['priority']; ?>
" onclick="<?php if ($this->_tpl_vars['events'][$this->_tpl_vars['eid']]['edit_allocate']): ?>plannedTime.showForm(event, {event: <?php echo $this->_tpl_vars['eid']; ?>
, user: <?php echo $this->_tpl_vars['uid']; ?>
});<?php else: ?>Event.stop(event);<?php endif; ?>" <?php echo smarty_function_popup(array('trigger' => 'onMouseOver','text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['event_info'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'html', 'UTF-8') : smarty_modifier_escape($_tmp, 'html', 'UTF-8')),'caption' => ((is_array($_tmp=$this->_tpl_vars['day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])),'width' => 350,'sticky' => 1,'closetext' => 'X','closetitle' => $this->_config[0]['vars']['close'],'closeclick' => true,'hauto' => true,'vauto' => true), $this);?>
><?php if ($this->_tpl_vars['events'][$this->_tpl_vars['eid']]['allday_event'] == 1): ?>--:--<?php elseif ($this->_tpl_vars['events'][$this->_tpl_vars['eid']]['event_start'] < $this->_tpl_vars['day']): ?>&raquo;&raquo;:&raquo;&raquo;<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['events'][$this->_tpl_vars['eid']]['duration'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div>
<?php endforeach; endif; unset($_from); ?>