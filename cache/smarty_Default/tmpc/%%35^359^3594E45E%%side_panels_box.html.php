<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/side_panels_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/side_panels_box.html', 6, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/side_panels_box.html', 20, false),)), $this); ?>
<input type="hidden" name="side_panel_model_type" id="side_panel_model_type" value="<?php echo $this->_tpl_vars['model_type']; ?>
" />
<?php $_from = $this->_tpl_vars['side_panels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['pos'] => $this->_tpl_vars['panel']):
        $this->_foreach['i']['iteration']++;
?>
  <?php if (! empty ( $this->_tpl_vars['panel'] )): ?>
    <?php if (($this->_foreach['i']['iteration'] <= 1)): ?>
    <div id="side_panel_loading" class="drag_side_panel loading">
      <div><?php echo ((is_array($_tmp=$this->_config[0]['vars']['loading_side_panels'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
      <img height="19" width="300" alt="" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
loading.gif" />
    </div>
    <?php endif; ?>
    <div id="side_panel_<?php echo $this->_tpl_vars['panel']; ?>
" class="draggable drag_side_panel t_table1 hidden">
      <div id="title_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
" class="t_panel_caption" style="height: 21px;">
        <div id="move_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
" class="floatl" style="padding-bottom: 0px;">
          <a href="#">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
move.png" width="16" height="16" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['draggable'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['draggable'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="icon_button" style="background: none; border: none;" />
          </a>
        </div>
        <div class="floatl" style="max-width: 550px; height: 13px;">
        <?php if ($this->_tpl_vars['side_panels_urls'][$this->_tpl_vars['panel']]): ?>
        <a href="<?php echo $this->_tpl_vars['side_panels_urls'][$this->_tpl_vars['panel']]; ?>
">
          <span class="t_panel_caption_title title_side_panel"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['side_panels_titles'][$this->_tpl_vars['panel']])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 90, "...") : smarty_modifier_mb_truncate($_tmp, 90, "...")))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        </a>
        <?php else: ?>
        <span class="t_panel_caption_title title_side_panel"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['side_panels_titles'][$this->_tpl_vars['panel']])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 90, "...") : smarty_modifier_mb_truncate($_tmp, 90, "...")))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        <?php endif; ?>
        </div>
        <div class="floatr">
          <input type="image" id="img_close_<?php echo $this->_tpl_vars['panel']; ?>
" name="img_close[<?php echo $this->_tpl_vars['panel']; ?>
]" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" class="pointer" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="hideSidePanel('<?php echo $this->_tpl_vars['panel']; ?>
');" />
        </div>
      </div>
      <div id="content_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
" class="side_panel_loader side_panel_content">
        <script type="text/javascript">
        sidePanelsLoad('content_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
', '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', '<?php echo $this->_tpl_vars['model_id']; ?>
', '<?php echo $this->_tpl_vars['panel']; ?>
'<?php if ($this->_tpl_vars['archive']): ?>, 1<?php endif; ?>);
        </script>
      </div>
      <script type="text/javascript">
        new Zapatec.Utils.Draggable({container:'side_panel_<?php echo $this->_tpl_vars['panel']; ?>
', 
                                     handler: 'move_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
',
                                     beforeDragInit: function(){draggableBeforeDragInit(this);},
                                     beforeDragMove: function(){draggablebBeforeDragMove(this);},
                                     beforeDragEnd: function(){draggablebBeforeDragEnd(this);},
                                     onDragInit: function(){draggableOnDragInit(this);},
                                     onDragMove: function(){draggablebOnDragMove(this);},
                                     onDragEnd : function(){draggableOnDragEnd(this);}
                                    });
      </script>
    </div>
  <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>