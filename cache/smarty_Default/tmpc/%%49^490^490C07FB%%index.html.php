<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:12
         compiled from /var/www/Nzoom-Hella/_libs/modules/finance/templates/index.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/index.html', 1, false),)), $this); ?>
<h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
menu/finance.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
<h2><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_hotlinks'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2>

<h3>
<ul style="margin: 0px;">
<?php $_from = $this->_tpl_vars['fin_menu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['menu_item']):
?>
<?php if ($this->_tpl_vars['menu_item']['label']): ?>
  <?php if (! empty ( $this->_tpl_vars['menu_item']['submenu'] )): ?>
  <?php ob_start(); ?><?php echo $this->_tpl_vars['key']; ?>
_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('box_name', ob_get_contents());ob_end_clean(); ?>
  <div class="t_caption2_title" onclick="toggleViewLayouts(this)" id="<?php echo $this->_tpl_vars['key']; ?>
_switch" style="margin-left: -36px;"><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['box_name']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div></div>
  <?php endif; ?>
  <li style="list-style-image: url('<?php echo $this->_tpl_vars['menu_item']['icon']; ?>
')"><a href="<?php echo $this->_tpl_vars['menu_item']['url']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['menu_item']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
    <?php if (! empty ( $this->_tpl_vars['menu_item']['submenu'] )): ?>
    <span id="<?php echo $this->_tpl_vars['key']; ?>
"<?php if ($_COOKIE[$this->_tpl_vars['box_name']] == 'off'): ?> style="display: none;"<?php endif; ?>>
    <ul style="padding-left: 25px;">
    <?php endif; ?>
      <?php $_from = $this->_tpl_vars['menu_item']['submenu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['sub_item']):
?>
        <?php if (! empty ( $this->_tpl_vars['sub_item']['submenu'] )): ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['key']; ?>
_<?php echo $this->_tpl_vars['idx']; ?>
_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('box_name_submenu', ob_get_contents());ob_end_clean(); ?>
        <div class="t_caption2_title" onclick="toggleViewLayouts(this)" id="<?php echo $this->_tpl_vars['key']; ?>
_<?php echo $this->_tpl_vars['idx']; ?>
_switch" style="margin-left: -36px;"><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['box_name_submenu']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div></div>
        <?php endif; ?>
        <li style="list-style-image: url('<?php echo $this->_tpl_vars['sub_item']['icon']; ?>
')"><a href="<?php echo $this->_tpl_vars['sub_item']['url']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['sub_item']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></li>
          <?php if (! empty ( $this->_tpl_vars['sub_item']['submenu'] )): ?>
          <span id="<?php echo $this->_tpl_vars['key']; ?>
_<?php echo $this->_tpl_vars['idx']; ?>
"<?php if ($_COOKIE[$this->_tpl_vars['box_name_submenu']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <ul style="padding-left: 25px;">
          <?php endif; ?>
            <?php $_from = $this->_tpl_vars['sub_item']['submenu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx2'] => $this->_tpl_vars['sub_item2']):
?>
            <li style="list-style-image: url('<?php echo $this->_tpl_vars['sub_item2']['icon']; ?>
')"><a href="<?php echo $this->_tpl_vars['sub_item2']['url']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['sub_item2']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></li>
            <?php endforeach; endif; unset($_from); ?>
          <?php if (! empty ( $this->_tpl_vars['sub_item']['submenu'] )): ?>
          </ul>
          </span>
          <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php if (! empty ( $this->_tpl_vars['menu_item']['submenu'] )): ?>
    </ul>
    </span>
    <?php endif; ?>
  </li>
<?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</ul>
</h3>