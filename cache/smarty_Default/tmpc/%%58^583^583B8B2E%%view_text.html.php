<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from view_text.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'view_text.html', 2, false),array('modifier', 'regex_replace', 'view_text.html', 10, false),array('modifier', 'url2href', 'view_text.html', 16, false),)), $this); ?>
        <tr<?php if ($this->_tpl_vars['var']['hidden']): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['var']['help']), $this);?>
</td>
          <td class="required"><?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td<?php if ($this->_tpl_vars['var']['text_align'] && $this->_tpl_vars['var']['text_align'] != 'right'): ?> style="text-align: <?php echo $this->_tpl_vars['var']['text_align']; ?>
;"<?php endif; ?>>
          <?php echo ''; ?><?php if ($this->_tpl_vars['var']['text_align'] == 'right'): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['width']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['width']; ?><?php echo ''; ?><?php elseif (preg_match ( '#\b(num|small|short)\b#' , $this->_tpl_vars['var']['custom_class'] )): ?><?php echo '80'; ?><?php elseif (preg_match ( '#\bdoubled\b#' , $this->_tpl_vars['var']['custom_class'] )): ?><?php echo '400'; ?><?php else: ?><?php echo '200'; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_width', ob_get_contents());ob_end_clean(); ?><?php echo '<div style="float: left; text-align: '; ?><?php echo $this->_tpl_vars['var']['text_align']; ?><?php echo '; width: '; ?><?php echo $this->_tpl_vars['var_width']; ?><?php echo ''; ?><?php if (preg_match ( '#^[\d\.]+$#' , $this->_tpl_vars['var_width'] )): ?><?php echo 'px'; ?><?php endif; ?><?php echo ';">'; ?><?php endif; ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['use_asterisk'] && preg_match ( '#^asteriskcall_(fax|phone|gsm).*$#' , $this->_tpl_vars['var']['name'] )): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var']['name'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1') : smarty_modifier_regex_replace($_tmp, '/^asteriskcall_(fax|phone|gsm).*$/', '$1')); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('asterisk_contact', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['grouping']): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['var']['value']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['val']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['asterisk_contact']): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => $this->_tpl_vars['asterisk_contact'],'number' => $this->_tpl_vars['val']['value'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['val']['value'])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?><?php echo ','; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php if ($this->_tpl_vars['asterisk_contact']): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => $this->_tpl_vars['asterisk_contact'],'number' => $this->_tpl_vars['var']['value'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var']['value'])) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['text_align'] == 'right'): ?><?php echo '</div>'; ?><?php endif; ?><?php echo ''; ?><?php echo ''; ?><?php if (( $this->_tpl_vars['var']['value'] || $this->_tpl_vars['var']['value'] === '0' ) && $this->_tpl_vars['var']['back_label']): ?><?php echo '&nbsp;'; ?><?php echo $this->_tpl_vars['var']['back_label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

          </td>
        </tr>