<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from view_autocompleter.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'view_autocompleter.html', 2, false),array('modifier', 'trim', 'view_autocompleter.html', 10, false),)), $this); ?>
        <tr<?php if ($this->_tpl_vars['var']['hidden']): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['var']['help']), $this);?>
</td>
          <td class="required"><?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td>
            <?php if ($this->_tpl_vars['var']['grouping']): ?>
              <?php $_from = $this->_tpl_vars['var']['value']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['val']):
?>
                  <?php echo $this->_tpl_vars['val']['value']; ?>
, 
              <?php endforeach; endif; unset($_from); ?>
            <?php else: ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => trim($this->_tpl_vars['var']['value']),'value_id' => $this->_tpl_vars['var']['value_id'],'view_mode_url' => $this->_tpl_vars['var']['autocomplete']['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
                        <?php if ($this->_tpl_vars['var']['value'] || $this->_tpl_vars['var']['value'] === '0'): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('back_label' => $this->_tpl_vars['var']['back_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
          </td>
        </tr>