<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_side_panel_options.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_side_panel_options.html', 1, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_side_panel_options.html', 16, false),)), $this); ?>
                  <div id="side_panel_settings_show_div" class="side_panel_settings_show_div button" onclick="toggleAvailableSidePanelOptions(1);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_personal_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
raquo_big.png" width="12" height="12" alt="&raquo;" />
                  </div>

                  <form name="side_panels" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=users&amp;users=ajax_mynzoom&amp;ajax_save=1" method="post">
                    <div id="side_panel_options" class="side_panel_options">
                      <div id="side_panel_settings_hide_div" class="side_panel_settings_hide_div button" onclick="toggleAvailableSidePanelOptions(0);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
laquo_big.png" width="12" height="12" alt="&laquo;" />
                      </div>
                      <div id="side_panel_options_title" class="t_caption3 strong">
                        <div class="drag floatr">
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/move.png" alt="" width="10" height="10" title="<?php echo $this->_config[0]['vars']['draggable']; ?>
" />
                        </div>

                        <div class="drag">
                        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" alt="" style="padding-right: 1px; cursor: help;" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['side_panels_personal_settings'],'text_content' => $this->_config[0]['vars']['help_side_panels_personal_settings'],'popup_only' => 1), $this);?>
 /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_personal_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                        </div>

                      </div>
                      <div id="side_panel_all_options" class="side_panel_all_options"></div>
                      <div id="side_panel_options_buttons" class="side_panel_options_buttons t_caption3_reverted">
                        <div id="side_panel_restore_defaults" class="" style="padding-bottom: 3px;">
                          <input type="checkbox" id="update_all_actions" name="update_all_actions" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_update_all_actions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <label for="update_all_actions"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_update_all_actions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                          <br />
                          <input type="checkbox" id="restore_defaults" name="restore_defaults" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_restore_defaults'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <label for="restore_defaults"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_restore_defaults'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                        </div>
                        <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><button type="button" name="cancel" class="button" onclick="toggleAvailableSidePanelOptions(0);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
                      </div>
                    </div>
                  </form>
                  <script type="text/javascript">
                    new Draggable('side_panel_options', {handle: 'side_panel_options_title'});
                  </script>