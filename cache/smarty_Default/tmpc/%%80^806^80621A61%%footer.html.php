<?php /* Smarty version 2.6.33, created on 2025-05-21 15:13:35
         compiled from footer.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'date_format', 'footer.html', 3, false),array('modifier', 'escape', 'footer.html', 3, false),)), $this); ?>
<div class="m_footer_divider"></div>
<div class="m_footer_copyright">
  &copy; <?php echo ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y') : smarty_modifier_date_format($_tmp, '%Y')); ?>
 - <span class="strong"><?php echo ((is_array($_tmp=$this->_tpl_vars['system_options']['system'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['system_options']['version'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> 
  <span class="grey">(build <?php echo $this->_tpl_vars['system_options']['build']; ?>
<?php if ($this->_tpl_vars['system_options']['updated']): ?> from <?php echo ((is_array($_tmp=$this->_tpl_vars['system_options']['updated'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
<?php endif; ?>)</span><br />
  Powered by <a href="http://www.bgservice.net" target="_blank">BGService Ltd.</a><br />
  <span class="grey"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['timer_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: [timer_total] <?php echo ((is_array($_tmp=$this->_config[0]['vars']['seconds'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
, <?php echo ((is_array($_tmp=$this->_config[0]['vars']['timer_render'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: [timer_render] <?php echo ((is_array($_tmp=$this->_config[0]['vars']['seconds'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
, <?php echo ((is_array($_tmp=$this->_config[0]['vars']['content_size'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: [content_size]</span>
  </div>