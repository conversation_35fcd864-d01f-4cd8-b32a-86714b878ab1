<?php /* Smarty version 2.6.33, created on 2025-05-21 16:13:40
         compiled from _attachments_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_attachments_side_panel.html', 10, false),array('modifier', 'mb_truncate', '_attachments_side_panel.html', 19, false),array('modifier', 'default', '_attachments_side_panel.html', 29, false),array('function', 'cycle', '_attachments_side_panel.html', 15, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<?php echo ''; ?><?php if ($this->_tpl_vars['module'] && $this->_tpl_vars['controller']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['controller'] != $this->_tpl_vars['module']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('controller_action_string', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

<input type="hidden" id="<?php echo $this->_tpl_vars['side_panel']; ?>
_total" name="<?php echo $this->_tpl_vars['side_panel']; ?>
_total" class="total" value="<?php echo $this->_tpl_vars['total']; ?>
" />
<table cellpadding="0" cellspacing="0" border="0" class="t_layout_table" width="100%">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_path'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <?php if ($this->_tpl_vars['files']['attachments']): ?>
  <?php $_from = $this->_tpl_vars['files']['attachments']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['k'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['k']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['attachment']):
        $this->_foreach['k']['iteration']++;
?>
  <tr class="<?php echo smarty_function_cycle(array('name' => 'attachments','values' => 't_odd,t_even'), $this);?>
 vtop">
    <td class="t_border" style="font-weight: normal; text-align: left; white-space: nowrap;">
      <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
          <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        </a>
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" alt="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="floatl" style="border: 0px none; padding-right: 4px;" />
        <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
      <?php endif; ?>
    </td>
    <td style="font-weight: normal; text-align: left; white-space: nowrap;">
      <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
          <span title="<?php echo $this->_tpl_vars['attachment']['description']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['description'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</span>
        </a>
      <?php else: ?>
        <span title="<?php echo $this->_tpl_vars['attachment']['description']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['description'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</span>
      <?php endif; ?>
    </td>
  </tr>
  <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
  <?php if ($this->_tpl_vars['files']['generated']): ?>
  <?php $_from = $this->_tpl_vars['files']['generated']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['k'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['k']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['attachment']):
        $this->_foreach['k']['iteration']++;
?>
  <tr class="<?php echo smarty_function_cycle(array('name' => 'attachments','values' => 't_odd,t_even'), $this);?>
 vtop">
    <td class="t_border" style="font-weight: normal; text-align: left; white-space: nowrap;">
      <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
          <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        </a>
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" alt="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="floatl" style="border: 0px none; padding-right: 4px;" />
        <span title="<?php echo $this->_tpl_vars['attachment']['name']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
      <?php endif; ?>
    </td>
    <td style="font-weight: normal; text-align: left; white-space: nowrap;">
      <?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['attachment']['id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
          <span title="<?php echo $this->_tpl_vars['attachment']['description']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['description'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</span>
        </a>
      <?php else: ?>
        <span title="<?php echo $this->_tpl_vars['attachment']['description']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['description'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 40) : smarty_modifier_mb_truncate($_tmp, 40)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</span>
      <?php endif; ?>
    </td>
  </tr>
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>
</table>
<?php endif; ?>