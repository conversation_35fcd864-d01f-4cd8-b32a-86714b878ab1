<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_submenu_actions_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_submenu_actions_box.html', 10, false),)), $this); ?>
<?php if ($this->_tpl_vars['show_divider']): ?>
<div class="t_footer" style="border:1px solid #CCCCCC; border-bottom: 0"></div>
<?php endif; ?>
<?php if ($this->_tpl_vars['available_actions_left'] || $this->_tpl_vars['available_actions_right']): ?>
<table cellpadding="0" cellspacing="0" border="0" class="t_submenu_table">
  <tr>
    <td>
      <div class="action_tabs action_tabs_submenu_left" id="action_subtabs">
        <?php if ($this->_tpl_vars['available_actions_left']): ?>
          <ul id="submenu_<?php echo smarty_function_counter(array('start' => 1,'name' => 'submenu_left_counter','assign' => 'submenu_left_menu_num','print' => true), $this);?>
" class="zpHideOnLoad">
            <?php $_from = $this->_tpl_vars['available_actions_left']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
              <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
                </ul>
                <script type="text/javascript">
                  new Zapatec.Menu({source: 'submenu_<?php echo $this->_tpl_vars['submenu_left_menu_num']; ?>
',
                                    hideDelay: 100,
                                    theme: 'nzoom'});
                </script>
                <div class="clear"></div>
                <ul id="submenu_<?php echo smarty_function_counter(array('start' => 1,'name' => 'submenu_left_counter','assign' => 'submenu_left_menu_num','print' => true), $this);?>
" class="zpHideOnLoad">
              <?php elseif ($this->_tpl_vars['available_action']['drop_menu']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_drop_menu_item.html", 'smarty_include_vars' => array('action_options' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php else: ?>
                <li id="submenu_item<?php echo $this->_tpl_vars['available_action']['name']; ?>
" class="<?php if ($this->_tpl_vars['available_action']['selected']): ?>menu-path<?php endif; ?><?php if (! $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?> tab_no_label<?php endif; ?>">
                  <?php if ($this->_tpl_vars['available_action']['name'] == 'view' && $_REQUEST['toggle_attachments']): ?>
                    <script type="text/javascript">
                      Event.observe(window, 'load', getAttachmentsExclusively);
                      function getAttachmentsExclusively() {
                        getActionOptions('td_attachments_options', '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', 'attachments', <?php if ($this->_tpl_vars['model'] && $this->_tpl_vars['model']->get('id')): ?>'<?php echo $this->_tpl_vars['model']->get('id'); ?>
'<?php else: ?>0<?php endif; ?>, {});
                        toggleActionOptions($('attachments_action'));
                      }
                    </script>
                  <?php endif; ?>
                  <?php if (! $this->_tpl_vars['available_action']['no_img']): ?><img src="<?php if ($this->_tpl_vars['available_action']['custom_img']): ?><?php echo $this->_tpl_vars['available_action']['custom_img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['available_action']['img']): ?><?php echo $this->_tpl_vars['available_action']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['name']; ?>
<?php endif; ?>.png<?php endif; ?>" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" border="0" /><?php endif; ?>
                  <a href="<?php echo $this->_tpl_vars['available_action']['url']; ?>
"<?php if ($this->_tpl_vars['available_action']['target']): ?> target="<?php echo $this->_tpl_vars['available_action']['target']; ?>
"<?php endif; ?> title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"><?php if ($this->_tpl_vars['available_action']['no_img'] || $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?><?php echo $this->_tpl_vars['available_action']['label']; ?>
<?php endif; ?></a>
                                    <span style="display: none"<?php if ($this->_tpl_vars['available_action']['onclick']): ?> onclick="<?php echo $this->_tpl_vars['available_action']['onclick']; ?>
"<?php endif; ?> id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_action"></span>
                </li>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </ul>
          <script type="text/javascript">
            new Zapatec.Menu({source: 'submenu_<?php echo $this->_tpl_vars['submenu_left_menu_num']; ?>
',
                              hideDelay: 100,
                              theme: 'nzoom'});
          </script>
        <?php endif; ?>
      </div>
    </td>
    <td>
      <div class="action_tabs action_tabs_submenu_right" id="action_subtabs_right">
        <ul id="submenu_right_<?php echo smarty_function_counter(array('start' => 1,'name' => 'submenu_right_counter','assign' => 'submenu_right_menu_num','print' => true), $this);?>
" class="zpHideOnLoad">
          <?php if ($this->_tpl_vars['available_actions_right']): ?>
            <?php $_from = $this->_tpl_vars['available_actions_right']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
              <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
                </ul>
                <script type="text/javascript">
                  new Zapatec.Menu({source: 'submenu_right_<?php echo $this->_tpl_vars['submenu_right_menu_num']; ?>
',
                                  hideDelay: 100,
                                  theme: 'nzoom'});
                </script>
                <div class="clear"></div>
                <ul id="submenu_right_<?php echo smarty_function_counter(array('start' => 1,'name' => 'submenu_right_counter','assign' => 'submenu_right_menu_num','print' => true), $this);?>
" class="zpHideOnLoad">
              <?php elseif ($this->_tpl_vars['available_action']['drop_menu']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_drop_menu_item.html", 'smarty_include_vars' => array('action_options' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php else: ?>
                <li id="submenu_item<?php echo $this->_tpl_vars['available_action']['name']; ?>
" class="tab_no_label<?php if ($this->_tpl_vars['available_action']['selected']): ?> menu-path<?php endif; ?>">
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['available_action']['img']): ?><?php echo $this->_tpl_vars['available_action']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['name']; ?>
<?php endif; ?>.png" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" border="0" />
                  <a href="<?php echo $this->_tpl_vars['available_action']['url']; ?>
"<?php if ($this->_tpl_vars['available_action']['target']): ?> target="<?php echo $this->_tpl_vars['available_action']['target']; ?>
"<?php endif; ?> id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_action" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"></a>
                </li>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php endif; ?>
        </ul>
        <script type="text/javascript">
          new Zapatec.Menu({source: 'submenu_right_<?php echo $this->_tpl_vars['submenu_right_menu_num']; ?>
',
                            hideDelay: 100,
                            theme: 'nzoom'});
        </script>
      </div>
      <?php if (! ( $this->_tpl_vars['action'] == 'add' || $this->_tpl_vars['action'] == 'addquick' || $this->_tpl_vars['action'] == 'custom_action' )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layouts_index.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
    </td>
  </tr>
</table>
<?php endif; ?>