<?php /* Smarty version 2.6.33, created on 2023-11-15 17:38:14
         compiled from logout.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'popup', 'logout.html', 1, false),array('modifier', 'escape', 'logout.html', 1, false),)), $this); ?>
  <a href="<?php echo $this->_tpl_vars['logout']['url']; ?>
" onclick="return confirmAction('logout', function(el) { window.location.href = el.href; }, this);" <?php echo smarty_function_popup(array('text' => $this->_tpl_vars['logout']['legend'],'caption' => ((is_array($_tmp=$this->_tpl_vars['logout']['i18n'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
><?php echo ((is_array($_tmp=$this->_tpl_vars['logout']['i18n'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>