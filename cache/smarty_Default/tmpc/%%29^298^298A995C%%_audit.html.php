<?php /* Smarty version 2.6.33, created on 2023-11-15 17:55:56
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html', 1, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html', 1, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html', 42, false),array('modifier', 'regex_replace', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html', 42, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html', 44, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html', 19, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_audit.html', 27, false),)), $this); ?>
    <h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
list.png" border="0" alt="<?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['audit_title'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['audit_vars']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['audit_vars'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['audit_title'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['audit_vars']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['audit_vars'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
    <h2><?php echo ((is_array($_tmp=$this->_tpl_vars['audit_legend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2>
    <?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['audit']['vars'] ) && ! empty ( $this->_tpl_vars['audit']['vars'][0]['action'] ) && $this->_tpl_vars['audit']['vars'][0]['action'] == 'email'): ?>0<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('display_old_value', ob_get_contents());ob_end_clean(); ?>
    <div class="audit clear<?php if (! $this->_tpl_vars['display_old_value']): ?> email<?php endif; ?>">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_header t_table_border">
        <tr>
          <td colspan="4" class="t_caption3 strong legend"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['data'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <tr>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 20px;"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 150px;"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['var_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <?php if ($this->_tpl_vars['display_old_value']): ?>
          <td class="t_caption t_border" nowrap="nowrap" style="width: 200px;"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['var_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption" nowrap="nowrap" style="width: 200px;"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['old_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <?php else: ?>
          <td class="t_caption" nowrap="nowrap" style="width: 400px;" colspan="2"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['var_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <?php endif; ?>
        </tr>
        <?php echo smarty_function_counter(array('name' => 'audit_counter','start' => 0,'assign' => 'audit_counter'), $this);?>

        <?php if ($this->_tpl_vars['module'] == 'finance' && $this->_tpl_vars['controller'] == 'warehouses'): ?><?php $this->assign('warehouse_audit', 1); ?><?php $this->assign('audit_classes', 't_odd2,t_even2'); ?><?php else: ?><?php $this->assign('audit_classes', 't_odd,t_even'); ?><?php endif; ?>
        <?php $_from = $this->_tpl_vars['audit']['vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var']):
        $this->_foreach['i']['iteration']++;
?>
          <?php if ($this->_tpl_vars['var']['field_name'] == 'bb_delimiter'): ?>
          <tr>
            <td colspan="4" class="t_caption3 strong legend"><?php if (empty ( $this->_tpl_vars['var']['deleted'] )): ?><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['var']['model_id']; ?>
<?php if ($_REQUEST['archive']): ?>&amp;archive=1<?php endif; ?>#bb_row_<?php echo $this->_tpl_vars['var']['field_value']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</a><?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></td>
          </tr>
          <?php else: ?>
          <tr class="<?php echo smarty_function_cycle(array('values' => $this->_tpl_vars['audit_classes'],'name' => 'audit_vars'), $this);?>
 vtop">
            <td class="t_border hright"><?php echo smarty_function_counter(array('name' => 'audit_counter','print' => true), $this);?>
</td>
            <td class="t_border"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['var_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
            <td <?php if ($this->_tpl_vars['display_old_value']): ?>class="legend t_border"<?php else: ?>class="legend" colspan="2"<?php endif; ?>>
              <?php if ($this->_tpl_vars['var']['field_name'] == 'mail_content' || $this->_tpl_vars['var']['field_name'] == 'content' && in_array ( $this->_tpl_vars['var']['action'] , array ( 'add_comment' , 'edit_comment' ) )): ?>
                <?php echo $this->_tpl_vars['var']['field_value']; ?>

              <?php elseif ($this->_tpl_vars['warehouse_audit'] && is_array ( $this->_tpl_vars['var']['field_value'] )): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_batch_view.html", 'smarty_include_vars' => array('no_row' => true,'val' => $this->_tpl_vars['var']['field_value'],'idx' => ($this->_foreach['i']['iteration'])."_new")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php else: ?>
                <?php if (isset ( $this->_tpl_vars['var']['label'] ) && $this->_tpl_vars['var']['label'] !== ''): ?>
                  <?php $this->assign('new_value', $this->_tpl_vars['var']['label']); ?>
                <?php else: ?>
                  <?php $this->assign('new_value', $this->_tpl_vars['var']['field_value']); ?>
                <?php endif; ?>
                <?php if (preg_match ( '/<\w.*>/' , $this->_tpl_vars['new_value'] )): ?>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['new_value'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;") : smarty_modifier_regex_replace($_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;")); ?>

                <?php else: ?>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['new_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

                <?php endif; ?>
              <?php endif; ?>
            </td>
            <?php if ($this->_tpl_vars['display_old_value']): ?>
            <td class="legend">
              <?php if ($this->_tpl_vars['var']['field_name'] == 'content' && in_array ( $this->_tpl_vars['var']['action'] , array ( 'add_comment' , 'edit_comment' ) )): ?>
                <?php echo $this->_tpl_vars['var']['old_value']; ?>

              <?php elseif ($this->_tpl_vars['warehouse_audit'] && is_array ( $this->_tpl_vars['var']['old_value'] )): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_batch_view.html", 'smarty_include_vars' => array('no_row' => true,'val' => $this->_tpl_vars['var']['old_value'],'idx' => ($this->_foreach['i']['iteration'])."_old")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              <?php elseif (preg_match ( '/<\w.*>/' , $this->_tpl_vars['var']['old_value'] )): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['old_value'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;") : smarty_modifier_regex_replace($_tmp, "#<([\w.]+@[\w.]+)>#", "&lt;\$1&gt;")); ?>

              <?php else: ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var']['old_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

              <?php endif; ?>
            </td>
            <?php endif; ?>
          </tr>
          <?php endif; ?>
        <?php endforeach; else: ?>
          <tr class="<?php echo smarty_function_cycle(array('values' => $this->_tpl_vars['audit_classes'],'name' => 'audit_vars'), $this);?>
">
            <td class="error" colspan="4"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_changes_made'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          </tr>
        <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="4"></td>
        </tr>
      </table>
    </div>