<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 1, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 8, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 9, false),array('modifier', 'string_format', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 14, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 28, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 67, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 3, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 26, false),array('function', 'json', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_calendar.html', 77, false),)), $this); ?>
<?php if (is_array ( $this->_tpl_vars['days'] ) && count($this->_tpl_vars['days']) && is_array ( $this->_tpl_vars['users'] ) && count($this->_tpl_vars['users'])): ?>
<?php $this->assign('allocate_allowed', $this->_tpl_vars['currentUser']->checkRights('tasks','allocate')); ?>
<?php echo smarty_function_math(array('assign' => 'droppable_width','equation' => 'cw-lw-14*s','cw' => $this->_tpl_vars['cal_width'],'lw' => 258,'s' => $this->_tpl_vars['vscroll']), $this);?>

<table id="calendar_table_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" class="t_table" cellspacing="0" cellpadding="3" border="0">
<?php $_from = $this->_tpl_vars['days']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['dk'] => $this->_tpl_vars['day']):
?>
    <tr class="t_panel_caption_title" style="height: <?php echo $this->_tpl_vars['dashlet_data']['cal_row_height']; ?>
px;">
    <td class="strong" style="background-color: <?php if ($this->_tpl_vars['working_days'][$this->_tpl_vars['dk']]): ?>#FABF8F<?php else: ?>#D6D6D6<?php endif; ?>;<?php if ($this->_tpl_vars['day'] == ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short']))): ?> border: 2px solid red; color: white;<?php endif; ?>"><div style="width: <?php if ($this->_tpl_vars['day'] == ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short']))): ?>156<?php else: ?>160<?php endif; ?>px;"><?php echo ((is_array($_tmp=$this->_tpl_vars['day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, '%d.%m.%Y (%A)') : smarty_modifier_date_format($_tmp, '%d.%m.%Y (%A)')); ?>
</div></td>
    <td class="t_panel_caption hcenter" style="font-weight: normal;"><div style="width: 98px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_without_fixed_period'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php if ($this->_tpl_vars['settings']['week_start_hour'] > 0): ?>
      <td class="t_border t_panel_caption hcenter" style="font-weight: normal;"><div style="width: 49px;">&laquo;</div></td>
    <?php endif; ?>
    <?php unset($this->_sections['hour']);
$this->_sections['hour']['name'] = 'hour';
$this->_sections['hour']['start'] = (int)$this->_tpl_vars['settings']['week_start_hour'];
$this->_sections['hour']['loop'] = is_array($_loop=$this->_tpl_vars['settings']['week_end_hour']+1) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['hour']['show'] = true;
$this->_sections['hour']['max'] = $this->_sections['hour']['loop'];
$this->_sections['hour']['step'] = 1;
if ($this->_sections['hour']['start'] < 0)
    $this->_sections['hour']['start'] = max($this->_sections['hour']['step'] > 0 ? 0 : -1, $this->_sections['hour']['loop'] + $this->_sections['hour']['start']);
else
    $this->_sections['hour']['start'] = min($this->_sections['hour']['start'], $this->_sections['hour']['step'] > 0 ? $this->_sections['hour']['loop'] : $this->_sections['hour']['loop']-1);
if ($this->_sections['hour']['show']) {
    $this->_sections['hour']['total'] = min(ceil(($this->_sections['hour']['step'] > 0 ? $this->_sections['hour']['loop'] - $this->_sections['hour']['start'] : $this->_sections['hour']['start']+1)/abs($this->_sections['hour']['step'])), $this->_sections['hour']['max']);
    if ($this->_sections['hour']['total'] == 0)
        $this->_sections['hour']['show'] = false;
} else
    $this->_sections['hour']['total'] = 0;
if ($this->_sections['hour']['show']):

            for ($this->_sections['hour']['index'] = $this->_sections['hour']['start'], $this->_sections['hour']['iteration'] = 1;
                 $this->_sections['hour']['iteration'] <= $this->_sections['hour']['total'];
                 $this->_sections['hour']['index'] += $this->_sections['hour']['step'], $this->_sections['hour']['iteration']++):
$this->_sections['hour']['rownum'] = $this->_sections['hour']['iteration'];
$this->_sections['hour']['index_prev'] = $this->_sections['hour']['index'] - $this->_sections['hour']['step'];
$this->_sections['hour']['index_next'] = $this->_sections['hour']['index'] + $this->_sections['hour']['step'];
$this->_sections['hour']['first']      = ($this->_sections['hour']['iteration'] == 1);
$this->_sections['hour']['last']       = ($this->_sections['hour']['iteration'] == $this->_sections['hour']['total']);
?>
      <td class="t_border t_panel_caption hcenter" style="font-weight: normal;"><div style="width: 49px;"><?php echo ((is_array($_tmp=$this->_sections['hour']['index'])) ? $this->_run_mod_handler('string_format', true, $_tmp, '%02d') : smarty_modifier_string_format($_tmp, '%02d')); ?>
:00</div></td>
    <?php endfor; endif; ?>
    <?php if ($this->_tpl_vars['settings']['week_end_hour'] < 23): ?>
      <td class="t_border t_panel_caption hcenter" style="font-weight: normal;"><div style="width: 49px;">&raquo;</div></td>
    <?php endif; ?>
    <td class="t_border strong t_panel_caption hcenter"><div style="width: 79px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_duration'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>

  <?php ob_start(); ?><?php if ($this->_tpl_vars['allocate_allowed'] && $this->_tpl_vars['day'] >= ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short']))): ?>1<?php else: ?><?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('add_pt', ob_get_contents());ob_end_clean(); ?>

  <?php $_from = $this->_tpl_vars['users']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['uid'] => $this->_tpl_vars['user']):
?>
    <tr class="<?php echo smarty_function_cycle(array('name' => "day".($this->_tpl_vars['day']),'values' => 't_odd,t_even'), $this);?>
" style="height: <?php echo $this->_tpl_vars['dashlet_data']['cal_row_height']; ?>
px;">
    <?php ob_start(); ?><?php echo $this->_tpl_vars['user']['name']; ?>
<?php if ($this->_tpl_vars['user']['department']): ?> <?php echo $this->_tpl_vars['user']['department']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('user_name', ob_get_contents());ob_end_clean(); ?>
    <td class="t_border" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['user_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><div style="height: 14px; overflow: hidden;"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['user_name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 25, '...', true) : smarty_modifier_mb_truncate($_tmp, 25, '...', true)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_border nopadding<?php if ($this->_tpl_vars['add_pt']): ?> pointer" onclick="plannedTime.showForm(event, {date: '<?php echo $this->_tpl_vars['day']; ?>
', user: <?php echo $this->_tpl_vars['uid']; ?>
, user_name: '<?php echo ((is_array($_tmp=$this->_tpl_vars['user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')); ?>
'});<?php elseif ($this->_tpl_vars['day'] >= ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso_short']))): ?>" onclick="plannedTime.updateMessagesPanel(['<ul class=\'error\'><li><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_add_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo $this->_config[0]['vars']['of']; ?>
 <?php echo $this->_config[0]['vars']['tasks_planned_time']; ?>
</li></ul>']);<?php endif; ?>">
      <div style="width: 0px; overflow: visible;">
        <div class="cal_week_day droppable" id="evt_<?php echo $this->_tpl_vars['day']; ?>
_<?php echo $this->_tpl_vars['uid']; ?>
" style="position: relative; height: 20px; width: <?php echo $this->_tpl_vars['droppable_width']; ?>
px;">
                    <?php $this->assign('offset', 0); ?>
          <?php $this->assign('width', $this->_tpl_vars['dashlet_data']['w0']); ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events.html", 'smarty_include_vars' => array('event_ids_chunk' => $this->_tpl_vars['event_ids'][$this->_tpl_vars['day']][$this->_tpl_vars['uid']]['allday_events'],'max_count' => $this->_tpl_vars['dashlet_data']['max_no_ol'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

                    <?php if ($this->_tpl_vars['settings']['week_start_hour'] > 0): ?>
            <?php echo smarty_function_math(array('assign' => 'offset','equation' => 'o+w','o' => $this->_tpl_vars['offset'],'w' => $this->_tpl_vars['width']), $this);?>

            <?php $this->assign('width', $this->_tpl_vars['dashlet_data']['w1']); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events.html", 'smarty_include_vars' => array('event_ids_chunk' => $this->_tpl_vars['event_ids'][$this->_tpl_vars['day']][$this->_tpl_vars['uid']]['events_before'],'max_count' => $this->_tpl_vars['dashlet_data']['max_no_ol'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; ?>

                    <?php echo smarty_function_math(array('assign' => 'offset','equation' => 'o+w','o' => $this->_tpl_vars['offset'],'w' => $this->_tpl_vars['width']), $this);?>

          <?php $this->assign('width', $this->_tpl_vars['dashlet_data']['w1']); ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events.html", 'smarty_include_vars' => array('event_ids_chunk' => $this->_tpl_vars['event_ids'][$this->_tpl_vars['day']][$this->_tpl_vars['uid']]['events'],'max_count' => 0,'day_start' => $this->_tpl_vars['dashlet_data']['day_start'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

                    <?php if ($this->_tpl_vars['settings']['week_end_hour'] < 23): ?>
            <?php echo smarty_function_math(array('assign' => 'offset','equation' => 'o+w*(e-s+1)','o' => $this->_tpl_vars['offset'],'w' => $this->_tpl_vars['width'],'s' => $this->_tpl_vars['settings']['week_start_hour'],'e' => $this->_tpl_vars['settings']['week_end_hour']), $this);?>

            <?php $this->assign('width', $this->_tpl_vars['dashlet_data']['w1']); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events.html", 'smarty_include_vars' => array('event_ids_chunk' => $this->_tpl_vars['event_ids'][$this->_tpl_vars['day']][$this->_tpl_vars['uid']]['events_after'],'max_count' => $this->_tpl_vars['dashlet_data']['max_no_ol'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; ?>
        </div>
      </div>
    </td>
    <?php if ($this->_tpl_vars['settings']['week_start_hour'] > 0): ?>
      <td class="t_border<?php if ($this->_tpl_vars['add_pt']): ?> pointer" onclick="plannedTime.showForm(event, {date: '<?php echo $this->_tpl_vars['day']; ?>
', user: <?php echo $this->_tpl_vars['uid']; ?>
, user_name: '<?php echo ((is_array($_tmp=$this->_tpl_vars['user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')); ?>
', start: <?php echo smarty_function_math(array('equation' => 'x-1','x' => $this->_tpl_vars['settings']['week_start_hour']), $this);?>
});<?php endif; ?>"></td>
    <?php endif; ?>
    <?php unset($this->_sections['hour']);
$this->_sections['hour']['name'] = 'hour';
$this->_sections['hour']['start'] = (int)$this->_tpl_vars['settings']['week_start_hour'];
$this->_sections['hour']['loop'] = is_array($_loop=$this->_tpl_vars['settings']['week_end_hour']+1) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['hour']['show'] = true;
$this->_sections['hour']['max'] = $this->_sections['hour']['loop'];
$this->_sections['hour']['step'] = 1;
if ($this->_sections['hour']['start'] < 0)
    $this->_sections['hour']['start'] = max($this->_sections['hour']['step'] > 0 ? 0 : -1, $this->_sections['hour']['loop'] + $this->_sections['hour']['start']);
else
    $this->_sections['hour']['start'] = min($this->_sections['hour']['start'], $this->_sections['hour']['step'] > 0 ? $this->_sections['hour']['loop'] : $this->_sections['hour']['loop']-1);
if ($this->_sections['hour']['show']) {
    $this->_sections['hour']['total'] = min(ceil(($this->_sections['hour']['step'] > 0 ? $this->_sections['hour']['loop'] - $this->_sections['hour']['start'] : $this->_sections['hour']['start']+1)/abs($this->_sections['hour']['step'])), $this->_sections['hour']['max']);
    if ($this->_sections['hour']['total'] == 0)
        $this->_sections['hour']['show'] = false;
} else
    $this->_sections['hour']['total'] = 0;
if ($this->_sections['hour']['show']):

            for ($this->_sections['hour']['index'] = $this->_sections['hour']['start'], $this->_sections['hour']['iteration'] = 1;
                 $this->_sections['hour']['iteration'] <= $this->_sections['hour']['total'];
                 $this->_sections['hour']['index'] += $this->_sections['hour']['step'], $this->_sections['hour']['iteration']++):
$this->_sections['hour']['rownum'] = $this->_sections['hour']['iteration'];
$this->_sections['hour']['index_prev'] = $this->_sections['hour']['index'] - $this->_sections['hour']['step'];
$this->_sections['hour']['index_next'] = $this->_sections['hour']['index'] + $this->_sections['hour']['step'];
$this->_sections['hour']['first']      = ($this->_sections['hour']['iteration'] == 1);
$this->_sections['hour']['last']       = ($this->_sections['hour']['iteration'] == $this->_sections['hour']['total']);
?>
      <td class="t_border<?php if ($this->_tpl_vars['add_pt']): ?> pointer" onclick="plannedTime.showForm(event, {date: '<?php echo $this->_tpl_vars['day']; ?>
', user: <?php echo $this->_tpl_vars['uid']; ?>
, user_name: '<?php echo ((is_array($_tmp=$this->_tpl_vars['user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')); ?>
', start: <?php echo $this->_sections['hour']['index']; ?>
});<?php endif; ?>"></td>
    <?php endfor; endif; ?>
    <?php if ($this->_tpl_vars['settings']['week_end_hour'] < 23): ?>
      <td class="t_border<?php if ($this->_tpl_vars['add_pt']): ?> pointer" onclick="plannedTime.showForm(event, {date: '<?php echo $this->_tpl_vars['day']; ?>
', user: <?php echo $this->_tpl_vars['uid']; ?>
, user_name: '<?php echo ((is_array($_tmp=$this->_tpl_vars['user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')); ?>
', start: <?php echo smarty_function_math(array('equation' => 'x+1','x' => $this->_tpl_vars['settings']['week_end_hour']), $this);?>
});<?php endif; ?>"></td>
    <?php endif; ?>
    <?php echo smarty_function_math(array('assign' => 'workload','equation' => 'd/r*100','d' => ((is_array($_tmp=@$this->_tpl_vars['event_ids'][$this->_tpl_vars['day']][$this->_tpl_vars['uid']]['duration'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'r' => ((is_array($_tmp=@$this->_tpl_vars['user']['working_time'])) ? $this->_run_mod_handler('default', true, $_tmp, 480) : smarty_modifier_default($_tmp, 480))), $this);?>

    <?php ob_start(); ?><?php $this->assign('val_set', 0); ?><?php $_from = $this->_tpl_vars['dashlet_data']['workload']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['val']):
?><?php if (! $this->_tpl_vars['val_set'] && $this->_tpl_vars['workload'] > $this->_tpl_vars['key']): ?><?php echo $this->_tpl_vars['val']; ?>
<?php $this->assign('val_set', 1); ?><?php endif; ?><?php endforeach; endif; unset($_from); ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('workload_class', ob_get_contents());ob_end_clean(); ?>
    <td class="hright duration_<?php echo $this->_tpl_vars['day']; ?>
_<?php echo $this->_tpl_vars['uid']; ?>
" id="duration_<?php echo $this->_tpl_vars['day']; ?>
_<?php echo $this->_tpl_vars['uid']; ?>
">
      <span class="strong <?php echo $this->_tpl_vars['workload_class']; ?>
"><span class="hours"><?php echo smarty_function_math(array('equation' => 'floor(d/60)','d' => ((is_array($_tmp=@$this->_tpl_vars['event_ids'][$this->_tpl_vars['day']][$this->_tpl_vars['uid']]['duration'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0))), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['hours_short'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <span class="minutes"><?php echo $this->_tpl_vars['event_ids'][$this->_tpl_vars['day']][$this->_tpl_vars['uid']]['duration']%60; ?>
</span> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['minutes_short'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
    </td>
  </tr>
  <?php endforeach; endif; unset($_from); ?>
<?php endforeach; endif; unset($_from); ?>
</table>
<script type="text/javascript" defer="defer">
  if (typeof plannedTime == 'object') { plannedTime.data = <?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['dashlet_data'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, ''))), $this);?>
; plannedTime.adjustDisplay(); }
</script>
<?php endif; ?>