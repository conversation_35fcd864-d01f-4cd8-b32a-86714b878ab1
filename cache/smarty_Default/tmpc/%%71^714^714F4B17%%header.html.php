<?php /* Smarty version 2.6.33, created on 2023-11-15 17:38:07
         compiled from header.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'header.html', 9, false),array('modifier', 'date_format', 'header.html', 9, false),array('function', 'mailto', 'header.html', 13, false),)), $this); ?>
      <table width="100%" border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td class="m_header_logo"<?php if ($this->_tpl_vars['header_info']['logo']): ?> style="background: url('<?php echo $this->_tpl_vars['header_info']['logo']; ?>
') <?php if ($this->_tpl_vars['header_info']['info_background_color']): ?><?php echo $this->_tpl_vars['header_info']['info_background_color']; ?>
<?php else: ?>transparent<?php endif; ?> no-repeat"<?php endif; ?>>
            <?php if (! $this->_tpl_vars['header_info']['logo']): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
logo.png" width="196" height="51" border="0" alt="" style="float:left;" />
            <?php endif; ?>
            <div class="m_header_info"<?php if ($this->_tpl_vars['header_info']['info_color']): ?> style="color: <?php echo $this->_tpl_vars['header_info']['info_color']; ?>
; hover: <?php echo $this->_tpl_vars['header_info']['info_color']; ?>
;"<?php endif; ?>>
              <?php if ($this->_tpl_vars['installation_info']['company']): ?>
                <?php if ($this->_tpl_vars['installation_info']['company']): ?><strong><?php echo ((is_array($_tmp=$this->_tpl_vars['installation_info']['company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if ($this->_tpl_vars['system_options']['database_copy_date']): ?> (<?php echo $this->_config[0]['vars']['database_copy_date']; ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['system_options']['database_copy_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
)<?php endif; ?></strong><br /><?php endif; ?>
                <?php if ($this->_tpl_vars['installation_info']['address']): ?><?php echo $this->_tpl_vars['installation_info']['address']; ?>
<br /><?php endif; ?>
                <?php if ($this->_tpl_vars['installation_info']['phone']): ?><?php echo $this->_config[0]['vars']['phone']; ?>
: <?php echo $this->_tpl_vars['installation_info']['phone']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['installation_info']['fax']): ?>, <?php echo $this->_config[0]['vars']['fax']; ?>
: <?php echo $this->_tpl_vars['installation_info']['fax']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['installation_info']['phone'] || $this->_tpl_vars['installation_info']['fax']): ?><br /><?php endif; ?>
                <?php ob_start(); ?><?php if ($this->_tpl_vars['header_info']['info_color']): ?>style="color: <?php echo $this->_tpl_vars['header_info']['info_color']; ?>
"<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('mailto_extra', ob_get_contents());ob_end_clean(); ?>
                <?php if ($this->_tpl_vars['installation_info']['email']): ?><?php echo smarty_function_mailto(array('address' => $this->_tpl_vars['installation_info']['email'],'extra' => $this->_tpl_vars['mailto_extra']), $this);?>
<?php endif; ?><?php if ($this->_tpl_vars['installation_info']['url']): ?><?php if ($this->_tpl_vars['installation_info']['email']): ?>, <?php endif; ?><a href="<?php echo $this->_tpl_vars['installation_info']['url']; ?>
" target="_blank"<?php if ($this->_tpl_vars['header_info']['info_color']): ?> style="color: <?php echo $this->_tpl_vars['header_info']['info_color']; ?>
"<?php endif; ?>><?php echo $this->_tpl_vars['installation_info']['url']; ?>
</a><?php endif; ?>
              <?php elseif ($this->_tpl_vars['system_options']['database_copy_date']): ?>
                <strong>(<?php echo $this->_config[0]['vars']['database_copy_date']; ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['system_options']['database_copy_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
)</strong>
              <?php else: ?>
                &nbsp;
              <?php endif; ?>
            </div>
          </td>
        </tr>
        <tr>
          <td class="m_header_divider2"></td>
        </tr>
      </table>