<?php /* Smarty version 2.6.33, created on 2025-05-21 16:13:39
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_vars.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_vars.html', 5, false),)), $this); ?>
<?php $this->assign('vars', $this->_tpl_vars['contract']->get('formula_vars')); ?>
<div class="m_header_menu" style="width: 550px;">
  <ul>
    <li>
      <span id="tab_empty" class="selected"><a onclick="toggleVars('empty');"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['vars_empty'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span>
    </li>
    <li>
      <span id="tab_completed"><a onclick="toggleVars('completed');"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['vars_completed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></span>
    </li>
  </ul>
</div>
<div class="clear"></div>
<?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['vars']['empty']['standard'] ) && ( $this->_tpl_vars['pedit'] || $this->_tpl_vars['pview'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('edit_empty_standard_vars_allowed', ob_get_contents());ob_end_clean(); ?>
<?php if ($this->_tpl_vars['edit_empty_standard_vars_allowed']): ?>
  <form method="post" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=saveformulavals&amp;saveformulavals=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
">
<?php endif; ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table empty_vars">
  <tr>
    <td class="t_caption" colspan="3">
      <div class="t_caption_title"><?php echo $this->_config[0]['vars']['contracts_model_vars']; ?>
</div>
    </td>
  </tr>
  <?php $_from = $this->_tpl_vars['vars']['empty']['standard']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
?>
    <?php if ($this->_tpl_vars['pedit']): ?>
      <?php $this->assign('vdisabled', 0); ?>
    <?php else: ?>
      <?php $this->assign('vdisabled', 1); ?>
    <?php endif; ?>
    <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['prev_formula'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'value' => $this->_tpl_vars['var']['value'],'readonly' => $this->_tpl_vars['var']['readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['vdisabled'],'options_align' => $this->_tpl_vars['var']['options_align'],'restrict' => $this->_tpl_vars['var']['restrict'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endforeach; else: ?>
    <tr>
      <td colspan="3">
        <?php echo $this->_config[0]['vars']['contracts_model_vars_no_empty_standard_vars']; ?>

      </td>
    </tr>
  <?php endif; unset($_from); ?>
  <?php if ($this->_tpl_vars['edit_empty_standard_vars_allowed']): ?>
    <tr>
      <td colspan="3">
        <button class="button" type="submit" name="submit"><?php echo $this->_config[0]['vars']['save']; ?>
</button>
      </td>
    </tr>
  <?php endif; ?>
</table>
<?php if ($this->_tpl_vars['edit_empty_standard_vars_allowed']): ?>
  </form>
<?php endif; ?>

<?php if (! empty ( $this->_tpl_vars['vars']['empty']['global'] ) && ( $this->_tpl_vars['pgedit'] || $this->_tpl_vars['pgview'] )): ?>
  <?php ob_start(); ?><?php if (( $this->_tpl_vars['pgedit'] || $this->_tpl_vars['pgview'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('edit_empty_global_vars_allowed', ob_get_contents());ob_end_clean(); ?>
  <?php if ($this->_tpl_vars['edit_empty_global_vars_allowed']): ?>
    <form method="post" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=saveglobalvals&amp;saveglobalvals=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
">
  <?php endif; ?>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table empty_vars">
    <tr>
      <td class="t_caption" colspan="3">
        <div class="t_caption_title" style="color: #ff0000;">!!! <?php echo $this->_config[0]['vars']['contracts_global_vars']; ?>
 !!!</div>
      </td>
    </tr>
    <?php $_from = $this->_tpl_vars['vars']['empty']['global']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
?>
      <?php if ($this->_tpl_vars['pgedit']): ?>
        <?php $this->assign('vdisabled', 0); ?>
      <?php else: ?>
        <?php $this->assign('vdisabled', 1); ?>
      <?php endif; ?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['prev_formula'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'value' => $this->_tpl_vars['var']['value'],'readonly' => $this->_tpl_vars['var']['readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['vdisabled'],'options_align' => $this->_tpl_vars['var']['options_align'],'restrict' => $this->_tpl_vars['var']['restrict'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endforeach; else: ?>
      <tr>
        <td colspan="3">
          <?php echo $this->_config[0]['vars']['contracts_model_vars_no_empty_global_vars']; ?>

        </td>
      </tr>
    <?php endif; unset($_from); ?>
    <?php if ($this->_tpl_vars['edit_empty_global_vars_allowed']): ?>
      <tr>
        <td colspan="3">
          <button class="button" type="submit" name="submit_global" onclick="return confirmAction('edit_global_vars', submitForm, this, i18n['messages']['confirm_save_formula_vars_global']);"><?php echo $this->_config[0]['vars']['save']; ?>
</button>
        </td>
      </tr>
    <?php endif; ?>
  </table>
  <?php if ($this->_tpl_vars['edit_empty_global_vars_allowed']): ?>
    </form>
  <?php endif; ?>
<?php endif; ?>

<?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['vars']['completed']['standard'] ) && ( $this->_tpl_vars['pedit'] || $this->_tpl_vars['pview'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('edit_completed_standard_vars_allowed', ob_get_contents());ob_end_clean(); ?>
<?php if ($this->_tpl_vars['edit_completed_standard_vars_allowed']): ?>
  <form method="post" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=saveformulavals&amp;saveformulavals=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
&amp;force=1">
<?php endif; ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table completed_vars" style="display: none">
  <tr>
    <td class="t_caption" colspan="3">
      <div class="t_caption_title"><?php echo $this->_config[0]['vars']['contracts_model_vars']; ?>
</div>
    </td>
  </tr>
  <?php $_from = $this->_tpl_vars['vars']['completed']['standard']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
?>
    <?php if ($this->_tpl_vars['pedit']): ?>
      <?php $this->assign('vdisabled', 0); ?>
    <?php else: ?>
      <?php $this->assign('vdisabled', 1); ?>
    <?php endif; ?>
    <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['prev_formula'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'value' => $this->_tpl_vars['var']['value'],'readonly' => $this->_tpl_vars['var']['readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['vdisabled'],'options_align' => $this->_tpl_vars['var']['options_align'],'restrict' => $this->_tpl_vars['var']['restrict'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endforeach; else: ?>
    <tr>
      <td colspan="3">
        <?php echo $this->_config[0]['vars']['contracts_model_vars_no_completed_standard_vars']; ?>

      </td>
    </tr>
  <?php endif; unset($_from); ?>
  <?php if ($this->_tpl_vars['edit_completed_standard_vars_allowed']): ?>
    <tr>
      <td colspan="3">
        <button class="button" type="submit" name="submit_force" onclick="return confirmAction('edit_vars', submitForm, this, i18n['messages']['confirm_save_formula_vars_force_model']);"><?php echo $this->_config[0]['vars']['save']; ?>
</button>
      </td>
    </tr>
  <?php endif; ?>
</table>
<?php if ($this->_tpl_vars['edit_completed_standard_vars_allowed']): ?>
  </form>
<?php endif; ?>

<?php if (! empty ( $this->_tpl_vars['vars']['completed']['global'] ) && ( $this->_tpl_vars['pedit'] || $this->_tpl_vars['pview'] )): ?>
  <?php ob_start(); ?><?php if (( $this->_tpl_vars['pedit'] || $this->_tpl_vars['pview'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('edit_completed_global_vars_allowed', ob_get_contents());ob_end_clean(); ?>
  <?php if ($this->_tpl_vars['edit_completed_global_vars_allowed']): ?>
    <form method="post" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=saveglobalvals&amp;saveglobalvals=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
&amp;force=1">
  <?php endif; ?>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table completed_vars" style="display: none">
    <tr>
      <td class="t_caption" colspan="3">
        <div class="t_caption_title" style="color: #ff0000;">!!! <?php echo $this->_config[0]['vars']['contracts_global_vars']; ?>
 !!!</div>
      </td>
    </tr>
    <?php $_from = $this->_tpl_vars['vars']['completed']['global']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['var']):
?>
      <?php if ($this->_tpl_vars['pgedit']): ?>
        <?php $this->assign('vdisabled', 0); ?>
      <?php else: ?>
        <?php $this->assign('vdisabled', 1); ?>
      <?php endif; ?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['var']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['prev_formula'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'value' => $this->_tpl_vars['var']['value'],'readonly' => $this->_tpl_vars['var']['readonly'],'hidden' => $this->_tpl_vars['var']['hidden'],'disabled' => $this->_tpl_vars['vdisabled'],'options_align' => $this->_tpl_vars['var']['options_align'],'restrict' => $this->_tpl_vars['var']['restrict'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endforeach; else: ?>
      <tr>
        <td colspan="3">
          <?php echo $this->_config[0]['vars']['contracts_model_vars_no_completed_global_vars']; ?>

        </td>
      </tr>
    <?php endif; unset($_from); ?>
    <?php if ($this->_tpl_vars['edit_completed_global_vars_allowed']): ?>
      <tr>
        <td colspan="3">
          <button class="button" type="submit" name="submit_force_global" onclick="return confirmAction('edit_global_vars', submitForm, this, i18n['messages']['confirm_save_formula_vars_force_global']);"><?php echo $this->_config[0]['vars']['save']; ?>
</button>
        </td>
      </tr>
    <?php endif; ?>
  </table>
  <?php if ($this->_tpl_vars['edit_completed_global_vars_allowed']): ?>
    </form>
  <?php endif; ?>
<?php endif; ?>