<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 28, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 59, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 151, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 151, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 55, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 59, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 59, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_view_company.html', 166, false),)), $this); ?>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
      <?php $this->assign('super_layout_offset', '0'); ?>
      <?php $_from = $this->_tpl_vars['customer']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>
      <?php if ($this->_tpl_vars['layout']['view']): ?>

      <?php if ($this->_tpl_vars['layout']['place'] > $this->_tpl_vars['super_layout_offset']): ?>
        <?php if ($this->_tpl_vars['layout']['place'] <= @PH_CUSTOMERS_LAYOUTS_MAIN_TO): ?>
          <?php $this->assign('super_layout_offset', @PH_CUSTOMERS_LAYOUTS_MAIN_TO); ?>
          <?php $this->assign('super_layout', 'main_data'); ?>
          <?php $this->assign('super_layout_class', 'customers_main_data'); ?>
        <?php elseif ($this->_tpl_vars['layout']['place'] <= @PH_CUSTOMERS_LAYOUTS_ADDRESS_TO): ?>
          <?php $this->assign('super_layout_offset', @PH_CUSTOMERS_LAYOUTS_ADDRESS_TO); ?>
          <?php $this->assign('super_layout', 'contact_data'); ?>
          <?php $this->assign('super_layout_class', 'customers_contact_data'); ?>
        <?php elseif ($this->_tpl_vars['layout']['place'] <= @PH_CUSTOMERS_LAYOUTS_REG_TO): ?>
          <?php $this->assign('super_layout_offset', @PH_CUSTOMERS_LAYOUTS_REG_TO); ?>
          <?php $this->assign('super_layout', 'company_data'); ?>
          <?php $this->assign('super_layout_class', 'customers_company_data'); ?>
        <?php else: ?>
          <?php $this->assign('super_layout', ''); ?>
          <?php $this->assign('super_layout_class', ''); ?>
        <?php endif; ?>
        <?php $this->assign('super_layout_cookie', ''); ?>
        <?php if ($this->_tpl_vars['super_layout']): ?>
        <tr>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
            </div>
            <div class="layout_switch" onclick="toggleViewLayouts(this)" id="customers_<?php echo $this->_tpl_vars['super_layout']; ?>
_switch">
              <?php ob_start(); ?>customers_<?php echo $this->_tpl_vars['super_layout']; ?>
_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('super_layout_cookie', ob_get_contents());ob_end_clean(); ?>
              <?php ob_start(); ?>customers_<?php echo $this->_tpl_vars['super_layout']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('super_layout_name', ob_get_contents());ob_end_clean(); ?>
              <a name="customer_<?php echo $this->_tpl_vars['super_layout']; ?>
_index"></a><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['super_layout_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if ($this->_tpl_vars['super_layout'] == 'contact_data' && $this->_tpl_vars['customer']->get('main_branch_name')): ?> - <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('main_branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div>
            </div>
          </td>
        </tr>
        <?php endif; ?>
      <?php endif; ?>

        <?php if ($this->_tpl_vars['layout']['system'] || array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
        <tr<?php if ($this->_tpl_vars['layout']['visible']): ?> class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
"<?php endif; ?><?php if (! $this->_tpl_vars['layout']['visible'] || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td colspan="3" class="t_caption3 pointer">
            <div class="floatr index_arrow_anchor">
              <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
            </div>
            <div class="layout_switch" <?php if ($this->_tpl_vars['layout']['system']): ?>onclick="toggleViewLayouts(this)" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch"<?php else: ?>onclick="toggleLayouts(this)" id="layout_<?php echo $this->_tpl_vars['layout']['id']; ?>
_switch"<?php endif; ?>>
              <a name="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
            </div>
          </td>
        </tr>
        <?php endif; ?>

        <?php if ($this->_tpl_vars['lkey'] == 'name'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
            <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>
</span>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'is_company'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['customer']->get('is_company')): ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php else: ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'code'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'num'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'admit_VAT_credit'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required">&nbsp;</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['customer']->get('admit_VAT_credit')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'assigned'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('assigned_to_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'country'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('country_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'city'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('city'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'postal_code'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('postal_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'address'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('address'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'contacts'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td colspan="2" nowrap="nowrap" class="nopadding">
            <?php echo smarty_function_counter(array('name' => 'k','start' => 0,'print' => false), $this);?>

            <?php $_from = $this->_tpl_vars['customer']->contactParameters; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['z'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['z']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_param']):
        $this->_foreach['z']['iteration']++;
?>
              <?php ob_start(); ?>customers_<?php echo $this->_tpl_vars['contact_param']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_label', ob_get_contents());ob_end_clean(); ?>
              <?php ob_start(); ?><?php echo $this->_tpl_vars['contact_param']; ?>
_note<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_note_var', ob_get_contents());ob_end_clean(); ?>
              <?php $this->assign('contact_param_notes', $this->_tpl_vars['customer']->get($this->_tpl_vars['contact_param_note_var'])); ?>
              <?php if ($this->_tpl_vars['customer']->get($this->_tpl_vars['contact_param'])): ?>
                <?php $_from = $this->_tpl_vars['customer']->get($this->_tpl_vars['contact_param']); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['n'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['n']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_num'] => $this->_tpl_vars['contact']):
        $this->_foreach['n']['iteration']++;
?>
                  <div style="width: 10px; padding: 5px; float: left; clear: left;"><?php if (in_array ( $this->_tpl_vars['contact_param'] , $this->_tpl_vars['required_fields'] ) && ($this->_foreach['n']['iteration'] <= 1)): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?></div>
                  <?php if ($this->_tpl_vars['use_asterisk'] && ( $this->_tpl_vars['contact_param'] == 'fax' || $this->_tpl_vars['contact_param'] == 'phone' || $this->_tpl_vars['contact_param'] == 'gsm' )): ?>
                    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => $this->_tpl_vars['contact_param'],'number' => $this->_tpl_vars['contact'],'label' => $this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']],'error' => $this->_tpl_vars['messages']->getErrors($this->_tpl_vars['contact_param_error']),'note' => $this->_tpl_vars['contact_param_notes'][$this->_tpl_vars['contact_num']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                  <?php else: ?>
                    <div style="float: left; padding: 5px;">
                      <?php if ($this->_tpl_vars['contact_param'] == 'web'): ?>
                        <a href="http://<?php echo $this->_tpl_vars['contact']; ?>
" target="_blank">
                      <?php elseif ($this->_tpl_vars['contact_param'] == 'email'): ?>
                        <a href="mailto:<?php echo $this->_tpl_vars['contact']; ?>
" target="_self">
                      <?php endif; ?>
                      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['contact_param']; ?>
.png" alt="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']]; ?>
:" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" />&nbsp;<?php echo $this->_tpl_vars['contact']; ?>
<?php if ($this->_tpl_vars['contact_param_notes'][$this->_tpl_vars['contact_num']]): ?> - <?php echo $this->_tpl_vars['contact_param_notes'][$this->_tpl_vars['contact_num']]; ?>
<?php endif; ?>
                      <?php if ($this->_tpl_vars['contact_param'] == 'web' || $this->_tpl_vars['contact_param'] == 'email'): ?>
                        </a>
                      <?php endif; ?>
                    </div>
                  <?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'company_name'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'in_dds'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('in_dds'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'eik'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('eik'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'registration_file'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('registration_file'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'registration_volume'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('registration_volume'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'registration_number'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('registration_number'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'registration_address'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('registration_address'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'mol'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('mol'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'bank'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bank'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'iban'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('iban'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif ($this->_tpl_vars['lkey'] == 'bic'): ?>
        <tr class="<?php echo $this->_tpl_vars['super_layout_class']; ?>
" id="customer_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' || $_COOKIE[$this->_tpl_vars['super_layout_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text_content' => ((is_array($_tmp=$this->_tpl_vars['layout']['description'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</td>
          <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('bic'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
        <?php elseif (array_key_exists ( $this->_tpl_vars['layout']['id'] , $this->_tpl_vars['layouts_vars'] )): ?>
        <!-- Customer Additional Vars -->
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_view_vars.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>

      <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
        <?php if ($this->_tpl_vars['customer']->get('buttons')): ?>
          <tr>
            <td colspan="3">&nbsp;</td>
          </tr>
          <tr>
            <td colspan="3">
              <?php echo ''; ?><?php $_from = $this->_tpl_vars['customer']->get('buttons'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['button']):
?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_button.html", 'smarty_include_vars' => array('label' => $this->_tpl_vars['button']['label'],'standalone' => true,'name' => $this->_tpl_vars['button']['name'],'source' => $this->_tpl_vars['button']['source'],'disabled' => $this->_tpl_vars['button']['disabled'],'hidden' => $this->_tpl_vars['button']['hidden'],'width' => $this->_tpl_vars['button']['width'],'height' => $this->_tpl_vars['button']['height'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>

            </td>
          </tr>
        <?php endif; ?>
      </table>