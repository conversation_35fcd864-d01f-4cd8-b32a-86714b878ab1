<?php /* Smarty version 2.6.33, created on 2023-11-16 11:25:04
         compiled from /var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_categories.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_categories.html', 3, false),array('function', 'repeat', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/_categories.html', 49, false),)), $this); ?>
<?php if ($this->_tpl_vars['from_type']): ?>
  <tr>
    <td colspan="3" class="t_caption3 pointer" onclick="toggleViewLayouts(this)" id="nom_categories_switch"><div class="switch_<?php if ($_COOKIE['nom_categories_box'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_categories'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <tr id="nom_categories"<?php if ($_COOKIE['nom_categories_box'] == 'off'): ?> style="display: none;"<?php endif; ?>>
<?php else: ?>
  <tr id="nomenclature_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
<?php endif; ?>
  <td colspan="3">
    <input type="hidden" name="update_categories" value="1" />
        <script type='text/javascript'>
    <?php echo '
    var tree;
    window.onload = function() {
      new Zapatec.Tree({
          tree: "tree",
          theme : "default",
          initLevel: 10, 
          hiliteSelectedNode: false
      });
    }
    '; ?>

    </script>

    <a href="javascript:Zapatec.Tree.all['tree'].collapseAll()"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['collapse_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a> |
    <a href="javascript:Zapatec.Tree.all['tree'].expandAll()"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['expand_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
    <div id="tree_container">
    <ul id="tree">
    <?php echo ''; ?><?php $_from = $this->_tpl_vars['categories_tree']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['category']):
        $this->_foreach['i']['iteration']++;
?><?php echo ''; ?><?php $this->assign('level', $this->_tpl_vars['category']->get('level')); ?><?php echo ''; ?><?php if (($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?><?php echo ''; ?><?php $this->assign('next_level', 0); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php $this->assign('next_idx', $this->_tpl_vars['idx']+1); ?><?php echo ''; ?><?php $this->assign('next_category', $this->_tpl_vars['categories_tree'][$this->_tpl_vars['next_idx']]); ?><?php echo ''; ?><?php $this->assign('next_level', $this->_tpl_vars['next_category']->get('level')); ?><?php echo ''; ?><?php endif; ?><?php echo '<li><input type="checkbox" name="categories[]" id="category_'; ?><?php echo $this->_tpl_vars['category']->get('id'); ?><?php echo '" value="'; ?><?php echo $this->_tpl_vars['category']->get('id'); ?><?php echo '"'; ?><?php if (@ in_array ( $this->_tpl_vars['category']->get('id') , $this->_tpl_vars['cats'] )): ?><?php echo ' checked="checked"'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['action'] == 'view' || $this->_tpl_vars['category']->isDeleted() || ! $this->_tpl_vars['category']->isActivated() || ( ! $this->_tpl_vars['from_type'] && ! $this->_tpl_vars['layout']['edit'] )): ?><?php echo ' disabled="disabled"'; ?><?php endif; ?><?php echo ''; ?><?php if (! $this->_tpl_vars['category']->isDeleted() && $this->_tpl_vars['category']->isActivated()): ?><?php echo ' onclick="toggleCategories(this)"'; ?><?php endif; ?><?php echo ' /><label for="category_'; ?><?php echo $this->_tpl_vars['category']->get('id'); ?><?php echo '"'; ?><?php if ($this->_tpl_vars['category']->isDeleted() || ! $this->_tpl_vars['category']->isActivated()): ?><?php echo ' class="inactive_option" title="'; ?><?php echo $this->_config[0]['vars']['inactive_option']; ?><?php echo '"'; ?><?php endif; ?><?php echo '>'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['category']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</label>'; ?><?php if (! $this->_tpl_vars['from_type'] && $this->_tpl_vars['action'] != 'view' && ! $this->_tpl_vars['layout']['edit'] && @ in_array ( $this->_tpl_vars['category']->get('id') , $this->_tpl_vars['cats'] )): ?><?php echo '<input type="hidden" name="categories[]" value="'; ?><?php echo $this->_tpl_vars['category']->get('id'); ?><?php echo '" />'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['next_level'] > $this->_tpl_vars['level']): ?><?php echo '<ul>'; ?><?php elseif ($this->_tpl_vars['next_level'] == $this->_tpl_vars['level']): ?><?php echo '</li>'; ?><?php else: ?><?php echo ''; ?><?php echo smarty_function_repeat(array('string' => '</li></ul>','num' => $this->_tpl_vars['level']-$this->_tpl_vars['next_level']), $this);?><?php echo '</li>'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>

    </ul>
    </div>
  </td>
</tr>