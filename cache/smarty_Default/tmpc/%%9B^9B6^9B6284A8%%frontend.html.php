<?php /* Smarty version 2.6.33, created on 2023-11-15 17:38:14
         compiled from /var/www/Nzoom-Hella/_libs/modules/index/templates/frontend.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/index/templates/frontend.html', 5, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/index/templates/frontend.html', 9, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/index/templates/frontend.html', 16, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/index/templates/frontend.html', 16, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/modules/index/templates/frontend.html', 16, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/index/templates/frontend.html', 16, false),)), $this); ?>
<div class="clear" id="index_page_clear_flag"></div>
<?php echo smarty_function_counter(array('name' => 'idx','assign' => 'empty_idx','start' => 0,'print' => false), $this);?>

<?php $_from = $this->_tpl_vars['settings']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['position']):
        $this->_foreach['i']['iteration']++;
?>
<?php $this->assign('dashlet', $this->_tpl_vars['dashlets'][$this->_tpl_vars['key']]); ?>
  <?php if (! empty ( $this->_tpl_vars['dashlet'] )): ?>
    <div id="dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" class="t_table1 draggable <?php if (preg_match ( '#\d+l#' , $this->_tpl_vars['position'] )): ?>drag_left<?php elseif (preg_match ( '#\d+r#' , $this->_tpl_vars['position'] )): ?>drag_right<?php else: ?><?php echo smarty_function_cycle(array('values' => 'drag_left, drag_right'), $this);?>
<?php endif; ?><?php if ($this->_tpl_vars['dashlet']->get('full_width') == 1): ?> full_width<?php endif; ?>" <?php if ($this->_tpl_vars['dashlet']->get('full_width') == 1): ?>style="width: 99%"<?php endif; ?>>
      <div id="title_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" class="cal_title_bar t_caption2 t_border t_border_left hcenter">
        <div style="padding: 7px 0px; width: auto;" class="layout_switch pointer" id="content_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
_switch" onclick="if (!isSubelementClicked('toggleViewLayouts')) { toggleViewLayouts(this); processEmptyElements(true);}">
            <?php ob_start(); ?>content_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('dashlet_id_cookie', ob_get_contents());ob_end_clean(); ?>
            <div style="float: left; margin: 4px 5px; padding: 0;" class="switch_<?php if ($_COOKIE[$this->_tpl_vars['dashlet_id_cookie']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div>
            <?php if ($this->_tpl_vars['dashlet']->get('description') !== ''): ?>
            <div style="float: left; padding-left: 3px; padding-right: 3px;">
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
info.png" width="16" height="16" border="0" alt="" class="help" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['dashlet']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
            </div>
            <?php endif; ?>
            <div id="move_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" style="float: left; padding-left: 3px;">
              <a href="#">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
move.png" width="16" height="16" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['draggable'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['draggable'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="icon_button" style="background: none; border: none; margin: 0;"/>
              </a>
            </div>
            <?php if ($this->_tpl_vars['dashlet']->get('module') != 'plugin'): ?>
              <?php if ($this->_tpl_vars['dashlet']->get('module') == 'reports'): ?>
                <a href="<?php echo $this->_tpl_vars['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=reports&amp;report_type=<?php echo $this->_tpl_vars['dashlet']->get('controller'); ?>
">
              <?php else: ?>
                <?php $this->assign('dashlet_filters', $this->_tpl_vars['dashlet']->get('filters')); ?>
                <a href="<?php echo $this->_tpl_vars['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['dashlet']->get('module'); ?>
&amp;<?php if ($this->_tpl_vars['dashlet']->get('module') != $this->_tpl_vars['dashlet']->get('controller')): ?>controller=<?php echo $this->_tpl_vars['dashlet']->get('controller'); ?>
&amp;<?php echo $this->_tpl_vars['dashlet']->get('controller'); ?>
=<?php else: ?><?php echo $this->_tpl_vars['dashlet']->get('module'); ?>
=<?php endif; ?>search&amp;session_param_prefix=dashlets_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
<?php if ($this->_tpl_vars['dashlet_filters']['display']): ?>&amp;display=<?php echo $this->_tpl_vars['dashlet_filters']['display']; ?>
<?php endif; ?>">
              <?php endif; ?>
                  <span id="title_text_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" class="t_caption2_title"><?php echo $this->_tpl_vars['dashlet']->get('name'); ?>
</span>
                </a>
            <?php else: ?>
              <?php if ($this->_tpl_vars['dashlet']->get('controller') == 'calendar'): ?>
                <a href="<?php echo $this->_tpl_vars['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars">
              <?php endif; ?>
              <span id="title_text_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" class="t_caption2_title"><?php echo $this->_tpl_vars['dashlet']->get('name'); ?>
</span>
              <?php if ($this->_tpl_vars['dashlet']->get('controller') == 'calendar'): ?>
                </a>
              <?php endif; ?>
            <?php endif; ?>

        </div>
      </div>
      <div id="content_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
" class="dashlet_loader dashlet_content"<?php if ($_COOKIE[$this->_tpl_vars['dashlet_id_cookie']] == 'off'): ?> style="display: none;"<?php endif; ?>>
        <script type="text/javascript">
          dashletsLoad('content_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
', '<?php echo $this->_tpl_vars['dashlet']->get('module'); ?>
', '<?php echo $this->_tpl_vars['dashlet']->get('controller'); ?>
', '<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
');
        </script>
      </div>
      <script type="text/javascript">
        new Zapatec.Utils.Draggable({container:'dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
', 
                                     handler: 'move_dashlet_<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
',
                                     beforeDragInit: function(){draggableBeforeDragInit(this);},
                                     beforeDragMove: function(){draggablebBeforeDragMove(this);},
                                     beforeDragEnd: function(){draggablebBeforeDragEnd(this);},
                                     onDragInit: function(){draggableOnDragInit(this);},
                                     onDragMove: function(){draggablebOnDragMove(this);},
                                     onDragEnd : function(){draggableOnDragEnd(this);}
                                    });
      </script>
    </div>
  <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
<div class="clear"></div>