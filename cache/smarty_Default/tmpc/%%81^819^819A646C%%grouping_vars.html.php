<?php /* Smarty version 2.6.33, created on 2023-11-15 17:48:33
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/grouping_vars.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/grouping_vars.html', 24, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/grouping_vars.html', 45, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/grouping_vars.html', 45, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/grouping_vars.html', 47, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/grouping_vars.html', 30, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['var']['values'] ) && count ( $this->_tpl_vars['var']['values'] )): ?>
    <?php ob_start(); ?>print_columns<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('default_action_columns', ob_get_contents());ob_end_clean(); ?>
  <?php ob_start(); ?>print_columns_pattern<?php if (! empty ( $this->_tpl_vars['pattern_id'] )): ?><?php echo $this->_tpl_vars['pattern_id']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('action_columns', ob_get_contents());ob_end_clean(); ?>
  <?php if (! isset ( $this->_tpl_vars['var'][$this->_tpl_vars['action_columns']] ) && ! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['default_action_columns']] )): ?>
    <?php $this->assign('action_columns', $this->_tpl_vars['default_action_columns']); ?>
  <?php endif; ?>

    <?php ob_start(); ?>print_exclude_columns<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('default_exclude_action_columns', ob_get_contents());ob_end_clean(); ?>
  <?php ob_start(); ?>print_exclude_columns_pattern<?php if (! empty ( $this->_tpl_vars['pattern_id'] )): ?><?php echo $this->_tpl_vars['pattern_id']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('action_exclude_columns', ob_get_contents());ob_end_clean(); ?>
  <?php if (! isset ( $this->_tpl_vars['var'][$this->_tpl_vars['action_exclude_columns']] ) && ! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['default_exclude_action_columns']] )): ?>
    <?php $this->assign('action_exclude_columns', $this->_tpl_vars['default_exclude_action_columns']); ?>
  <?php endif; ?>
  <?php if ($this->_tpl_vars['var']['bb'] == 0): ?>
    <div class="t_caption2_title"><?php echo $this->_tpl_vars['var']['label']; ?>
</div>
      <?php $this->assign('t_width', $this->_tpl_vars['var']['t_width_print']); ?>
  <?php else: ?>
    <?php $this->assign('t_width', '100%'); ?>
  <?php endif; ?>
  <table class="t_grouping_table" cellpadding="5" cellspacing="0" border="1"<?php if ($this->_tpl_vars['t_width']): ?> width="<?php echo $this->_tpl_vars['t_width']; ?>
"<?php endif; ?>>
    <tr>
      <?php if (empty ( $this->_tpl_vars['var']['hide_row_numbers'] )): ?>
      <th width="20" style="text-align: right;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
      <?php endif; ?>
      <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
        $this->_foreach['i']['iteration']++;
?>
        <?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']] && ( ! isset ( $this->_tpl_vars['var'][$this->_tpl_vars['action_columns']] ) || is_array ( $this->_tpl_vars['var'][$this->_tpl_vars['action_columns']] ) && in_array ( $this->_tpl_vars['name'] , $this->_tpl_vars['var'][$this->_tpl_vars['action_columns']] ) ) && ( ! isset ( $this->_tpl_vars['var'][$this->_tpl_vars['action_exclude_columns']] ) || is_array ( $this->_tpl_vars['var'][$this->_tpl_vars['action_exclude_columns']] ) && ! in_array ( $this->_tpl_vars['name'] , $this->_tpl_vars['var'][$this->_tpl_vars['action_exclude_columns']] ) )): ?>
          <th<?php if ($this->_tpl_vars['var']['width'][$this->_tpl_vars['key']]): ?> width="<?php echo smarty_function_math(array('equation' => "x-y",'x' => $this->_tpl_vars['var']['width'][$this->_tpl_vars['key']],'y' => 10), $this);?>
"<?php endif; ?>><?php echo $this->_tpl_vars['var']['labels'][$this->_tpl_vars['key']]; ?>
</th>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    </tr>
    <?php $_from = $this->_tpl_vars['var']['values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['val']):
        $this->_foreach['i']['iteration']++;
?>
      <tr>
        <?php if (empty ( $this->_tpl_vars['var']['hide_row_numbers'] )): ?>
        <td style="text-align: right;"><?php echo $this->_foreach['i']['iteration']; ?>
</td>
        <?php endif; ?>
        <?php $_from = $this->_tpl_vars['var']['names']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['name']):
?>
          <?php if (! $this->_tpl_vars['var']['hidden'][$this->_tpl_vars['key']] && ( ! isset ( $this->_tpl_vars['var'][$this->_tpl_vars['action_columns']] ) || is_array ( $this->_tpl_vars['var'][$this->_tpl_vars['action_columns']] ) && in_array ( $this->_tpl_vars['name'] , $this->_tpl_vars['var'][$this->_tpl_vars['action_columns']] ) ) && ( ! isset ( $this->_tpl_vars['var'][$this->_tpl_vars['action_exclude_columns']] ) || is_array ( $this->_tpl_vars['var'][$this->_tpl_vars['action_exclude_columns']] ) && ! in_array ( $this->_tpl_vars['name'] , $this->_tpl_vars['var'][$this->_tpl_vars['action_exclude_columns']] ) )): ?>
            <td style="<?php if ($this->_tpl_vars['var']['text_align'][$this->_tpl_vars['key']]): ?>text-align: <?php echo $this->_tpl_vars['var']['text_align'][$this->_tpl_vars['key']]; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['var']['height'][$this->_tpl_vars['key']]): ?>height: <?php echo $this->_tpl_vars['var']['height'][$this->_tpl_vars['key']]; ?>
px;<?php endif; ?>">
              <?php if ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'text' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'autocompleter' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'textarea'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>

              <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'date'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>

              <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'datetime'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>

              <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'time'): ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['time_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['time_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>

              <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'dropdown' || $this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'radio'): ?>
                <?php if (! empty ( $this->_tpl_vars['var'][$this->_tpl_vars['name']]['overwrite_value'] )): ?>
                  <?php echo ((is_array($_tmp=@$this->_tpl_vars['val'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

                <?php elseif ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']): ?>
                  <?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                    <?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['val'][$this->_tpl_vars['key']]): ?>
                      <?php if (( isset ( $this->_tpl_vars['option']['extended_value'] ) && $this->_tpl_vars['option']['extended_value'] && preg_match ( "#^<#" , $this->_tpl_vars['option']['extended_value'] ) ) || preg_match ( "#^<#" , $this->_tpl_vars['option']['label'] )): ?>
                        <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['option']['extended_value'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['option']['label']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['option']['label'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

                      <?php else: ?>
                        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

                      <?php endif; ?>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                  <?php if (! $this->_tpl_vars['val'][$this->_tpl_vars['key']]): ?>
                    &nbsp;
                  <?php endif; ?>
                <?php elseif ($this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups']): ?>
                  <?php $_from = $this->_tpl_vars['var'][$this->_tpl_vars['name']]['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optgroup_name'] => $this->_tpl_vars['optgroup']):
?>
                    <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                      <?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['val'][$this->_tpl_vars['key']]): ?>
                        <?php if (( isset ( $this->_tpl_vars['option']['extended_value'] ) && $this->_tpl_vars['option']['extended_value'] && preg_match ( "#^<#" , $this->_tpl_vars['option']['extended_value'] ) ) || preg_match ( "#^<#" , $this->_tpl_vars['option']['label'] )): ?>
                          <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['option']['extended_value'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['option']['label']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['option']['label'])))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

                        <?php else: ?>
                          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>

                        <?php endif; ?>
                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                  <?php endforeach; endif; unset($_from); ?>
                <?php endif; ?>
              <?php elseif ($this->_tpl_vars['var']['types'][$this->_tpl_vars['key']] == 'file_upload'): ?>
                <?php echo $this->_tpl_vars['val'][$this->_tpl_vars['key']]; ?>

              <?php endif; ?>
              <?php if (( $this->_tpl_vars['val'][$this->_tpl_vars['key']] || $this->_tpl_vars['val'][$this->_tpl_vars['key']] === '0' ) && $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]): ?><?php echo $this->_tpl_vars['var']['back_labels'][$this->_tpl_vars['key']]; ?>
<?php endif; ?>
            </td>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      </tr>
    <?php endforeach; endif; unset($_from); ?>
  </table>
<?php endif; ?>