<?php /* Smarty version 2.6.33, created on 2023-11-15 17:45:29
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/system_settings_groups.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/system_settings_groups.html', 4, false),array('modifier', 'indent', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/system_settings_groups.html', 23, false),)), $this); ?>
  <tr<?php if (! $this->_tpl_vars['viewable']): ?> style="display: none;"<?php endif; ?>>
    <td class="labelbox">
    <?php if ($this->_tpl_vars['action'] == 'view'): ?>
      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['ownership_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:
    <?php else: ?>
      <a name="error_group"><label for="group"<?php if ($this->_tpl_vars['messages']->getErrors('group')): ?> class="error"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['ownership_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</label></a>
    <?php endif; ?>
    </td>
    <td class="unrequired">&nbsp;</td>
    <td nowrap="nowrap">
    <?php if ($this->_tpl_vars['action'] == 'view'): ?>
      <?php if ($this->_tpl_vars['object']->get('group') == '0'): ?>
        <?php echo ((is_array($_tmp=$this->_config[0]['vars']['undefined'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php else: ?>
        <?php echo $this->_tpl_vars['group']; ?>

      <?php endif; ?>
    <?php else: ?>
        <?php ob_start(); ?><?php if (preg_match ( '#^(ajax_)?add(quick)?$#' , $this->_tpl_vars['action'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_add_action', ob_get_contents());ob_end_clean(); ?>
        <select class="selbox<?php if (! $this->_tpl_vars['object']->get('group') && ! $this->_tpl_vars['is_add_action']): ?> undefined<?php endif; ?>" name="group" id="group" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" onkeypress="dropdownTypingSearch(this, event);">
              <option value="0" class="undefined">[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]</option>
          <?php $_from = $this->_tpl_vars['groups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['group']):
?>
              <?php if (( ! $this->_tpl_vars['group']->isDeleted() && $this->_tpl_vars['group']->isActivated() ) || ( ( $this->_tpl_vars['object']->get('group') == $this->_tpl_vars['group']->get('id') ) || ( ! $this->_tpl_vars['object']->get('group') && $this->_tpl_vars['group']->get('id') == @PH_ROOT_GROUP && $this->_tpl_vars['is_add_action'] ) )): ?>
              <option value="<?php echo $this->_tpl_vars['group']->get('id'); ?>
"<?php if (( $this->_tpl_vars['object']->get('group') == $this->_tpl_vars['group']->get('id') ) || ( ! $this->_tpl_vars['object']->get('group') && $this->_tpl_vars['group']->get('id') == @PH_ROOT_GROUP && $this->_tpl_vars['is_add_action'] )): ?> selected="selected"<?php endif; ?><?php if ($this->_tpl_vars['group']->isDeleted() || ! $this->_tpl_vars['group']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if ($this->_tpl_vars['group']->isDeleted() || ! $this->_tpl_vars['group']->isActivated()): ?>*&nbsp;<?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['group']->get('name'))) ? $this->_run_mod_handler('indent', true, $_tmp, $this->_tpl_vars['group']->get('level'), '-') : smarty_modifier_indent($_tmp, $this->_tpl_vars['group']->get('level'), '-')); ?>
</option>
              <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        </select>
    <?php endif; ?>
    </td>
  </tr>