<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from _tags.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', '_tags.html', 6, false),array('modifier', 'escape', '_tags.html', 12, false),array('function', 'math', '_tags.html', 7, false),array('function', 'counter', '_tags.html', 8, false),array('function', 'mb_truncate_overlib', '_tags.html', 12, false),)), $this); ?>
<?php if ($this->_tpl_vars['mode'] == 'view'): ?>
  <table cellspacing="3" cellpadding="0" border="0" width="100%">
    <tr class="vtop">
      <td class="nopadding">
        <?php $this->assign('tags', $this->_tpl_vars['model']->get('available_tags')); ?>
        <?php ob_start(); ?><?php if (count($this->_tpl_vars['model']->get('tags')) > 24): ?>4<?php elseif (count($this->_tpl_vars['model']->get('tags')) > 12): ?>3<?php elseif (count($this->_tpl_vars['model']->get('tags')) > 6): ?>2<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('c0', ob_get_contents());ob_end_clean(); ?>
        <?php echo smarty_function_math(array('assign' => 'tag_label_length','equation' => 'round(80/x)','x' => $this->_tpl_vars['c0']), $this);?>

        <?php echo smarty_function_counter(array('name' => 't','start' => 0,'print' => false), $this);?>

        <?php $_from = $this->_tpl_vars['tags']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['key'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['key']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tag']):
        $this->_foreach['key']['iteration']++;
?>
          <?php if (is_array ( $this->_tpl_vars['model']->get('tags') ) && in_array ( $this->_tpl_vars['tag']->get('id') , $this->_tpl_vars['model']->get('tags') )): ?>
           <?php echo smarty_function_counter(array('name' => 't','assign' => 'c','print' => false), $this);?>

            <span class="<?php echo $this->_tpl_vars['tag']->get('color'); ?>
_pushpin" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo smarty_function_mb_truncate_overlib(array('length' => $this->_tpl_vars['tag_label_length'],'break_words' => true,'text' => ((is_array($_tmp=$this->_tpl_vars['tag']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
            <?php if (! ( $this->_tpl_vars['c'] % $this->_tpl_vars['c0'] )): ?>
              <?php echo smarty_function_math(array('equation' => "x+y",'x' => $this->_tpl_vars['c'],'y' => $this->_tpl_vars['c0'],'assign' => 'c'), $this);?>

            </td></tr>
            <tr class="vtop"><td class="nopadding">
            <?php else: ?>
          </td><td class="nopadding">
            <?php endif; ?>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      </td>
    </tr>
  </table>
<?php else: ?>
  <table cellspacing="0" cellpadding="0" border="0" width="100%">
    <?php $this->assign('tags_count', $this->_tpl_vars['model']->get('available_tags_count')); ?>
    <?php $this->assign('tags_grouped', $this->_tpl_vars['model']->get('available_tags_grouped')); ?>
    <?php ob_start(); ?><?php if ($this->_tpl_vars['tags_count'] > 24): ?>4<?php elseif ($this->_tpl_vars['tags_count'] > 12): ?>3<?php elseif ($this->_tpl_vars['tags_count'] > 6): ?>2<?php else: ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('c0', ob_get_contents());ob_end_clean(); ?>
    <?php echo smarty_function_math(array('assign' => 'tag_label_length','equation' => 'round(80/x)','x' => $this->_tpl_vars['c0']), $this);?>

    <?php $_from = $this->_tpl_vars['tags_grouped']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['tgi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['tgi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tgk'] => $this->_tpl_vars['tags']):
        $this->_foreach['tgi']['iteration']++;
?>
    <?php if ($this->_tpl_vars['tgk']): ?>
    <tr class="vtop">
      <td colspan="<?php echo $this->_tpl_vars['c0']; ?>
" class="labelbox" style="width: 100%!important; padding: <?php if (($this->_foreach['tgi']['iteration'] <= 1)): ?>0<?php else: ?>13px<?php endif; ?> 5px 2px;">
        <?php echo ((is_array($_tmp=$this->_tpl_vars['tgk'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </td>
    </tr>
    <?php endif; ?>
    <tr class="vtop">
      <td class="nopadding">
        <?php ob_start(); ?><?php echo $this->_tpl_vars['c0']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('c', ob_get_contents());ob_end_clean(); ?>
        <?php $_from = $this->_tpl_vars['tags']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tk'] => $this->_tpl_vars['tag']):
        $this->_foreach['ti']['iteration']++;
?>
          <input type="checkbox" name="tags[]" id="tag_<?php echo $this->_tpl_vars['tk']; ?>
" value="<?php echo $this->_tpl_vars['tk']; ?>
" <?php if ($this->_tpl_vars['tag']->get('section') && $this->_tpl_vars['tag']->get('tag_limit') > 0): ?>class="section_<?php echo $this->_tpl_vars['tag']->get('section'); ?>
" onclick="return checkTagLimit(this, '<?php echo $this->_tpl_vars['tag']->get('tag_limit'); ?>
');" <?php endif; ?><?php if (is_array ( $this->_tpl_vars['model']->get('tags') ) && in_array ( $this->_tpl_vars['tk'] , $this->_tpl_vars['model']->get('tags') )): ?>checked="checked" <?php endif; ?>/>
          <label for="tag_<?php echo $this->_tpl_vars['tk']; ?>
"><span class="<?php echo $this->_tpl_vars['tag']->get('color'); ?>
_pushpin" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo smarty_function_mb_truncate_overlib(array('length' => $this->_tpl_vars['tag_label_length'],'break_words' => true,'text' => $this->_tpl_vars['tag']->get('name')), $this);?>
</span></label>
          <?php if ($this->_foreach['ti']['iteration'] == $this->_tpl_vars['c'] && count($this->_tpl_vars['tags']) - $this->_foreach['ti']['iteration'] > 0): ?>
             <?php echo smarty_function_math(array('equation' => "x+y",'x' => $this->_tpl_vars['c'],'y' => $this->_tpl_vars['c0'],'assign' => 'c'), $this);?>

           </td></tr>
           <tr class="vtop"><td class="nopadding">
          <?php elseif (($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total']) != 1): ?>
          </td><td class="nopadding">
          <?php endif; ?>
          <?php if (($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total']) && $this->_tpl_vars['c0'] > 1): ?>
            <?php echo smarty_function_math(array('equation' => "(x-(y%x))%x",'x' => $this->_tpl_vars['c0'],'y' => count($this->_tpl_vars['tags']),'assign' => 'c1'), $this);?>

            <?php if ($this->_tpl_vars['c1']): ?>
          </td><td class="nopadding" colspan="<?php echo $this->_tpl_vars['c1']; ?>
">&nbsp;
            <?php endif; ?>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      </td>
    </tr>
  <?php endforeach; endif; unset($_from); ?>
  </table>
<?php endif; ?>