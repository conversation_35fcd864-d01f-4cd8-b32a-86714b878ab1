<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/related_records_actions_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/related_records_actions_box.html', 4, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/related_records_actions_box.html', 21, false),)), $this); ?>
<?php if ($this->_tpl_vars['available_actions_related_records']): ?>
  <div style="position: relative;">
    <div class="action_tabs hidden" id="related_records_action_tabs">
      <ul id="related_records_model_actions_<?php echo smarty_function_counter(array('start' => 1,'name' => 'menu_counter','print' => true,'assign' => 'model_action_menu_num'), $this);?>
" class="zpHideOnLoad">
        <?php $_from = $this->_tpl_vars['available_actions_related_records']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
          <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
            </ul>
            <script type="text/javascript">
              new Zapatec.Menu({source: 'related_records_model_actions_<?php echo $this->_tpl_vars['model_action_menu_num']; ?>
',
                                hideDelay: 100,
                                theme: 'nzoom'});
            </script>
            <div class="clear"></div>
            <ul id="related_records_model_actions_<?php echo smarty_function_counter(array('name' => 'menu_counter','print' => true,'assign' => 'model_action_menu_num'), $this);?>
" class="zpHideOnLoad">
          <?php else: ?>
            <?php if ($this->_tpl_vars['available_action']['drop_menu']): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_drop_menu_item.html", 'smarty_include_vars' => array('action_options' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <li class="<?php if ($this->_tpl_vars['available_action']['name'] == $this->_tpl_vars['rel_type']): ?>menu-path<?php endif; ?><?php if (! $this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?> tab_no_label<?php endif; ?>" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
">
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['available_action']['img']): ?><?php echo $this->_tpl_vars['available_action']['img']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['name']; ?>
<?php endif; ?>.png" width="16" height="16" alt="" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" border="0" />
                <a id="a_tab_<?php echo $this->_tpl_vars['available_action']['session_param']; ?>
" <?php if ($this->_tpl_vars['available_action']['options']): ?>href="#"<?php else: ?>href="<?php echo $this->_tpl_vars['available_action']['url']; ?>
"<?php if ($this->_tpl_vars['available_action']['target']): ?> target="<?php echo $this->_tpl_vars['available_action']['target']; ?>
"<?php endif; ?><?php endif; ?> title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"><?php if ($this->_tpl_vars['currentUser']->getPersonalSettings('interface','action_labels')): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['available_action']['label'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20, '...', true) : smarty_modifier_mb_truncate($_tmp, 20, '...', true)); ?>
<?php endif; ?></a>
                                <span style="display: none"<?php if (preg_match ( '/^#related_subpanel/' , $this->_tpl_vars['available_action']['url'] )): ?> onclick="toggleRelatedTabs(this); $('rel_type').value='<?php echo $this->_tpl_vars['available_action']['name']; ?>
';"<?php endif; ?> id="tab_<?php echo $this->_tpl_vars['available_action']['name']; ?>
"></span>
              </li>
            <?php endif; ?>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
      </ul>
      <script type="text/javascript">
          new Zapatec.Menu({source: 'related_records_model_actions_<?php echo $this->_tpl_vars['model_action_menu_num']; ?>
',
                            hideDelay: 100,
                            theme: 'nzoom'});
      </script>
    </div>
    <div class="clear"></div>
  </div>
<?php endif; ?>
<div class="clear"></div>