<?php /* Smarty version 2.6.33, created on 2023-11-15 17:44:53
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 2, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 25, false),array('modifier', 'numerate', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 46, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 47, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 49, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 151, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 42, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 78, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/list.html', 124, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>
<?php if ($this->_tpl_vars['subtitle']): ?><h2><?php echo ((is_array($_tmp=$this->_tpl_vars['subtitle'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2><?php endif; ?>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <form name="documents" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['full_num']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['full_num']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['full_num'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_full_num']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_full_num'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['custom_num']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['custom_num']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['custom_num'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_custom_num']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_custom_num'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['type']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['type']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_type']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_type'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['customer']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['customer']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['customer'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_customer']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_customer'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['department']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['department']['link']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['department'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_department']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_department'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['status']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['status']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['tags']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['tags']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_tags'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['added']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['added']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php $_from = $this->_tpl_vars['documents']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['document']):
        $this->_foreach['i']['iteration']++;
?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo '<strong><u>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['full_num'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_full_num']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_full_num'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</u></strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['document']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['document']->get('direction'))); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, @PH_MAX_TRUNCATE_ABOUT) : smarty_modifier_mb_truncate($_tmp, @PH_MAX_TRUNCATE_ABOUT)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_type']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_type'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['status_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('status_modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('status_modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['document']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['document']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['document']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span><br />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('status') == 'opened'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_opened']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['document']->get('status') == 'locked'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_locked']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['document']->get('status') == 'closed'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_closed']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('substatus_name')): ?><?php echo '<br />'; ?><?php echo $this->_config[0]['vars']['help_documents_substatus']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['document']->get('substatus_name'); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_status', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."row_link_action.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('row_link', ob_get_contents()); ob_end_clean();
 ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['row_link']): ?>pointer<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_class', ob_get_contents());ob_end_clean(); ?>
      <?php if (! $this->_tpl_vars['document']->checkPermissions('list')): ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="<?php echo $this->_tpl_vars['document']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <td colspan="9" class="t_border dimmed"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_right_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'],'disabled' => 'edit,delete,view')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (! $this->_tpl_vars['document']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['document']->get('deleted_by')): ?> t_deleted<?php endif; ?>">
          <td class="t_border">
            <input onclick="sendIds(params = {
                                            the_element: this,
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            session_param: '<?php echo ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])); ?>
',
                                            total: <?php echo $this->_tpl_vars['pagination']['total']; ?>

                                           });"
                   type="checkbox"
                   name='items[]'
                   value="<?php echo $this->_tpl_vars['document']->get('id'); ?>
"
                   title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                   <?php if (@ in_array ( $this->_tpl_vars['document']->get('id') , $this->_tpl_vars['selected_items']['ids'] ) || ( @ $this->_tpl_vars['selected_items']['select_all'] == 1 && @ ! in_array ( $this->_tpl_vars['document']->get('id') , $this->_tpl_vars['selected_items']['ignore_ids'] ) )): ?>
                     checked="checked"
                   <?php endif; ?> />
          </td>
          <td class="t_border hright" nowrap="nowrap">
          <?php if ($this->_tpl_vars['document']->get('files_count')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=attachments&amp;attachments=<?php echo $this->_tpl_vars['document']->get('id'); ?>
">
                <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                     onmouseover="showFiles(this, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', <?php echo $this->_tpl_vars['document']->get('id'); ?>
)"
                     onmouseout="mclosetime()" />
              </a>
          <?php endif; ?>
          <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['full_num']['isSorted']; ?>
"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['document']->get('id'); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['document']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['document']->get('direction'))); ?>
</a></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['custom_num']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('custom_num'))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, @PH_MAX_TRUNCATE_ABOUT) : smarty_modifier_mb_truncate($_tmp, @PH_MAX_TRUNCATE_ABOUT)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['type']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['customer']['isSorted']; ?>
"><a href="<?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['document']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['department']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['status']['isSorted']; ?>
" nowrap="nowrap">
          <?php ob_start(); ?>
            <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['help_documents_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
<?php if ($this->_tpl_vars['document']->checkPermissions('setstatus')): ?> onclick="changeStatus(<?php echo $this->_tpl_vars['document']->get('id'); ?>
, 'documents')" style="cursor:pointer;"<?php endif; ?>
          <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('popup_and_onclick', ob_get_contents());ob_end_clean(); ?>
          <?php ob_start(); ?>
            <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
              <?php echo $this->_config[0]['vars']['documents_expired_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>!
            <?php endif; ?>
            <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
              <?php echo $this->_tpl_vars['documents_expired']; ?>
 <?php echo $this->_config[0]['vars']['documents_expired_validity_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>!
            <?php endif; ?>
          <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired', ob_get_contents());ob_end_clean(); ?>
          <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && ( ( $this->_tpl_vars['document']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) ) || ( $this->_tpl_vars['document']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) ) )): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" alt="" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
 />
          <?php endif; ?>
          <?php if ($this->_tpl_vars['document']->get('substatus_name')): ?>
            <?php if ($this->_tpl_vars['document']->get('icon_name')): ?>
              <img src="<?php echo @PH_DOCUMENTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['document']->get('icon_name'); ?>
" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_<?php echo $this->_tpl_vars['document']->get('status'); ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php endif; ?>
            <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo $this->_tpl_vars['document']->get('substatus_name'); ?>
</span>
          <?php else: ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_<?php echo $this->_tpl_vars['document']->get('status'); ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php ob_start(); ?>documents_status_<?php echo $this->_tpl_vars['document']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_param', ob_get_contents());ob_end_clean(); ?>
            <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_param']]; ?>
</span>
          <?php endif; ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['tags']['isSorted']; ?>
" <?php if ($this->_tpl_vars['document']->getModelTags() && $this->_tpl_vars['document']->get('available_tags_count') > 0 && $this->_tpl_vars['document']->checkPermissions('tags_view') && $this->_tpl_vars['document']->checkPermissions('tags_edit')): ?> onclick="changeTags(<?php echo $this->_tpl_vars['document']->get('id'); ?>
, 'documents')" style="cursor: pointer;" title="<?php echo $this->_config[0]['vars']['tags_change']; ?>
"<?php endif; ?>>
            <?php if (count($this->_tpl_vars['document']->get('model_tags')) > 0 && $this->_tpl_vars['document']->checkPermissions('tags_view')): ?>
              <?php $_from = $this->_tpl_vars['document']->get('model_tags'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tag']):
        $this->_foreach['ti']['iteration']++;
?>
                <span class="<?php echo $this->_tpl_vars['tag']->get('color'); ?>
_pushpin" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span><?php if (! ($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total'])): ?><br /><?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['added']['isSorted']; ?>
 <?php echo $this->_tpl_vars['row_link_class']; ?>
" nowrap="nowrap"<?php echo $this->_tpl_vars['row_link']; ?>
><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td class="hcenter" nowrap="nowrap">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php endif; ?>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="12"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="12"></td>
        </tr>
      </table>
      <br />
      <br />
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."multiple_actions_list.html", 'smarty_include_vars' => array('tags' => $this->_tpl_vars['tags'],'include' => "tags,multistatus,multiprint",'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>