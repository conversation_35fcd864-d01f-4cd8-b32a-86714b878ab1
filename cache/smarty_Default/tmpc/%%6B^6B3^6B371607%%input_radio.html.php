<?php /* Smarty version 2.6.33, created on 2023-11-15 17:44:53
         compiled from input_radio.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_radio.html', 51, false),array('modifier', 'escape', 'input_radio.html', 85, false),array('modifier', 'strip_tags', 'input_radio.html', 86, false),array('function', 'help', 'input_radio.html', 53, false),)), $this); ?>
<?php if ($this->_tpl_vars['index']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?>"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>

            <input 
      type="radio"
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_0<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      value=""
      <?php if ($this->_tpl_vars['custom_class']): ?>class="<?php echo $this->_tpl_vars['custom_class']; ?>
"<?php endif; ?>
      <?php if (empty ( $this->_tpl_vars['value'] ) && $this->_tpl_vars['value'] !== '0' && $this->_tpl_vars['value'] !== 0 && ! $this->_tpl_vars['required']): ?> checked="checked"<?php endif; ?>
      <?php if ($this->_tpl_vars['readonly']): ?> disabled="disabled"<?php endif; ?>
      style="display: none" />

        <?php $_from = $this->_tpl_vars['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['rb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['rb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['rb']['iteration']++;
?>
      <?php if (( ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || $this->_tpl_vars['option']['option_value'] === $this->_tpl_vars['value'] )): ?>
        <?php if (empty ( $this->_tpl_vars['value'] ) && $this->_tpl_vars['value'] !== '0' && $this->_tpl_vars['value'] !== 0 && $this->_tpl_vars['required'] && ( ( $this->_tpl_vars['option']['option_value'] !== '0' && $this->_tpl_vars['option']['option_value'] !== 0 ) || ( int ) $this->_tpl_vars['required'] == 2 )): ?>
          <?php $this->assign('value', $this->_tpl_vars['option']['option_value']); ?>
        <?php endif; ?>
        <input 
          type="radio"
          name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
          id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo $this->_foreach['rb']['iteration']; ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
          value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
          title="<?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['label']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['label'])))) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
          class="<?php if ($this->_tpl_vars['option']['class_name']): ?><?php echo $this->_tpl_vars['option']['class_name']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
          <?php if ($this->_tpl_vars['on_change']): ?>onchange="<?php echo $this->_tpl_vars['on_change']; ?>
"<?php endif; ?>
          <?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['value'] && ! ( empty ( $this->_tpl_vars['value'] ) && $this->_tpl_vars['value'] !== '0' && $this->_tpl_vars['value'] !== 0 )): ?> checked="checked"<?php endif; ?>
          <?php if ($this->_tpl_vars['disabled'] || $this->_tpl_vars['readonly'] || $this->_tpl_vars['option']['disabled']): ?> disabled="disabled"<?php endif; ?> />
          <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo $this->_foreach['rb']['iteration']; ?>
<?php if ($this->_tpl_vars['readonly']): ?>_readonly<?php endif; ?><?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" class="<?php if ($this->_tpl_vars['option']['class_name']): ?><?php echo $this->_tpl_vars['option']['class_name']; ?>
<?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?> inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
<?php endif; ?>"><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?>* <?php endif; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</label><?php if ($this->_tpl_vars['options_align'] != 'horizontal'): ?><br /><?php endif; ?>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
    <?php if ($this->_tpl_vars['readonly']): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo $this->_tpl_vars['value']; ?>
" readonly="readonly" class="radio_hidden readonly<?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>" />
    <?php endif; ?>

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => '','name' => '','index' => '','back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>