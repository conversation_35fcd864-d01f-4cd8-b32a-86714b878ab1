<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_filters.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_filters.html', 7, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_filters.html', 63, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/planned_time_allocation/templates/_filters.html', 7, false),)), $this); ?>
<table style="width: 100%;" cellpadding="1" cellspacing="0" border="0">
  <?php $_from = $this->_tpl_vars['filters']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['var']):
?>
  <tr>
    <td class="labelbox"><label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var']['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var']['name'])); ?>
<?php if ($this->_tpl_vars['var']['type'] == 'autocompleter'): ?>_autocomplete<?php endif; ?>"><?php ob_start(); ?><?php echo $this->_tpl_vars['var']['label']; ?>
<?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('var_label', ob_get_contents());ob_end_clean(); ?><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var_label']), $this);?>
</label></td>
  </tr>
  <tr>
    <td>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['var']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'standalone' => true,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'back_label' => $this->_tpl_vars['var']['back_label'],'back_label_style' => $this->_tpl_vars['var']['back_label_style'],'value' => $this->_tpl_vars['var']['value'],'value_id' => $this->_tpl_vars['var']['value_id'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'option_value' => $this->_tpl_vars['var']['option_value'],'first_option_label' => $this->_tpl_vars['var']['first_option_label'],'onclick' => $this->_tpl_vars['var']['onclick'],'on_change' => $this->_tpl_vars['var']['on_change'],'sequences' => $this->_tpl_vars['var']['sequences'],'check' => $this->_tpl_vars['var']['check'],'scrollable' => $this->_tpl_vars['var']['scrollable'],'calculate' => $this->_tpl_vars['var']['calculate'],'readonly' => $this->_tpl_vars['var']['readonly'],'source' => $this->_tpl_vars['var']['source'],'onchange' => $this->_tpl_vars['var']['onchange'],'map_params' => $this->_tpl_vars['var']['map_params'],'width' => $this->_tpl_vars['var']['width'],'hidden' => $this->_tpl_vars['var']['hidden'],'really_required' => $this->_tpl_vars['var']['required'],'required' => $this->_tpl_vars['var']['required'],'disabled' => $this->_tpl_vars['var']['disabled'],'options_align' => $this->_tpl_vars['var']['options_align'],'autocomplete' => $this->_tpl_vars['var']['autocomplete'],'js_methods' => $this->_tpl_vars['var']['js_methods'],'restrict' => $this->_tpl_vars['var']['js_filter'],'deleteid' => $this->_tpl_vars['var']['deleteid'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'autocomplete_var_type' => $this->_tpl_vars['var']['autocomplete_var_type'],'exclude_oldvalues' => $this->_tpl_vars['var']['exclude_oldvalues'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <tr>
    <td style="padding-bottom: 10px;">
      <div id="<?php echo $this->_tpl_vars['var']['custom_id']; ?>
_container" style="max-height: 150px; overflow: auto;">
      <?php $this->assign('var_name', $this->_tpl_vars['var']['name']); ?>
      <?php if (! empty ( $this->_tpl_vars['filters_values'][$this->_tpl_vars['var_name']] )): ?>
        <?php $_from = $this->_tpl_vars['filters_values'][$this->_tpl_vars['var_name']]; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['val']):
?>
          <div id="<?php echo $this->_tpl_vars['var']['custom_id']; ?>
_<?php echo $this->_tpl_vars['val']['id']; ?>
" style="padding-bottom: 3px;">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete2.png" onclick="plannedTime.removePlannedTimeFilter(this);" title="<?php echo $this->_config[0]['vars']['delete']; ?>
" alt="<?php echo $this->_config[0]['vars']['delete']; ?>
" class="pointer" />
            <input type="hidden" name="<?php echo $this->_tpl_vars['var_name']; ?>
_filters[]" value="<?php echo $this->_tpl_vars['val']['id']; ?>
" />
            <?php echo ((is_array($_tmp=$this->_tpl_vars['val']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </div>
        <?php endforeach; endif; unset($_from); ?>
      <?php endif; ?>
      </div>
    </td>
  </tr>
  <?php endforeach; endif; unset($_from); ?>
  <tr>
    <td style="padding-bottom: 10px;"><button type="button" name="filtersGo" id="pta_filtersGo" class="button" onclick="plannedTime.reloadContent(this);"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_display'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button></td>
  </tr>
</table>