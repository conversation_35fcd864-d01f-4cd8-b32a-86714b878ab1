<?php /* Smarty version 2.6.33, created on 2025-05-21 16:06:04
         compiled from _communication_emails_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '_communication_emails_panel.html', 12, false),array('modifier', 'escape', '_communication_emails_panel.html', 15, false),array('function', 'help', '_communication_emails_panel.html', 56, false),)), $this); ?>
<?php if ($_REQUEST['inline_add']): ?>
  <?php $this->assign('inline_add', 1); ?>
<?php endif; ?>
<form name="emails_add" id="emails_add" action="" method="post" onsubmit="saveCommunicationRecord(this, 'email', '<?php echo $this->_tpl_vars['model_id']; ?>
', '<?php echo $this->_tpl_vars['communication_type']; ?>
'); return false;">
  <?php if ($this->_tpl_vars['inline_add']): ?>
  <script type="text/javascript" defer="defer" src="<?php echo @PH_CKEDITOR_URL; ?>
ckeditor.js"></script>
  <script type="text/javascript" defer="defer" src="<?php echo @PH_MODULES_URL; ?>
communications/javascript/communications.js?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
  <?php endif; ?>
  <input type="hidden" name="model_id" id="model_id" value="<?php echo $this->_tpl_vars['current_model']->get('id'); ?>
" />
  <input type="hidden" name="model" id="model" value="<?php echo $this->_tpl_vars['current_model']->modelName; ?>
" />
  <input type="hidden" name="type" id="type" value="<?php echo $this->_tpl_vars['current_model']->get('type'); ?>
" />
  <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['current_model']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
  <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table t_table1 t_borderless">
    <tr>
      <td class="t_caption" nowrap="nowrap" colspan="5"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_add_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    </tr>
    <?php if ($this->_tpl_vars['emails_templates']): ?>
    <tr>
      <td class="labelbox"><label for="email_template"><?php echo $this->_config[0]['vars']['communications_mail_template']; ?>
</label></td>
      <td class="unrequired">&nbsp;</td>
      <td nowrap="nowrap" colspan="3">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'email_template','standalone' => true,'width' => 500,'options' => $this->_tpl_vars['emails_templates'],'onchange' => "changeEmailBody(this)",'label' => $this->_config[0]['vars']['communications_mail_template'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['customer_toggle_financial_persons']): ?>
    <tr>
      <td class="labelbox">&nbsp;</td>
      <td class="unrequired">&nbsp;</td>
      <td colspan="3" style="color: #888888; font-size: 11px;">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_checkbox.html", 'smarty_include_vars' => array('name' => 'toggle_financial_persons','standalone' => true,'option_value' => $this->_tpl_vars['customer_toggle_financial_persons'],'onclick' => "toggleFinancialPersonRecipients(this);",'no_br' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <label for="toggle_financial_persons"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_toggle_financial_persons'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
      </td>
    </tr>
    <?php endif; ?>
    <tr>
      <td class="labelbox"><a name="error_mail_to"><?php echo $this->_config[0]['vars']['communications_mail_to']; ?>
</a></td>
      <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
      <td colspan="3">
        <table cellspacing="0" cellpadding="0" border="0" class="t_table1 t_table_border" id="table_to" style="width: 500px!important;">
          <tr>
            <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
            <td class="t_caption3 t_border" nowrap="nowrap" style="width: 94%;"><div class="t_caption3_title floatl"><?php echo $this->_config[0]['vars']['communications_mail_to']; ?>
</div>
              <div class="t_buttons">
                <div id="table_to_plusButton" onclick="addField('table_to', false, false, true)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                <div id="table_to_minusButton" class="disabled" onclick="removeField('table_to')" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
              </div>
            </td>
          </tr>
          <?php $_from = $this->_tpl_vars['recipients']['to']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['r'] => $this->_tpl_vars['recipient_to']):
?>
            <?php $this->assign('r', $this->_tpl_vars['r']+1); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_communication_recipients_row.html", 'smarty_include_vars' => array('idx' => $this->_tpl_vars['r'],'recipient_type' => 'to','recipient' => $this->_tpl_vars['recipient_to'],'ac' => $this->_tpl_vars['autocomplete_email'],'dis' => $this->_tpl_vars['autocomplete_email_to']['disabled'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endforeach; else: ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_communication_recipients_row.html", 'smarty_include_vars' => array('idx' => 1,'recipient_type' => 'to','recipient' => '','ac' => $this->_tpl_vars['autocomplete_email'],'dis' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; unset($_from); ?>
        </table>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><a name="error_mail_cc">&nbsp;</a></td>
      <td class="unrequired">&nbsp;</td>
      <td style="color:#888888; font-size:11px;" colspan="3">
        <a onclick="toggleMailScreenTables(this)" id="mail_add_cc" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_add_cc'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer<?php if (! empty ( $this->_tpl_vars['recipients']['cc'] )): ?> communication_tab_selected<?php endif; ?>"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_add_cc'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a> |
        <a onclick="toggleMailScreenTables(this)" id="mail_add_bcc" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_add_bcc'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer<?php if (! empty ( $this->_tpl_vars['recipients']['bcc'] )): ?> communication_tab_selected<?php endif; ?>"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_add_bcc'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
      </td>
    </tr>
    <tr id="row_mail_cc"<?php if (empty ( $this->_tpl_vars['recipients']['cc'] )): ?> style="display: none;"<?php endif; ?>>
      <td class="labelbox">&nbsp;</td>
      <td class="unrequired">&nbsp;</td>
      <td colspan="3">
        <table cellspacing="0" cellpadding="0" border="0" class="t_table1 t_table_border" id="table_cc" style="width: 500px!important;">
          <tr>
            <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
            <td class="t_caption3 t_border" nowrap="nowrap" style="width: 94%;">
              <div class="t_caption3_title floatl"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_cc'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
              <div class="t_buttons">
                <div id="table_cc_plusButton" onclick="addField('table_cc', false, false, true)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                <div id="table_cc_minusButton" class="disabled" onclick="removeField('table_cc')" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
              </div>
            </td>
          </tr>
          <?php $_from = $this->_tpl_vars['recipients']['cc']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['r'] => $this->_tpl_vars['recipient_cc']):
?>
            <?php $this->assign('r', $this->_tpl_vars['r']+1); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_communication_recipients_row.html", 'smarty_include_vars' => array('idx' => $this->_tpl_vars['r'],'recipient_type' => 'cc','recipient' => $this->_tpl_vars['recipient_cc'],'ac' => $this->_tpl_vars['autocomplete_email_cc'],'dis' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endforeach; else: ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_communication_recipients_row.html", 'smarty_include_vars' => array('idx' => 1,'recipient_type' => 'cc','recipient' => '','ac' => $this->_tpl_vars['autocomplete_email_cc'],'dis' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; unset($_from); ?>
        </table>
      </td>
    </tr>
    <tr id="row_mail_bcc"<?php if (empty ( $this->_tpl_vars['recipients']['bcc'] )): ?> style="display: none;"<?php endif; ?>>
      <td class="labelbox"><a name="error_mail_bcc">&nbsp;</a></td>
      <td class="unrequired">&nbsp;</td>
      <td colspan="3">
        <table cellspacing="0" cellpadding="0" border="0" class="t_table1 t_table_border" id="table_bcc" style="width: 500px!important;">
          <tr>
            <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
            <td class="t_caption3 t_border" nowrap="nowrap" style="width: 94%;">
              <div class="t_caption3_title floatl"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_bcc'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
              <div class="t_buttons">
                <div id="table_bcc_plusButton" onclick="addField('table_bcc', false, false, true)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                <div id="table_bcc_minusButton" class="disabled" onclick="removeField('table_bcc')" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
              </div>
            </td>
          </tr>
          <?php $_from = $this->_tpl_vars['recipients']['bcc']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['r'] => $this->_tpl_vars['recipient_bcc']):
?>
            <?php $this->assign('r', $this->_tpl_vars['r']+1); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_communication_recipients_row.html", 'smarty_include_vars' => array('idx' => $this->_tpl_vars['r'],'recipient_type' => 'bcc','recipient' => $this->_tpl_vars['recipient_bcc'],'ac' => $this->_tpl_vars['autocomplete_email_bcc'],'dis' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endforeach; else: ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_communication_recipients_row.html", 'smarty_include_vars' => array('idx' => 1,'recipient_type' => 'bcc','recipient' => '','ac' => $this->_tpl_vars['autocomplete_email_bcc'],'dis' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; unset($_from); ?>
        </table>
      </td>
    </tr>
    <tr>
      <td class="labelbox"><label for="email_subject"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label></td>
      <td class="unrequired">&nbsp;</td>
      <td nowrap="nowrap" colspan="3">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => 'email_subject','standalone' => true,'width' => 500,'value' => ((is_array($_tmp=@$this->_tpl_vars['subject'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')),'label' => $this->_config[0]['vars']['communications_mail_subject'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
    <?php if ($this->_tpl_vars['files_options']): ?>
    <tr>
      <td class="labelbox">&nbsp;</td>
      <td class="unrequired">&nbsp;</td>
      <td style="color:#888888; font-size:11px;" colspan="3">
        <a onclick="toggleMailScreenTables(this)" id="mail_add_attachments" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_attach_file'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_mail_attach_file'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
      </td>
    </tr>
    <tr id="row_mail_attachments" style="display: none;">
      <td class="labelbox">&nbsp;</td>
      <td class="unrequired">&nbsp;</td>
      <td colspan="3">
        <table cellspacing="0" cellpadding="0" border="0" class="t_table1 t_table_border" id="table_attachments" style="width: 500px!important;">
          <tr>
            <td class="t_caption3 t_border" nowrap="nowrap"><div class="t_caption3_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
            <td class="t_caption3 t_border t_buttons_container" nowrap="nowrap" style="width: 94%;">
              <div class="t_caption3_title floatl"><?php echo $this->_config[0]['vars']['attached_files']; ?>
</div>
              <div class="t_buttons">
                <div id="table_attached_plusButton" onclick="addField('table_attachments', false, false, true)" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
                <div id="table_attached_minusButton" class="disabled" onclick="removeField('table_attachments')" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
              </div>
            </td>
          </tr>
          <tr id="table_attachments_1" class="input_inactive">
            <td class="t_border t_v_border t_border_left" nowrap="nowrap"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row" style="visibility: hidden;" onclick="confirmAction('delete_row', function() { hideField('table_attachments','1'); }, this);" />&nbsp;<a href="javascript: disableField('table_attachments','1')">1</a></td>
            <td class="t_border t_v_border" nowrap="nowrap">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'attached_files','index' => 1,'disabled' => true,'standalone' => true,'optgroups' => $this->_tpl_vars['files_options'],'label' => $this->_config[0]['vars']['file'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <?php endif; ?>
    <tr>
      <td class="labelbox">&nbsp;</td>
      <td class="unrequired">&nbsp;</td>
      <td style="font-size: 11px; width: 150px; color: #666666;"><input type="checkbox" onblur="unhighlight(this)" onfocus="highlight(this)" title="" value="1"<?php if ($this->_tpl_vars['attach_signature']): ?> checked="checked"<?php endif; ?> id="add_signature" name="add_signature" /> <label for="add_signature"><?php echo $this->_config[0]['vars']['communications_mail_signature']; ?>
</label></td>
      <td style="font-size: 11px; width: 150px; color: #666666;"><input type="checkbox" onblur="unhighlight(this)" onfocus="highlight(this)" title="" value="1" id="send_copy" name="send_copy" /> <label for="send_copy"><?php echo $this->_config[0]['vars']['communications_mail_copy']; ?>
</label></td>
      <td style="font-size: 11px; color: #666666;"><input type="checkbox" onblur="unhighlight(this)" onfocus="highlight(this)" title="" onclick="toggleMailScreenTables(this)" value="1" id="mail_add_read_receipt" name="read_receipt" /> <a name="error_read_receipt_email"></a><label for="mail_add_read_receipt"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_read_receipt'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label></td>
    </tr>
    <tr id="row_mail_read_receipt" style="display: none;">
      <td colspan="4">&nbsp;</td>
      <td style="padding-top: 0;">
        <table cellspacing="0" cellpadding="0" border="0" width="100%" id="table_read_receipt">
          <tr>
            <td style="padding-top: 0;">
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => 'read_receipt_email','label' => $this->_config[0]['vars']['communications_read_receipt_help'],'value' => $this->_tpl_vars['default_receipt_email'],'standalone' => true,'width' => 175)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td colspan="5" id="mail_content">
        <?php echo $this->_tpl_vars['editor_content']; ?>

      </td>
    </tr>
    <tr>
      <td colspan="5">
        <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['send'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><button type="button" name="cancel" class="button" onclick="confirmAction('cancel', function() { <?php if ($this->_tpl_vars['inline_add']): ?>lb.deactivate()<?php else: ?>manageCommunicationAddPanels('email', 'add', '<?php echo $this->_tpl_vars['current_model']->get('id'); ?>
')<?php endif; ?>; }, this)"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
      </td>
    </tr>
  </table>
</form>