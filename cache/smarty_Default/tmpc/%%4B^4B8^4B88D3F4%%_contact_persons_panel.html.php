<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:57
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html', 38, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html', 82, false),array('modifier', 'replace', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html', 100, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html', 122, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html', 122, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html', 48, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/_contact_persons_panel.html', 50, false),)), $this); ?>
<?php if ($this->_tpl_vars['messages']->getWarnings()): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'message.html', 'smarty_include_vars' => array('display' => 'warning','items' => $this->_tpl_vars['messages']->getWarnings())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php if ($this->_tpl_vars['messages']->getMessages()): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'message.html', 'smarty_include_vars' => array('display' => 'message','items' => $this->_tpl_vars['messages']->getMessages())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=contactpersons&amp;contactpersons=list&amp;parent_customer_id=<?php echo $this->_tpl_vars['customer']->get('id'); ?>
&amp;model_lang=<?php echo $this->_tpl_vars['customer']->get('model_lang'); ?>
&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<table cellpadding="0" cellspacing="0" border="0" width="100%">
  <tr>
    <td valign="top" style="padding-left: 0; margin-left: 0; border-collapse: collapse;">
      <table border="0" cellpadding="3" cellspacing="0" width="100%">
    <?php if ($this->_tpl_vars['pagination']['pages'] > 1): ?>
        <tr>
          <td class="pagemenu">
            <?php $this->assign('sort', $this->_tpl_vars['customers_contact_persons_sort']); ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'pagination' => $this->_tpl_vars['pagination'],'sort' => $this->_tpl_vars['customers_contact_persons_sort'],'session_param' => $this->_tpl_vars['customers_contact_persons_session_param'],'use_ajax' => $this->_tpl_vars['customers_contact_persons_use_ajax'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
    <?php endif; ?>
        <tr>
          <td id="customers_contact_persons_container" style="padding: 5px 0;">
          <form name="customers_contactpersons_form" action="" method="post" enctype="multipart/form-data">
            <input type="hidden" name="parent_customer_id" value="<?php echo $this->_tpl_vars['customer']->get('id'); ?>
" />
            <table border="0" cellpadding="0" cellspacing="0" class="t_table<?php if ($this->_tpl_vars['model_name'] != 'Project'): ?> t_table_border<?php endif; ?>" style="width: 100%;">
              <tr>
                <td class="t_caption t_border t_checkall">
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => $this->_tpl_vars['customers_contact_persons_session_param'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                </td>
                <td class="t_caption t_border" nowrap="nowrap" style="width: 20px"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['customers_contact_persons_sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['position']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['customers_contact_persons_sort']['position']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_position'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['branch']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['customers_contact_persons_sort']['branch']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->getBranchLabels('customers_contact_persons_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['assigned']['class']; ?>
" nowrap="nowrap"><div class ="t_caption_title" onclick="<?php echo $this->_tpl_vars['customers_contact_persons_sort']['assigned']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_assigned'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['contacts']['class']; ?>
" nowrap="nowrap"><div class ="t_caption_title" onclick="<?php echo $this->_tpl_vars['customers_contact_persons_sort']['contacts']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_contacts'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['notes']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['customers_contact_persons_sort']['notes']['link']; ?>
" style="width: 200px"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_notes'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['permission']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['customers_contact_persons_sort']['permission']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_permission'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
                <td class="t_caption" nowrap="nowrap">&nbsp;</td>
              </tr>
              <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

              <?php $_from = $this->_tpl_vars['customers_contact_persons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_person']):
        $this->_foreach['i']['iteration']++;
?>
              <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 vtop<?php if (! $this->_tpl_vars['contact_person']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['contact_person']->get('deleted_by')): ?> t_deleted<?php endif; ?>">
                <td class="t_border">
                  <input <?php if ($this->_tpl_vars['contact_person']->get('is_main')): ?> class="disabled" onclick="this.checked=false; alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_combined_actions_contactpersons_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')"
                           <?php else: ?>
                            onclick="sendIds(params = {
                                            the_element: this,
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            session_param: '<?php echo $this->_tpl_vars['customers_contact_persons_session_param']; ?>
',
                                            total: <?php echo $this->_tpl_vars['pagination']['total']; ?>

                                           });"
                            <?php if (@ in_array ( $this->_tpl_vars['contact_person']->get('id') , $this->_tpl_vars['selected_items']['ids'] ) || ( @ $this->_tpl_vars['selected_items']['select_all'] == 1 && @ ! in_array ( $this->_tpl_vars['contact_person']->get('id') , $this->_tpl_vars['selected_items']['ignore_ids'] ) )): ?>
                     checked="checked"
                            <?php endif; ?>
                         <?php endif; ?>
                       type="checkbox"
                       name='items[]'
                       value="<?php echo $this->_tpl_vars['contact_person']->get('id'); ?>
"
                       title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                    />
                </td>
                <td class="t_border hright"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
                <td class="t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['name']['isSorted']; ?>
" nowrap="nowrap">
                  <?php ob_start(); ?>salutation_<?php echo $this->_tpl_vars['contact_person']->get('salutation'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('salutation', ob_get_contents());ob_end_clean(); ?>
                  <?php $this->assign('salutation', ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['salutation']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))); ?>
                  <?php ob_start(); ?>
                    <?php if ($this->_tpl_vars['salutation']): ?><?php echo $this->_tpl_vars['salutation']; ?>
 <?php endif; ?><?php echo $this->_tpl_vars['contact_person']->get('name'); ?>
 <?php echo $this->_tpl_vars['contact_person']->get('lastname'); ?>

                  <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('full_name', ob_get_contents());ob_end_clean(); ?>
                  <?php if ($this->_tpl_vars['contact_person']->get('is_main')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_main_contact'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_main_contact'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /><?php endif; ?>
                  <?php if ($this->_tpl_vars['contact_person']->get('admit_VAT_credit')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
finance_fixing.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_financial_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_financial_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /><?php endif; ?>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['full_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                </td>
                <td class="t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['position']['isSorted']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contact_person']->get('position'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
                <td class="t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['branch']['isSorted']; ?>
" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contact_person']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
                <td class="t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['assigned']['isSorted']; ?>
" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contact_person']->get('assigned_to_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
                <td class="t_border" nowrap="nowrap">
                  <?php if (( ! empty ( $this->_tpl_vars['customer']->contactParameters ) )): ?>
                    <?php $_from = $this->_tpl_vars['customer']->contactParameters; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['z'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['z']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_param']):
        $this->_foreach['z']['iteration']++;
?>
                      <?php if ($this->_tpl_vars['contact_person']->get($this->_tpl_vars['contact_param'])): ?>
                        <?php ob_start(); ?>customers_<?php echo $this->_tpl_vars['contact_param']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_label', ob_get_contents());ob_end_clean(); ?>
                        <?php ob_start(); ?><?php echo $this->_tpl_vars['contact_param']; ?>
_note<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contact_param_note', ob_get_contents());ob_end_clean(); ?>
                        <?php $this->assign('contact_notes', $this->_tpl_vars['contact_person']->get($this->_tpl_vars['contact_param_note'])); ?>
                        <?php $_from = $this->_tpl_vars['contact_person']->get($this->_tpl_vars['contact_param']); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['n'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['n']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contact_number'] => $this->_tpl_vars['contact']):
        $this->_foreach['n']['iteration']++;
?>
                          <?php if ($this->_tpl_vars['use_asterisk'] && ( $this->_tpl_vars['contact_param'] == 'fax' || $this->_tpl_vars['contact_param'] == 'phone' || $this->_tpl_vars['contact_param'] == 'gsm' )): ?>
                            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_asterisk_contact.html", 'smarty_include_vars' => array('contact_type' => $this->_tpl_vars['contact_param'],'number' => $this->_tpl_vars['contact'],'label' => $this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                          <?php else: ?>
                            <div style="clear: both; padding-bottom: 2px;">
                              <?php if ($this->_tpl_vars['contact_param'] == 'web'): ?>
                                <a href="http://<?php echo ((is_array($_tmp=$this->_tpl_vars['contact'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'http://', '') : smarty_modifier_replace($_tmp, 'http://', '')); ?>
" target="_blank">
                              <?php elseif ($this->_tpl_vars['contact_param'] == 'email'): ?>
                                <a href="mailto:<?php echo $this->_tpl_vars['contact']; ?>
" target="_self">
                              <?php else: ?>
                                <label for="lk<?php echo $this->_tpl_vars['cnt']; ?>
">
                              <?php endif; ?>
                                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['contact_param']; ?>
.png" alt="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']]; ?>
" title="<?php echo $this->_config[0]['vars'][$this->_tpl_vars['contact_param_label']]; ?>
" border="0" />&nbsp;<?php echo $this->_tpl_vars['contact']; ?>

                              <?php if ($this->_tpl_vars['contact_param'] == 'web' || $this->_tpl_vars['contact_param'] == 'email'): ?>
                                </a>
                              <?php else: ?>
                                </label>
                              <?php endif; ?>
                              <?php if ($this->_tpl_vars['contact_notes'][$this->_tpl_vars['contact_number']]): ?> (<?php echo ((is_array($_tmp=$this->_tpl_vars['contact_notes'][$this->_tpl_vars['contact_number']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)<?php endif; ?>
                            </div>
                          <?php endif; ?>
                        <?php endforeach; endif; unset($_from); ?>
                      <?php endif; ?>
                    <?php endforeach; endif; unset($_from); ?>
                  <?php else: ?>
                    &nbsp;
                  <?php endif; ?>
                </td>
                <td class="t_border <?php echo $this->_tpl_vars['customers_contact_persons_sort']['notes']['isSorted']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contact_person']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
                <td class="t_border" <?php echo $this->_tpl_vars['customers_contact_persons_sort']['permission']['isSorted']; ?>
>
                  <?php ob_start(); ?>customers_contact_persons_permission_<?php echo $this->_tpl_vars['contact_person']->get('permission'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('permission', ob_get_contents());ob_end_clean(); ?>
                  <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['permission']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                </td>
                <td class="hcenter" nowrap="nowrap">
                  <?php ob_start(); ?><?php if (in_array ( $this->_tpl_vars['customer']->get('lang') , $this->_tpl_vars['contact_person']->get('translations') )): ?>edit<?php else: ?>translate<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('edit_act', ob_get_contents());ob_end_clean(); ?>
                  <?php if ($this->_tpl_vars['contact_person']->checkPermissions($this->_tpl_vars['edit_act'],'customers_contactpersons')): ?>
                    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/edit.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="page_menu_link" onclick="changeContactPerson(this, 'contact_persons_custom_panel', '<?php echo $this->_tpl_vars['edit_act']; ?>
', '<?php echo $this->_tpl_vars['customer']->get('model_lang'); ?>
', '<?php echo $this->_tpl_vars['customer']->get('id'); ?>
', '<?php echo $this->_tpl_vars['contact_person']->get('id'); ?>
'); return false;" />
                  <?php else: ?>
                    <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/edit.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="dimmed pointer" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" />
                  <?php endif; ?>
                </td>
              </tr>
              <?php endforeach; else: ?>
              <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
                <td class="error" colspan="10"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              </tr>
              <?php endif; unset($_from); ?>
              <tr>
                <td class="t_footer" colspan="10"></td>
              </tr>
            </table>
            <br />
            <br />
            <?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','activate')): ?><?php echo '<button type="submit" name="activateButton" class="button" onclick="return confirmationContactPersonsActions(this, \''; ?><?php echo $this->_tpl_vars['customers_contact_persons_session_param']; ?><?php echo '\', \'activate\', 1)">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['activate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','deactivate')): ?><?php echo '<button type="submit" name="deactivateButton" class="button" onclick="return confirmationContactPersonsActions(this, \''; ?><?php echo $this->_tpl_vars['customers_contact_persons_session_param']; ?><?php echo '\', \'deactivate\', 1)">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','delete')): ?><?php echo '<button type="submit" name="deleteButton" class="button" onclick="return confirmationContactPersonsActions(this, \''; ?><?php echo $this->_tpl_vars['customers_contact_persons_session_param']; ?><?php echo '\', \'delete\', 1)">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?>

            <input type="hidden" name="session_param" value="<?php echo $this->_tpl_vars['customers_contact_persons_session_param']; ?>
" />
          </form>
          </td>
        </tr>
        <tr>
          <td align="left" style="padding-left: 0;">
            <?php if ($this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add')): ?>
              <button class="button" name="addContactPerson" id="addContactPerson" onclick="changeContactPerson(this, 'contact_persons_custom_panel', 'add', '<?php echo $this->_tpl_vars['customer']->get('model_lang'); ?>
', '<?php echo $this->_tpl_vars['customer']->get('id'); ?>
'); return false;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
        <tr>
          <td class="pagemenu">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'pagination' => $this->_tpl_vars['pagination'],'sort' => $this->_tpl_vars['customers_contact_persons_sort'],'session_param' => $this->_tpl_vars['customers_contact_persons_session_param'],'use_ajax' => $this->_tpl_vars['customers_contact_persons_use_ajax'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>

<div id="contact_persons_custom_panel">
<?php if ($this->_tpl_vars['messages']->getErrors()): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'message.html', 'smarty_include_vars' => array('display' => 'error','items' => $this->_tpl_vars['messages']->getErrors())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php if ($this->_tpl_vars['failed_contact_person'] && $this->_tpl_vars['failed_action']): ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_contact_persons_".($this->_tpl_vars['failed_action']).".html", 'smarty_include_vars' => array('contact_person' => $this->_tpl_vars['failed_contact_person'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>
</div>