<?php /* Smarty version 2.6.33, created on 2025-05-21 16:13:53
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 5, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 52, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 54, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 9, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 43, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 125, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 230, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info_header.html', 230, false),)), $this); ?>
  <?php $_from = $this->_tpl_vars['contract']->getLayoutsDetails(); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>
    <?php if ($this->_tpl_vars['layout']['info_header_visibility'] && $this->_tpl_vars['layout']['view']): ?>
      <?php if ($this->_tpl_vars['lkey'] == 'status'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <div class="contracts_status <?php echo $this->_tpl_vars['contract']->get('status'); ?>
">
              <?php if ($this->_tpl_vars['contract']->get('status') == 'opened'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_opened'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

              <?php elseif ($this->_tpl_vars['contract']->get('status') == 'locked'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_locked'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

              <?php elseif ($this->_tpl_vars['contract']->get('status') == 'closed'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_closed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

              <?php endif; ?>
              <?php if ($this->_tpl_vars['contract']->get('substatus_name')): ?>
                &raquo; <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('substatus_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

              <?php endif; ?>
              <?php if ($this->_tpl_vars['contract']->checkPermissions('setstatus')): ?>
              <a href="#" onclick="toggleActionOptions($('setstatus_action')); return false;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
              <?php endif; ?>
            </div>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'num'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['contract']->get('num')): ?><?php if ($this->_tpl_vars['contract']->get('num') == 'system'): ?><?php echo $this->_config[0]['vars']['contracts_system_num']; ?>
<?php else: ?><?php echo $this->_tpl_vars['contract']->get('num'); ?>
<?php endif; ?><?php else: ?><i><?php echo $this->_config[0]['vars']['contracts_unfinished_contract']; ?>
</i><?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'custom_num'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('custom_num'))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
            <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
>
              <img src="<?php if ($this->_tpl_vars['contract']->get('icon_name')): ?><?php echo @PH_CONTRACTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['contract']->get('icon_name'); ?>
<?php else: ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
contracts_<?php echo $this->_tpl_vars['contract']->get('status'); ?>
.png<?php endif; ?>" class="t_info_image" alt="" title="" />
              <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

            </span>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php if ($this->_tpl_vars['contract']->get('branch')): ?>
              <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('contracts_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
            <?php if ($this->_tpl_vars['contract']->get('contact_person')): ?>
              <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['contracts_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['contract']->get('trademark')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures&amp;nomenclatures=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('trademark'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['contract']->get('project')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=projects&amp;projects=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('project'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'company'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'office'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('office_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'employee'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('employee_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_sign' && $this->_tpl_vars['contract']->get('include_date_sign')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_sign'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

            <?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('date_sign_formula')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_sign_formula')): ?><?php echo '('; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ')'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_start' && $this->_tpl_vars['contract']->get('include_date_start')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_start'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

            <?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('date_start_formula')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_start_formula')): ?><?php echo '('; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ')'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_validity' && $this->_tpl_vars['contract']->get('include_date_validity')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_validity'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

            <?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('date_validity_formula')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_validity_formula')): ?><?php echo '('; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ')'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_end' && $this->_tpl_vars['contract']->get('include_date_end')): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_end'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

            <?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('date_end_formula')): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_end_formula')): ?><?php echo '('; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ')'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_sign_subtype' && $this->_tpl_vars['contract']->get('subtype') == 'annex'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_sign_subtype'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_start_subtype' && $this->_tpl_vars['contract']->get('subtype') == 'annex'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_start_subtype'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date_end_subtype' && $this->_tpl_vars['contract']->get('subtype') == 'annex'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_end_subtype'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'referers'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
                        <?php if ($this->_tpl_vars['contract']->getParents() && $this->_tpl_vars['contract']->get('referers')): ?>
              <?php $_from = $this->_tpl_vars['contract']->get('referers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
                <?php echo $this->_foreach['i']['iteration']; ?>
. <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
" target="_blank"><?php if ($this->_tpl_vars['ref']['num']): ?>[<?php echo $this->_tpl_vars['ref']['num']; ?>
]<?php else: ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?>&nbsp;<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a><br />
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php endif; ?>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>