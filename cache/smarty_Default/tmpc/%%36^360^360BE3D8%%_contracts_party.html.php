<?php /* Smarty version 2.6.33, created on 2025-05-21 16:14:32
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_party.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_party.html', 3, false),array('function', 'json', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_party.html', 70, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_party.html', 6, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_party.html', 16, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_party.html', 16, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_party.html', 126, false),)), $this); ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="labelbox"><?php echo smarty_function_help(array('label' => $this->_tpl_vars['party']['type']), $this);?>
</td>
    <td>&nbsp;</td>
    <td colspan="2" class="strong">
        <?php echo ((is_array($_tmp=$this->_tpl_vars['party']['cname'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm'): ?>
        <input type="hidden" id="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_is_company" value="<?php echo $this->_tpl_vars['party']['is_company']; ?>
" />
        <input type="hidden" id='can_add' value="<?php echo $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add'); ?>
" />
        <?php endif; ?>
    </td>
  </tr>
  <tr>
    <td class="labelbox"><?php echo smarty_function_help(array('label' => 'party_address'), $this);?>
</td>
    <td>&nbsp;</td>
    <td colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['party']['address'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
  </tr>
  <tr>
    <td class="labelbox strong"><?php echo smarty_function_help(array('label' => 'party_contacts'), $this);?>
</td>
    <td>&nbsp;</td>
    <td colspan="2">&nbsp;</td>
  </tr>
  <tr>
    <td class="labelbox">
      <?php echo smarty_function_help(array('label' => 'party_administrative'), $this);?>

      <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm'): ?>
      <?php ob_start(); ?>error_<?php echo $this->_tpl_vars['party']['prefix']; ?>
_administrative<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('error_administrative', ob_get_contents());ob_end_clean(); ?>
      <a name="<?php echo $this->_tpl_vars['error_administrative']; ?>
"></a>
      <?php endif; ?>
      <?php ob_start(); ?>error_<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('error_adm_email', ob_get_contents());ob_end_clean(); ?>
      <a name="<?php echo $this->_tpl_vars['error_adm_email']; ?>
"></a>
    </td>
    <td>&nbsp;</td>
    <td colspan="2" style="white-space: nowrap;">
    <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && empty ( $this->_tpl_vars['party']['administrative']['options'] )): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_administrative",'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_adm_email",'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_email'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <input type="hidden" name="cstm_administrative_isCustom" id="cstm_administrative_isCustom" value="1" />
      <input type="hidden" name="cstm_adm_email_isCustom" id="cstm_adm_email_isCustom" value="1" />
    <?php else: ?>
      <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['party']['is_company'] && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add')): ?>
        <?php $this->assign('field_type', 'combobox'); ?>
      <?php else: ?>
        <?php $this->assign('field_type', 'dropdown'); ?>
      <?php endif; ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_".($this->_tpl_vars['field_type']).".html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_administrative",'options' => $this->_tpl_vars['party']['administrative']['options'],'optgroups' => $this->_tpl_vars['party']['administrative']['optgroups'],'value' => $this->_tpl_vars['party']['administrative']['selected'],'onchange' => 'changeContactEmails(this)','standalone' => true,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <script type="text/javascript">
        <?php echo $this->_tpl_vars['party']['prefix']; ?>
_administrative_options = <?php echo smarty_function_json(array('encode' => $this->_tpl_vars['party']['administrative']['options']), $this);?>
;
      </script>
      <select name="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email" id="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email" class="selbox<?php if (! $this->_tpl_vars['party']['administrative']['selected'] || ! $this->_tpl_vars['party']['administrative']['email_selected']): ?> undefined<?php endif; ?>" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_party_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
        <?php if ($this->_tpl_vars['party']['administrative']['selected'] || $this->_tpl_vars['party']['administrative']['email_selected']): ?>
        <option value="" class="undefined"><?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['party']['is_company'] && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['party']['administrative']['isCustom']): ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['select_or_input'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
        <?php endif; ?>
      <?php if ($this->_tpl_vars['party']['administrative']['selected']): ?>
        <?php $_from = $this->_tpl_vars['party']['administrative']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact']):
?>
          <?php if ($this->_tpl_vars['contact']['option_value'] == $this->_tpl_vars['party']['administrative']['selected']): ?>
            <?php $_from = $this->_tpl_vars['contact']['emails']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['mail']):
?>
              <?php if ($this->_tpl_vars['mail']): ?>
                <option value="<?php echo $this->_tpl_vars['mail']; ?>
"<?php if ($this->_tpl_vars['mail'] == $this->_tpl_vars['party']['administrative']['email_selected']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['mail']; ?>
</option>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
      </select>
      <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['party']['administrative']['isCustom']): ?>
        <script type="text/javascript">
          <?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email = new toCombo('<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email');
        </script>
        <input type="hidden" name="cstm_administrative_isCustom" id="cstm_administrative_isCustom" value="1" />
        <input type="hidden" name="cstm_adm_email_isCustom" id="cstm_adm_email_isCustom" value="1" />
      <?php endif; ?>
    <?php endif; ?>
    </td>
  </tr>
  <tr>
    <td class="labelbox">
      <?php echo smarty_function_help(array('label' => 'party_cc'), $this);?>

      <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm'): ?>
      <?php ob_start(); ?>error_<?php echo $this->_tpl_vars['party']['prefix']; ?>
_administrative_cc<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('error_administrative_cc', ob_get_contents());ob_end_clean(); ?>
      <a name="<?php echo $this->_tpl_vars['error_administrative_cc']; ?>
"></a>
      <?php endif; ?>
      <?php ob_start(); ?>error_<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email_cc<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('error_adm_email_cc', ob_get_contents());ob_end_clean(); ?>
      <a name="<?php echo $this->_tpl_vars['error_adm_email_cc']; ?>
"></a>
    </td>
    <td>&nbsp;</td>
    <td style="padding-right: 2px;">
      <table id="<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container" cellspacing="0" cellpadding="0" border="0">
        <tr style="display: none;"><td colspan="2"></td></tr>
        <?php if ($this->_tpl_vars['party']['prefix'] == 'self'): ?>
          <?php $this->assign('contacts_cc', $this->_tpl_vars['contract']->get('self_adm_cc')); ?>
        <?php else: ?>
          <?php $this->assign('contacts_cc', $this->_tpl_vars['contract']->get('cstm_adm_cc')); ?>
        <?php endif; ?>
        <?php $_from = $this->_tpl_vars['contacts_cc']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ci'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ci']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ck'] => $this->_tpl_vars['contact_cc']):
        $this->_foreach['ci']['iteration']++;
?>
        <tr id="<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container_<?php echo $this->_foreach['ci']['iteration']; ?>
">
          <td style="display: none;">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (count($this->_tpl_vars['contacts_cc']) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="confirmAction('delete_row', function() { hideField('<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container','<?php echo $this->_foreach['ci']['iteration']; ?>
'); }, this);" />
            <a href="javascript: disableField('<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container','<?php echo $this->_foreach['ci']['iteration']; ?>
');"><?php echo $this->_foreach['ci']['iteration']; ?>
</a>
          </td>
          <td nowrap="nowrap" style="padding: 5px 0px;">
          <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && empty ( $this->_tpl_vars['party']['administrative']['options'] )): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_administrative_cc",'index' => $this->_foreach['ci']['iteration'],'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_adm_email_cc",'index' => $this->_foreach['ci']['iteration'],'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_email'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <input type="hidden" name="cstm_administrative_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_administrative_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
            <input type="hidden" name="cstm_adm_email_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_adm_email_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
          <?php else: ?>
            <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['party']['is_company'] && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add')): ?>
              <?php $this->assign('field_type', 'combobox'); ?>
            <?php else: ?>
              <?php $this->assign('field_type', 'dropdown'); ?>
            <?php endif; ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_".($this->_tpl_vars['field_type']).".html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_administrative_cc",'index' => $this->_foreach['ci']['iteration'],'options' => $this->_tpl_vars['party']['administrative']['options'],'optgroups' => $this->_tpl_vars['party']['administrative']['optgroups'],'value' => $this->_tpl_vars['contact_cc']['customer'],'onchange' => 'changeContactEmails(this)','standalone' => true,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['party']['is_company'] && ( ! empty ( $this->_tpl_vars['party']['administrative']['isCustom_cc'][$this->_tpl_vars['ck']] ) || count($this->_tpl_vars['contacts_cc']) == 1 && ! $this->_tpl_vars['contact_cc']['customer'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('isCustom_cc', ob_get_contents());ob_end_clean(); ?>
            <select name="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email_cc[<?php echo $this->_tpl_vars['ck']; ?>
]" id="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email_cc_<?php echo $this->_foreach['ci']['iteration']; ?>
" class="selbox<?php if (! $this->_tpl_vars['contact_cc']['customer'] || ! $this->_tpl_vars['contact_cc']['email']): ?> undefined<?php endif; ?>" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_party_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
            <?php if ($this->_tpl_vars['contact_cc']['customer'] || $this->_tpl_vars['contact_cc']['email']): ?>
              <option value="" class="undefined"><?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['isCustom_cc']): ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['select_or_input'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['contact_cc']['customer']): ?>
              <?php $_from = $this->_tpl_vars['party']['administrative']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact']):
?>
                <?php if ($this->_tpl_vars['contact']['option_value'] == $this->_tpl_vars['contact_cc']['customer']): ?>
                  <?php $_from = $this->_tpl_vars['contact']['emails']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['mail']):
?>
                    <?php if ($this->_tpl_vars['mail']): ?>
                      <option value="<?php echo $this->_tpl_vars['mail']; ?>
"<?php if ($this->_tpl_vars['mail'] == $this->_tpl_vars['contact_cc']['email']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['mail']; ?>
</option>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
            </select>
            <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['isCustom_cc']): ?>
              <script type="text/javascript">
                <?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email_cc_<?php echo $this->_foreach['ci']['iteration']; ?>
 = new toCombo('<?php echo $this->_tpl_vars['party']['prefix']; ?>
_adm_email_cc_<?php echo $this->_foreach['ci']['iteration']; ?>
');
              </script>
              <input type="hidden" name="cstm_administrative_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_administrative_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
              <input type="hidden" name="cstm_adm_email_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_adm_email_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
            <?php endif; ?>
          <?php endif; ?>
          </td>
        </tr>
        <?php endforeach; endif; unset($_from); ?>
      </table>
    </td>
    <td style="padding: 10px 5px 0 0;" valign="top">
      <div class="t_buttons" style="width: 30px;">
        <div id="<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container_plusButton" onclick="addField('<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container', false, false, true);" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
        <div id="<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container_minusButton"<?php if (count($this->_tpl_vars['contacts_cc']) <= 1): ?> class="disabled"<?php endif; ?> onclick="removeField('<?php echo $this->_tpl_vars['party']['type']; ?>
_adm_container');" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
      </div>
    </td>
  </tr>
  <tr>
    <td colspan="4">&nbsp;</td>
  </tr>
  <tr>
    <td class="labelbox">
      <?php echo smarty_function_help(array('label' => 'party_financial'), $this);?>

      <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm'): ?>
      <?php ob_start(); ?>error_<?php echo $this->_tpl_vars['party']['prefix']; ?>
_financial<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('error_financial', ob_get_contents());ob_end_clean(); ?>
      <a name="<?php echo $this->_tpl_vars['error_financial']; ?>
"></a>
      <?php endif; ?>
      <?php ob_start(); ?>error_<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('error_fin_email', ob_get_contents());ob_end_clean(); ?>
      <a name="<?php echo $this->_tpl_vars['error_fin_email']; ?>
"></a>
    </td>
    <td<?php if ($this->_tpl_vars['party']['prefix'] == 'self'): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>>&nbsp;<?php endif; ?></td>
    <td colspan="2" style="white-space: nowrap;">
    <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && empty ( $this->_tpl_vars['party']['financial']['options'] )): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_financial",'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_fin_email",'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_email'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <input type="hidden" name="cstm_financial_isCustom" id="cstm_financial_isCustom" value="1" />
      <input type="hidden" name="cstm_fin_email_isCustom" id="cstm_fin_email_isCustom" value="1" />
    <?php else: ?>
      <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['party']['is_company'] && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add')): ?>
        <?php $this->assign('field_type', 'combobox'); ?>
      <?php else: ?>
        <?php $this->assign('field_type', 'dropdown'); ?>
      <?php endif; ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['party']['prefix'] == 'self'): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('required', ob_get_contents());ob_end_clean(); ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_".($this->_tpl_vars['field_type']).".html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_financial",'options' => $this->_tpl_vars['party']['financial']['options'],'optgroups' => $this->_tpl_vars['party']['financial']['optgroups'],'value' => $this->_tpl_vars['party']['financial']['selected'],'onchange' => 'changeContactEmails(this)','standalone' => true,'required' => $this->_tpl_vars['required'],'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <script type="text/javascript">
        <?php echo $this->_tpl_vars['party']['prefix']; ?>
_financial_options = <?php echo smarty_function_json(array('encode' => $this->_tpl_vars['party']['financial']['options']), $this);?>
;
      </script>
      <?php if ($this->_tpl_vars['party']['prefix'] == 'self'): ?>
        <?php if ($this->_tpl_vars['party']['financial']['selected']): ?>
          <?php $_from = $this->_tpl_vars['party']['financial']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact']):
?>
            <?php if ($this->_tpl_vars['contact']['option_value'] == $this->_tpl_vars['party']['financial']['selected']): ?>
              <span id="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_party_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['contact']['emails']['0']; ?>
</span>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        <?php else: ?>
          <span id="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email"></span>
        <?php endif; ?>
      <?php else: ?>
        <select name="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email" id="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email" class="selbox<?php if (! $this->_tpl_vars['party']['financial']['selected'] || ! $this->_tpl_vars['party']['financial']['email_selected']): ?> undefined<?php endif; ?>" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_party_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
        <?php if ($this->_tpl_vars['party']['financial']['selected'] || $this->_tpl_vars['party']['financial']['email_selected']): ?>
          <option value="" class="undefined"><?php if ($this->_tpl_vars['party']['is_company'] && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['party']['financial']['isCustom']): ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['select_or_input'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['party']['financial']['selected']): ?>
          <?php $_from = $this->_tpl_vars['party']['financial']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact']):
?>
            <?php if ($this->_tpl_vars['contact']['option_value'] == $this->_tpl_vars['party']['financial']['selected']): ?>
              <?php $_from = $this->_tpl_vars['contact']['emails']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['mail']):
?>
                <?php if ($this->_tpl_vars['mail']): ?>
                  <option value="<?php echo $this->_tpl_vars['mail']; ?>
"<?php if ($this->_tpl_vars['mail'] == $this->_tpl_vars['party']['financial']['email_selected']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['mail']; ?>
</option>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
                <?php endif; ?>
        </select>
        <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm' && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['party']['financial']['isCustom']): ?>
          <script type="text/javascript">
            <?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email = new toCombo('<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email');
          </script>
          <input type="hidden" name="cstm_financial_isCustom" id="cstm_financial_isCustom" value="1" />
          <input type="hidden" name="cstm_fin_email_isCustom" id="cstm_fin_email_isCustom" value="1" />
        <?php endif; ?>
      <?php endif; ?>
    <?php endif; ?>
    </td>
  </tr>
  <tr>
    <?php if ($this->_tpl_vars['party']['prefix'] == 'cstm'): ?>
    <td class="labelbox">
      <?php echo smarty_function_help(array('label' => 'party_cc'), $this);?>

      <a name="error_cstm_financial_cc"></a>
      <a name="error_cstm_fin_email_cc"></a>
    </td>
    <td>&nbsp;</td>
    <td style="padding-right: 2px;">
      <table id="<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container" cellspacing="0" cellpadding="0" border="0">
        <tr style="display: none;"><td colspan="2"></td></tr>
        <?php $this->assign('contacts_cc', $this->_tpl_vars['contract']->get('cstm_fin_cc')); ?>
        <?php $_from = $this->_tpl_vars['contacts_cc']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ci'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ci']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ck'] => $this->_tpl_vars['contact_cc']):
        $this->_foreach['ci']['iteration']++;
?>
        <tr id="<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container_<?php echo $this->_foreach['ci']['iteration']; ?>
">
          <td style="display: none;">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (count($this->_tpl_vars['contacts_cc']) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="confirmAction('delete_row', function() { hideField('<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container','<?php echo $this->_foreach['ci']['iteration']; ?>
'); }, this);" />
            <a href="javascript: disableField('<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container','<?php echo $this->_foreach['ci']['iteration']; ?>
');"><?php echo $this->_foreach['ci']['iteration']; ?>
</a>
          </td>
          <td nowrap="nowrap" style="padding: 5px 0px;">
          <?php if (empty ( $this->_tpl_vars['party']['financial']['options'] )): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_financial_cc",'index' => $this->_foreach['ci']['iteration'],'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_text.html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_fin_email_cc",'index' => $this->_foreach['ci']['iteration'],'standalone' => true,'required' => 1,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_email'],'show_placeholder' => 'help','help' => "[".($this->_config[0]['vars']['input'])."]")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <input type="hidden" name="cstm_financial_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_financial_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
            <input type="hidden" name="cstm_fin_email_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_fin_email_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
          <?php else: ?>
            <?php if ($this->_tpl_vars['party']['is_company'] && $this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add')): ?>
              <?php $this->assign('field_type', 'combobox'); ?>
            <?php else: ?>
              <?php $this->assign('field_type', 'dropdown'); ?>
            <?php endif; ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_".($this->_tpl_vars['field_type']).".html", 'smarty_include_vars' => array('name' => ($this->_tpl_vars['party']['prefix'])."_financial_cc",'index' => $this->_foreach['ci']['iteration'],'options' => $this->_tpl_vars['party']['financial']['options'],'optgroups' => $this->_tpl_vars['party']['financial']['optgroups'],'value' => $this->_tpl_vars['contact_cc']['customer'],'onchange' => 'changeContactEmails(this)','standalone' => true,'width' => 150,'label' => $this->_config[0]['vars']['contracts_party_name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php ob_start(); ?><?php if ($this->_tpl_vars['party']['is_company'] && ( ! empty ( $this->_tpl_vars['party']['financial']['isCustom_cc'][$this->_tpl_vars['ck']] ) || count($this->_tpl_vars['contacts_cc']) == 1 && ! $this->_tpl_vars['contact_cc']['customer'] )): ?>1<?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('isCustom_cc', ob_get_contents());ob_end_clean(); ?>
            <select name="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email_cc[<?php echo $this->_tpl_vars['ck']; ?>
]" id="<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email_cc_<?php echo $this->_foreach['ci']['iteration']; ?>
" class="selbox<?php if (! $this->_tpl_vars['contact_cc']['customer'] || ! $this->_tpl_vars['contact_cc']['email']): ?> undefined<?php endif; ?>" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this);" style="width: 150px!important;" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_party_email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
            <?php if ($this->_tpl_vars['contact_cc']['customer'] || $this->_tpl_vars['contact_cc']['email']): ?>
              <option value="" class="undefined"><?php if ($this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['isCustom_cc']): ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['select_or_input'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['contact_cc']['customer']): ?>
              <?php $_from = $this->_tpl_vars['party']['financial']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['contact']):
?>
                <?php if ($this->_tpl_vars['contact']['option_value'] == $this->_tpl_vars['contact_cc']['customer']): ?>
                  <?php $_from = $this->_tpl_vars['contact']['emails']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['mail']):
?>
                    <?php if ($this->_tpl_vars['mail']): ?>
                      <option value="<?php echo $this->_tpl_vars['mail']; ?>
"<?php if ($this->_tpl_vars['mail'] == $this->_tpl_vars['contact_cc']['email']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['mail']; ?>
</option>
                    <?php endif; ?>
                  <?php endforeach; endif; unset($_from); ?>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endif; ?>
            </select>
            <?php if ($this->_tpl_vars['currentUser']->checkRights('customers_contactpersons','add') && $this->_tpl_vars['isCustom_cc']): ?>
            <script type="text/javascript">
              <?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email_cc_<?php echo $this->_foreach['ci']['iteration']; ?>
 = new toCombo('<?php echo $this->_tpl_vars['party']['prefix']; ?>
_fin_email_cc_<?php echo $this->_foreach['ci']['iteration']; ?>
');
            </script>
            <input type="hidden" name="cstm_financial_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_financial_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
            <input type="hidden" name="cstm_fin_email_cc_isCustom[<?php echo $this->_tpl_vars['ck']; ?>
]" id="cstm_fin_email_cc_isCustom_<?php echo $this->_foreach['ci']['iteration']; ?>
" value="1" />
            <?php endif; ?>
          <?php endif; ?>
          </td>
        </tr>
        <?php endforeach; endif; unset($_from); ?>
      </table>
    </td>
    <td style="padding: 10px 5px 0 0;" valign="top">
      <div class="t_buttons" style="width: 30px;">
        <div id="<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container_plusButton" onclick="addField('<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container', false, false, true);" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['add_row'],'popup_only' => 1), $this);?>
><div class="t_plus"></div></div>
        <div id="<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container_minusButton"<?php if (count($this->_tpl_vars['contacts_cc']) <= 1): ?> class="disabled"<?php endif; ?> onclick="removeField('<?php echo $this->_tpl_vars['party']['type']; ?>
_fin_container');" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['remove_row'],'popup_only' => 1), $this);?>
><div class="t_minus"></div></div>
      </div>
    </td>
    <?php else: ?>
    <td colspan="4"></td>
    <?php endif; ?>
  </tr>
</table>