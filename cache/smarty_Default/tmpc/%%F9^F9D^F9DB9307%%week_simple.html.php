<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:10
         compiled from /var/www/Nzoom-Hella/_libs/modules/calendars/templates/week_simple.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/calendars/templates/week_simple.html', 13, false),array('modifier', 'string_format', '/var/www/Nzoom-Hella/_libs/modules/calendars/templates/week_simple.html', 48, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/calendars/templates/week_simple.html', 17, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/calendars/templates/week_simple.html', 19, false),)), $this); ?>
<?php $this->assign('table_cols', 8); ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['monday_start'] && $this->_tpl_vars['calendar']->day_of_week == 0): ?>7<?php else: ?><?php echo $this->_tpl_vars['calendar']->day_of_week; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_day_of_week', ob_get_contents());ob_end_clean(); ?>
<?php $this->assign('week_num', $this->_tpl_vars['calendar']->day->week); ?>
<?php if ($this->_tpl_vars['monday_start']): ?>
  <?php $this->assign('first_week_day', $this->_tpl_vars['calendar']->month->weeks[$this->_tpl_vars['week_num']]['0']); ?>
  <?php $this->assign('last_week_day', $this->_tpl_vars['first_week_day']+6); ?>
  <?php $this->assign('first_week_day', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['first_week_day']]->dateISO); ?>
  <?php $this->assign('last_week_day', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['last_week_day']]->dateISO); ?>
<?php endif; ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_caption2 hcenter" colspan="<?php echo $this->_tpl_vars['table_cols']; ?>
">
      <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['first_week_day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
 - <?php echo ((is_array($_tmp=$this->_tpl_vars['last_week_day'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
 (<?php echo $this->_config[0]['vars']['week']; ?>
 <?php echo $this->_config[0]['vars']['num']; ?>
<?php echo $this->_tpl_vars['week_num']; ?>
)</div>
    </td>
  </tr>
  <tr>
    <td class="cal_week_num_title t_border hcenter strong help" <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['calendars_week_number'],'text_content' => ' ','popup_only' => 1), $this);?>
><?php echo $this->_config[0]['vars']['num']; ?>
</td>
    <?php if (! $this->_tpl_vars['monday_start']): ?>
      <td class="cal_weekend_title t_border hcenter strong" width="<?php echo smarty_function_math(array('equation' => '100/x','x' => 7,'format' => '%d'), $this);?>
%"><?php echo $this->_config[0]['vars']['weekday_0']; ?>
</td>
    <?php endif; ?>

    <?php unset($this->_sections['weekdays']);
$this->_sections['weekdays']['name'] = 'weekdays';
$this->_sections['weekdays']['loop'] = is_array($_loop=7) ? count($_loop) : max(0, (int)$_loop); unset($_loop);
$this->_sections['weekdays']['start'] = (int)1;
$this->_sections['weekdays']['show'] = true;
$this->_sections['weekdays']['max'] = $this->_sections['weekdays']['loop'];
$this->_sections['weekdays']['step'] = 1;
if ($this->_sections['weekdays']['start'] < 0)
    $this->_sections['weekdays']['start'] = max($this->_sections['weekdays']['step'] > 0 ? 0 : -1, $this->_sections['weekdays']['loop'] + $this->_sections['weekdays']['start']);
else
    $this->_sections['weekdays']['start'] = min($this->_sections['weekdays']['start'], $this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] : $this->_sections['weekdays']['loop']-1);
if ($this->_sections['weekdays']['show']) {
    $this->_sections['weekdays']['total'] = min(ceil(($this->_sections['weekdays']['step'] > 0 ? $this->_sections['weekdays']['loop'] - $this->_sections['weekdays']['start'] : $this->_sections['weekdays']['start']+1)/abs($this->_sections['weekdays']['step'])), $this->_sections['weekdays']['max']);
    if ($this->_sections['weekdays']['total'] == 0)
        $this->_sections['weekdays']['show'] = false;
} else
    $this->_sections['weekdays']['total'] = 0;
if ($this->_sections['weekdays']['show']):

            for ($this->_sections['weekdays']['index'] = $this->_sections['weekdays']['start'], $this->_sections['weekdays']['iteration'] = 1;
                 $this->_sections['weekdays']['iteration'] <= $this->_sections['weekdays']['total'];
                 $this->_sections['weekdays']['index'] += $this->_sections['weekdays']['step'], $this->_sections['weekdays']['iteration']++):
$this->_sections['weekdays']['rownum'] = $this->_sections['weekdays']['iteration'];
$this->_sections['weekdays']['index_prev'] = $this->_sections['weekdays']['index'] - $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['index_next'] = $this->_sections['weekdays']['index'] + $this->_sections['weekdays']['step'];
$this->_sections['weekdays']['first']      = ($this->_sections['weekdays']['iteration'] == 1);
$this->_sections['weekdays']['last']       = ($this->_sections['weekdays']['iteration'] == $this->_sections['weekdays']['total']);
?>
      <?php ob_start(); ?>weekday_<?php echo $this->_sections['weekdays']['index']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_label', ob_get_contents());ob_end_clean(); ?>
      <td class="<?php if ($this->_sections['weekdays']['iteration'] >= 6): ?>cal_weekend_title<?php else: ?>cal_weekday_title<?php endif; ?> t_border hcenter strong" width="<?php echo smarty_function_math(array('equation' => '100/x','x' => 7,'format' => '%d'), $this);?>
%"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['week_label']]; ?>
</td>
    <?php endfor; endif; ?>
    <?php if ($this->_tpl_vars['monday_start']): ?>
      <td class="cal_weekend_title hcenter strong" width="<?php echo smarty_function_math(array('equation' => '100/x','x' => 7,'format' => '%d'), $this);?>
%"><?php echo $this->_config[0]['vars']['weekday_0']; ?>
</td>
    <?php endif; ?>
  </tr>
  <tr>
  <td class="cal_week_num">
    <?php $this->assign('week_num', $this->_tpl_vars['calendar']->day->week); ?>
    <?php $this->assign('week', $this->_tpl_vars['calendar']->month->weeks[$this->_tpl_vars['week_num']]); ?>
    <?php $this->assign('w_day', $this->_tpl_vars['week']['0']); ?>
    <?php ob_start(); ?><?php echo $this->_config[0]['vars']['week']; ?>
 <?php echo $this->_config[0]['vars']['num']; ?>
<?php echo $this->_tpl_vars['week_num']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_help_label', ob_get_contents());ob_end_clean(); ?>
    <?php ob_start(); ?><?php echo $this->_config[0]['vars']['calendars_view_event']; ?>
 <b><?php echo $this->_config[0]['vars']['week']; ?>
 <?php echo $this->_config[0]['vars']['num']; ?>
<?php echo $this->_tpl_vars['week_num']; ?>
</b><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('week_help_content', ob_get_contents());ob_end_clean(); ?>
    <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=week&amp;date=<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['w_day']]->year; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['w_day']]->month; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['w_day']]->day; ?>
" <?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['week_help_label'],'text_content' => $this->_tpl_vars['week_help_content'],'popup_only' => 1), $this);?>
><?php echo $this->_tpl_vars['week_num']; ?>
</a>
  </td>
  <?php $_from = $this->_tpl_vars['week']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['week_days'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['week_days']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['day']):
        $this->_foreach['week_days']['iteration']++;
?>
    <?php $this->assign('day_num', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day); ?>
    <?php $this->assign('month_num', $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month); ?>
    <td class="cal_month_day<?php if ($this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->isToday()): ?> cal_today<?php endif; ?><?php if (! empty ( $this->_tpl_vars['months_count_events'][$this->_tpl_vars['month_num']][$this->_tpl_vars['day_num']] )): ?> cal_day_with_events cal_<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->inPresentPastFuture(); ?>
<?php endif; ?><?php if (( $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month != $this->_tpl_vars['calendar']->month->getMonth() )): ?> cal_not_available<?php elseif (( $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->isWeekend() )): ?> cal_weekend<?php endif; ?><?php if (($this->_foreach['week_days']['iteration'] == $this->_foreach['week_days']['total'])): ?> cal_no_border<?php endif; ?>"<?php if ($this->_tpl_vars['action'] == 'dashlet'): ?> style="height: 30px"<?php endif; ?>>
      <?php ob_start(); ?><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('day_help_label', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php echo $this->_config[0]['vars']['calendars_view_event']; ?>
 <b><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
</b><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('day_help_content', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php echo $this->_config[0]['vars']['calendars_add_event']; ?>
 <b><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->timestamp)) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_calendar_day']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_calendar_day'])); ?>
</b><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('addevent_help_content', ob_get_contents());ob_end_clean(); ?>
      <div class="cal_month_daynum">
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=events&amp;events=add&amp;date=<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->year; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day; ?>
%20<?php echo ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%T') : smarty_modifier_date_format($_tmp, '%T')); ?>
" class="cal_month_daynum_addlink" <?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['day_help_label'],'text_content' => $this->_tpl_vars['addevent_help_content'],'popup_only' => 1), $this);?>
></a>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=calendars&amp;calendars=day&amp;date=<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->year; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->month; ?>
-<?php echo $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day; ?>
" <?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['day_help_label'],'text_content' => $this->_tpl_vars['day_help_content'],'popup_only' => 1), $this);?>
><?php echo ((is_array($_tmp=$this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']]->day)) ? $this->_run_mod_handler('string_format', true, $_tmp, '%d') : smarty_modifier_string_format($_tmp, '%d')); ?>
</a>
      </div>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_events_week_simple.html", 'smarty_include_vars' => array('num_events' => $this->_tpl_vars['months_count_events'][$this->_tpl_vars['month_num']][$this->_tpl_vars['day_num']],'day' => $this->_tpl_vars['calendar']->month->days[$this->_tpl_vars['day']])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  <?php endforeach; endif; unset($_from); ?>
  </tr>
  <tr>
    <td class="t_footer" colspan="<?php echo $this->_tpl_vars['table_cols']; ?>
"></td>
  </tr>
</table>