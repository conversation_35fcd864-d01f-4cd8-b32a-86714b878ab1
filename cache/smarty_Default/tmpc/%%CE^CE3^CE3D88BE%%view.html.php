<?php /* Smarty version 2.6.33, created on 2025-05-21 16:05:51
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/view.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/customers/templates/view.html', 15, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

        <form name="customers" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['customer']->get('id'); ?>
" />
        <input type="hidden" name="is_company" id="is_company" value="<?php echo $this->_tpl_vars['customer']->get('is_company'); ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['customer']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['view_template'], 'smarty_include_vars' => array('layouts_vars' => $this->_tpl_vars['customer']->get('vars'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['customer'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </form>
      </div>
    </td>
    <?php if (isset ( $this->_tpl_vars['side_panels'] )): ?>
    <td class="side_panel_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_side_panel_options.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."side_panels_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
    <?php endif; ?>
  </tr>
</table>
<br />
<br />
<?php if (isset ( $this->_tpl_vars['side_panels'] ) && in_array ( 'related_records' , $this->_tpl_vars['side_panels'] ) && $this->_tpl_vars['related_records_modules']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="subpanel_container">
  <tr>
    <td>
      <?php if ($_COOKIE['customers_selected_related_tab'] && in_array ( $_COOKIE['customers_selected_related_tab'] , $this->_tpl_vars['related_records_modules'] )): ?>
        <?php $this->assign('rel_type', $_COOKIE['customers_selected_related_tab']); ?>
      <?php else: ?>
        <?php $this->assign('rel_type', $this->_tpl_vars['related_records_modules']['0']); ?>
      <?php endif; ?>
      <input type="hidden" id="rel_type" name="rel_type" value="<?php echo $this->_tpl_vars['rel_type']; ?>
" />
      <a name="related_subpanel_customer<?php echo $this->_tpl_vars['customer']->get('id'); ?>
"></a>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."related_records_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <div class="m_header_m_menu scroll_box_container">
        <?php $_from = $this->_tpl_vars['related_records_modules']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['model'] => $this->_tpl_vars['module']):
?>
          <div id="<?php echo $this->_tpl_vars['session_params'][$this->_tpl_vars['module']]; ?>
" class="rel_tab<?php if ($this->_tpl_vars['rel_type'] == $this->_tpl_vars['module']): ?> loaded<?php else: ?>" style="display: none;<?php endif; ?>">
            <?php if ($this->_tpl_vars['rel_type'] == $this->_tpl_vars['module']): ?>
              <script type="text/javascript">
                ajaxUpdater({
                  link: '<?php echo $this->_tpl_vars['related'][$this->_tpl_vars['module']]; ?>
',
                  target: '<?php echo $this->_tpl_vars['session_params'][$this->_tpl_vars['module']]; ?>
',
                  execute_after: function() { removeClass($('related_records_action_tabs'), 'hidden'); } 
                });
              </script>
            <?php endif; ?>
          </div>
        <?php endforeach; endif; unset($_from); ?>
      </div>
    </td>
  </tr>
</table>
<?php endif; ?>