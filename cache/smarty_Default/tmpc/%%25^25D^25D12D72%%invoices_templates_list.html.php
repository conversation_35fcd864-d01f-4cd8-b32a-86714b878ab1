<?php /* Smarty version 2.6.33, created on 2025-05-21 15:57:28
         compiled from /var/www/Nzoom-Hella/_libs/modules/finance/templates/invoices_templates_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/invoices_templates_list.html', 24, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/invoices_templates_list.html', 29, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/invoices_templates_list.html', 75, false),array('modifier', 'strip_tags', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/invoices_templates_list.html', 107, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/invoices_templates_list.html', 39, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/invoices_templates_list.html', 41, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=invoices_templates&amp;invoices_templates=<?php echo $this->_tpl_vars['action']; ?>
&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <form name="finance_invoices_templates" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=invoices_templates" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall" width="15">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['customer']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['customer']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['type']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['type']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['total']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['total']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['gt2_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['total_with_vat']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['total_with_vat']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['gt2_total_with_vat'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_issue_date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_fiscal_event_date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption" nowrap="nowrap">&nbsp;</td>
        </tr>
      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php $_from = $this->_tpl_vars['templates']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['template']):
        $this->_foreach['i']['iteration']++;
?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 pointer" id="invoice_<?php echo $this->_tpl_vars['template']->get('id'); ?>
">
          <td class="t_border">
            <input onclick="sendIds(params = {
                                            the_element: this,
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            session_param: '<?php echo ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])); ?>
',
                                            total: <?php echo $this->_tpl_vars['pagination']['total']; ?>

                                           });" 
                   type="checkbox"
                   name='items[]'
                   value="<?php echo $this->_tpl_vars['template']->get('id'); ?>
"
                   title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                   <?php if (@ in_array ( $this->_tpl_vars['template']->get('id') , $this->_tpl_vars['selected_items']['ids'] ) || ( @ $this->_tpl_vars['selected_items']['select_all'] == 1 && @ ! in_array ( $this->_tpl_vars['template']->get('id') , $this->_tpl_vars['selected_items']['ignore_ids'] ) )): ?>
                     checked="checked"
                   <?php endif; ?> />
          </td>
          <td class="t_border hright"><div class="switch_expand" id="switch_invoice_<?php echo $this->_tpl_vars['template']->get('id'); ?>
" onclick="toggleInvoicePreview2({template: <?php echo $this->_tpl_vars['template']->get('id'); ?>
})"></div><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
" nowrap="nowrap"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=viewfinance&amp;viewfinance=<?php echo $this->_tpl_vars['template']->get('contract_id'); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['customer']['isSorted']; ?>
" nowrap="nowrap">
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['template']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['type']['isSorted']; ?>
" nowrap="nowrap">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['total']['isSorted']; ?>
" nowrap="nowrap" align="right">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('total'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['total_with_vat']['isSorted']; ?>
" nowrap="nowrap" align="right">
            <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('total_with_vat'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
          <td class="t_border" nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template']->get('template_issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
          <td class="t_border" nowrap="nowrap">
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template']->get('template_issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
          <td class="" nowrap="nowrap">
            <?php if ($this->_tpl_vars['template']->get('type') > @PH_FINANCE_TYPE_MAX): ?>
              <?php $this->assign('fer_type', $this->_tpl_vars['template']->get('type')); ?>
              <?php $this->assign('pats', $this->_tpl_vars['patterns'][$this->_tpl_vars['fer_type']]); ?>
            <?php else: ?>
              <?php if ($this->_tpl_vars['template']->get('proforma')): ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['proforma_invoice']); ?><?php else: ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['invoice']); ?><?php endif; ?>
            <?php endif; ?>
            <?php $this->assign('template_company', $this->_tpl_vars['template']->get('company')); ?>
            <?php $_from = $this->_tpl_vars['pats']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pattern']):
?>
              <?php if (! $this->_tpl_vars['pattern']['for_printform'] && ( $this->_tpl_vars['pattern']['company'] == $this->_tpl_vars['template_company'] || $this->_tpl_vars['pattern']['company'] == '0' ) && ( ! $this->_tpl_vars['template']->get('pattern') || $this->_tpl_vars['pattern']['id'] == $this->_tpl_vars['template']->get('pattern') )): ?>
                <?php if (empty ( $this->_tpl_vars['tpl_errors'][$this->_tpl_vars['key']] )): ?>
                  <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=invoices_templates&amp;invoices_templates=preview&amp;preview=<?php echo $this->_tpl_vars['template']->get('id'); ?>
&amp;pattern=<?php echo $this->_tpl_vars['pattern']['id']; ?>
" target="_blank" title="<?php echo $this->_config[0]['vars']['preview']; ?>
: <?php echo $this->_tpl_vars['pattern']['name']; ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
pdf.png" alt="" border="0" /></a>
                <?php else: ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
pdf.png" alt="" border="0" class="dimmed" />
                <?php endif; ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>
        <tr style="display:none" id="invoice_<?php echo $this->_tpl_vars['template']->get('id'); ?>
_preview">
          <td colspan="10" class="t_top_border t_bottom_border">
            <?php $this->assign('tmp_table', $this->_tpl_vars['template']->get('grouping_table_2')); ?>
            <?php if (( ! empty ( $this->_tpl_vars['tmp_table']['values'] ) )): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_gt2_view.html", 'smarty_include_vars' => array('table' => $this->_tpl_vars['tmp_table'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php elseif (! empty ( $this->_tpl_vars['tpl_errors'][$this->_tpl_vars['key']] )): ?>
              <div class="red" style="margin: 10px 30px;">
              <?php $_from = $this->_tpl_vars['tpl_errors'][$this->_tpl_vars['key']]; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['error']):
        $this->_foreach['j']['iteration']++;
?>
                <?php echo ((is_array($_tmp=$this->_tpl_vars['error'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp) : smarty_modifier_strip_tags($_tmp)); ?>
<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?><br /><?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
              </div>
            <?php else: ?>
              <div class="red" style="margin: 10px 30px;"><?php echo $this->_config[0]['vars']['error_gt2_no_rows_for_template']; ?>
</div>
            <?php endif; ?>
            <?php $this->assign('tmp_table', ''); ?>
          </td>
        </tr>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="10"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="10"></td>
        </tr>
      </table>
    <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve')): ?>
      <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_issue_date') || $this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_future_issue_date')): ?>
      <br />
      <table>
      <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_issue_date')): ?>
        <?php $this->assign('disallow_date_before', 0); ?>
      <?php else: ?>
        <?php $this->assign('disallow_date_before', 1); ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_future_issue_date')): ?>
        <?php $this->assign('disallow_date_after', 0); ?>
      <?php else: ?>
        <?php $this->assign('disallow_date_after', 1); ?>
      <?php endif; ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'issue_date','label' => $this->_config[0]['vars']['finance_incomes_reasons_issue_date'],'help' => $this->_config[0]['vars']['help_finance_incomes_reasons_issue_date'],'width' => 200,'show_calendar_icon' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </table>
      <?php endif; ?>
      <br />
      <input type="hidden" name="session_param" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])); ?>
" />
      <?php echo '<button type="submit" name="approve_send" class="button" onclick="return confirmation(this, \'finance\', \'approve_send\', \'invoices_templates\')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve_send'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><button type="submit" name="approve" class="button" onclick="return confirmation(this, \'finance\', \'approve\', \'invoices_templates\')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php ob_start(); ?><?php echo 'finance_incomes_reasons'; ?><?php echo @PH_FINANCE_TYPE_INVOICE; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('right_invoice', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'finance_incomes_reasons'; ?><?php echo @PH_FINANCE_TYPE_PRO_INVOICE; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('right_proforma', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_invoice'],'add') || $this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['right_proforma'],'add')): ?><?php echo '<button type="submit" name="issue" class="button'; ?><?php if (! $this->_tpl_vars['issue_access']): ?><?php echo ' inactive'; ?><?php endif; ?><?php echo '" onclick="return confirmation(this, \'finance\', \'issue\', \'invoices_templates\')"'; ?><?php if (! $this->_tpl_vars['issue_access']): ?><?php echo ' disabled="disabled" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_finance_issue_locked'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '"'; ?><?php endif; ?><?php echo '>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><button type="submit" name="issue_send" class="button'; ?><?php if (! $this->_tpl_vars['issue_access']): ?><?php echo ' inactive'; ?><?php endif; ?><?php echo '" onclick="return confirmation(this, \'finance\', \'issue_send\', \'invoices_templates\')"'; ?><?php if (! $this->_tpl_vars['issue_access']): ?><?php echo ' disabled="disabled" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_finance_issue_locked'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '"'; ?><?php endif; ?><?php echo '>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue_send'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo '<button type="submit" name="disapprove" class="button" onclick="return confirmation(this, \'finance\', \'disapprove\', \'invoices_templates\')">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['disapprove'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?>

    <?php endif; ?>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>