<?php /* Smarty version 2.6.33, created on 2023-11-16 11:24:48
         compiled from /var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/index.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/index.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/nomenclatures/templates/index.html', 12, false),)), $this); ?>
<h1><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
menu/nomenclatures_index.png" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
<h2><?php echo ((is_array($_tmp=$this->_config[0]['vars']['nomenclatures_hotlinks'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2>

<?php $this->assign('default_icon', ($this->_tpl_vars['theme']->imagesUrl)."nomenclatures.png"); ?>
<?php $_from = $this->_tpl_vars['nomenclatures_components']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['component_name'] => $this->_tpl_vars['component_options']):
        $this->_foreach['i']['iteration']++;
?>
    <?php if ($this->_tpl_vars['component_options']['options']): ?>
      <?php ob_start(); ?><?php echo $this->_tpl_vars['component_name']; ?>
_box<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('box_name', ob_get_contents());ob_end_clean(); ?>
      <div class="t_caption2_title" onclick="toggleViewLayouts(this)" id="<?php echo $this->_tpl_vars['component_name']; ?>
_switch"><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['box_name']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div></div>
    <?php else: ?>
      <div class="t_caption2_title"><div class="switch_collapse"></div></div>
    <?php endif; ?>
      <img src="<?php echo ((is_array($_tmp=@$this->_tpl_vars['component_options']['icon'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_icon']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_icon'])); ?>
" width="16" height="16" alt="" border="0" /> <a href="<?php echo $this->_tpl_vars['component_options']['url']; ?>
" class="strong"><?php echo $this->_tpl_vars['component_options']['i18n']; ?>
</a>
      <?php if ($this->_tpl_vars['component_options']['options']): ?>
      <span id="<?php echo $this->_tpl_vars['component_name']; ?>
"<?php if ($_COOKIE[$this->_tpl_vars['box_name']] == 'off'): ?> style="display: none"<?php endif; ?>>
        <?php $_from = $this->_tpl_vars['component_options']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['j']['iteration']++;
?>
          <br />&nbsp; &nbsp; &nbsp;<img src="<?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['icon'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['default_icon']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['default_icon'])); ?>
" width="16" height="16" alt="" border="0" /> <a href="<?php echo $this->_tpl_vars['option']['url']; ?>
" class="strong"><?php echo $this->_tpl_vars['option']['i18n']; ?>
</a>
        <?php endforeach; endif; unset($_from); ?>
      </span>
      <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>