<?php /* Smarty version 2.6.33, created on 2025-05-21 16:20:36
         compiled from /var/www/Nzoom-Hella/_libs/modules/finance/templates/_export_list_invoices_templates.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/_export_list_invoices_templates.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/_export_list_invoices_templates.html', 22, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/_export_list_invoices_templates.html', 48, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/_export_list_invoices_templates.html', 16, false),)), $this); ?>
    <table border="1" cellpadding="3" cellspacing="0">
      <tr>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_currency'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_total_with_vat'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_currency'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_fiscal_total_with_vat'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_currency'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_issue_date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_invoices_templates_fiscal_event_date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
      </tr>
    <?php echo smarty_function_counter(array('start' => 0,'name' => 'item_counter','print' => false), $this);?>

    <?php $_from = $this->_tpl_vars['finance_invoices_templates']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['template']):
        $this->_foreach['i']['iteration']++;
?>
      <tr>
        <td><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
        <td><?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        <td>
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        </td>
        <td>
          <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </td>
        <td align="right" style="mso-number-format:'0\.00'">
          <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('total'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </td>
        <td>
          <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </td>
        <td align="right" style="mso-number-format:'0\.00'">
          <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('total_with_vat'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </td>
        <td>
          <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </td>
        <?php echo $this->_tpl_vars['template']->calculateFiscalTotals(); ?>

        <td align="right" style="mso-number-format:'0\.00'">
          <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('fiscal_total_with_vat'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </td>
        <td>
          <?php echo ((is_array($_tmp=$this->_tpl_vars['template']->get('fiscal_currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </td>
        <td style="mso-number-format:'\@'">
          <?php if ($this->_tpl_vars['template']->get('template_issue_date') && ! preg_match ( '/0000-00-00/' , $this->_tpl_vars['template']->get('template_issue_date') )): ?>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template']->get('template_issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          <?php else: ?>
            <?php echo ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

          <?php endif; ?>
        </td>
        <td style="mso-number-format:'\@'">
          <?php if ($this->_tpl_vars['template']->get('template_issue_date') && ! preg_match ( '/0000-00-00/' , $this->_tpl_vars['template']->get('template_issue_date') )): ?>
            <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['template']->get('template_issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          <?php else: ?>
            <?php echo ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

          <?php endif; ?>
        </td>
      </tr>
    <?php endforeach; else: ?>
      <tr>
        <td colspan="12"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
    <?php endif; unset($_from); ?>
    </table>