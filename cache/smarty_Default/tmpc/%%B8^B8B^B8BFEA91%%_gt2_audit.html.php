<?php /* Smarty version 2.6.33, created on 2023-11-15 17:55:56
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit.html', 2, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit.html', 28, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit.html', 42, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit.html', 48, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/_gt2_audit.html', 61, false),)), $this); ?>
  <div class="gt2_audit_legend">
    <h1><?php echo ((is_array($_tmp=$this->_config[0]['vars']['legend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
    <table border="0" cellpadding="0" cellspacing="5">
      <tr class="finance_audit_added">
        <td class="legend_color">&nbsp;</td>
        <td class="legend_text"><?php echo $this->_config[0]['vars']['legend_added_row']; ?>
</td>
      </tr>
      <tr class="finance_audit_deleted">
        <td class="legend_color">&nbsp;</td>
        <td class="legend_text"><?php echo $this->_config[0]['vars']['legend_deleted_row']; ?>
</td>
      </tr>
      <tr class="finance_audit_updated">
        <td class="legend_color old_value">&nbsp;</td>
        <td class="legend_text"><?php echo $this->_config[0]['vars']['legend_old_value']; ?>
</td>
      </tr>
      <tr class="finance_audit_updated">
        <td class="legend_color new_value">&nbsp;</td>
        <td class="legend_text"><?php echo $this->_config[0]['vars']['legend_new_value']; ?>
</td>
      </tr>
    </table>
  </div>

<div class="clear"></div>
<br />
<span class="red"><?php echo $this->_config[0]['vars']['gt2_audit_only_changed_rows']; ?>
</span>
<br /><br />
<?php if (! empty ( $this->_tpl_vars['gt2_audit']['new_values'] ) || ! empty ( $this->_tpl_vars['gt2_audit']['old_values'] )): ?>
  <?php ob_start(); ?><?php echo count($this->_tpl_vars['gt2_audit']['labels']); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('colspan', ob_get_contents());ob_end_clean(); ?>
  <?php $this->assign('colspan', $this->_tpl_vars['colspan']*2); ?>
  <table border="0" cellpadding="0" cellspacing="0" class="t_table t_header t_table_border">
    <tr>
      <td colspan="<?php echo $this->_tpl_vars['colspan']; ?>
" class="t_caption3 strong legend"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['data'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
    <tr>
      <?php $_from = $this->_tpl_vars['gt2_audit']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['label']):
        $this->_foreach['i']['iteration']++;
?>
        <td class="t_caption<?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_border<?php endif; ?>" colspan="2" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      <?php endforeach; endif; unset($_from); ?>
    </tr>
    <?php $_from = $this->_tpl_vars['gt2_audit']['new_values']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['row'] => $this->_tpl_vars['values']):
        $this->_foreach['i']['iteration']++;
?>
    <?php if (! empty ( $this->_tpl_vars['values']['field_name'] ) && $this->_tpl_vars['values']['field_name'] == 'bb_delimiter'): ?>
    <tr>
      <td colspan="<?php echo $this->_tpl_vars['colspan']; ?>
" class="t_caption3 strong legend"><?php if (empty ( $this->_tpl_vars['values']['deleted'] )): ?><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['values']['model_id']; ?>
<?php if ($_REQUEST['archive']): ?>&amp;archive=1<?php endif; ?>#bb_row_<?php echo $this->_tpl_vars['values']['bb_id']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</a><?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></td>
    </tr>
    <?php elseif ($this->_tpl_vars['values']['article_id'] || $this->_tpl_vars['values']['price'] || $this->_tpl_vars['values']['average_weighted_delivery_price'] || $this->_tpl_vars['values']['last_delivery_price'] || $this->_tpl_vars['values']['quantity'] || $this->_tpl_vars['values']['subtotal'] || $this->_tpl_vars['values']['action'] == 'deleted'): ?>
    <tr class="finance_audit_<?php if ($this->_tpl_vars['values']['action'] == 'deleted' || empty ( $this->_tpl_vars['values'] )): ?>deleted<?php elseif ($this->_tpl_vars['values']['action'] == 'added' || empty ( $this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']] )): ?>added<?php else: ?>updated<?php endif; ?>">
      <?php $_from = $this->_tpl_vars['gt2_audit']['labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['j'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['j']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var'] => $this->_tpl_vars['bla']):
        $this->_foreach['j']['iteration']++;
?>
        <?php if ($this->_tpl_vars['values']['action'] == 'deleted' || empty ( $this->_tpl_vars['values'] )): ?>
          <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?>" colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php elseif ($this->_tpl_vars['values']['action'] == 'added' || empty ( $this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']] )): ?>
          <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?>" colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php elseif ($this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']] == $this->_tpl_vars['values'][$this->_tpl_vars['var']]): ?>
          <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?>" colspan="2"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php else: ?>
          <td class="t_border<?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?> old_value"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['gt2_audit']['old_values'][$this->_tpl_vars['row']][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
          <td class="<?php if (! ($this->_foreach['j']['iteration'] == $this->_foreach['j']['total'])): ?>t_border<?php endif; ?><?php if (! ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?> t_v_border<?php endif; ?> new_value"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['values'][$this->_tpl_vars['var']])) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</td>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    </tr>
    <?php endif; ?>
    <?php endforeach; else: ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
      <td class="error" colspan="<?php echo $this->_tpl_vars['colspan']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_changes_made'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
    <?php endif; unset($_from); ?>
    <tr>
      <td class="t_footer" colspan="<?php echo $this->_tpl_vars['colspan']; ?>
"></td>
    </tr>
  </table>
<?php endif; ?>