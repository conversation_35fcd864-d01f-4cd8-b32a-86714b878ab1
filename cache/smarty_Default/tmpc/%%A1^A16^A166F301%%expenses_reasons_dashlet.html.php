<?php /* Smarty version 2.6.33, created on 2025-05-21 16:43:40
         compiled from /var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 8, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 22, false),array('modifier', 'string_format', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 76, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 116, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 165, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 165, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 16, false),array('function', 'array', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 17, false),array('function', 'array_push', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 49, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 54, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 76, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/finance/templates/expenses_reasons_dashlet.html', 88, false),)), $this); ?>
<table cellpadding="0" border="0" cellspacing="0" width="100%" class="t_table t_list">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php $_from = $this->_tpl_vars['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['column']):
?>
    <td class="t_caption t_border <?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['class']; ?>
" nowrap="nowrap">
      <div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['link']; ?>
">
        <?php ob_start(); ?>finance_<?php if (! preg_match ( '#^owner|responsible|observer|decision$#' , $this->_tpl_vars['column'] )): ?>expenses_reasons_<?php endif; ?><?php echo $this->_tpl_vars['column']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('column_name', ob_get_contents());ob_end_clean(); ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels'][$this->_tpl_vars['column']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars'][$this->_tpl_vars['column_name']]) : smarty_modifier_default($_tmp, @$this->_config[0]['vars'][$this->_tpl_vars['column_name']])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </div>
    </td>
    <?php endforeach; endif; unset($_from); ?>
    <td class="t_caption" nowrap="nowrap">
      &nbsp;
    </td>
  </tr>
      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php echo smarty_function_array(array('assign' => 'background_colors'), $this);?>

      <?php $_from = $this->_tpl_vars['finance_expenses_reasons']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['expenses_reason']):
        $this->_foreach['i']['iteration']++;
?>
      <?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['basic_vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['finance_expenses_reasons_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['finance_expenses_reasons_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['expenses_reason']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['expenses_reason']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span><br />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['expenses_reason']->get('status') == 'opened'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_finance_documents_status_opened']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['expenses_reason']->get('status') == 'locked'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_finance_documents_status_locked']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['expenses_reason']->get('status') == 'finished'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_finance_documents_status_finished']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['expenses_reason']->get('substatus_name')): ?><?php echo '<br />'; ?><?php echo $this->_config[0]['vars']['help_finance_documents_substatus']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['expenses_reason']->get('substatus_name'); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('expenses_reason_status', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <?php $this->assign('background_style', ''); ?>
      <?php $this->assign('background_properties', $this->_tpl_vars['expenses_reason']->getBackgroundColor()); ?>
      <?php if (! empty ( $this->_tpl_vars['background_properties'] )): ?>
        <?php echo smarty_function_array_push(array('array' => 'background_colors','new_item' => $this->_tpl_vars['background_properties']['background_color'],'key' => $this->_tpl_vars['background_properties']['definition']), $this);?>

        <?php ob_start(); ?> style="background-color: #<?php echo $this->_tpl_vars['background_properties']['background_color']; ?>
; color: #<?php echo $this->_tpl_vars['background_properties']['text_color']; ?>
;"<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('background_style', ob_get_contents());ob_end_clean(); ?>
      <?php endif; ?>
      <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."row_link_action.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['expenses_reason'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('row_link', ob_get_contents()); ob_end_clean();
 ?>
      <?php ob_start(); ?><?php if ($this->_tpl_vars['row_link']): ?>pointer<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_class', ob_get_contents());ob_end_clean(); ?>
    <tr class="<?php if ($this->_tpl_vars['background_style']): ?>t_row<?php else: ?><?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php endif; ?><?php if ($this->_tpl_vars['expenses_reason']->get('annulled_by')): ?> t_strike<?php endif; ?><?php if (! $this->_tpl_vars['expenses_reason']->get('active')): ?> t_inactive<?php endif; ?>"<?php echo $this->_tpl_vars['background_style']; ?>
>
      <td class="t_border hright" nowrap="nowrap">
        <?php if ($this->_tpl_vars['expenses_reason']->get('files_count')): ?>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;<?php echo $this->_tpl_vars['controller_param']; ?>
=expenses_reasons&amp;expenses_reasons=attachments&amp;attachments=<?php echo $this->_tpl_vars['expenses_reason']->get('id'); ?>
">
            <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                 onmouseover="showFiles(this, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', <?php echo $this->_tpl_vars['expenses_reason']->get('id'); ?>
)"
                 onmouseout="mclosetime()" />
          </a>
        <?php endif; ?>
        <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

      </td>
      <?php $_from = $this->_tpl_vars['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['column']):
?>
      <?php if (in_array ( $this->_tpl_vars['column'] , array ( 'comments' , 'emails' , 'history_activity' ) )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/outlooks/templates/td/default_".($this->_tpl_vars['column']).".html", 'smarty_include_vars' => array('single' => $this->_tpl_vars['expenses_reason'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php else: ?>
      <td class="t_border <?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['isSorted']; ?>
<?php if (in_array ( $this->_tpl_vars['column'] , array ( 'fiscal_total_vat' , 'total_vat_rate' , 'total_vat' , 'fiscal_total' , 'fiscal_total_with_vat' , 'total_with_vat' , 'total_remaining_amount' , 'total_paid_amount' , 'total' ) )): ?> hright<?php endif; ?><?php if (! in_array ( $this->_tpl_vars['column'] , array ( 'status' , 'tags' , 'owner' , 'responsible' , 'observer' , 'decision' ) )): ?> <?php echo $this->_tpl_vars['row_link_class']; ?>
<?php endif; ?>"<?php if (! in_array ( $this->_tpl_vars['column'] , array ( 'status' , 'tags' , 'owner' , 'responsible' , 'observer' , 'decision' ) )): ?><?php echo $this->_tpl_vars['row_link']; ?>
<?php endif; ?>>
      <?php if ($this->_tpl_vars['column'] == 'total_vat_rate'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "0.00") : smarty_modifier_default($_tmp, "0.00")); ?>
 %
      <?php elseif (preg_match ( '#^total((_with)?_vat)?$#' , $this->_tpl_vars['column'] )): ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'total_remaining_amount'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('type') != @PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON && ! ( $this->_tpl_vars['expenses_reason']->get('type') == @PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE && $this->_tpl_vars['expenses_reason']->get('payment_status') == 'invoiced' )): ?>
          <?php echo smarty_function_math(array('equation' => "x-y",'x' => ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('total_with_vat'))) ? $this->_run_mod_handler('string_format', true, $_tmp, "%.2F") : smarty_modifier_string_format($_tmp, "%.2F")),'y' => $this->_tpl_vars['expenses_reason']->getFullPaidAmount(),'format' => "%.2F"), $this);?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        <?php else: ?>
          &nbsp;
        <?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'total_paid_amount'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('type') != @PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON && ! ( $this->_tpl_vars['expenses_reason']->get('type') == @PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE && $this->_tpl_vars['expenses_reason']->get('payment_status') == 'invoiced' )): ?>
          <?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->getFullPaidAmount())) ? $this->_run_mod_handler('string_format', true, $_tmp, "%.2F") : smarty_modifier_string_format($_tmp, "%.2F")); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        <?php else: ?>
          &nbsp;
        <?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'status'): ?>
        <?php ob_start(); ?>
          <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['expenses_reason_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['help_finance_documents_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
<?php if (! $this->_tpl_vars['expenses_reason']->get('annulled_by') && $this->_tpl_vars['expenses_reason']->checkPermissions('setstatus')): ?> onclick="changeStatus(<?php echo $this->_tpl_vars['expenses_reason']->get('id'); ?>
, 'finance', 'expenses_reasons')" style="cursor:pointer;"<?php endif; ?>
        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('popup_and_onclick', ob_get_contents());ob_end_clean(); ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('substatus_name')): ?>
          <?php if ($this->_tpl_vars['expenses_reason']->get('icon_name')): ?>
            <img src="<?php echo @PH_FINANCE_DOCUMENTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['expenses_reason']->get('icon_name'); ?>
" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
          <?php else: ?>
            <?php if ($this->_tpl_vars['expenses_reason']->get('status') == 'opened'): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_opened.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php elseif ($this->_tpl_vars['expenses_reason']->get('status') == 'locked'): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_locked.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php elseif ($this->_tpl_vars['expenses_reason']->get('status') == 'finished'): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_closed.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
            <?php endif; ?>
          <?php endif; ?>
          <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('substatus_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        <?php else: ?>
          <?php if ($this->_tpl_vars['expenses_reason']->get('status') == 'opened'): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_opened.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
          <?php elseif ($this->_tpl_vars['expenses_reason']->get('status') == 'locked'): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_locked.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
          <?php elseif ($this->_tpl_vars['expenses_reason']->get('status') == 'finished'): ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_closed.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
          <?php endif; ?>
          <?php ob_start(); ?>finance_documents_status_<?php echo $this->_tpl_vars['expenses_reason']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_param', ob_get_contents());ob_end_clean(); ?>
          <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_param']]; ?>
</span>
        <?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'tags'): ?>
        <div<?php if ($this->_tpl_vars['expenses_reason']->getModelTags() && $this->_tpl_vars['expenses_reason']->get('available_tags_count') > 0 && $this->_tpl_vars['expenses_reason']->checkPermissions('tags_view') && $this->_tpl_vars['expenses_reason']->checkPermissions('tags_edit')): ?> style="padding: 3px 0 3px 0; cursor: pointer;" onclick="changeTags(<?php echo $this->_tpl_vars['expenses_reason']->get('id'); ?>
, 'finance', 'expenses_reasons')" title="<?php echo $this->_config[0]['vars']['tags_change']; ?>
"<?php endif; ?>>
          <?php if (count($this->_tpl_vars['expenses_reason']->get('model_tags')) > 0 && $this->_tpl_vars['expenses_reason']->checkPermissions('tags_view')): ?>
            <?php $_from = $this->_tpl_vars['expenses_reason']->get('model_tags'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tag']):
        $this->_foreach['ti']['iteration']++;
?>
              <span class="<?php echo $this->_tpl_vars['tag']->get('color'); ?>
_pushpin" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span><?php if (! ($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total'])): ?><br /><?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php else: ?>
            &nbsp;
          <?php endif; ?>
        </div>
      <?php elseif (in_array ( $this->_tpl_vars['column'] , array ( 'owner' , 'responsible' , 'observer' , 'decision' ) )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assignments_dashlet.html", 'smarty_include_vars' => array('a_type' => "assignments_".($this->_tpl_vars['column']),'model' => $this->_tpl_vars['expenses_reason'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['column'] == 'name_num'): ?>
        &#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'customer_name_code'): ?>
        &#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('customer_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'trademark_name_code'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('trademark')): ?>&#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('trademark_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php else: ?>&nbsp;<?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'project_name_code'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('project')): ?>&#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('project_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php else: ?>&nbsp;<?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'payment_type'): ?>
        <?php ob_start(); ?>finance_payment_type_<?php if ($this->_tpl_vars['expenses_reason']->get('cheque')): ?>cheque<?php else: ?><?php echo $this->_tpl_vars['expenses_reason']->get('payment_type'); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('payment_type', ob_get_contents());ob_end_clean(); ?>
        <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['payment_type']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'added' || $this->_tpl_vars['column'] == 'modified'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'admit_VAT_credit'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('admit_VAT_credit')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'accounting_period'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('accounting_period') && $this->_tpl_vars['expenses_reason']->get('accounting_period') > '0000-00-00'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('accounting_period'))) ? $this->_run_mod_handler('date_format', true, $_tmp, '%B %Y') : smarty_modifier_date_format($_tmp, '%B %Y')); ?>
<?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'payment_status'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('type') != @PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON): ?>
          <?php ob_start(); ?>finance_payment_status_<?php echo $this->_tpl_vars['expenses_reason']->get('payment_status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('payment_status', ob_get_contents());ob_end_clean(); ?>
          <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['payment_status']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        <?php else: ?>
          &nbsp;
        <?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'handovered_status'): ?>
        <?php ob_start(); ?>finance_handovered_<?php echo $this->_tpl_vars['expenses_reason']->get('handovered_status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('handovered_status', ob_get_contents());ob_end_clean(); ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['handovered_status']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'allocated_status'): ?>
        <?php ob_start(); ?>finance_expenses_reasons_allocated_status_<?php echo $this->_tpl_vars['expenses_reason']->get('allocated_status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('allocated_status', ob_get_contents());ob_end_clean(); ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['allocated_status']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'issue_date' || $this->_tpl_vars['column'] == 'date_of_payment'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'fiscal_event_date'): ?>
        <?php if ($this->_tpl_vars['expenses_reason']->get('fiscal_event_date') != 0 && in_array ( $this->_tpl_vars['expenses_reason']->get('type') , array ( @PH_FINANCE_TYPE_EXPENSES_INVOICE , @PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE , @PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE ) )): ?>
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        <?php else: ?>
          -
        <?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'description'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php else: ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['expenses_reason']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php endif; ?>
      </td>
      <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
      <td class="hcenter" nowrap="nowrap">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['expenses_reason'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  <?php endforeach; else: ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
      <td class="error" colspan="<?php echo smarty_function_math(array('equation' => 'count+2','count' => count($this->_tpl_vars['columns'])), $this);?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
  <?php endif; unset($_from); ?>
  <tr>
    <td colspan="<?php echo smarty_function_math(array('equation' => 'count+2','count' => count($this->_tpl_vars['columns'])), $this);?>
" class="t_footer"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=expenses_reasons&amp;expenses_reasons=dashlet&amp;dashlet=<?php echo $this->_tpl_vars['dashlet_id']; ?>
&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<?php ob_start(); ?>content_dashlet_<?php echo $this->_tpl_vars['dashlet_id']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('container', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'target' => $this->_tpl_vars['container'],'link' => $this->_tpl_vars['link'],'use_ajax' => 1,'hide_rpp' => 1,'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <?php if ($this->_tpl_vars['background_colors']): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_invoices_reasons_legend.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <?php endif; ?>
</table>