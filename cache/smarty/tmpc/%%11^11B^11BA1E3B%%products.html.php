<?php /* Smarty version 2.6.33, created on 2023-07-25 12:36:31
         compiled from /var/www/Nzoom-Hella/_libs/modules/patterns/plugins/hbsteel/templates/products.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/patterns/plugins/hbsteel/templates/products.html', 10, false),)), $this); ?>
<?php $_from = $this->_tpl_vars['products']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['p'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['p']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['k'] => $this->_tpl_vars['product']):
        $this->_foreach['p']['iteration']++;
?>
  <div class="product">
    <div class="title"><?php echo $this->_tpl_vars['product']['name']; ?>
 - <?php echo $this->_tpl_vars['product']['weight']; ?>
 кг.</div>
    <table class="details">
    <?php $_from = $this->_tpl_vars['product']['details']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['kd'] => $this->_tpl_vars['detail']):
?>
      <?php if ($this->_tpl_vars['kd'] % 2 == 0): ?><tr><?php endif; ?>
        <td class="detail">
          <table>
            <tr>
              <td class="name"><div><?php echo ((is_array($_tmp=@$this->_tpl_vars['detail']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</div></td>
              <td class="quantity"><?php echo $this->_tpl_vars['detail']['quantity']; ?>
</td>
              <td class="spacer">X</td>
              <td class="length"><?php echo $this->_tpl_vars['detail']['length']; ?>
</td>
              <td class="image"><?php if ($this->_tpl_vars['detail']['image']): ?><img src="<?php echo $this->_tpl_vars['detail']['image']; ?>
" /><?php endif; ?></td>
            </tr>
          </table>
        <td/>
      <?php if ($this->_tpl_vars['kd'] % 2 != 0): ?></tr><?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
    </table>
  </div>
  <?php if (! ($this->_foreach['p']['iteration'] == $this->_foreach['p']['total'])): ?><div class="break">[system_pagebreak]</div><?php endif; ?>
<?php endforeach; endif; unset($_from); ?>

<?php if ($this->_tpl_vars['is_html']): ?>
<style>
    <?php echo '
    @media screen {
        body {
            display: none;
        }
    }
    '; ?>

</style>
<script type="text/javascript">
    window.print();
    window.onafterprint = window.close;
</script>
<?php endif; ?>
