<?php /* Smarty version 2.6.33, created on 2023-07-13 16:44:05
         compiled from input_file_upload.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_file_upload.html', 70, false),array('modifier', 'encrypt', 'input_file_upload.html', 89, false),array('modifier', 'escape', 'input_file_upload.html', 89, false),array('modifier', 'strip_tags', 'input_file_upload.html', 130, false),array('function', 'help', 'input_file_upload.html', 72, false),array('function', 'getimagesize', 'input_file_upload.html', 87, false),)), $this); ?>
<?php if ($this->_tpl_vars['index']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone']): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] )): ?><?php echo '100%'; ?><?php elseif (is_numeric ( $this->_tpl_vars['width'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['width']; ?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo $this->_tpl_vars['height']; ?>
px<?php elseif ($this->_tpl_vars['height']): ?><?php echo $this->_tpl_vars['height']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>
<?php if (! $this->_tpl_vars['view_mode']): ?><?php $this->assign('view_mode', $this->_tpl_vars['var']['view_mode']); ?><?php endif; ?>
<?php if (! $this->_tpl_vars['thumb_width']): ?><?php $this->assign('thumb_width', $this->_tpl_vars['var']['thumb_width']); ?><?php endif; ?>
<?php if (! $this->_tpl_vars['thumb_height']): ?><?php $this->assign('thumb_height', $this->_tpl_vars['var']['thumb_height']); ?><?php endif; ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden'] || ( $this->_tpl_vars['width'] === '0' )): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
" style="white-space: nowrap;"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td style="width: 10px;"<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap" valign="top">
<?php endif; ?>

  <?php if ($this->_tpl_vars['standalone']): ?><div class="stretch_file_upload_field" style="width: <?php echo $this->_tpl_vars['width']; ?>
; float: left"></div><?php endif; ?>
    <?php if (! empty ( $this->_tpl_vars['value'] ) && is_object ( $this->_tpl_vars['value'] ) && ! $this->_tpl_vars['value']->get('deleted_by')): ?>
      <?php $this->assign('file_info', $this->_tpl_vars['value']); ?>
      <?php if (! $this->_tpl_vars['file_info']->get('not_exist')): ?>
        <?php if ($this->_tpl_vars['view_mode'] == 'thumbnail' && $this->_tpl_vars['file_info']->isImage()): ?>
          <?php echo smarty_function_getimagesize(array('assign' => 'image_dimensions','image_path' => $this->_tpl_vars['file_info']->get('path')), $this);?>

          <div style="position: relative;">
            <img src="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=files&amp;files=viewfile&amp;viewfile=<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_viewfile_') : smarty_modifier_encrypt($_tmp, '_viewfile_')))) ? $this->_run_mod_handler('escape', true, $_tmp, 'url') : smarty_modifier_escape($_tmp, 'url')); ?>
<?php if ($this->_tpl_vars['thumb_width']): ?>&amp;maxwidth=<?php echo $this->_tpl_vars['thumb_width']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['thumb_height']): ?>&amp;maxheight=<?php echo $this->_tpl_vars['thumb_height']; ?>
<?php endif; ?>"
                 onclick="showFullLBImage('<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
', '<?php echo $this->_tpl_vars['var']['label']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['width']; ?>
', '<?php echo $this->_tpl_vars['image_dimensions']['height']; ?>
')"
                 <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ! $this->_tpl_vars['file_info']->get('not_exist') && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
                   onmouseover="img_show_delete_icon('img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>')"
                   onmouseout="img_delete_icon_closetime('');"
                   onload="img_position_delete_icon('img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>');"
                 <?php endif; ?>
                 alt=""
            />
            <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ! $this->_tpl_vars['file_info']->get('not_exist') && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
              <div style="position: absolute; top: 5px; right: 5px; visibility: hidden; display: none;"
                   id="img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
                   onmouseover="img_delete_icon_cancelclosetime();"
                   onmouseout="img_delete_icon_closetime('img_del_div_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>');">
                <a onclick="return confirmAction('delete_file', function(el) { deleteFileAsAdditionalVar(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
'); }, this);">
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" class="pointer" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                </a>
              </div>
            <?php endif; ?>
          </div>
        <?php else: ?>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=viewfile&amp;viewfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
" target="_blank"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['controller_string']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=getfile&amp;getfile=<?php echo $this->_tpl_vars['var']['model_id']; ?>
&amp;file=<?php echo $this->_tpl_vars['file_info']->get('id'); ?>
"> <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
          <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ! $this->_tpl_vars['file_info']->get('not_exist') && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
            <a onclick="return confirmAction('delete_file', function(el) { deleteFileAsAdditionalVar(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
'); }, this);"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" class="pointer" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
          <?php else: ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_delete_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" class="pointer dimmed" />
          <?php endif; ?>
        <?php endif; ?>
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php echo $this->_tpl_vars['file_info']->getIconName(); ?>
.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['open'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['download'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer dimmed" />
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['module'],'delete_file') && ( $this->_tpl_vars['currentUser']->get('id') == $this->_tpl_vars['file_info']->get('added_by') ) && ( $this->_tpl_vars['file_info']->get('model_id') == $this->_tpl_vars['var']['model_id'] )): ?>
          <a onclick="return confirmAction('delete_file', function(el) { deleteFileAsAdditionalVar(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
'); }, this);"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" class="pointer" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
        <?php else: ?>
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
delete.png" width="16" height="16" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_delete_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" class="pointer dimmed" />
        <?php endif; ?>
      <?php endif; ?>
      <input type="hidden" name="dbid_<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>" id="dbid_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['file_info']->get('id'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <?php endif; ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
file_upload.png" width="14" height="14" border="0" alt="<?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
" title="<?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
" style="cursor: pointer;<?php if ($this->_tpl_vars['readonly'] || $this->_tpl_vars['disabled'] || ( ! empty ( $this->_tpl_vars['value'] ) && is_object ( $this->_tpl_vars['value'] ) && ! $this->_tpl_vars['value']->get('deleted_by') )): ?> display: none;<?php endif; ?>" onclick="activateUploadFileBrowser('<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>');" id="file_upld_trigger_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" />

    <input
      type="file"
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="txtbox file_upload<?php if ($this->_tpl_vars['readonly']): ?> readonly<?php endif; ?><?php if ($this->_tpl_vars['class_name']): ?> <?php echo $this->_tpl_vars['class_name']; ?>
<?php endif; ?>"
      style="position: absolute; left: -100000px;<?php if ($this->_tpl_vars['width']): ?> width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?> height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      onfocus="highlight(this);"
      onblur="unhighlight(this);"
      onchange="changeTriggerIcon(this);<?php if ($this->_tpl_vars['onchange']): ?><?php echo $this->_tpl_vars['onchange']; ?>
<?php endif; ?>"
      <?php if ($this->_tpl_vars['onclick']): ?> onclick="<?php echo $this->_tpl_vars['onclick']; ?>
"<?php endif; ?>
      <?php if ($this->_tpl_vars['accept']): ?> accept="<?php echo $this->_tpl_vars['accept']; ?>
"<?php endif; ?>
      <?php if ($this->_tpl_vars['readonly']): ?> readonly="readonly"<?php endif; ?>
      <?php if ($this->_tpl_vars['disabled'] || ( ! empty ( $this->_tpl_vars['value'] ) && is_object ( $this->_tpl_vars['value'] ) && ! $this->_tpl_vars['value']->get('deleted_by') && $this->_tpl_vars['source'] != 'gt2' )): ?> disabled="disabled"<?php endif; ?> />
    <input type="hidden" name="deleteid_<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>" id="deleteid_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php if ($this->_tpl_vars['deleteid'] > 0): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['deleteid'])) ? $this->_run_mod_handler('encrypt', true, $_tmp, '_delete_file_') : smarty_modifier_encrypt($_tmp, '_delete_file_')); ?>
<?php endif; ?>" />

    <script type="text/javascript">
      if (navigator.appName.match(/microsoft/i)) {
        <?php if (! ( $this->_tpl_vars['hidden'] || ( ! empty ( $this->_tpl_vars['value'] ) && is_object ( $this->_tpl_vars['value'] ) && ! $this->_tpl_vars['value']->get('deleted_by') ) )): ?>
          $('<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>').style.position = 'static';
        <?php endif; ?>
        $('file_upld_trigger_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>').style.display = 'none';
      }
    </script>

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => $this->_tpl_vars['custom_id'],'name' => $this->_tpl_vars['name'],'back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>