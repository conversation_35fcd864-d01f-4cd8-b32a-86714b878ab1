<?php /* Smarty version 2.6.33, created on 2023-07-13 16:43:49
         compiled from /var/www/Nzoom-Hella/_libs/themes/Default/templates/system_settings_active.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Default/templates/system_settings_active.html', 4, false),)), $this); ?>
  <tr<?php if (! $this->_tpl_vars['viewable']): ?> style="display: none;"<?php endif; ?>>
    <td class="labelbox" nowrap="nowrap">
    <?php if ($this->_tpl_vars['action'] == 'view'): ?>
      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['activation'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:
    <?php else: ?>
      <a name="error_activate"><label for="active1"<?php if ($this->_tpl_vars['messages']->getErrors('activate')): ?> class="error"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['activation'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</label></a>
    <?php endif; ?>
    </td>
    <td class="unrequired">&nbsp;</td>
    <td nowrap="nowrap">
    <?php if ($this->_tpl_vars['action'] == 'view'): ?>
      <?php if ($this->_tpl_vars['object']->isActivated()): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['activated'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivated'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
    <?php else: ?>
      <?php ob_start(); ?><?php echo ''; ?><?php if (isset ( $_POST['active'] )): ?><?php echo ''; ?><?php echo $_POST['active']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['object']->isDefined('active') && $this->_tpl_vars['object']->get('active') == 0): ?><?php echo '0'; ?><?php else: ?><?php echo '1'; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('activated', ob_get_contents());ob_end_clean(); ?>
      <input type="radio" name="active" id="active1" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['activated'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" <?php if (! $this->_tpl_vars['object']->checkPermissions('activate')): ?> disabled="disabled"<?php endif; ?><?php if ($this->_tpl_vars['activated']): ?> checked="checked"<?php endif; ?> /><label for="active1"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['activated'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
      <input type="radio" name="active" id="active2" value="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivated'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! $this->_tpl_vars['object']->checkPermissions('deactivate')): ?> disabled="disabled"<?php endif; ?><?php if (! $this->_tpl_vars['activated']): ?> checked="checked"<?php endif; ?> /><label for="active2"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivated'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
      <?php if (! $this->_tpl_vars['object']->checkPermissions('activate') && $this->_tpl_vars['currentUser']->get('is_portal')): ?>
        <input type="hidden" name="active" id="active3" value="1" />
      <?php endif; ?>
    <?php endif; ?>
    </td>
  </tr>