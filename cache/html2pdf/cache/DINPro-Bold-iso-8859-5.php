<?php
$type='TrueType';
$name='';
$desc=array('Ascent'=>1015,'Descent'=>-206,'CapHeight'=>1015,'Flags'=>32,'FontBBox'=>'[-208 -234 1174 1195]','ItalicAngle'=>0,'StemV'=>70);
$up=-263;
$ut=87;
$cw=array(
	chr(0)=>0,chr(1)=>1000,chr(2)=>1000,chr(3)=>1000,chr(4)=>1000,chr(5)=>1000,chr(6)=>1000,chr(7)=>1000,chr(8)=>1000,chr(9)=>1000,chr(10)=>1000,chr(11)=>1000,chr(12)=>1000,chr(13)=>232,chr(14)=>1000,chr(15)=>1000,chr(16)=>1000,chr(17)=>1000,chr(18)=>1000,chr(19)=>1000,chr(20)=>1000,chr(21)=>1000,
	chr(22)=>1000,chr(23)=>1000,chr(24)=>1000,chr(25)=>1000,chr(26)=>1000,chr(27)=>1000,chr(28)=>1000,chr(29)=>1000,chr(30)=>1000,chr(31)=>1000,' '=>232,'!'=>347,'"'=>464,'#'=>691,'$'=>619,'%'=>848,'&'=>721,'\''=>259,'('=>336,')'=>336,'*'=>494,'+'=>543,
	','=>279,'-'=>426,'.'=>285,'/'=>403,'0'=>543,'1'=>543,'2'=>543,'3'=>543,'4'=>543,'5'=>543,'6'=>543,'7'=>543,'8'=>543,'9'=>543,':'=>309,';'=>309,'<'=>543,'='=>543,'>'=>543,'?'=>523,'@'=>758,'A'=>638,
	'B'=>658,'C'=>620,'D'=>659,'E'=>605,'F'=>590,'G'=>649,'H'=>685,'I'=>305,'J'=>511,'K'=>665,'L'=>575,'M'=>811,'N'=>711,'O'=>644,'P'=>628,'Q'=>643,'R'=>654,'S'=>591,'T'=>571,'U'=>668,'V'=>581,'W'=>887,
	'X'=>603,'Y'=>572,'Z'=>548,'['=>378,'\\'=>403,']'=>378,'^'=>602,'_'=>602,'`'=>500,'a'=>534,'b'=>560,'c'=>480,'d'=>561,'e'=>548,'f'=>342,'g'=>555,'h'=>573,'i'=>275,'j'=>275,'k'=>559,'l'=>325,'m'=>876,
	'n'=>573,'o'=>543,'p'=>560,'q'=>561,'r'=>455,'s'=>501,'t'=>352,'u'=>573,'v'=>493,'w'=>759,'x'=>527,'y'=>494,'z'=>478,'{'=>457,'|'=>350,'}'=>457,'~'=>606,chr(127)=>1000,chr(128)=>1000,chr(129)=>1000,chr(130)=>1000,chr(131)=>1000,
	chr(132)=>1000,chr(133)=>1000,chr(134)=>1000,chr(135)=>1000,chr(136)=>1000,chr(137)=>1000,chr(138)=>1000,chr(139)=>1000,chr(140)=>1000,chr(141)=>1000,chr(142)=>1000,chr(143)=>1000,chr(144)=>1000,chr(145)=>1000,chr(146)=>1000,chr(147)=>1000,chr(148)=>1000,chr(149)=>1000,chr(150)=>1000,chr(151)=>1000,chr(152)=>1000,chr(153)=>1000,
	chr(154)=>1000,chr(155)=>1000,chr(156)=>1000,chr(157)=>1000,chr(158)=>1000,chr(159)=>1000,chr(160)=>232,chr(161)=>605,chr(162)=>748,chr(163)=>561,chr(164)=>619,chr(165)=>591,chr(166)=>305,chr(167)=>305,chr(168)=>511,chr(169)=>1008,chr(170)=>1008,chr(171)=>748,chr(172)=>641,chr(173)=>426,chr(174)=>581,chr(175)=>686,
	chr(176)=>638,chr(177)=>654,chr(178)=>658,chr(179)=>561,chr(180)=>705,chr(181)=>605,chr(182)=>941,chr(183)=>606,chr(184)=>706,chr(185)=>706,chr(186)=>641,chr(187)=>686,chr(188)=>811,chr(189)=>685,chr(190)=>644,chr(191)=>685,chr(192)=>628,chr(193)=>620,chr(194)=>571,chr(195)=>581,chr(196)=>814,chr(197)=>603,
	chr(198)=>712,chr(199)=>648,chr(200)=>988,chr(201)=>1014,chr(202)=>727,chr(203)=>893,chr(204)=>628,chr(205)=>619,chr(206)=>914,chr(207)=>650,chr(208)=>534,chr(209)=>548,chr(210)=>550,chr(211)=>434,chr(212)=>580,chr(213)=>548,chr(214)=>763,chr(215)=>496,chr(216)=>589,chr(217)=>589,chr(218)=>534,chr(219)=>571,
	chr(220)=>677,chr(221)=>573,chr(222)=>543,chr(223)=>568,chr(224)=>560,chr(225)=>480,chr(226)=>456,chr(227)=>494,chr(228)=>726,chr(229)=>527,chr(230)=>594,chr(231)=>548,chr(232)=>822,chr(233)=>843,chr(234)=>590,chr(235)=>763,chr(236)=>529,chr(237)=>480,chr(238)=>774,chr(239)=>535,chr(240)=>965,chr(241)=>548,
	chr(242)=>573,chr(243)=>434,chr(244)=>480,chr(245)=>501,chr(246)=>275,chr(247)=>275,chr(248)=>275,chr(249)=>824,chr(250)=>827,chr(251)=>573,chr(252)=>534,chr(253)=>531,chr(254)=>494,chr(255)=>573);
$enc='iso-8859-5';
$diff='128 /u0080 /u0081 /u0082 /u0083 /u0084 /u0085 /u0086 /u0087 /u0088 /u0089 /u008A /u008B /u008C /u008D /u008E /u008F /u0090 /u0091 /u0092 /u0093 /u0094 /u0095 /u0096 /u0097 /u0098 /u0099 /u009A /u009B /u009C /u009D /u009E /u009F 161 /afii10023 /afii10051 /afii10052 /afii10053 /afii10054 /afii10055 /afii10056 /afii10057 /afii10058 /afii10059 /afii10060 /afii10061 174 /afii10062 /afii10145 /afii10017 /afii10018 /afii10019 /afii10020 /afii10021 /afii10022 /afii10024 /afii10025 /afii10026 /afii10027 /afii10028 /afii10029 /afii10030 /afii10031 /afii10032 /afii10033 /afii10034 /afii10035 /afii10036 /afii10037 /afii10038 /afii10039 /afii10040 /afii10041 /afii10042 /afii10043 /afii10044 /afii10045 /afii10046 /afii10047 /afii10048 /afii10049 /afii10065 /afii10066 /afii10067 /afii10068 /afii10069 /afii10070 /afii10072 /afii10073 /afii10074 /afii10075 /afii10076 /afii10077 /afii10078 /afii10079 /afii10080 /afii10081 /afii10082 /afii10083 /afii10084 /afii10085 /afii10086 /afii10087 /afii10088 /afii10089 /afii10090 /afii10091 /afii10092 /afii10093 /afii10094 /afii10095 /afii10096 /afii10097 /numero /afii10071 /afii10099 /afii10100 /afii10101 /afii10102 /afii10103 /afii10104 /afii10105 /afii10106 /afii10107 /afii10108 /afii10109 /section /afii10110 /afii10193';
$cmap=array(
0x00 => 0x0000,
0x01 => 0x0001,
0x02 => 0x0002,
0x03 => 0x0003,
0x04 => 0x0004,
0x05 => 0x0005,
0x06 => 0x0006,
0x07 => 0x0007,
0x08 => 0x0008,
0x09 => 0x0009,
0x0A => 0x000A,
0x0B => 0x000B,
0x0C => 0x000C,
0x0D => 0x000D,
0x0E => 0x000E,
0x0F => 0x000F,
0x10 => 0x0010,
0x11 => 0x0011,
0x12 => 0x0012,
0x13 => 0x0013,
0x14 => 0x0014,
0x15 => 0x0015,
0x16 => 0x0016,
0x17 => 0x0017,
0x18 => 0x0018,
0x19 => 0x0019,
0x1A => 0x001A,
0x1B => 0x001B,
0x1C => 0x001C,
0x1D => 0x001D,
0x1E => 0x001E,
0x1F => 0x001F,
0x20 => 0x0020,
0x21 => 0x0021,
0x22 => 0x0022,
0x23 => 0x0023,
0x24 => 0x0024,
0x25 => 0x0025,
0x26 => 0x0026,
0x27 => 0x0027,
0x28 => 0x0028,
0x29 => 0x0029,
0x2A => 0x002A,
0x2B => 0x002B,
0x2C => 0x002C,
0x2D => 0x002D,
0x2E => 0x002E,
0x2F => 0x002F,
0x30 => 0x0030,
0x31 => 0x0031,
0x32 => 0x0032,
0x33 => 0x0033,
0x34 => 0x0034,
0x35 => 0x0035,
0x36 => 0x0036,
0x37 => 0x0037,
0x38 => 0x0038,
0x39 => 0x0039,
0x3A => 0x003A,
0x3B => 0x003B,
0x3C => 0x003C,
0x3D => 0x003D,
0x3E => 0x003E,
0x3F => 0x003F,
0x40 => 0x0040,
0x41 => 0x0041,
0x42 => 0x0042,
0x43 => 0x0043,
0x44 => 0x0044,
0x45 => 0x0045,
0x46 => 0x0046,
0x47 => 0x0047,
0x48 => 0x0048,
0x49 => 0x0049,
0x4A => 0x004A,
0x4B => 0x004B,
0x4C => 0x004C,
0x4D => 0x004D,
0x4E => 0x004E,
0x4F => 0x004F,
0x50 => 0x0050,
0x51 => 0x0051,
0x52 => 0x0052,
0x53 => 0x0053,
0x54 => 0x0054,
0x55 => 0x0055,
0x56 => 0x0056,
0x57 => 0x0057,
0x58 => 0x0058,
0x59 => 0x0059,
0x5A => 0x005A,
0x5B => 0x005B,
0x5C => 0x005C,
0x5D => 0x005D,
0x5E => 0x005E,
0x5F => 0x005F,
0x60 => 0x0060,
0x61 => 0x0061,
0x62 => 0x0062,
0x63 => 0x0063,
0x64 => 0x0064,
0x65 => 0x0065,
0x66 => 0x0066,
0x67 => 0x0067,
0x68 => 0x0068,
0x69 => 0x0069,
0x6A => 0x006A,
0x6B => 0x006B,
0x6C => 0x006C,
0x6D => 0x006D,
0x6E => 0x006E,
0x6F => 0x006F,
0x70 => 0x0070,
0x71 => 0x0071,
0x72 => 0x0072,
0x73 => 0x0073,
0x74 => 0x0074,
0x75 => 0x0075,
0x76 => 0x0076,
0x77 => 0x0077,
0x78 => 0x0078,
0x79 => 0x0079,
0x7A => 0x007A,
0x7B => 0x007B,
0x7C => 0x007C,
0x7D => 0x007D,
0x7E => 0x007E,
0x7F => 0x007F,
0x80 => 0x0080,
0x81 => 0x0081,
0x82 => 0x0082,
0x83 => 0x0083,
0x84 => 0x0084,
0x85 => 0x0085,
0x86 => 0x0086,
0x87 => 0x0087,
0x88 => 0x0088,
0x89 => 0x0089,
0x8A => 0x008A,
0x8B => 0x008B,
0x8C => 0x008C,
0x8D => 0x008D,
0x8E => 0x008E,
0x8F => 0x008F,
0x90 => 0x0090,
0x91 => 0x0091,
0x92 => 0x0092,
0x93 => 0x0093,
0x94 => 0x0094,
0x95 => 0x0095,
0x96 => 0x0096,
0x97 => 0x0097,
0x98 => 0x0098,
0x99 => 0x0099,
0x9A => 0x009A,
0x9B => 0x009B,
0x9C => 0x009C,
0x9D => 0x009D,
0x9E => 0x009E,
0x9F => 0x009F,
0xA0 => 0x00A0,
0xA1 => 0x0401,
0xA2 => 0x0402,
0xA3 => 0x0403,
0xA4 => 0x0404,
0xA5 => 0x0405,
0xA6 => 0x0406,
0xA7 => 0x0407,
0xA8 => 0x0408,
0xA9 => 0x0409,
0xAA => 0x040A,
0xAB => 0x040B,
0xAC => 0x040C,
0xAD => 0x00AD,
0xAE => 0x040E,
0xAF => 0x040F,
0xB0 => 0x0410,
0xB1 => 0x0411,
0xB2 => 0x0412,
0xB3 => 0x0413,
0xB4 => 0x0414,
0xB5 => 0x0415,
0xB6 => 0x0416,
0xB7 => 0x0417,
0xB8 => 0x0418,
0xB9 => 0x0419,
0xBA => 0x041A,
0xBB => 0x041B,
0xBC => 0x041C,
0xBD => 0x041D,
0xBE => 0x041E,
0xBF => 0x041F,
0xC0 => 0x0420,
0xC1 => 0x0421,
0xC2 => 0x0422,
0xC3 => 0x0423,
0xC4 => 0x0424,
0xC5 => 0x0425,
0xC6 => 0x0426,
0xC7 => 0x0427,
0xC8 => 0x0428,
0xC9 => 0x0429,
0xCA => 0x042A,
0xCB => 0x042B,
0xCC => 0x042C,
0xCD => 0x042D,
0xCE => 0x042E,
0xCF => 0x042F,
0xD0 => 0x0430,
0xD1 => 0x0431,
0xD2 => 0x0432,
0xD3 => 0x0433,
0xD4 => 0x0434,
0xD5 => 0x0435,
0xD6 => 0x0436,
0xD7 => 0x0437,
0xD8 => 0x0438,
0xD9 => 0x0439,
0xDA => 0x043A,
0xDB => 0x043B,
0xDC => 0x043C,
0xDD => 0x043D,
0xDE => 0x043E,
0xDF => 0x043F,
0xE0 => 0x0440,
0xE1 => 0x0441,
0xE2 => 0x0442,
0xE3 => 0x0443,
0xE4 => 0x0444,
0xE5 => 0x0445,
0xE6 => 0x0446,
0xE7 => 0x0447,
0xE8 => 0x0448,
0xE9 => 0x0449,
0xEA => 0x044A,
0xEB => 0x044B,
0xEC => 0x044C,
0xED => 0x044D,
0xEE => 0x044E,
0xEF => 0x044F,
0xF0 => 0x2116,
0xF1 => 0x0451,
0xF2 => 0x0452,
0xF3 => 0x0453,
0xF4 => 0x0454,
0xF5 => 0x0455,
0xF6 => 0x0456,
0xF7 => 0x0457,
0xF8 => 0x0458,
0xF9 => 0x0459,
0xFA => 0x045A,
0xFB => 0x045B,
0xFC => 0x045C,
0xFD => 0x00A7,
0xFE => 0x045E,
0xFF => 0x045F,
);
$file='DINPro-Bold.z';
$originalsize=138336;
?>
