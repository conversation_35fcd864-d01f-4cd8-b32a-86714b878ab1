<?php
$type='TrueType';
$name='TimesNewRomanPSMT';
$desc=array('Ascent'=>891,'Descent'=>-216,'CapHeight'=>891,'Flags'=>32,'FontBBox'=>'[-568 -307 2000 1007]','ItalicAngle'=>0,'StemV'=>70,'MissingWidth'=>778);
$up=-109;
$ut=49;
$cw=array(
	chr(0)=>778,chr(1)=>778,chr(2)=>778,chr(3)=>778,chr(4)=>778,chr(5)=>778,chr(6)=>778,chr(7)=>778,chr(8)=>778,chr(9)=>778,chr(10)=>778,chr(11)=>778,chr(12)=>778,chr(13)=>778,chr(14)=>778,chr(15)=>778,chr(16)=>778,chr(17)=>778,chr(18)=>778,chr(19)=>778,chr(20)=>778,chr(21)=>778,
	chr(22)=>778,chr(23)=>778,chr(24)=>778,chr(25)=>778,chr(26)=>778,chr(27)=>778,chr(28)=>778,chr(29)=>778,chr(30)=>778,chr(31)=>778,' '=>250,'!'=>333,'"'=>408,'#'=>500,'$'=>500,'%'=>833,'&'=>778,'\''=>180,'('=>333,')'=>333,'*'=>500,'+'=>564,
	','=>250,'-'=>333,'.'=>250,'/'=>278,'0'=>500,'1'=>500,'2'=>500,'3'=>500,'4'=>500,'5'=>500,'6'=>500,'7'=>500,'8'=>500,'9'=>500,':'=>278,';'=>278,'<'=>564,'='=>564,'>'=>564,'?'=>444,'@'=>921,'A'=>722,
	'B'=>667,'C'=>667,'D'=>722,'E'=>611,'F'=>556,'G'=>722,'H'=>722,'I'=>333,'J'=>389,'K'=>722,'L'=>611,'M'=>889,'N'=>722,'O'=>722,'P'=>556,'Q'=>722,'R'=>667,'S'=>556,'T'=>611,'U'=>722,'V'=>722,'W'=>944,
	'X'=>722,'Y'=>722,'Z'=>611,'['=>333,'\\'=>278,']'=>333,'^'=>469,'_'=>500,'`'=>333,'a'=>444,'b'=>500,'c'=>444,'d'=>500,'e'=>444,'f'=>333,'g'=>500,'h'=>500,'i'=>278,'j'=>278,'k'=>500,'l'=>278,'m'=>778,
	'n'=>500,'o'=>500,'p'=>500,'q'=>500,'r'=>333,'s'=>389,'t'=>278,'u'=>500,'v'=>500,'w'=>722,'x'=>500,'y'=>500,'z'=>444,'{'=>480,'|'=>200,'}'=>480,'~'=>541,chr(127)=>778,chr(128)=>778,chr(129)=>778,chr(130)=>778,chr(131)=>778,
	chr(132)=>778,chr(133)=>778,chr(134)=>778,chr(135)=>778,chr(136)=>778,chr(137)=>778,chr(138)=>778,chr(139)=>778,chr(140)=>778,chr(141)=>778,chr(142)=>778,chr(143)=>778,chr(144)=>778,chr(145)=>778,chr(146)=>778,chr(147)=>778,chr(148)=>778,chr(149)=>778,chr(150)=>778,chr(151)=>778,chr(152)=>778,chr(153)=>778,
	chr(154)=>778,chr(155)=>778,chr(156)=>778,chr(157)=>778,chr(158)=>778,chr(159)=>778,chr(160)=>778,chr(161)=>333,chr(162)=>500,chr(163)=>500,chr(164)=>500,chr(165)=>500,chr(166)=>200,chr(167)=>500,chr(168)=>333,chr(169)=>760,chr(170)=>276,chr(171)=>500,chr(172)=>564,chr(173)=>778,chr(174)=>760,chr(175)=>500,
	chr(176)=>400,chr(177)=>549,chr(178)=>300,chr(179)=>300,chr(180)=>333,chr(181)=>536,chr(182)=>453,chr(183)=>333,chr(184)=>333,chr(185)=>300,chr(186)=>310,chr(187)=>500,chr(188)=>750,chr(189)=>750,chr(190)=>750,chr(191)=>444,chr(192)=>722,chr(193)=>722,chr(194)=>722,chr(195)=>722,chr(196)=>722,chr(197)=>722,
	chr(198)=>889,chr(199)=>667,chr(200)=>611,chr(201)=>611,chr(202)=>611,chr(203)=>611,chr(204)=>333,chr(205)=>333,chr(206)=>333,chr(207)=>333,chr(208)=>722,chr(209)=>722,chr(210)=>722,chr(211)=>722,chr(212)=>722,chr(213)=>722,chr(214)=>722,chr(215)=>564,chr(216)=>722,chr(217)=>722,chr(218)=>722,chr(219)=>722,
	chr(220)=>722,chr(221)=>722,chr(222)=>556,chr(223)=>500,chr(224)=>444,chr(225)=>444,chr(226)=>444,chr(227)=>444,chr(228)=>444,chr(229)=>444,chr(230)=>667,chr(231)=>444,chr(232)=>444,chr(233)=>444,chr(234)=>444,chr(235)=>444,chr(236)=>278,chr(237)=>278,chr(238)=>278,chr(239)=>278,chr(240)=>500,chr(241)=>500,
	chr(242)=>500,chr(243)=>500,chr(244)=>500,chr(245)=>500,chr(246)=>500,chr(247)=>549,chr(248)=>500,chr(249)=>500,chr(250)=>500,chr(251)=>500,chr(252)=>500,chr(253)=>500,chr(254)=>500,chr(255)=>500);
$enc='iso-8859-1';
$diff='128 /u0080 /u0081 /u0082 /u0083 /u0084 /u0085 /u0086 /u0087 /u0088 /u0089 /u008A /u008B /u008C /u008D /u008E /u008F /u0090 /u0091 /u0092 /u0093 /u0094 /u0095 /u0096 /u0097 /u0098 /u0099 /u009A /u009B /u009C /u009D /u009E /u009F';
$cmap=array(
0x00 => 0x0000,
0x01 => 0x0001,
0x02 => 0x0002,
0x03 => 0x0003,
0x04 => 0x0004,
0x05 => 0x0005,
0x06 => 0x0006,
0x07 => 0x0007,
0x08 => 0x0008,
0x09 => 0x0009,
0x0A => 0x000A,
0x0B => 0x000B,
0x0C => 0x000C,
0x0D => 0x000D,
0x0E => 0x000E,
0x0F => 0x000F,
0x10 => 0x0010,
0x11 => 0x0011,
0x12 => 0x0012,
0x13 => 0x0013,
0x14 => 0x0014,
0x15 => 0x0015,
0x16 => 0x0016,
0x17 => 0x0017,
0x18 => 0x0018,
0x19 => 0x0019,
0x1A => 0x001A,
0x1B => 0x001B,
0x1C => 0x001C,
0x1D => 0x001D,
0x1E => 0x001E,
0x1F => 0x001F,
0x20 => 0x0020,
0x21 => 0x0021,
0x22 => 0x0022,
0x23 => 0x0023,
0x24 => 0x0024,
0x25 => 0x0025,
0x26 => 0x0026,
0x27 => 0x0027,
0x28 => 0x0028,
0x29 => 0x0029,
0x2A => 0x002A,
0x2B => 0x002B,
0x2C => 0x002C,
0x2D => 0x002D,
0x2E => 0x002E,
0x2F => 0x002F,
0x30 => 0x0030,
0x31 => 0x0031,
0x32 => 0x0032,
0x33 => 0x0033,
0x34 => 0x0034,
0x35 => 0x0035,
0x36 => 0x0036,
0x37 => 0x0037,
0x38 => 0x0038,
0x39 => 0x0039,
0x3A => 0x003A,
0x3B => 0x003B,
0x3C => 0x003C,
0x3D => 0x003D,
0x3E => 0x003E,
0x3F => 0x003F,
0x40 => 0x0040,
0x41 => 0x0041,
0x42 => 0x0042,
0x43 => 0x0043,
0x44 => 0x0044,
0x45 => 0x0045,
0x46 => 0x0046,
0x47 => 0x0047,
0x48 => 0x0048,
0x49 => 0x0049,
0x4A => 0x004A,
0x4B => 0x004B,
0x4C => 0x004C,
0x4D => 0x004D,
0x4E => 0x004E,
0x4F => 0x004F,
0x50 => 0x0050,
0x51 => 0x0051,
0x52 => 0x0052,
0x53 => 0x0053,
0x54 => 0x0054,
0x55 => 0x0055,
0x56 => 0x0056,
0x57 => 0x0057,
0x58 => 0x0058,
0x59 => 0x0059,
0x5A => 0x005A,
0x5B => 0x005B,
0x5C => 0x005C,
0x5D => 0x005D,
0x5E => 0x005E,
0x5F => 0x005F,
0x60 => 0x0060,
0x61 => 0x0061,
0x62 => 0x0062,
0x63 => 0x0063,
0x64 => 0x0064,
0x65 => 0x0065,
0x66 => 0x0066,
0x67 => 0x0067,
0x68 => 0x0068,
0x69 => 0x0069,
0x6A => 0x006A,
0x6B => 0x006B,
0x6C => 0x006C,
0x6D => 0x006D,
0x6E => 0x006E,
0x6F => 0x006F,
0x70 => 0x0070,
0x71 => 0x0071,
0x72 => 0x0072,
0x73 => 0x0073,
0x74 => 0x0074,
0x75 => 0x0075,
0x76 => 0x0076,
0x77 => 0x0077,
0x78 => 0x0078,
0x79 => 0x0079,
0x7A => 0x007A,
0x7B => 0x007B,
0x7C => 0x007C,
0x7D => 0x007D,
0x7E => 0x007E,
0x7F => 0x007F,
0x80 => 0x0080,
0x81 => 0x0081,
0x82 => 0x0082,
0x83 => 0x0083,
0x84 => 0x0084,
0x85 => 0x0085,
0x86 => 0x0086,
0x87 => 0x0087,
0x88 => 0x0088,
0x89 => 0x0089,
0x8A => 0x008A,
0x8B => 0x008B,
0x8C => 0x008C,
0x8D => 0x008D,
0x8E => 0x008E,
0x8F => 0x008F,
0x90 => 0x0090,
0x91 => 0x0091,
0x92 => 0x0092,
0x93 => 0x0093,
0x94 => 0x0094,
0x95 => 0x0095,
0x96 => 0x0096,
0x97 => 0x0097,
0x98 => 0x0098,
0x99 => 0x0099,
0x9A => 0x009A,
0x9B => 0x009B,
0x9C => 0x009C,
0x9D => 0x009D,
0x9E => 0x009E,
0x9F => 0x009F,
0xA0 => 0x00A0,
0xA1 => 0x00A1,
0xA2 => 0x00A2,
0xA3 => 0x00A3,
0xA4 => 0x00A4,
0xA5 => 0x00A5,
0xA6 => 0x00A6,
0xA7 => 0x00A7,
0xA8 => 0x00A8,
0xA9 => 0x00A9,
0xAA => 0x00AA,
0xAB => 0x00AB,
0xAC => 0x00AC,
0xAD => 0x00AD,
0xAE => 0x00AE,
0xAF => 0x00AF,
0xB0 => 0x00B0,
0xB1 => 0x00B1,
0xB2 => 0x00B2,
0xB3 => 0x00B3,
0xB4 => 0x00B4,
0xB5 => 0x00B5,
0xB6 => 0x00B6,
0xB7 => 0x00B7,
0xB8 => 0x00B8,
0xB9 => 0x00B9,
0xBA => 0x00BA,
0xBB => 0x00BB,
0xBC => 0x00BC,
0xBD => 0x00BD,
0xBE => 0x00BE,
0xBF => 0x00BF,
0xC0 => 0x00C0,
0xC1 => 0x00C1,
0xC2 => 0x00C2,
0xC3 => 0x00C3,
0xC4 => 0x00C4,
0xC5 => 0x00C5,
0xC6 => 0x00C6,
0xC7 => 0x00C7,
0xC8 => 0x00C8,
0xC9 => 0x00C9,
0xCA => 0x00CA,
0xCB => 0x00CB,
0xCC => 0x00CC,
0xCD => 0x00CD,
0xCE => 0x00CE,
0xCF => 0x00CF,
0xD0 => 0x00D0,
0xD1 => 0x00D1,
0xD2 => 0x00D2,
0xD3 => 0x00D3,
0xD4 => 0x00D4,
0xD5 => 0x00D5,
0xD6 => 0x00D6,
0xD7 => 0x00D7,
0xD8 => 0x00D8,
0xD9 => 0x00D9,
0xDA => 0x00DA,
0xDB => 0x00DB,
0xDC => 0x00DC,
0xDD => 0x00DD,
0xDE => 0x00DE,
0xDF => 0x00DF,
0xE0 => 0x00E0,
0xE1 => 0x00E1,
0xE2 => 0x00E2,
0xE3 => 0x00E3,
0xE4 => 0x00E4,
0xE5 => 0x00E5,
0xE6 => 0x00E6,
0xE7 => 0x00E7,
0xE8 => 0x00E8,
0xE9 => 0x00E9,
0xEA => 0x00EA,
0xEB => 0x00EB,
0xEC => 0x00EC,
0xED => 0x00ED,
0xEE => 0x00EE,
0xEF => 0x00EF,
0xF0 => 0x00F0,
0xF1 => 0x00F1,
0xF2 => 0x00F2,
0xF3 => 0x00F3,
0xF4 => 0x00F4,
0xF5 => 0x00F5,
0xF6 => 0x00F6,
0xF7 => 0x00F7,
0xF8 => 0x00F8,
0xF9 => 0x00F9,
0xFA => 0x00FA,
0xFB => 0x00FB,
0xFC => 0x00FC,
0xFD => 0x00FD,
0xFE => 0x00FE,
0xFF => 0x00FF,
);
$file='times.z';
$originalsize=409280;
?>
