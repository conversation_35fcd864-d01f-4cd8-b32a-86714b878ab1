<?php
$type='TrueType';
$name='';
$desc=array('Ascent'=>928,'Descent'=>-236,'CapHeight'=>928,'Flags'=>32,'FontBBox'=>'[-619.140625 -292.96875 1258.7890625 1068.84765625]','ItalicAngle'=>0,'StemV'=>70);
$up=-76;
$ut=49;
$cw=array(
	chr(0)=>1000,chr(1)=>1000,chr(2)=>1000,chr(3)=>1000,chr(4)=>1000,chr(5)=>1000,chr(6)=>1000,chr(7)=>1000,chr(8)=>1000,chr(9)=>1000,chr(10)=>1000,chr(11)=>1000,chr(12)=>1000,chr(13)=>1000,chr(14)=>1000,chr(15)=>1000,chr(16)=>1000,chr(17)=>1000,chr(18)=>1000,chr(19)=>1000,chr(20)=>1000,chr(21)=>1000,
	chr(22)=>1000,chr(23)=>1000,chr(24)=>1000,chr(25)=>1000,chr(26)=>1000,chr(27)=>1000,chr(28)=>1000,chr(29)=>1000,chr(30)=>1000,chr(31)=>1000,' '=>259,'!'=>286,'"'=>472,'#'=>645,'$'=>550,'%'=>880,'&'=>722,'\''=>266,'('=>338,')'=>338,'*'=>544,'+'=>550,
	','=>290,'-'=>321,'.'=>285,'/'=>413,'0'=>550,'1'=>550,'2'=>550,'3'=>550,'4'=>550,'5'=>550,'6'=>550,'7'=>550,'8'=>550,'9'=>550,':'=>285,';'=>290,'<'=>550,'='=>550,'>'=>550,'?'=>458,'@'=>866,'A'=>649,
	'B'=>642,'C'=>637,'D'=>700,'E'=>560,'F'=>548,'G'=>724,'H'=>725,'I'=>389,'J'=>331,'K'=>633,'L'=>535,'M'=>913,'N'=>783,'O'=>755,'P'=>598,'Q'=>755,'R'=>629,'S'=>523,'T'=>548,'U'=>715,'V'=>609,'W'=>926,
	'X'=>626,'Y'=>583,'Z'=>539,'['=>331,'\\'=>413,']'=>331,'^'=>532,'_'=>411,'`'=>577,'a'=>574,'b'=>607,'c'=>499,'d'=>607,'e'=>581,'f'=>387,'g'=>551,'h'=>626,'i'=>305,'j'=>305,'k'=>589,'l'=>305,'m'=>941,
	'n'=>626,'o'=>599,'p'=>607,'q'=>607,'r'=>434,'s'=>480,'t'=>414,'u'=>626,'v'=>539,'w'=>806,'x'=>547,'y'=>539,'z'=>457,'{'=>363,'|'=>550,'}'=>363,'~'=>550,chr(127)=>1000,chr(128)=>1000,chr(129)=>1000,chr(130)=>1000,chr(131)=>1000,
	chr(132)=>1000,chr(133)=>1000,chr(134)=>1000,chr(135)=>1000,chr(136)=>1000,chr(137)=>1000,chr(138)=>1000,chr(139)=>1000,chr(140)=>1000,chr(141)=>1000,chr(142)=>1000,chr(143)=>1000,chr(144)=>1000,chr(145)=>1000,chr(146)=>1000,chr(147)=>1000,chr(148)=>1000,chr(149)=>1000,chr(150)=>1000,chr(151)=>1000,chr(152)=>1000,chr(153)=>1000,
	chr(154)=>1000,chr(155)=>1000,chr(156)=>1000,chr(157)=>1000,chr(158)=>1000,chr(159)=>1000,chr(160)=>259,chr(161)=>286,chr(162)=>550,chr(163)=>550,chr(164)=>550,chr(165)=>550,chr(166)=>550,chr(167)=>485,chr(168)=>577,chr(169)=>832,chr(170)=>362,chr(171)=>584,chr(172)=>550,chr(173)=>321,chr(174)=>832,chr(175)=>500,
	chr(176)=>428,chr(177)=>550,chr(178)=>378,chr(179)=>378,chr(180)=>577,chr(181)=>629,chr(182)=>654,chr(183)=>285,chr(184)=>205,chr(185)=>378,chr(186)=>368,chr(187)=>584,chr(188)=>880,chr(189)=>880,chr(190)=>880,chr(191)=>458,chr(192)=>649,chr(193)=>649,chr(194)=>649,chr(195)=>649,chr(196)=>649,chr(197)=>649,
	chr(198)=>921,chr(199)=>637,chr(200)=>560,chr(201)=>560,chr(202)=>560,chr(203)=>560,chr(204)=>389,chr(205)=>389,chr(206)=>389,chr(207)=>389,chr(208)=>700,chr(209)=>783,chr(210)=>755,chr(211)=>755,chr(212)=>755,chr(213)=>755,chr(214)=>755,chr(215)=>550,chr(216)=>755,chr(217)=>715,chr(218)=>715,chr(219)=>715,
	chr(220)=>715,chr(221)=>583,chr(222)=>598,chr(223)=>681,chr(224)=>574,chr(225)=>574,chr(226)=>574,chr(227)=>574,chr(228)=>574,chr(229)=>574,chr(230)=>881,chr(231)=>499,chr(232)=>581,chr(233)=>581,chr(234)=>581,chr(235)=>581,chr(236)=>305,chr(237)=>305,chr(238)=>305,chr(239)=>305,chr(240)=>577,chr(241)=>626,
	chr(242)=>599,chr(243)=>599,chr(244)=>599,chr(245)=>599,chr(246)=>599,chr(247)=>550,chr(248)=>599,chr(249)=>626,chr(250)=>626,chr(251)=>626,chr(252)=>626,chr(253)=>539,chr(254)=>607,chr(255)=>539);
$enc='iso-8859-1';
$diff='128 /u0080 /u0081 /u0082 /u0083 /u0084 /u0085 /u0086 /u0087 /u0088 /u0089 /u008A /u008B /u008C /u008D /u008E /u008F /u0090 /u0091 /u0092 /u0093 /u0094 /u0095 /u0096 /u0097 /u0098 /u0099 /u009A /u009B /u009C /u009D /u009E /u009F';
$cmap=array(
0x00 => 0x0000,
0x01 => 0x0001,
0x02 => 0x0002,
0x03 => 0x0003,
0x04 => 0x0004,
0x05 => 0x0005,
0x06 => 0x0006,
0x07 => 0x0007,
0x08 => 0x0008,
0x09 => 0x0009,
0x0A => 0x000A,
0x0B => 0x000B,
0x0C => 0x000C,
0x0D => 0x000D,
0x0E => 0x000E,
0x0F => 0x000F,
0x10 => 0x0010,
0x11 => 0x0011,
0x12 => 0x0012,
0x13 => 0x0013,
0x14 => 0x0014,
0x15 => 0x0015,
0x16 => 0x0016,
0x17 => 0x0017,
0x18 => 0x0018,
0x19 => 0x0019,
0x1A => 0x001A,
0x1B => 0x001B,
0x1C => 0x001C,
0x1D => 0x001D,
0x1E => 0x001E,
0x1F => 0x001F,
0x20 => 0x0020,
0x21 => 0x0021,
0x22 => 0x0022,
0x23 => 0x0023,
0x24 => 0x0024,
0x25 => 0x0025,
0x26 => 0x0026,
0x27 => 0x0027,
0x28 => 0x0028,
0x29 => 0x0029,
0x2A => 0x002A,
0x2B => 0x002B,
0x2C => 0x002C,
0x2D => 0x002D,
0x2E => 0x002E,
0x2F => 0x002F,
0x30 => 0x0030,
0x31 => 0x0031,
0x32 => 0x0032,
0x33 => 0x0033,
0x34 => 0x0034,
0x35 => 0x0035,
0x36 => 0x0036,
0x37 => 0x0037,
0x38 => 0x0038,
0x39 => 0x0039,
0x3A => 0x003A,
0x3B => 0x003B,
0x3C => 0x003C,
0x3D => 0x003D,
0x3E => 0x003E,
0x3F => 0x003F,
0x40 => 0x0040,
0x41 => 0x0041,
0x42 => 0x0042,
0x43 => 0x0043,
0x44 => 0x0044,
0x45 => 0x0045,
0x46 => 0x0046,
0x47 => 0x0047,
0x48 => 0x0048,
0x49 => 0x0049,
0x4A => 0x004A,
0x4B => 0x004B,
0x4C => 0x004C,
0x4D => 0x004D,
0x4E => 0x004E,
0x4F => 0x004F,
0x50 => 0x0050,
0x51 => 0x0051,
0x52 => 0x0052,
0x53 => 0x0053,
0x54 => 0x0054,
0x55 => 0x0055,
0x56 => 0x0056,
0x57 => 0x0057,
0x58 => 0x0058,
0x59 => 0x0059,
0x5A => 0x005A,
0x5B => 0x005B,
0x5C => 0x005C,
0x5D => 0x005D,
0x5E => 0x005E,
0x5F => 0x005F,
0x60 => 0x0060,
0x61 => 0x0061,
0x62 => 0x0062,
0x63 => 0x0063,
0x64 => 0x0064,
0x65 => 0x0065,
0x66 => 0x0066,
0x67 => 0x0067,
0x68 => 0x0068,
0x69 => 0x0069,
0x6A => 0x006A,
0x6B => 0x006B,
0x6C => 0x006C,
0x6D => 0x006D,
0x6E => 0x006E,
0x6F => 0x006F,
0x70 => 0x0070,
0x71 => 0x0071,
0x72 => 0x0072,
0x73 => 0x0073,
0x74 => 0x0074,
0x75 => 0x0075,
0x76 => 0x0076,
0x77 => 0x0077,
0x78 => 0x0078,
0x79 => 0x0079,
0x7A => 0x007A,
0x7B => 0x007B,
0x7C => 0x007C,
0x7D => 0x007D,
0x7E => 0x007E,
0x7F => 0x007F,
0x80 => 0x0080,
0x81 => 0x0081,
0x82 => 0x0082,
0x83 => 0x0083,
0x84 => 0x0084,
0x85 => 0x0085,
0x86 => 0x0086,
0x87 => 0x0087,
0x88 => 0x0088,
0x89 => 0x0089,
0x8A => 0x008A,
0x8B => 0x008B,
0x8C => 0x008C,
0x8D => 0x008D,
0x8E => 0x008E,
0x8F => 0x008F,
0x90 => 0x0090,
0x91 => 0x0091,
0x92 => 0x0092,
0x93 => 0x0093,
0x94 => 0x0094,
0x95 => 0x0095,
0x96 => 0x0096,
0x97 => 0x0097,
0x98 => 0x0098,
0x99 => 0x0099,
0x9A => 0x009A,
0x9B => 0x009B,
0x9C => 0x009C,
0x9D => 0x009D,
0x9E => 0x009E,
0x9F => 0x009F,
0xA0 => 0x00A0,
0xA1 => 0x00A1,
0xA2 => 0x00A2,
0xA3 => 0x00A3,
0xA4 => 0x00A4,
0xA5 => 0x00A5,
0xA6 => 0x00A6,
0xA7 => 0x00A7,
0xA8 => 0x00A8,
0xA9 => 0x00A9,
0xAA => 0x00AA,
0xAB => 0x00AB,
0xAC => 0x00AC,
0xAD => 0x00AD,
0xAE => 0x00AE,
0xAF => 0x00AF,
0xB0 => 0x00B0,
0xB1 => 0x00B1,
0xB2 => 0x00B2,
0xB3 => 0x00B3,
0xB4 => 0x00B4,
0xB5 => 0x00B5,
0xB6 => 0x00B6,
0xB7 => 0x00B7,
0xB8 => 0x00B8,
0xB9 => 0x00B9,
0xBA => 0x00BA,
0xBB => 0x00BB,
0xBC => 0x00BC,
0xBD => 0x00BD,
0xBE => 0x00BE,
0xBF => 0x00BF,
0xC0 => 0x00C0,
0xC1 => 0x00C1,
0xC2 => 0x00C2,
0xC3 => 0x00C3,
0xC4 => 0x00C4,
0xC5 => 0x00C5,
0xC6 => 0x00C6,
0xC7 => 0x00C7,
0xC8 => 0x00C8,
0xC9 => 0x00C9,
0xCA => 0x00CA,
0xCB => 0x00CB,
0xCC => 0x00CC,
0xCD => 0x00CD,
0xCE => 0x00CE,
0xCF => 0x00CF,
0xD0 => 0x00D0,
0xD1 => 0x00D1,
0xD2 => 0x00D2,
0xD3 => 0x00D3,
0xD4 => 0x00D4,
0xD5 => 0x00D5,
0xD6 => 0x00D6,
0xD7 => 0x00D7,
0xD8 => 0x00D8,
0xD9 => 0x00D9,
0xDA => 0x00DA,
0xDB => 0x00DB,
0xDC => 0x00DC,
0xDD => 0x00DD,
0xDE => 0x00DE,
0xDF => 0x00DF,
0xE0 => 0x00E0,
0xE1 => 0x00E1,
0xE2 => 0x00E2,
0xE3 => 0x00E3,
0xE4 => 0x00E4,
0xE5 => 0x00E5,
0xE6 => 0x00E6,
0xE7 => 0x00E7,
0xE8 => 0x00E8,
0xE9 => 0x00E9,
0xEA => 0x00EA,
0xEB => 0x00EB,
0xEC => 0x00EC,
0xED => 0x00ED,
0xEE => 0x00EE,
0xEF => 0x00EF,
0xF0 => 0x00F0,
0xF1 => 0x00F1,
0xF2 => 0x00F2,
0xF3 => 0x00F3,
0xF4 => 0x00F4,
0xF5 => 0x00F5,
0xF6 => 0x00F6,
0xF7 => 0x00F7,
0xF8 => 0x00F8,
0xF9 => 0x00F9,
0xFA => 0x00FA,
0xFB => 0x00FB,
0xFC => 0x00FC,
0xFD => 0x00FD,
0xFE => 0x00FE,
0xFF => 0x00FF,
);
$file='DroidSans-Bold.z';
$originalsize=191032;
?>
