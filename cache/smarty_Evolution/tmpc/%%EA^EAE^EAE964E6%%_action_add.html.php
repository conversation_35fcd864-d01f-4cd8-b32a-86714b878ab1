<?php /* Smarty version 2.6.33, created on 2025-05-21 13:23:53
         compiled from /var/www/Nzoom-Hella/_libs/modules/tasks/view/templates/_action_add.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/tasks/view/templates/_action_add.html', 5, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/tasks/view/templates/_action_add.html', 9, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/tasks/view/templates/_action_add.html', 9, false),array('modifier', 'mb_lower', '/var/www/Nzoom-Hella/_libs/modules/tasks/view/templates/_action_add.html', 27, false),)), $this); ?>
<div>
  <table border="0" cellpadding="3" cellspacing="3" >
    <tr>
      <td class="labelbox">
        <?php echo smarty_function_help(array('label' => 'adds_operations'), $this);?>
:
      </td>
      <td class="nowrap" id="adds_suboptions_row">
        <?php $_from = $this->_tpl_vars['available_actions']['adds']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['adds_operations'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['adds_operations']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['operation_name'] => $this->_tpl_vars['operation']):
        $this->_foreach['adds_operations']['iteration']++;
?>
            <input type="radio" name="operation" id="<?php echo $this->_tpl_vars['operation']['action']; ?>
" value="<?php echo $this->_tpl_vars['operation']['action']; ?>
" onclick="toggleCombinedActionOptions(this)"<?php if (($this->_foreach['adds_operations']['iteration'] <= 1)): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['operation']['action']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['operation']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, "") : smarty_modifier_default($_tmp, "")))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>&nbsp;
        <?php endforeach; else: ?>
          &nbsp;
        <?php endif; unset($_from); ?>
      </td>
    </tr>
  </table>

  <div>
    <?php if ($this->_tpl_vars['available_action']['options']['add']): ?>
      <table border="0" cellpadding="3" cellspacing="3" id="adds_add_box">
        <tr>
          <td><?php echo smarty_function_help(array('label' => 'type'), $this);?>
</td>
          <td>&nbsp;</td>
        </tr>
        <tr>
          <td>
            <select name="type" id="type__" class="selbox<?php if (! $this->_tpl_vars['available_action']['options']['add']['options']['types']): ?> missing_records<?php elseif (! $this->_tpl_vars['available_action']['options']['add']['options']['default_task_type']): ?> undefined<?php endif; ?>" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this); changeAddOtions(this, 'conf_select');" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
              <option value="" class="undefined"<?php if (! $this->_tpl_vars['available_action']['options']['add']['options']['default_task_type']): ?> selected="selected"<?php endif; ?>><?php if ($this->_tpl_vars['first_option_label']): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['first_option_label'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
              <?php $_from = $this->_tpl_vars['available_action']['options']['add']['options']['types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                <option value="<?php echo $this->_tpl_vars['option']['option_value']; ?>
"<?php if ($this->_tpl_vars['available_action']['options']['add']['options']['default_task_type'] == $this->_tpl_vars['option']['option_value']): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
              <?php endforeach; endif; unset($_from); ?>
            </select>
          </td>
          <td id="conf_select">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_conf_select.html", 'smarty_include_vars' => array('available_action' => $this->_tpl_vars['available_action']['options']['add'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td colspan="4">
            <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"<?php if ($this->_tpl_vars['available_action']['confirm']): ?> onclick="return confirmAction('<?php echo $this->_tpl_vars['available_action']['name']; ?>
', submitForm, this);"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
          </td>
        </tr>
      </table>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['available_action']['options']['multiadd']): ?>
      <table border="0" cellpadding="3" cellspacing="3" id="adds_multiadd_box" style="display: none;">
        <tr>
          <td><?php echo smarty_function_help(array('label' => 'type'), $this);?>
</td>
          <td>&nbsp;</td>
        </tr>
        <tr>
          <td>
            <select name="type" id="type___" class="selbox<?php if (! $this->_tpl_vars['available_action']['options']['multiadd']['options']['types']): ?> missing_records<?php elseif (! $this->_tpl_vars['available_action']['options']['multiadd']['options']['default_task_type']): ?> undefined<?php endif; ?>" onfocus="highlight(this)" onblur="unhighlight(this)" onchange="toggleUndefined(this);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['tasks_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" disabled>
              <option value="" class="undefined"<?php if (! $this->_tpl_vars['available_action']['options']['multiadd']['options']['default_task_type']): ?> selected="selected"<?php endif; ?>><?php if ($this->_tpl_vars['first_option_label']): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['first_option_label'])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
]<?php else: ?>[<?php echo ((is_array($_tmp=$this->_config[0]['vars']['please_select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php endif; ?></option>
              <?php $_from = $this->_tpl_vars['available_action']['options']['multiadd']['options']['types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                <option value="<?php echo $this->_tpl_vars['option']['option_value']; ?>
"<?php if ($this->_tpl_vars['available_action']['options']['multiadd']['options']['default_task_type'] == $this->_tpl_vars['option']['option_value']): ?> selected="selected"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</option>
              <?php endforeach; endif; unset($_from); ?>
            </select>
          </td>
          <td>&nbsp;</td>
        </tr>
        <tr>
          <td colspan="4">
            <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['options']['multiadd']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['options']['multiadd']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['options']['multiadd']['label']; ?>
"<?php if ($this->_tpl_vars['available_action']['options']['multiadd']['confirm']): ?> onclick="return confirmAction('<?php echo $this->_tpl_vars['available_action']['name']; ?>
', submitForm, this);"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['multiadd'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
          </td>
        </tr>
      </table>
    <?php endif; ?>
  </div>
</div>