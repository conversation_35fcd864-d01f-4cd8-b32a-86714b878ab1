<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:55
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/email.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/email.html', 7, false),)), $this); ?>
<?php echo '
<script id="grid-col-email" type="text/x-template">
  ${for(r of properties.email)}
  <a href="${Nz.getViewUrl(properties.relatives_parent[rIndex].id)}"
     class="${getRecordDirectionClass(properties.relatives_parent[rIndex].direction)} nz-tooltip-trigger nz-tooltip-autoinit"
     data-tooltip-element="#doc-rel-child-${properties.id}-${r.id}"
     data-tooltip-title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_relative_document_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '"
     data-tooltip-position="panel: top center at: bottom center">${getRecordDirectionIcon(properties.relatives_parent[rIndex].direction)} ${r.full_num}</a>
  <div id="doc-rel-child-${properties.id}-${r.id}" class="nz-tooltip-content nz-tooltip-notch__top-center">
    <div>${r.name}</div>
  </div>
  <br />
  ${/for}
</script>
'; ?>
