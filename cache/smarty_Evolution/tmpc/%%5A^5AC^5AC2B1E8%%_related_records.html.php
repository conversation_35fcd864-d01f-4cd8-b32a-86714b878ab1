<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:32
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_related_records.html */ ?>
<div class="subpanel_container nz-subpanel_container">
    <?php if ($_COOKIE[$this->_tpl_vars['coockiename']] && in_array ( $_COOKIE[$this->_tpl_vars['coockiename']] , $this->_tpl_vars['related_records_modules'] )): ?>
    <?php $this->assign('rel_type', $_COOKIE[$this->_tpl_vars['coockiename']]); ?>
    <?php else: ?>
    <?php $this->assign('rel_type', $this->_tpl_vars['related_records_modules']['0']); ?>
    <?php endif; ?>
    <input type="hidden" id="rel_type" name="rel_type" value="<?php echo $this->_tpl_vars['rel_type']; ?>
" />
    <a name="<?php echo $this->_tpl_vars['subpanel_name']; ?>
"></a>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array('available_actions' => $this->_tpl_vars['available_actions_related_records'],'tabs' => true,'selected_tab' => $this->_tpl_vars['rel_type'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <div class="m_header_m_menu scroll_box_container nz-surface nz-subpanel_contents">
        <?php $_from = $this->_tpl_vars['related_records_modules']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['model'] => $this->_tpl_vars['module']):
?>
        <div id="<?php echo $this->_tpl_vars['session_params'][$this->_tpl_vars['module']]; ?>
" data-mod="<?php echo $this->_tpl_vars['module']; ?>
" class="rel_tab">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."__inline-loading.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </div>
        <?php endforeach; endif; unset($_from); ?>
    </div>
</div>