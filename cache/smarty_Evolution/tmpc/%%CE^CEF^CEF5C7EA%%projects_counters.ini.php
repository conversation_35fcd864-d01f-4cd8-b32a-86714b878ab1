<?php $_config_vars = array (
  'projects_counters' => 'Броячи за проекти',
  'projects_counters_name' => 'Име',
  'projects_counters_formula' => 'Формула',
  'projects_counters_description' => 'Описание',
  'projects_counters_next_number' => 'Следващ номер',
  'projects_counters_count_projects' => 'Брой проекти',
  'projects_counters_types_used' => 'Използван в типове проекти',
  'projects_counters_status' => 'Статус',
  'projects_counters_status_active' => 'Активен',
  'projects_counters_status_inactive' => 'Неактивен',
  'projects_counters_added_by' => 'Добавен от',
  'projects_counters_modified_by' => 'Променен от',
  'projects_counters_added' => 'Добавен на',
  'projects_counters_modified' => 'Променен на',
  'projects_counters_add' => 'Добавяне на брояч за проекти',
  'projects_counters_edit' => 'Редакция на брояч за проекти',
  'projects_counters_view' => 'Разглеждане на брояч за проекти',
  'projects_counters_translate' => 'Превод на брояч за проекти',
  'message_projects_counters_add_success' => 'Данните за брояч бяха добавени успешно!',
  'message_projects_counters_edit_success' => 'Данните за брояч бяха редактирани успешно!',
  'message_projects_counters_translate_success' => 'Броячът беше успешно преведен!',
  'error_projects_counters_edit_failed' => 'Данните за брояч не бяха редактирани успешно:',
  'error_projects_counters_add_failed' => 'Данните за брояч не бяха добавени:',
  'error_projects_counters_translate_failed' => 'Броячът не беше успешно преведен:',
  'error_no_counter_name_specified' => 'Не сте въвели име!',
  'error_no_counter_formula_specified' => 'Не сте въвели формула!',
  'error_invalid_next_number' => 'Моля, въведете следващ номер за брояча, състоящ се само от цифри и със стойност по-голяма от 0!',
  'error_projects_counter_mutex_num' => 'Формулата за брояча може да съдържа само един от елементите [num] и [customer_num]!',
  'error_no_types_used' => 'не е използван в нито един тип проект',
  'error_no_such_project_counter' => 'Нямате възможност да прегледате този запис!',
  'projects_counters_formula_delimiter' => 'Разделител',
  'projects_counters_empty_delimiter' => 'без разделител',
  'projects_counters_formula_leading_zeroes' => 'Брой водещи нули',
  'projects_counters_formula_date_format' => 'формат',
  'projects_counters_formula_date_delimiter' => 'с разделител',
  'projects_counters_formula_date_format_year' => 'гггг',
  'projects_counters_formula_date_format_year_short' => 'гг',
  'projects_counters_formula_date_format_month' => 'мм',
  'projects_counters_formula_date_format_day' => 'дд',
  'projects_counters_formula_date_format1' => 'гггг',
  'projects_counters_formula_date_format2' => 'мм/гггг',
  'projects_counters_formula_date_format3' => 'мм/гг',
  'projects_counters_formula_date_format4' => 'гггг/мм',
  'projects_counters_formula_date_format5' => 'гг/мм',
  'projects_counters_formula_date_format6' => 'дд/мм/гггг',
  'projects_counters_formula_date_format7' => 'дд/мм/гг',
  'projects_counters_formula_date_format8' => 'мм/дд/гггг',
  'projects_counters_formula_date_format9' => 'мм/дд/гг',
  'projects_counters_formula_date_format10' => 'гггг/дд/мм',
  'projects_counters_formula_date_format11' => 'гг/дд/мм',
  'projects_counters_formula_date_format12' => 'гггг/мм/дд',
  'projects_counters_formula_date_format13' => 'гг/мм/дд',
  'projects_counters_formula_date_format14' => 'гг',
  'projects_counters_formula_date_format15' => 'ггг/мм',
  'projects_counters_formula_legend' => 'Легенда за попълването на формулата на брояча',
  'projects_counters_formula_prefix' => 'Префикс',
  'projects_counters_formula_num' => 'Номер на проект',
  'projects_counters_formula_customer_code' => 'Код на контрагент',
  'projects_counters_formula_code' => 'Код на проект',
  'projects_counters_formula_user_code' => 'Код на потребител',
  'projects_counters_formula_parent_num' => 'Номер на родителския проект',
  'projects_counters_formula_customer_num' => 'Номер на проект към контрагент',
  'projects_counters_formula_customer_year' => 'Само за текущата година',
  'projects_counters_formula_added' => 'Дата на проект',
  'projects_counters_formula_prefix_descr' => 'попълва се директно с 2-3 букви, например за проект PRJ.',
  'projects_counters_formula_num_descr' => 'попълва поредния номер на проект.',
  'projects_counters_formula_customer_code_descr' => 'попълва кода на контрагента, избран за проекта.',
  'projects_counters_formula_code_descr' => 'попълва кода на проекта.',
  'projects_counters_formula_user_code_descr' => 'попълва кода на потребител, създал проекта.',
  'projects_counters_formula_parent_num_descr' => 'попълва номера на родителския проект.',
  'projects_counters_formula_customer_num_descr' => 'попълва пореден номер на проект към контрагента. Номерът може да бъде зададен спрямо проектите, добавени през текущата година, или всички.',
  'projects_counters_formula_added_descr' => 'дата на добавяне на проекта.',
  'projects_counters_formula_note' => '<strong>ЗАБЕЛЕЖКА:</strong> Към формулата на брояча могат да се добавят <strong>само 5 елемента</strong>',
  'help_projects_counters_name' => '',
  'help_projects_counters_formula' => '',
  'help_projects_counters_count_projects' => '',
  'help_projects_counters_types_used' => '',
  'help_projects_counters_status' => '',
  'help_projects_counters_description' => '',
  'help_projects_counters_next_number' => 'Следващ номер. Използвайте това поле, за да зададете номер, от който да започва броячът.',
); ?>