<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:50
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_attachments_info_items.html */ ?>
<?php $_from = $this->_tpl_vars['filesList']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['k'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['k']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['attachment']):
        $this->_foreach['k']['iteration']++;
?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_attachments_info_item.html", 'smarty_include_vars' => array('attachment' => $this->_tpl_vars['attachment'],'module' => $this->_tpl_vars['module'],'controller_action_string' => $this->_tpl_vars['controller_action_string'],'model_id' => $this->_tpl_vars['model_id'],'archive' => $this->_tpl_vars['archive'],'n' => $this->_foreach['k']['iteration'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endforeach; endif; unset($_from); ?>