<?php /* Smarty version 2.6.33, created on 2025-05-21 13:03:02
         compiled from _list_actions.html */ ?>
<?php if (isset ( $this->_tpl_vars['available_actions']['adds'] )): ?>
<?php if (! empty ( $this->_tpl_vars['available_actions']['adds']['options'] )): ?>
<span class="nz-add-button nz-button nz-action__add nz-popout-trigger nz-popout-autoinit"
      data-popout-template="#add-popout"
      data-popout-position="panel: top center at: bottom center 0 0">
          <i class="material-icons"><?php echo $this->_tpl_vars['theme']->getIconForAction('adds'); ?>
</i>
          <?php echo $this->_tpl_vars['available_actions']['adds']['label']; ?>

    </span>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['moduleTemplatesDir'])."_add_popout_xtemplate.html", 'smarty_include_vars' => array('actionData' => $this->_tpl_vars['available_actions']['adds'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php elseif (! empty ( $this->_tpl_vars['available_actions']['adds']['url'] )): ?>
<a class="nz-add-button nz-button nz-action__add"
   href="<?php echo $this->_tpl_vars['available_actions']['adds']['url']; ?>
">
  <i class="material-icons"><?php echo $this->_tpl_vars['theme']->getIconForAction('adds'); ?>
</i>
  <?php echo $this->_tpl_vars['available_actions']['adds']['label']; ?>

</a>
<?php endif; ?>
<?php endif; ?>

<?php if (isset ( $this->_tpl_vars['available_actions']['add'] )): ?>
<?php if (! empty ( $this->_tpl_vars['available_actions']['add']['options'] )): ?>
<span class="nz-add-button nz-button nz-action__add nz-popout-trigger nz-popout-autoinit"
      data-popout-template="#add-popout"
      data-popout-position="panel: top center at: bottom center 0 0">
      <i class="material-icons"><?php echo $this->_tpl_vars['theme']->getIconForAction('add'); ?>
</i>
      <?php echo $this->_tpl_vars['available_actions']['add']['label']; ?>

    </span>
<?php elseif (! empty ( $this->_tpl_vars['available_actions']['add']['url'] )): ?>
<a class="nz-add-button nz-button nz-action__add"
   href="<?php echo $this->_tpl_vars['available_actions']['add']['url']; ?>
">
  <i class="material-icons"><?php echo $this->_tpl_vars['theme']->getIconForAction('add'); ?>
</i>
  <?php echo $this->_tpl_vars['available_actions']['add']['label']; ?>

</a>
<?php endif; ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['moduleTemplatesDir'])."_add_popout_xtemplate.html", 'smarty_include_vars' => array('actionData' => $this->_tpl_vars['available_actions']['add'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>

<?php if (isset ( $this->_tpl_vars['available_actions']['statements'] )): ?>
<a class="nz-add-button nz-button nz-action__add"
   href="<?php echo $this->_tpl_vars['available_actions']['statements']['url']; ?>
">
  <i class="material-icons"><?php echo $this->_tpl_vars['theme']->getIconForAction('statements'); ?>
</i>
  <?php echo $this->_tpl_vars['available_actions']['statements']['label']; ?>

</a>
<?php endif; ?>

<?php if (isset ( $this->_tpl_vars['available_actions']['calendars'] )): ?>
<a class="nz-add-button nz-button nz-action__calendar"
   href="<?php echo $this->_tpl_vars['available_actions']['calendars']['url']; ?>
">
  <i class="material-icons"><?php echo $this->_tpl_vars['available_actions']['calendars']['icon']; ?>
</i>
  <?php echo $this->_tpl_vars['available_actions']['calendars']['label']; ?>

</a>
<?php endif; ?>