<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:04
         compiled from /var/www/Nzoom-Hella/_libs/modules/roles/templates/_permissions_section.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/_permissions_section.html', 16, false),array('modifier', 'current', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/_permissions_section.html', 36, false),array('modifier', 'mb_upper', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/_permissions_section.html', 40, false),)), $this); ?>
<table id="<?php echo $this->_tpl_vars['section_name']; ?>
_holder" class="permissions_holder t_layout_table" cellspacing="0">
  <?php $_from = $this->_tpl_vars['current_section_permissions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['module_name'] => $this->_tpl_vars['module_permissions']):
?>
    <?php $this->assign('module_cookie_var', ($this->_tpl_vars['module_name'])."_rights_box"); ?>
    <?php $this->assign('label_index', -1); ?>
    <tr class="<?php echo $this->_tpl_vars['section_name']; ?>
_section_permissions section_permissions_class">
      <td colspan="2" class="t_caption3 pointer" onclick="toggleModulePermissions(this)" id="module_<?php echo $this->_tpl_vars['module_name']; ?>
_switch">
        <a name="<?php echo $this->_tpl_vars['module_name']; ?>
_index"></a>
        <div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['module_cookie_var']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div>
        <div class="t_caption2_title">
          <?php $_from = $this->_tpl_vars['module_permissions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['mp'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['mp']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['permissions']):
        $this->_foreach['mp']['iteration']++;
?>
            <?php if (($this->_foreach['mp']['iteration'] <= 1)): ?><?php echo $this->_tpl_vars['permissions'][$this->_tpl_vars['label_index']]['module_label']; ?>
<?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        </div>
      </td>
      <td class="t_caption3 pointer" align="right" style="width: 32px;">
        <a href="#vars_index" class="index_arrow_anchor floatl"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
        <a href="#system_settings_container" class="index_arrow_anchor floatl"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_bottom.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['go_to_end'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['go_to_end'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
      </td>
    </tr>
    <tr id="<?php echo $this->_tpl_vars['module_name']; ?>
_rights"<?php if ($_COOKIE[$this->_tpl_vars['module_cookie_var']] == 'off'): ?> style="display: none"<?php endif; ?>>
      <td colspan="3" class="nopadding">
        <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
          <tr class="t_bottom_border row_yellow">
            <td colspan="3">
              <span class="pointer" onclick="grantModulePermissions(this, 'none', '<?php echo $this->_tpl_vars['module_name']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
              <span class="pointer" onclick="grantModulePermissions(this, 'mine', '<?php echo $this->_tpl_vars['module_name']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
              <span class="pointer" onclick="grantModulePermissions(this, 'group', '<?php echo $this->_tpl_vars['module_name']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
              <span class="pointer" onclick="grantModulePermissions(this, 'all', '<?php echo $this->_tpl_vars['module_name']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
            </td>
          </tr>
          <?php $_from = $this->_tpl_vars['module_permissions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['permissions']):
?>
            <?php if (count ( $this->_tpl_vars['module_permissions'] ) > 1): ?>
              <tr class="t_bottom_border">
                <td colspan="2" class="t_caption2 strong">
                                    <?php $this->assign('perm', current($this->_tpl_vars['permissions'])); ?>
                  <?php if ($this->_tpl_vars['perm']['model_type'] != 0): ?>
                    <a name="<?php echo $this->_tpl_vars['module_name']; ?>
_<?php echo $this->_tpl_vars['perm']['model_type']; ?>
_index"></a>
                  <?php endif; ?>
                  <?php echo ((is_array($_tmp=$this->_tpl_vars['perm']['subtitle_label'])) ? $this->_run_mod_handler('mb_upper', true, $_tmp) : smarty_modifier_mb_upper($_tmp)); ?>

                </td>
                <td class="t_caption2 pointer" align="right" style="width: 32px;">
                  <a href="#vars_index" class="index_arrow_anchor floatl"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                  <a href="#system_settings_container" class="index_arrow_anchor floatl"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_bottom.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['go_to_end'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['go_to_end'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                </td>
              </tr>
              <tr class="t_bottom_border">
                <td colspan="3">
                  <span class="pointer" onclick="grantModulePermissions(this, 'none', '<?php echo $this->_tpl_vars['module_name']; ?>
<?php echo $this->_tpl_vars['perm']['model_type']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
                  <span class="pointer" onclick="grantModulePermissions(this, 'mine', '<?php echo $this->_tpl_vars['module_name']; ?>
<?php echo $this->_tpl_vars['perm']['model_type']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
                  <span class="pointer" onclick="grantModulePermissions(this, 'group', '<?php echo $this->_tpl_vars['module_name']; ?>
<?php echo $this->_tpl_vars['perm']['model_type']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
                  <span class="pointer" onclick="grantModulePermissions(this, 'all', '<?php echo $this->_tpl_vars['module_name']; ?>
<?php echo $this->_tpl_vars['perm']['model_type']; ?>
')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_grant_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                </td>
              </tr>
            <?php endif; ?>
            <tr class="t_bottom_border">
              <th class="strong" style="width: 320px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_action'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
              <th class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_permissions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
              <th>&nbsp;</th>
            </tr>
            <?php $_from = $this->_tpl_vars['permissions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['permission']):
?>
              <tr class="t_bottom_border<?php if ($this->_tpl_vars['permission']['action'] == '_access_'): ?> stronger<?php if (! $this->_tpl_vars['permission']['permission'] || $this->_tpl_vars['permission']['permission'] == 'none'): ?> red<?php else: ?> green<?php endif; ?><?php endif; ?>">
                <td class="strong"><?php echo $this->_tpl_vars['permission']['action_label']; ?>
</td>
                <td nowrap="nowrap" colspan="2">
                <?php if ($this->_tpl_vars['mode'] == 'view'): ?>
                  <?php if ($this->_tpl_vars['permission']['requires_model']): ?>
                    <?php if (! $this->_tpl_vars['permission']['permission'] || $this->_tpl_vars['permission']['permission'] == 'none'): ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php elseif ($this->_tpl_vars['permission']['permission'] == 'mine'): ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php elseif ($this->_tpl_vars['permission']['permission'] == 'group'): ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php elseif ($this->_tpl_vars['permission']['permission'] == 'all'): ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php endif; ?>
                  <?php else: ?>
                    <?php if (! $this->_tpl_vars['permission']['permission'] || $this->_tpl_vars['permission']['permission'] == 'none'): ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_disabled'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php elseif ($this->_tpl_vars['permission']['permission'] == 'all'): ?>
                      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_enabled'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                    <?php endif; ?>
                  <?php endif; ?>
                <?php else: ?>
                  <?php ob_start(); ?><?php echo $this->_tpl_vars['module_name']; ?>
<?php if ($this->_tpl_vars['permission']['model_type']): ?><?php echo $this->_tpl_vars['permission']['model_type']; ?>
<?php endif; ?>_<?php echo $this->_tpl_vars['permission']['action']; ?>
_right<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('permission_name', ob_get_contents());ob_end_clean(); ?>
                  <?php ob_start(); ?><?php echo $this->_tpl_vars['module_name']; ?>
<?php if ($this->_tpl_vars['permission']['model_type']): ?><?php echo $this->_tpl_vars['permission']['model_type']; ?>
<?php else: ?>0<?php endif; ?>_<?php echo $this->_tpl_vars['permission']['action']; ?>
_right<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('permission_id', ob_get_contents());ob_end_clean(); ?>
                  <?php if ($this->_tpl_vars['permission']['requires_model']): ?>
                    <input type="radio" name="<?php echo $this->_tpl_vars['permission_name']; ?>
" value="none" id="<?php echo $this->_tpl_vars['permission_id']; ?>
_none"<?php if (! $this->_tpl_vars['permission']['permission'] || $this->_tpl_vars['permission']['permission'] == 'none'): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['permission_id']; ?>
_none"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                    <input type="radio" name="<?php echo $this->_tpl_vars['permission_name']; ?>
" value="mine" id="<?php echo $this->_tpl_vars['permission_id']; ?>
_mine"<?php if ($this->_tpl_vars['permission']['permission'] == 'mine'): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['permission_id']; ?>
_mine"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_mine'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                    <input type="radio" name="<?php echo $this->_tpl_vars['permission_name']; ?>
" value="group" id="<?php echo $this->_tpl_vars['permission_id']; ?>
_group"<?php if ($this->_tpl_vars['permission']['permission'] == 'group'): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['permission_id']; ?>
_group"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                    <input type="radio" name="<?php echo $this->_tpl_vars['permission_name']; ?>
" value="all" id="<?php echo $this->_tpl_vars['permission_id']; ?>
_all"<?php if ($this->_tpl_vars['permission']['permission'] == 'all'): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['permission_id']; ?>
_all"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                  <?php else: ?>
                    <input type="radio" name="<?php echo $this->_tpl_vars['permission_name']; ?>
" value="none" id="<?php echo $this->_tpl_vars['permission_id']; ?>
_none"<?php if (! $this->_tpl_vars['permission']['permission'] || $this->_tpl_vars['permission']['permission'] == 'none'): ?> checked="checked"<?php endif; ?><?php if ($this->_tpl_vars['permission']['action'] == '_access_'): ?> onclick="return grantModulePermissions(this, 'none', '<?php echo $this->_tpl_vars['module_name']; ?>
')"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['permission_id']; ?>
_none"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_disabled'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                    <input type="radio" name="<?php echo $this->_tpl_vars['permission_name']; ?>
" value="all" id="<?php echo $this->_tpl_vars['permission_id']; ?>
_all"<?php if ($this->_tpl_vars['permission']['permission'] == 'all'): ?> checked="checked"<?php endif; ?><?php if ($this->_tpl_vars['permission']['action'] == '_access_'): ?> onclick="switchPermissions('<?php echo $this->_tpl_vars['module_name']; ?>
0', 'all')"<?php endif; ?> /><label for="<?php echo $this->_tpl_vars['permission_id']; ?>
_all" ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['role_permission_enabled'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                  <?php endif; ?>
                <?php endif; ?>
                </td>
              </tr>
            <?php endforeach; endif; unset($_from); ?>
          <?php endforeach; endif; unset($_from); ?>
        </table>
      </td>
    </tr>
  <?php endforeach; endif; unset($_from); ?>
</table>