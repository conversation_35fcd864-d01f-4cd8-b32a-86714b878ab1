<?php /* Smarty version 2.6.33, created on 2025-05-22 13:24:31
         compiled from /var/www/Nzoom-Hella/_libs/modules/outlooks/view/templates/_outlook_options_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/outlooks/view/templates/_outlook_options_panel.html', 5, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/outlooks/view/templates/_outlook_options_panel.html', 16, false),)), $this); ?>
  <!-- Outlooks Personal Manager -->
  <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['outlook']->get('id'); ?>
" />
  <input type="hidden" name="module_controller" id="module_controller" value="<?php echo $this->_tpl_vars['outlook']->get('module'); ?>
|<?php echo $this->_tpl_vars['outlook']->get('controller'); ?>
" />
  <input type="hidden" name="model_id" id="model_id" value="<?php echo $this->_tpl_vars['outlook']->get('model_id'); ?>
" />
  <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['outlook']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
  <input type="hidden" name="section" id="section" value="<?php echo $this->_tpl_vars['outlook']->get('section'); ?>
" />
  <input type="hidden" name="assignments_type" id="assignments_type" value="Users" />
  <input type="hidden" name="previous_assignments_type" id="previous_assignments_type" value="<?php echo $this->_tpl_vars['outlook']->get('assignments_type'); ?>
" />
  <input type="hidden" name="users[0]" id="users" value="<?php echo $this->_tpl_vars['currentUser']->get('id'); ?>
" />
  <ul class="nz-outlook-grid-list nz-drag-list" id="outlooks_all_settings">
    <?php $_from = $this->_tpl_vars['outlook']->get('current_custom_fields'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['oo'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['oo']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['fields']):
        $this->_foreach['oo']['iteration']++;
?>
      <li class="nz-drag-element">
        <i class="nz-drag-handle material-icons">drag_indicator</i>
        <input type="checkbox" id="<?php echo $this->_tpl_vars['fields']['name']; ?>
|<?php echo $this->_tpl_vars['fields']['model_type']; ?>
" name="positions[<?php echo $this->_tpl_vars['fields']['name']; ?>
|<?php echo $this->_tpl_vars['fields']['model_type']; ?>
]" value="1"<?php if ($this->_tpl_vars['fields']['position']): ?> checked="checked"<?php endif; ?> />
        <label for="<?php echo $this->_tpl_vars['fields']['name']; ?>
|<?php echo $this->_tpl_vars['fields']['model_type']; ?>
"><?php echo $this->_tpl_vars['fields']['label']; ?>
</label>
        <input type="hidden" name="labels[<?php echo $this->_tpl_vars['fields']['name']; ?>
|<?php echo $this->_tpl_vars['fields']['model_type']; ?>
]" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['fields']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
        <input type="hidden" name="origins[<?php echo $this->_tpl_vars['fields']['name']; ?>
|<?php echo $this->_tpl_vars['fields']['model_type']; ?>
]" value="<?php echo $this->_tpl_vars['fields']['origin']; ?>
" />
        <input type="hidden" name="field_types[<?php echo $this->_tpl_vars['fields']['name']; ?>
|<?php echo $this->_tpl_vars['fields']['model_type']; ?>
]" value="<?php echo $this->_tpl_vars['fields']['field_type']; ?>
" />
        <input type="hidden" name="column_widths[<?php echo $this->_tpl_vars['fields']['name']; ?>
|<?php echo $this->_tpl_vars['fields']['model_type']; ?>
]" value="<?php echo $this->_tpl_vars['fields']['column_width']; ?>
" />
      </li>
    <?php endforeach; endif; unset($_from); ?>
  </ul>