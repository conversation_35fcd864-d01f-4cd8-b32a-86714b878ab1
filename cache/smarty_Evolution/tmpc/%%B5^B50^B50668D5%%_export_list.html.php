<?php /* Smarty version 2.6.33, created on 2025-05-21 17:36:40
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/templates/_export_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/_export_list.html', 3, false),array('modifier', 'numerate', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/_export_list.html', 24, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/_export_list.html', 25, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/_export_list.html', 39, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/_export_list.html', 47, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/_export_list.html', 14, false),)), $this); ?>
      <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_full_num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_custom_num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_department'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_tags'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
          <th nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
        </tr>
      <?php echo smarty_function_counter(array('start' => 0,'name' => 'item_counter','print' => false), $this);?>

      <?php $_from = $this->_tpl_vars['documents']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['document']):
        $this->_foreach['i']['iteration']++;
?>
      <?php if (! $this->_tpl_vars['document']->checkPermissions('list')): ?>
        <tr>
          <td nowrap="nowrap"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <td colspan="9"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_right_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
        <?php else: ?>
        <tr>
          <td><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <td><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['document']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['document']->get('direction'))); ?>
</td>
          <td><?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('custom_num'))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <td nowrap="nowrap">
            <?php if ($this->_tpl_vars['document']->get('substatus_name')): ?>
              <?php echo $this->_tpl_vars['document']->get('substatus_name'); ?>

            <?php else: ?>
              <?php ob_start(); ?>documents_status_<?php echo $this->_tpl_vars['document']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_param', ob_get_contents());ob_end_clean(); ?>
              <?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_param']]; ?>

            <?php endif; ?>
          </td>
          <td>
          <?php if (count($this->_tpl_vars['document']->get('model_tags')) > 0 && $this->_tpl_vars['document']->checkPermissions('tags_view')): ?>
            <?php $_from = $this->_tpl_vars['document']->get('model_tags'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tag']):
        $this->_foreach['ti']['iteration']++;
?>
              <?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! ($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total'])): ?>, <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php else: ?>
            &nbsp;
          <?php endif; ?>
          </td>
          <td nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; ?>
      <?php endforeach; else: ?>
        <tr>
          <td colspan="10"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
      </table>