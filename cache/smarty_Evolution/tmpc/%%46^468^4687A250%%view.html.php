<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:32
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 1, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 13, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 179, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 278, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 278, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 37, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 92, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/view.html', 92, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

        <input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['contract']->get('id'); ?>
" />
        <input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <?php $_from = $this->_tpl_vars['contract']->get('layouts_details'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>
                <?php if ($this->_tpl_vars['layout']['view']): ?>

                <?php ob_start(); ?>include_<?php echo $this->_tpl_vars['lkey']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('type_include_date', ob_get_contents());ob_end_clean(); ?>
                <?php if (! ( preg_match ( '#^date_(sign|start|validity|end)(_subtype)?$#' , $this->_tpl_vars['lkey'] ) ) || $this->_tpl_vars['contract']->get('subtype') == 'annex' && ( preg_match ( '#^date_(sign|start|end)_subtype$#' , $this->_tpl_vars['lkey'] ) ) || $this->_tpl_vars['contract']->get($this->_tpl_vars['type_include_date']) && preg_match ( '#^date_(sign|start|validity|end)$#' , $this->_tpl_vars['lkey'] )): ?>
                <tr<?php if (! $this->_tpl_vars['layout']['visible']): ?> style="display: none;"<?php endif; ?>>
                  <td colspan="3" class="t_caption3 pointer">
                    <div class="floatr index_arrow_anchor">
                      <a href="#vars_index"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
arrow_top.png" border="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['back_to_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /></a>
                    </div>
                    <div class="layout_switch" onclick="toggleViewLayouts(this)" id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_switch">
                      <a name="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
_index"></a><div class="switch_<?php if ($this->_tpl_vars['layout']['cookie'] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>"></div><div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                    </div>
                  </td>
                </tr>
                <?php endif; ?>

                <?php if ($this->_tpl_vars['lkey'] == 'status'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <div class="contracts_status <?php echo $this->_tpl_vars['contract']->get('status'); ?>
">
                      <?php if ($this->_tpl_vars['contract']->get('status') == 'opened'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_opened'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php elseif ($this->_tpl_vars['contract']->get('status') == 'locked'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_locked'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php elseif ($this->_tpl_vars['contract']->get('status') == 'closed'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_closed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['contract']->get('substatus_name')): ?>
                        &raquo; <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('substatus_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                      <?php if ($this->_tpl_vars['contract']->checkPermissions('setstatus')): ?>
                      <a href="#" onclick="toggleActionOptions($('setstatus_action')); return false;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_setstatus'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'type'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'num'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['contract']->get('num')): ?>
                      <?php if ($this->_tpl_vars['contract']->get('num') == 'system'): ?><?php echo $this->_config[0]['vars']['contracts_system_num']; ?>
<?php else: ?><?php echo $this->_tpl_vars['contract']->get('num'); ?>
<?php endif; ?>
                      <?php if ($this->_tpl_vars['last_annex']): ?>
                        / <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['last_annex']['id']; ?>
"><?php echo $this->_config[0]['vars']['contracts_annex']; ?>
 <?php if ($this->_tpl_vars['last_annex']['num'] == 'system'): ?><?php echo $this->_config[0]['vars']['contracts_system_num']; ?>
<?php else: ?><?php echo $this->_tpl_vars['last_annex']['num']; ?>
<?php endif; ?></a>
                      <?php endif; ?>
                    <?php else: ?>
                      <i><?php echo $this->_config[0]['vars']['contracts_unfinished_contract']; ?>
</i>
                    <?php endif; ?>
                    <input type="hidden" value="<?php if ($this->_tpl_vars['contract']->get('num')): ?><?php echo $this->_tpl_vars['contract']->get('num'); ?>
<?php endif; ?>" name="num" />
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'custom_num'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=@$this->_tpl_vars['contract']->get('custom_num'))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
                    <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>
</span>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                    <?php if ($this->_tpl_vars['contract']->get('branch') && $this->_tpl_vars['customer_branch']): ?>
                      <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['contract']->getBranchLabels('contracts_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                      <span<?php if (! $this->_tpl_vars['customer_branch']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      
                    <?php endif; ?>
                    <?php if ($this->_tpl_vars['contract']->get('contact_person') && $this->_tpl_vars['contact_person']): ?>
                      <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_config[0]['vars']['contracts_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span>
                      <span<?php if (! $this->_tpl_vars['contact_person']->isActivated()): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php else: ?>><?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['contract']->get('trademark')): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures&amp;nomenclatures=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('trademark'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
</a>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['contract']->get('project')): ?>
                      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=projects&amp;projects=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('project'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'company'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'office'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                  <td>
                    <?php if ($this->_tpl_vars['office']): ?>
                      <?php if (! $this->_tpl_vars['office']->isActivated()): ?>
                      <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php echo ((is_array($_tmp=$this->_tpl_vars['office']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      <?php else: ?>
                      <?php echo ((is_array($_tmp=$this->_tpl_vars['office']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php else: ?>
                      &nbsp;
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'employee'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['employee']): ?>
                      <?php if (! $this->_tpl_vars['employee']->isDeleted() && $this->_tpl_vars['employee']->isActivated()): ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php else: ?>
                        <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*<?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['employee']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      <?php endif; ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_sign' && $this->_tpl_vars['contract']->get('include_date_sign')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_sign'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <?php if ($this->_tpl_vars['contract']->get('date_sign_formula')): ?>
                      <?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?>
                        <?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_sign_formula')): ?>
                          (<?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_start' && $this->_tpl_vars['contract']->get('include_date_start')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_start'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <?php if ($this->_tpl_vars['contract']->get('date_start_formula')): ?>
                      <?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?>
                        <?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_start_formula')): ?>
                          (<?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_validity' && $this->_tpl_vars['contract']->get('include_date_validity')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_validity'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <?php if ($this->_tpl_vars['contract']->get('date_validity_formula')): ?>
                      <?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?>
                        <?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_validity_formula')): ?>
                          (<?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_end' && $this->_tpl_vars['contract']->get('include_date_end')): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_end'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                    <?php if ($this->_tpl_vars['contract']->get('date_end_formula')): ?>
                      <?php $_from = $this->_tpl_vars['contract']->get('formulas'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['formula']):
?>
                        <?php if ($this->_tpl_vars['formula']['option_value'] == $this->_tpl_vars['contract']->get('date_end_formula')): ?>
                          (<?php echo ((is_array($_tmp=$this->_tpl_vars['formula']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)
                        <?php endif; ?>
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_sign_subtype' && $this->_tpl_vars['contract']->get('subtype') == 'annex'): ?>
                <tr>
                  <td colspan="3">&nbsp;</td>
                </tr>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_sign_subtype'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_start_subtype' && $this->_tpl_vars['contract']->get('subtype') == 'annex'): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_start_subtype'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'date_end_subtype' && $this->_tpl_vars['contract']->get('subtype') == 'annex'): ?>
                <tr class="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' ) || ! $this->_tpl_vars['layout']['view']): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('date_end_subtype'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'referers'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if (( $this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off' )): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="unrequired">&nbsp;</td>
                  <td>
                    <?php if ($this->_tpl_vars['contract']->get('referers')): ?>
                      <?php $_from = $this->_tpl_vars['contract']->get('referers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['ref_id'] => $this->_tpl_vars['ref']):
        $this->_foreach['i']['iteration']++;
?>
                        <?php echo $this->_foreach['i']['iteration']; ?>
. <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['ref_id']; ?>
" target="_blank"><?php if ($this->_tpl_vars['ref']['num']): ?>[<?php echo $this->_tpl_vars['ref']['num']; ?>
]<?php else: ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?>&nbsp;<?php echo ((is_array($_tmp=$this->_tpl_vars['ref']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a><br />
                      <?php endforeach; endif; unset($_from); ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  </td>
                </tr>
                <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
                <tr id="contract_<?php echo $this->_tpl_vars['layout']['keyword']; ?>
"<?php if ($this->_tpl_vars['layout']['visible'] && $this->_tpl_vars['layout']['cookie'] == 'off'): ?> style="display: none;"<?php endif; ?>>
                  <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name'],'text_content' => $this->_tpl_vars['layout']['description']), $this);?>
</td>
                  <td class="required"><?php if (in_array ( $this->_tpl_vars['layout']['keyword'] , $this->_tpl_vars['required_fields'] )): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
                  <td>
                    <?php if ($this->_tpl_vars['department']): ?>
                      <?php if (! $this->_tpl_vars['department']->isActivated()): ?>
                        <span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"> *<?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
                      <?php else: ?>
                        <?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

                      <?php endif; ?>
                    <?php endif; ?>
                  </td>
                </tr>
                <?php endif; ?>

                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
              </table>
            </td>
          </tr>
        </table>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['contract'],'exclude' => 'is_portal')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
    </td>
    <?php if (isset ( $this->_tpl_vars['side_panels'] )): ?>
    <td class="side_panel_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_side_panel_options.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

      <?php $this->assign('pedit', $this->_tpl_vars['contract']->checkPermissions('edit_vars')); ?>
      <?php $this->assign('pgedit', $this->_tpl_vars['contract']->checkPermissions('edit_global_vars')); ?>
      <?php $this->assign('pview', $this->_tpl_vars['contract']->checkPermissions('view_vars')); ?>
      <?php $this->assign('pgview', $this->_tpl_vars['contract']->checkPermissions('view_global_vars')); ?>
      <?php if ($this->_tpl_vars['contract']->get('formula_vars') && $this->_tpl_vars['contract']->get('subtype') != 'original' && ( $this->_tpl_vars['pedit'] || $this->_tpl_vars['pgedit'] || $this->_tpl_vars['pview'] || $this->_tpl_vars['pgview'] )): ?>
      <div id="formula_vars" class="info_extra_panel">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_vars.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['contract']->get('subelements')): ?>
      <div class="info_extra_panel">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_contracts_info_panel.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </div>
      <?php endif; ?>

      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."side_panels_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
    <?php endif; ?>
  </tr>
</table>
<br />
<br />
<?php if (isset ( $this->_tpl_vars['side_panels'] ) && in_array ( 'related_records' , $this->_tpl_vars['side_panels'] ) && $this->_tpl_vars['related_records_modules']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="subpanel_container">
  <tr>
    <td>
      <?php if ($_COOKIE['contracts_selected_related_tab'] && in_array ( $_COOKIE['contracts_selected_related_tab'] , $this->_tpl_vars['related_records_modules'] )): ?>
        <?php $this->assign('rel_type', $_COOKIE['contracts_selected_related_tab']); ?>
      <?php else: ?>
        <?php $this->assign('rel_type', $this->_tpl_vars['related_records_modules']['0']); ?>
      <?php endif; ?>
      <input type="hidden" id="rel_type" name="rel_type" value="<?php echo $this->_tpl_vars['rel_type']; ?>
" />
      <a name="related_subpanel_contract<?php echo $this->_tpl_vars['contract']->get('id'); ?>
"></a>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."related_records_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <div class="m_header_m_menu scroll_box_container">
        <?php $_from = $this->_tpl_vars['related_records_modules']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['model'] => $this->_tpl_vars['module']):
?>
          <div id="<?php echo $this->_tpl_vars['session_params'][$this->_tpl_vars['module']]; ?>
" class="rel_tab<?php if ($this->_tpl_vars['rel_type'] == $this->_tpl_vars['module']): ?> loaded<?php else: ?>" style="display: none;<?php endif; ?>">
            <?php if ($this->_tpl_vars['rel_type'] == $this->_tpl_vars['module']): ?>
              <script type="text/javascript">
                ajaxUpdater({
                  link: '<?php echo $this->_tpl_vars['related'][$this->_tpl_vars['module']]; ?>
',
                  target: '<?php echo $this->_tpl_vars['session_params'][$this->_tpl_vars['module']]; ?>
',
                  execute_after: function() { removeClass($('related_records_action_tabs'), 'hidden'); } 
                });
              </script>
            <?php endif; ?>
          </div>
        <?php endforeach; endif; unset($_from); ?>
      </div>
    </td>
  </tr>
</table>
<?php endif; ?>