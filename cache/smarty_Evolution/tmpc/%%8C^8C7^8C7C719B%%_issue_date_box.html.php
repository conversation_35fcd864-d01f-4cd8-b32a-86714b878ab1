<?php /* Smarty version 2.6.33, created on 2025-05-21 16:47:00
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_issue_date_box.html */ ?>
<form method="get" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
">
  <input type="hidden" name="<?php echo $this->_tpl_vars['module_param']; ?>
" value="finance" />
  <input type="hidden" name="controller" value="invoices_templates" />
  <input type="hidden" name="invoices_templates" id="invoices_templates" value="" />
  <input type="hidden" name="issue_invoice" id="issue_invoice" value="" />
  <input type="hidden" name="items" id="items" value="" />
  <table border="0" cellpadding="3" cellspacing="3" width="100%">
      <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_issue_date')): ?>
        <?php $this->assign('disallow_date_before', 0); ?>
      <?php else: ?>
        <?php $this->assign('disallow_date_before', 1); ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_future_issue_date')): ?>
        <?php $this->assign('disallow_date_after', 0); ?>
      <?php else: ?>
        <?php $this->assign('disallow_date_after', 1); ?>
      <?php endif; ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'issue_date','label' => $this->_config[0]['vars']['contracts_issue_date'],'help' => $this->_config[0]['vars']['help_finance_contracts_issue_date'],'width' => 200,'show_calendar_icon' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php $this->assign('disallow_date_before', 0); ?>
      <?php $this->assign('disallow_date_after', 0); ?>
    <tr>
      <td colspan="3">
        <button type="submit" class="button" id="issue_btn"></button>
      </td>
    </tr>
  </table>
</form>