<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/system_settings_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/system_settings_box.html', 10, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_divs']): ?>
<div id="system_settings_container"
     <?php if (! $this->_tpl_vars['object']->checkPermissions('system_settings_active') && ! $this->_tpl_vars['object']->checkPermissions('system_settings_portal') && ! $this->_tpl_vars['object']->checkPermissions('system_settings_group')): ?> style="display: none"<?php endif; ?>>
  <div id="system_settings_details" <?php if ($_COOKIE['system_settings_box'] == 'off'): ?> style="display: none"<?php endif; ?>>
<?php endif; ?>
    <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
      <tr>
        <td class="labelbox strong" colspan="3"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</td>
      </tr>
<?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#active#' , $this->_tpl_vars['exclude'] )): ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_active.html", 'smarty_include_vars' => array('viewable' => $this->_tpl_vars['object']->checkPermissions('system_settings_active'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>
<?php if (! $this->_tpl_vars['exclude'] || ! preg_match ( '#groups#' , $this->_tpl_vars['exclude'] )): ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_groups.html", 'smarty_include_vars' => array('viewable' => $this->_tpl_vars['object']->checkPermissions('system_settings_group'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['include_portal_users_option'] && ! $this->_tpl_vars['currentUser']->get('is_portal') && ( ! $this->_tpl_vars['exclude'] || ! preg_match ( '#is_portal#' , $this->_tpl_vars['exclude'] ) )): ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_is_portal.html", 'smarty_include_vars' => array('viewable' => $this->_tpl_vars['object']->checkPermissions('system_settings_portal'))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>
    </table>
<?php if (! $this->_tpl_vars['hide_divs']): ?>
  </div>
  <div class="clear"></div>
  <div id="system_settings_switch" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_settings_show'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><div class="<?php if ($_COOKIE['system_settings_box'] == 'off'): ?>switch_down<?php else: ?>switch_up<?php endif; ?>"></div></div>
</div>
<?php endif; ?>