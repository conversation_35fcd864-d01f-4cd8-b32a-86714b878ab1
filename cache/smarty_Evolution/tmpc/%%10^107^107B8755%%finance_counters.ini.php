<?php $_config_vars = array (
  'finance' => '',
  'finance_counters' => 'Броячи',
  'finance_counters_name' => 'Име',
  'finance_counters_type' => 'Тип',
  'finance_counters_company' => 'Фирма',
  'finance_counters_office' => 'Офис',
  'finance_counters_offices' => 'Офиси',
  'finance_counters_next_number' => 'Следващ номер',
  'finance_counters_formula' => 'Формула',
  'finance_counters_formula_leading_zeroes' => 'Брой водещи нули',
  'finance_counters_formula_date_format' => 'формат',
  'finance_counters_formula_date_delimiter' => 'с разделител',
  'finance_counters_default' => 'По подразбиране',
  'finance_counters_formula_delimiter' => 'Разделител',
  'finance_counters_empty_delimiter' => 'без разделител',
  'finance_counters_cashbox' => 'Каса',
  'finance_counters_bank_account' => 'Банкова сметка',
  'finance_counters_model' => 'Вид',
  'finance_counters_model_Finance_Payment' => 'Плащане',
  'finance_counters_model_Finance_Transfer' => 'Трансфер',
  'finance_counters_model_Finance_Incomes_Reason' => 'Приходен документ',
  'finance_counters_model_Finance_Expenses_Reason' => 'Разходен документ',
  'finance_counters_model_Finance_Warehouses_Document' => 'Складов документ',
  'finance_counters_model_Finance_Annulment' => 'Протокол за анулиране',
  'finance_counters_description' => 'Описание',
  'finance_counters_model_type' => 'Тип',
  'finance_counters_model_type_PKO' => 'ПКО',
  'finance_counters_model_type_RKO' => 'РКО',
  'finance_counters_model_type_BP' => 'Банков превод',
  'finance_counters_model_type_PN' => 'Платежно нареждане',
  'finance_counters_model_type_TR' => 'Транзакционен разход',
  'finance_counters_model_type_transfer' => 'Трансфер',
  'finance_counters_company_independent' => 'Не зависи от фирма',
  'finance_counters_office_independent' => 'Не зависи от офис',
  'finance_counters_bank_account_cashbox_independent' => 'Не зависи от каса/банкова сметка',
  'finance_counters_add' => 'Добавяне на брояч',
  'finance_counters_edit' => 'Редакция на брояч',
  'finance_counters_translate' => 'Превод на брояч',
  'finance_counters_view' => 'Разглеждане на брояч',
  'finance_counters_formula_legend' => 'Легенда за попълването на формулата на брояча',
  'finance_counters_formula_date_format_year' => 'гггг',
  'finance_counters_formula_date_format_year_short' => 'гг',
  'finance_counters_formula_date_format_month' => 'мм',
  'finance_counters_formula_date_format_day' => 'дд',
  'finance_counters_formula_date_format1' => 'гггг',
  'finance_counters_formula_date_format2' => 'мм/гггг',
  'finance_counters_formula_date_format3' => 'мм/гг',
  'finance_counters_formula_date_format4' => 'гггг/мм',
  'finance_counters_formula_date_format5' => 'гг/мм',
  'finance_counters_formula_date_format6' => 'дд/мм/гггг',
  'finance_counters_formula_date_format7' => 'дд/мм/гг',
  'finance_counters_formula_date_format8' => 'мм/дд/гггг',
  'finance_counters_formula_date_format9' => 'мм/дд/гг',
  'finance_counters_formula_date_format10' => 'гггг/дд/мм',
  'finance_counters_formula_date_format11' => 'гг/дд/мм',
  'finance_counters_formula_date_format12' => 'гггг/мм/дд',
  'finance_counters_formula_date_format13' => 'гг/мм/дд',
  'finance_counters_formula_date_format14' => 'гг',
  'finance_counters_formula_date_format15' => 'ггг/мм',
  'finance_counters_formula_prefix' => 'Префикс',
  'finance_counters_formula_num' => 'Номер на документ',
  'finance_counters_formula_company_code' => 'Код на фирмата',
  'finance_counters_formula_office_code' => 'Код на офис',
  'finance_counters_formula_user_code' => 'Код на потребител',
  'finance_counters_formula_project_code' => 'Код на проект',
  'finance_counters_formula_document_date' => 'Дата на документа',
  'finance_counters_formula_prefix_descr' => 'попълва се директно с 2-3 букви.',
  'finance_counters_formula_num_descr' => 'попълва поредния номер на документ.',
  'finance_counters_formula_company_code_descr' => 'попълва кода на фирмата за документа.',
  'finance_counters_formula_office_code_descr' => 'попълва кода на офис, избран за документа.',
  'finance_counters_formula_user_code_descr' => 'попълва кода на потребител, създал документа.',
  'finance_counters_formula_project_code_descr' => 'попълва кода на проекта, избран за документа. Ако не е избран проект, код не се попълва.',
  'finance_counters_formula_document_date_descr' => 'дата на добавяне на документа.',
  'message_finance_counter_edit_success' => 'Броячът е редактиран успешно',
  'message_finance_counter_add_success' => 'Броячът е добавен успешно',
  'message_finance_counter_translate_success' => 'Броячът е преведен успешно',
  'error_no_such_finance_counter' => 'Няма брояч с този идентификационен номер',
  'error_finance_counter_add_failed' => 'Неуспешно добавяне на брояч',
  'error_finance_counter_edit_failed' => 'Неуспешна редакция на брояч',
  'error_finance_counter_translate_failed' => 'Неуспешен превод на брояч',
  'error_no_counter_name_specified' => 'Не е въведено име!',
  'error_exist_counter' => 'Съществува брояч с такива настройки',
  'error_no_model_type_specified' => 'Не е избран тип документ!',
  'error_no_counter_company_specified' => 'Не е избрана фирма!',
  'error_no_counter_formula_specified' => 'Изберете формула за брояча!',
  'error_no_counter_office' => 'Изберете поне една опция за офис!',
); ?>