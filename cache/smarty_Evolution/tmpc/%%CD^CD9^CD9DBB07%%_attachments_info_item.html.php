<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:50
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_attachments_info_item.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_attachments_info_item.html', 4, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_attachments_info_item.html', 9, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_attachments_info_item.html', 9, false),)), $this); ?>
<?php ob_start(); ?><?php if (file_exists ( $this->_tpl_vars['attachment']['path'] )): ?>1<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('fileExists', ob_get_contents());ob_end_clean(); ?>
<li class="nz-attachments-info_item">
  <?php if ($this->_tpl_vars['fileExists']): ?>
    <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['fileAction'])) ? $this->_run_mod_handler('default', true, $_tmp, 'viewfile') : smarty_modifier_default($_tmp, 'viewfile')); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('fileAction', ob_get_contents());ob_end_clean(); ?>
    <?php ob_start(); ?><?php echo ''; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['controller_action_string']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['fileAction']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['fileAction']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['model_id']; ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['attachment']['id']; ?><?php echo ''; ?><?php if ($this->_tpl_vars['archive']): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('fileUrl', ob_get_contents());ob_end_clean(); ?>
    <span class="nz-attachments-info_item--enum"><?php echo $this->_tpl_vars['n']; ?>
.</span>
    <span class="nz-attachments-info_item--name"><?php echo '<a href="'; ?><?php echo $this->_tpl_vars['fileUrl']; ?><?php echo '" target="_blank"><span title="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '">'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</span></a>'; ?>
</span>
    <span class="nz-attachments-info_item--filename"><?php echo '<a href="'; ?><?php echo $this->_tpl_vars['fileUrl']; ?><?php echo '" target="_blank"><span title="'; ?><?php echo $this->_tpl_vars['attachment']['filename']; ?><?php echo '">'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['filename'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</span></a>'; ?>
</span>
  <?php else: ?>
    <span class="nz-attachments-info_item--enum"><span class="material-icons nz-file-not-exist" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_file_not_exist'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">warning</span><?php echo $this->_tpl_vars['n']; ?>
.</span>
    <span class="nz-attachments-info_item--name"><span title="<?php echo ((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['name'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span></span>
    <span class="nz-attachments-info_item--filename"><span title="<?php echo ((is_array($_tmp=$this->_tpl_vars['attachment']['filename'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['attachment']['filename'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 20) : smarty_modifier_mb_truncate($_tmp, 20)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span></span>
  <?php endif; ?>
  <span class="nz-attachments-info_item--revision"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_revision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo $this->_tpl_vars['attachment']['revision']; ?>
</span>
</li>