<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/help_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/help_box.html', 8, false),)), $this); ?>
<?php if (( $this->_tpl_vars['help_text'] )): ?>
  <div class="nz-help-container">
    <div class="nz-record-section__caption<?php if ($_COOKIE['help_box'] != 'off'): ?> nz--oppen<?php endif; ?>"
         data-layoutid="help-show"
         data-cookie-name="help-show"
         data-body-id="help-box">
      <div class="nz-record-section__wrapper">
        <div class="nz-record-section__title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['help_information_panel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
        <div class="nz-record-section__indicator">
          <span class="material-icons">expand_more</span>
        </div>
      </div>
    </div>
    <div id="help-box" class="nz-record-section__body<?php if ($_COOKIE['help_box'] != 'off'): ?> nz--oppen<?php endif; ?>">
      <div class="nz-record-section--outline"><div><?php echo $this->_tpl_vars['help_text']->get('content'); ?>
</div></div>
    </div>
  </div>
<?php endif; ?>