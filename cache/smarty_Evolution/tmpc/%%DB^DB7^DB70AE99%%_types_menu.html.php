<?php /* Smarty version 2.6.33, created on 2025-05-21 13:23:50
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_types_menu.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_types_menu.html', 8, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['menu'] )): ?>
<div class="nz-types-menu nz-block nz-block__horizontal">
    <div class="nz-block-label"><?php echo $this->_config[0]['vars']['type']; ?>
</div>
    <div class="nz-block-content">
      <?php $this->assign('baseUrl', ($_SERVER['PHP_SELF'])."?".($this->_tpl_vars['module_param'])."=".($this->_tpl_vars['module'])."&amp;type_section="); ?>
      <?php echo '<a href="'; ?><?php echo $this->_tpl_vars['baseUrl']; ?><?php echo '&amp;type="class="nz-chip'; ?><?php if (! isset ( $this->_tpl_vars['type_section'] ) && ( ! isset ( $this->_tpl_vars['type'] ) || $this->_tpl_vars['type'] == '' )): ?><?php echo ' nz--active'; ?><?php endif; ?><?php echo ' nz-types__all">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</a>'; ?><?php $_from = $this->_tpl_vars['menu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['t'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['t']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['menuType']):
        $this->_foreach['t']['iteration']++;
?><?php echo '<a href="'; ?><?php echo $this->_tpl_vars['baseUrl']; ?><?php echo '&amp;type='; ?><?php echo $this->_tpl_vars['menuType']->get('id'); ?><?php echo '"class="nz-chip'; ?><?php if (! isset ( $this->_tpl_vars['type_section'] ) && $this->_tpl_vars['menuType']->get('id') == $this->_tpl_vars['type']): ?><?php echo ' nz--active'; ?><?php endif; ?><?php echo ' nz-types__'; ?><?php echo $this->_tpl_vars['menuType']->get('id'); ?><?php echo '">'; ?><?php echo $this->_tpl_vars['menuType']->get('name'); ?><?php echo '</a>'; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>

    </div>
</div>
<?php endif; ?>