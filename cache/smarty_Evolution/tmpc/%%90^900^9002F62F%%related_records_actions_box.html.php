<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:32
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/related_records_actions_box.html */ ?>
<?php if ($this->_tpl_vars['available_actions_related_records']): ?>
  <?php ob_start(); ?>related_subpanel_<?php echo $this->_tpl_vars['module']; ?>
<?php echo $this->_tpl_vars['model']->get('id'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents(); ob_end_clean(); ?>
  <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_selected_related_tab<?php $this->_smarty_vars['capture']['default'] = ob_get_contents(); ob_end_clean(); ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_related_records.html", 'smarty_include_vars' => array('related_records_modules' => $this->_tpl_vars['related_records_modules'],'subpanel_name' => ($this->_tpl_vars['subpanel_name']),'coockiename' => $this->_tpl_vars['coockiename'],'available_actions_related_records' => $this->_tpl_vars['available_actions_related_records'],'session_params' => $this->_tpl_vars['session_params'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php endif; ?>