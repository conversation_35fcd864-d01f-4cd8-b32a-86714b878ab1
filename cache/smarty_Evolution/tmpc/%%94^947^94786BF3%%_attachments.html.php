<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:55
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/_attachments.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/_attachments.html', 8, false),)), $this); ?>
<?php echo '
<script id="grid-col-_attachments" type="text/x-template">
  ${if(properties.files_count && properties.files_count != \'0\')}
  <a href="'; ?>
<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&<?php echo $this->_tpl_vars['action_param']; ?>
=attachments&attachments=<?php echo '${properties.id}"
        class="material-icons nz-grid-attachment nz-tooltip-trigger nz-tooltip-autoinit"
        data-tooltip-position="panel: left middle at: right middle"
        data-interactive="true"
        data-tooltip-title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '"
        data-endpoint="'; ?>
<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&<?php echo $this->_tpl_vars['action_param']; ?>
=ajax_getfiles&real_module=<?php echo $this->_tpl_vars['module']; ?>
&real_controller=<?php echo $this->_tpl_vars['controller']; ?>
&model_id=<?php echo '${properties.id}"
  >'; ?>
<?php echo $this->_tpl_vars['theme']->getIconForAction('attachments'); ?>
<?php echo '</a>
  ${/if}
</script>
'; ?>
