<?php $_config_vars = array (
  'contracts' => 'Договори',
  'contracts_counters' => 'Броячи',
  'contracts_counters_name' => 'Име',
  'contracts_counters_model_type' => 'Тип',
  'contracts_counters_company' => 'Фирма',
  'contracts_counters_office' => 'Офис',
  'contracts_counters_next_number' => 'Текущ номер',
  'contracts_counters_formula' => 'Формула',
  'contracts_counters_prefix_annex' => 'Префикс за споразумения',
  'contracts_counters_description' => 'Описание',
  'contracts_counters_formula_delimiter' => 'Разделител',
  'contracts_counters_empty_delimiter' => 'без разделител',
  'contracts_counters_formula_leading_zeroes' => 'Брой водещи нули',
  'contracts_counters_formula_date_format' => 'формат',
  'contracts_counters_formula_date_delimiter' => 'с разделител',
  'contracts_counters_office_independent' => 'Не зависи от офис',
  'contracts_counters_add' => 'Добавяне на брояч',
  'contracts_counters_edit' => 'Редакция на брояч',
  'contracts_counters_translate' => 'Превод на брояч',
  'contracts_counters_view' => 'Разглеждане на брояч',
  'contracts_counters_formula_legend' => 'Легенда за попълване на формулата на брояча',
  'contracts_counters_formula_date_format_year' => 'гггг',
  'contracts_counters_formula_date_format_year_short' => 'гг',
  'contracts_counters_formula_date_format_month' => 'мм',
  'contracts_counters_formula_date_format_day' => 'дд',
  'contracts_counters_formula_date_format1' => 'гггг',
  'contracts_counters_formula_date_format2' => 'мм/гггг',
  'contracts_counters_formula_date_format3' => 'мм/гг',
  'contracts_counters_formula_date_format4' => 'гггг/мм',
  'contracts_counters_formula_date_format5' => 'гг/мм',
  'contracts_counters_formula_date_format6' => 'дд/мм/гггг',
  'contracts_counters_formula_date_format7' => 'дд/мм/гг',
  'contracts_counters_formula_date_format8' => 'мм/дд/гггг',
  'contracts_counters_formula_date_format9' => 'мм/дд/гг',
  'contracts_counters_formula_date_format10' => 'гггг/дд/мм',
  'contracts_counters_formula_date_format11' => 'гг/дд/мм',
  'contracts_counters_formula_date_format12' => 'гггг/мм/дд',
  'contracts_counters_formula_date_format13' => 'гг/мм/дд',
  'contracts_counters_formula_date_format14' => 'гг',
  'contracts_counters_formula_date_format15' => 'ггг/мм',
  'contracts_counters_formula_prefix' => 'Префикс',
  'contracts_counters_formula_num' => 'Номер на договор',
  'contracts_counters_formula_company_code' => 'Код на фирма',
  'contracts_counters_formula_office_code' => 'Код на офис',
  'contracts_counters_formula_user_code' => 'Код на потребител',
  'contracts_counters_formula_customer_code' => 'Код на контрагент',
  'contracts_counters_formula_customer_num' => 'Номер на договор към контрагент',
  'contracts_counters_formula_customer_year' => 'Само за текущата година',
  'contracts_counters_formula_parent_num' => 'Номер на рамков договор',
  'contracts_counters_formula_subnum' => 'Номер на договор по рамков договор',
  'contracts_counters_formula_document_date' => 'Дата на договор',
  'contracts_counters_formula_prefix_descr' => 'попълва се директно с 2-3 букви.',
  'contracts_counters_formula_num_descr' => 'попълва поредния номер на договор.',
  'contracts_counters_formula_company_code_descr' => 'попълва кода на фирмата за договора.',
  'contracts_counters_formula_office_code_descr' => 'попълва кода на офис, избран за договора.',
  'contracts_counters_formula_user_code_descr' => 'попълва кода на потребител, създал договора.',
  'contracts_counters_formula_customer_code_descr' => 'попълва кода на контрагента за договора.',
  'contracts_counters_formula_customer_num_descr' => 'попълва пореден номер на договор към контрагент. Номерът може да бъде зададен спрямо договорите, приключени през текущата година, или всички.',
  'contracts_counters_formula_parent_num_descr' => 'попълва номер на рамков договор, по който е договорът.',
  'contracts_counters_formula_subnum_descr' => 'попълва пореден (в рамките на рамковия договор) номер на договора.',
  'contracts_counters_formula_document_date_descr' => 'дата на приключване на договора.',
  'message_contracts_counter_edit_success' => 'Броячът е редактиран успешно',
  'message_contracts_counter_add_success' => 'Броячът е добавен успешно',
  'message_contracts_counter_translate_success' => 'Броячът е преведен успешно',
  'error_no_such_contract_counter' => 'Нямате възможност да прегледате този запис!',
  'error_contracts_counter_add_failed' => 'Грешка при добавяне на брояч',
  'error_contracts_counter_edit_failed' => 'Грешка при редакцията на брояч',
  'error_contracts_counter_translate_failed' => 'Грешка при превода на брояч',
  'error_no_counter_name_specified' => 'Не е въведено име!',
  'error_no_counter_model_type_specified' => 'Не е избран тип!',
  'error_no_counter_company_specified' => 'Не е избрана фирма!',
  'error_no_counter_formula_specified' => 'Не сте въвели формула!',
  'error_exist_counter' => 'Съществува брояч с такива настройки.',
  'error_contracts_counter_mutex_num' => 'Формулата за брояча може да съдържа само един от елементите [num], [subnum] и [customer_num]!',
); ?>