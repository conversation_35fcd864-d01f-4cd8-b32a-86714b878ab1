<?php /* Smarty version 2.6.33, created on 2025-05-27 12:06:24
         compiled from _side_panel_options_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_side_panel_options_panel.html', 11, false),)), $this); ?>
  <!-- Side Panels Personal Manager -->
  <input type="hidden" name="side_panel_settings" id="side_panel_settings" value="1" />
  <input type="hidden" name="layout" id="layout" value="side_panels" />
  <input type="hidden" name="real_module" id="real_module" value="<?php echo $this->_tpl_vars['real_module']; ?>
" />
  <input type="hidden" name="real_controller" id="real_controller" value="<?php echo $this->_tpl_vars['real_controller']; ?>
" />
  <input type="hidden" name="real_model_type" id="real_model_type" value="<?php echo $this->_tpl_vars['real_model_type']; ?>
" />
  <input type="hidden" name="real_action" id="real_action" value="<?php echo $this->_tpl_vars['real_action']; ?>
" />
  <ol id="side_panel_all_settings">
    <?php $_from = $this->_tpl_vars['side_panels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['p'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['p']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['panel']):
        $this->_foreach['p']['iteration']++;
?>
      <li class="sortable">
        <input type="checkbox" id="side_panel_settings_<?php echo $this->_tpl_vars['panel']['name']; ?>
" name="side_panels[]" value="<?php echo $this->_tpl_vars['panel']['name']; ?>
"<?php if ($this->_tpl_vars['panel']['position'] !== ''): ?> checked="checked"<?php endif; ?> /> <label for="side_panel_settings_<?php echo $this->_tpl_vars['panel']['name']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['panel']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
      </li>
    <?php endforeach; endif; unset($_from); ?>
  </ol>
  <script type="text/javascript">
      Position.includeScrollOffsets = true;
      Sortable.create('side_panel_all_settings', {tag: 'LI',
                                                containment: 'side_panel_all_settings',
                                                constraint: 'vertical',
                                                only: 'sortable',
                                                scroll: 'side_panel_all_options'
                                               });
  </script>