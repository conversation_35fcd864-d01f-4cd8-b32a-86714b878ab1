<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from layout/mainmenu_item.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'regex_replace', 'layout/mainmenu_item.html', 6, false),array('modifier', 'escape', 'layout/mainmenu_item.html', 6, false),array('modifier', 'replace', 'layout/mainmenu_item.html', 6, false),)), $this); ?>
<ul class="nz-main-menu-list">
    <?php ob_start(); ?>/&(?![a-zA-Z]{2,5};)/<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('amp_regex', ob_get_contents());ob_end_clean(); ?>
    <?php $_from = $this->_tpl_vars['menu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['m'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['m']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['menu_item']):
        $this->_foreach['m']['iteration']++;
?>
    <?php $this->assign('subitems', $this->_tpl_vars['menu_item']->getSubitems()); ?>
    <li class="nz-main-menu-item<?php if ($this->_tpl_vars['menu_item']->isSelected()): ?> nz--selected<?php endif; ?>">
        <a href="<?php if (empty ( $this->_tpl_vars['subitems'] )): ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['menu_item']->getUrl())) ? $this->_run_mod_handler('regex_replace', true, $_tmp, $this->_tpl_vars['amp_regex'], '&amp;') : smarty_modifier_regex_replace($_tmp, $this->_tpl_vars['amp_regex'], '&amp;')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('replace', true, $_tmp, '&amp;', '&') : smarty_modifier_replace($_tmp, '&amp;', '&')); ?>
<?php else: ?>#<?php endif; ?>"
           class="nz-main-menu-item--action<?php if (! empty ( $this->_tpl_vars['subitems'] )): ?> nz-main-menu-item--has-submenu<?php endif; ?>"
           title="<?php echo $this->_tpl_vars['menu_item']->getLegend(); ?>
"
           target="<?php echo $this->_tpl_vars['menu_item']->getTarget(); ?>
">
            <span class="nz-main-menu-item--graphic"><?php echo $this->_tpl_vars['menu_item']->renderIcon(); ?>
</span>
            <span class="nz-main-menu-item--label"><?php echo $this->_tpl_vars['menu_item']->getI18n(); ?>
</span>
        </a>
        <div class="nz-main-menu-item--submenu">
            <?php if (! empty ( $this->_tpl_vars['subitems'] )): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."layout/mainmenu_item.html", 'smarty_include_vars' => array('menu' => $this->_tpl_vars['subitems'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
        </div>
    </li>
    <?php endforeach; endif; unset($_from); ?>
</ul>