<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from layout/navbar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'layout/navbar.html', 6, false),array('modifier', 'regex_replace', 'layout/navbar.html', 6, false),array('modifier', 'escape', 'layout/navbar.html', 7, false),)), $this); ?>
<?php ob_start(); ?>/&(?![a-zA-Z]{2,5};)/<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('amp_regex', ob_get_contents());ob_end_clean(); ?>
<ul class="nz-navbar-list"><?php echo ''; ?><?php $_from = $this->_tpl_vars['navBar']->getMenu(); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['nb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['nb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['item']):
        $this->_foreach['nb']['iteration']++;
?><?php echo '<li class="nz-navbar-item'; ?><?php if (($this->_foreach['nb']['iteration'] == $this->_foreach['nb']['total'])): ?><?php echo ' nz--active"'; ?><?php endif; ?><?php echo '">'; ?><?php if ($this->_tpl_vars['item']->getUrl()): ?><?php echo '<a href="'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['item']->getUrl())) ? $this->_run_mod_handler('default', true, $_tmp, '#') : smarty_modifier_default($_tmp, '#')))) ? $this->_run_mod_handler('regex_replace', true, $_tmp, $this->_tpl_vars['amp_regex'], '&amp;') : smarty_modifier_regex_replace($_tmp, $this->_tpl_vars['amp_regex'], '&amp;')); ?><?php echo '"title="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->getLegend())) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '">'; ?><?php if ($this->_tpl_vars['item']->getIcon()): ?><?php echo ''; ?><?php echo $this->_tpl_vars['item']->renderIcon(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['item']->getI18n()): ?><?php echo ' '; ?><?php echo $this->_tpl_vars['item']->getI18n(); ?><?php echo ''; ?><?php endif; ?><?php echo '</a>'; ?><?php else: ?><?php echo '<span title="'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['item']->getLegend())) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '">'; ?><?php if ($this->_tpl_vars['item']->getIcon()): ?><?php echo ''; ?><?php echo $this->_tpl_vars['item']->renderIcon(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['item']->getI18n()): ?><?php echo ' '; ?><?php echo $this->_tpl_vars['item']->getI18n(); ?><?php echo ''; ?><?php endif; ?><?php echo '</span>'; ?><?php endif; ?><?php echo '</li>'; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>
</ul>