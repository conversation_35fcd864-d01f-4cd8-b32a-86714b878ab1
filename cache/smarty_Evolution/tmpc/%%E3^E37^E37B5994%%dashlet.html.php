<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:46
         compiled from /var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 7, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 10, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 23, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 30, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 34, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 52, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 38, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/minitasks/view/templates/dashlet.html', 100, false),)), $this); ?>

<form name="minitasks" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=minitasks" method="post">
  <div id="communication_messages_container<?php echo $this->_tpl_vars['dashlet_id']; ?>
"></div>
  <table cellpadding="0" border="0" cellspacing="0" width="100%" class="t_table t_list">
    <tr>
      <?php $_from = $this->_tpl_vars['minitasks_columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['column']):
?>
      <th class="t_caption t_border <?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['class']; ?>
" nowrap="nowrap" style="width: <?php echo ((is_array($_tmp=@$this->_tpl_vars['column_widths'][$this->_tpl_vars['column']])) ? $this->_run_mod_handler('default', true, $_tmp, "") : smarty_modifier_default($_tmp, "")); ?>
;<?php if (! in_array ( $this->_tpl_vars['column'] , $this->_tpl_vars['columns'] )): ?> display: none;<?php endif; ?>">
        <div onclick="<?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['link']; ?>
">
          <?php ob_start(); ?>minitasks_<?php echo $this->_tpl_vars['column']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('column_name', ob_get_contents());ob_end_clean(); ?>
          <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['column_name']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

        </div>
      </th>
      <?php endforeach; endif; unset($_from); ?>
      <td class="t_caption hright" nowrap="nowrap" style="vertical-align: middle; width: 104px;">
        <?php if ($this->_tpl_vars['currentUser']->checkRights('minitasks','add')): ?>
        <span class="nz-icon-button" onclick="insertNewMinitaskRow(this, <?php echo $this->_tpl_vars['dashlet_id']; ?>
);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['minitasks_add_new'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">add</span>
        <?php else: ?>
        <span class="nz-icon-button" onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_add_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['minitasks_add_new'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">add</span>
        <?php endif; ?>
      </td>
    </tr>
        <?php $this->assign('row_index', count($this->_tpl_vars['minitasks'])); ?>
    <tr class="t_selected_row_for_edit row_data_<?php echo $this->_tpl_vars['row_index']; ?>
" id="row_data_<?php echo $this->_tpl_vars['row_index']; ?>
" style="display: none">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_minitasks_edit.html", 'smarty_include_vars' => array('minitask' => $this->_tpl_vars['empty_minitask'],'row_index' => $this->_tpl_vars['row_index'],'real_module' => $this->_tpl_vars['module'],'real_controller' => $this->_tpl_vars['controller'],'real_action' => $this->_tpl_vars['action'],'autocomplete_filters' => $this->_tpl_vars['customer_autocomplete_filters'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </tr>
    <?php $_from = $this->_tpl_vars['minitasks']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['row_index'] => $this->_tpl_vars['minitask']):
        $this->_foreach['i']['iteration']++;
?>
    <?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['status_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('status_modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('status_modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['minitask']->get('status') != 'opened'): ?><?php echo '<strong>'; ?><?php echo $this->_config[0]['vars']['comment']; ?><?php echo '</strong>: '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('comment'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 row_data_<?php echo $this->_tpl_vars['row_index']; ?>
 <?php echo $this->_tpl_vars['minitask']->get('severity'); ?>
" id="row_data_<?php echo $this->_tpl_vars['row_index']; ?>
">
        <?php $_from = $this->_tpl_vars['minitasks_columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['column']):
?>
        <td class="t_border <?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['isSorted']; ?>
"<?php if (! in_array ( $this->_tpl_vars['column'] , $this->_tpl_vars['columns'] )): ?> style="display: none;"<?php endif; ?>>
        <?php if ($this->_tpl_vars['column'] == 'for_record'): ?>
          <?php if ($this->_tpl_vars['minitask']->get('model_id')): ?>
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['minitask']->get('module'); ?>
&amp;<?php if ($this->_tpl_vars['minitask']->get('controller')): ?><?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['minitask']->get('controller'); ?>
&amp;<?php echo $this->_tpl_vars['minitask']->get('controller'); ?>
<?php else: ?><?php echo $this->_tpl_vars['minitask']->get('module'); ?>
<?php endif; ?>=view&amp;view=<?php echo $this->_tpl_vars['minitask']->get('model_id'); ?>
<?php if ($this->_tpl_vars['minitask']->get('archive')): ?>&amp;archive=1<?php endif; ?>"
               title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('record_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
"><?php if ($this->_tpl_vars['minitask']->get('record_num')): ?>[<?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('record_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
]<?php elseif ($this->_tpl_vars['minitask']->get('model') == 'Contract'): ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['minitasks_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?> <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('record_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
          <?php else: ?>
            &nbsp;
          <?php endif; ?>
        <?php elseif ($this->_tpl_vars['column'] == 'customer'): ?>
          <?php if ($this->_tpl_vars['minitask']->get('customer')): ?><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['minitask']->get('customer'); ?>
"
                                            title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['minitask']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a><?php else: ?>&nbsp;<?php endif; ?>
        <?php elseif ($this->_tpl_vars['column'] == 'description'): ?>
          <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

        <?php elseif ($this->_tpl_vars['column'] == 'deadline'): ?>
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        <?php elseif ($this->_tpl_vars['column'] == 'assigned_to'): ?>
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get('assigned_to_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        <?php else: ?>
          <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['minitask']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        <?php endif; ?>
        </td>
       <?php endforeach; endif; unset($_from); ?>
        <td class="hcenter nz-buttons-wrapper" nowrap="nowrap">
          <?php if ($this->_tpl_vars['minitask']->checkPermissions('edit')): ?>
            <span class="nz-icon-button" id="img_edit_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_edit[<?php echo $this->_tpl_vars['row_index']; ?>
]" title="<?php echo $this->_config[0]['vars']['edit']; ?>
"
                  onclick="manageMinitask(this.form, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['action']; ?>
', 'edit', this, <?php echo $this->_tpl_vars['minitask']->get('id'); ?>
); return false;"
            ><?php echo $this->_tpl_vars['theme']->getIconForAction('edit'); ?>
</span>
          <?php else: ?>
            <span class="nz-icon-button" id="img_edit_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_edit[<?php echo $this->_tpl_vars['row_index']; ?>
]"
                  onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_edit_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"><?php echo $this->_tpl_vars['theme']->getIconForAction('edit'); ?>
</span>
          <?php endif; ?>
          <?php if ($this->_tpl_vars['minitask']->checkPermissions('setstatus')): ?>
            <span class="nz-icon-button img_finished_<?php echo $this->_tpl_vars['row_index']; ?>
" id="img_finished_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_finished[<?php echo $this->_tpl_vars['row_index']; ?>
]" title="<?php echo $this->_config[0]['vars']['minitasks_finish']; ?>
"
                  onclick="changeMinitaskStatus(this.className, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['action']; ?>
', <?php echo $this->_tpl_vars['minitask']->get('id'); ?>
, 'finished'); return false;"><?php echo $this->_tpl_vars['theme']->getIconForAction('communications_minitasks_finish'); ?>
</span>
            <span class="nz-icon-button img_failed_<?php echo $this->_tpl_vars['row_index']; ?>
" id="img_failed_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_failed[<?php echo $this->_tpl_vars['row_index']; ?>
]"  title="<?php echo $this->_config[0]['vars']['minitasks_cancel']; ?>
"
                  onclick="changeMinitaskStatus(this.className, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['action']; ?>
', <?php echo $this->_tpl_vars['minitask']->get('id'); ?>
, 'failed'); return false;"><?php echo $this->_tpl_vars['theme']->getIconForAction('communications_minitasks_fail'); ?>
</span>
          <?php else: ?>
          <?php if ($this->_tpl_vars['minitask']->get('status') != 'failed'): ?>
            <span class="nz-icon-button nz--finished" id="img_finished_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_finished[<?php echo $this->_tpl_vars['row_index']; ?>
]" title="<?php if ($this->_tpl_vars['minitask']->get('status') == 'finished'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['minitasks_status_finished'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo $this->_config[0]['vars']['minitasks_finish']; ?>
<?php endif; ?>"
                  onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_changestatus_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"><?php echo $this->_tpl_vars['theme']->getIconForAction('communications_minitasks_finish'); ?>
</span>
          <?php endif; ?>
          <?php if ($this->_tpl_vars['minitask']->get('status') != 'finished'): ?>
            <span class="nz-icon-button nz--failed" id="img_failed_<?php echo $this->_tpl_vars['row_index']; ?>
" name="img_failed[<?php echo $this->_tpl_vars['row_index']; ?>
]" title="<?php if ($this->_tpl_vars['minitask']->get('status') == 'failed'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['minitasks_status_failed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo $this->_config[0]['vars']['minitasks_cancel']; ?>
<?php endif; ?>"
                  onclick="alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_changestatus_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"><?php echo $this->_tpl_vars['theme']->getIconForAction('communications_minitasks_fail'); ?>
</span>
          <?php endif; ?>
          <?php endif; ?>
          <div class="nz-tooltip-content" id="minitask_info_<?php echo $this->_tpl_vars['minitask']->get('id'); ?>
"><?php echo $this->_tpl_vars['info']; ?>
</div>
          <span class="nz-tooltip-trigger nz-tooltip-autoinit"
                data-tooltip-position="panel: bottom center at: top center"
                data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                data-tooltip-element="#minitask_info_<?php echo $this->_tpl_vars['minitask']->get('id'); ?>
"
          ><i class="nz-tooltip--icon material-icons">info</i></span>
          <?php if ($this->_tpl_vars['minitask']->checkPermissions('edit') && $this->_tpl_vars['minitask']->get('status') == 'opened'): ?>
          <span id="img_severity_<?php echo $this->_tpl_vars['row_index']; ?>
" class="nz-icon-button img_severity_<?php echo $this->_tpl_vars['row_index']; ?>
<?php if ($this->_tpl_vars['dashlet_id']): ?>_<?php echo $this->_tpl_vars['dashlet_id']; ?>
<?php endif; ?> <?php echo $this->_tpl_vars['minitask']->get('severity'); ?>
" name="img_severity[<?php echo $this->_tpl_vars['row_index']; ?>
]" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['minitasks_severity'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                onclick="showSeveritySlider(this, '<?php echo $this->_tpl_vars['action']; ?>
', <?php echo $this->_tpl_vars['minitask']->get('id'); ?>
); return false;"><?php echo $this->_tpl_vars['theme']->getIconForAction('communications_minitasks_severity'); ?>
</span>
          <?php endif; ?>
        </td>
      </tr>
    <?php endforeach; else: ?>
      <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
        <td class="error" colspan="<?php echo smarty_function_math(array('equation' => 'count+1','count' => count($this->_tpl_vars['columns'])), $this);?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      </tr>
    <?php endif; unset($_from); ?>
  </table>
  <div class="pagemenu">
    <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=minitasks&amp;minitasks=dashlet&amp;dashlet=<?php echo $this->_tpl_vars['dashlet_id']; ?>
&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
    <?php ob_start(); ?>content_dashlet_<?php echo $this->_tpl_vars['dashlet_id']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('container', ob_get_contents());ob_end_clean(); ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'target' => $this->_tpl_vars['container'],'link' => $this->_tpl_vars['link'],'use_ajax' => 1,'hide_rpp' => 1,'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </div>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_severity_legend.html", 'smarty_include_vars' => array('prefix' => 'minitasks')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</form>