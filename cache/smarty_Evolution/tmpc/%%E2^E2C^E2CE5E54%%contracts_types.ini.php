<?php $_config_vars = array (
  'contracts_types' => 'Типове договори',
  'contracts_types_name' => 'Име',
  'contracts_types_name_plural' => 'Име (за списък и меню)',
  'contracts_types_status' => 'Статус',
  'contracts_types_status_active' => 'Активен',
  'contracts_types_status_inactive' => 'Неактивен',
  'contracts_types_added_by' => 'Добавен от',
  'contracts_types_modified_by' => 'Променен от',
  'contracts_types_added' => 'Добавен на',
  'contracts_types_modified' => 'Променен на',
  'contracts_types_description' => 'Описание',
  'contracts_types_inheritance' => 'Признак',
  'contracts_types_inheritance_primary' => 'Първичен',
  'contracts_types_inheritance_secondary' => 'Вторичен',
  'contracts_types_party' => 'Роля',
  'contracts_types_default_customer' => 'Контрагент по подразбиране',
  'contracts_types_opened_requires_comment' => 'Коментар при статус Отворен',
  'contracts_types_locked_requires_comment' => 'Коментар при статус Заключен',
  'contracts_types_closed_requires_comment' => 'Коментар при статус Затворен',
  'contracts_types_requires_completed_minitasks' => 'Изисквай приключване на мини задачи',
  'contracts_types_count_contracts' => 'Брой договори',
  'contracts_types_default_name' => 'Име на договора',
  'contracts_types_invoice_issue_auto_message' => 'Описание период фактура',
  'contracts_types_pattern' => 'Шаблон за печат',
  'contracts_types_email' => 'Шаблон за писмо',
  'contracts_types_department' => 'Отдел по подразбиране',
  'contracts_types_group' => 'Група по подразбиране',
  'contracts_types_default_user_group' => '[Група по подразбиране на текущия потребител]',
  'contracts_types_default_user_department' => '[Отдел по подразбиране на текущия потребител]',
  'contracts_types_code' => 'Код',
  'contracts_transformations' => 'Трансформации',
  'contracts_multitransformations' => 'Трансформации +',
  'contracts_default_settings' => 'Настройки по подразбиране',
  'contracts_system_settings' => 'Системни настройки',
  'contracts_types_gt2' => 'Ново поколение групова таблица',
  'contracts_types_gt2_layout' => 'Секция за таблицата',
  'contracts_types_assignment_types' => 'Типове назначения',
  'contracts_types_dates' => 'Дати',
  'contracts_types_date_sign' => 'Дата на подписване',
  'contracts_types_date_start' => 'Дата на влизане в сила',
  'contracts_types_date_validity' => 'Дата на изтичане',
  'contracts_types_date_end' => 'Дата на опция за прекратяване',
  'contracts_types_validate' => 'Допълнителни задължителни полета',
  'contracts_types_validate_unique' => 'Уникални полета',
  'contracts_types_validate_unique_current_year' => 'Само за текущата година',
  'contracts_types_sections' => 'Секции',
  'contracts_types_section_about' => 'предмет',
  'contracts_types_section_clause' => 'условия',
  'contracts_types_section_payments' => 'плащания',
  'contracts_types_section_phases' => 'етапи на изпълнение',
  'contracts_types_section_penalty' => 'неустойки',
  'contracts_types_amount' => 'Стойност',
  'contracts_types_amount_recurring' => 'регулярни плащания',
  'contracts_types_amount_amount' => 'стойност',
  'contracts_types_amount_hoursrate' => 'часова ставка',
  'contracts_types_commodity' => 'Стоково отношение',
  'contracts_commodity_relation_none' => 'не се издават стокови документи',
  'contracts_commodity_relation_incoming' => 'само приемателни протоколи',
  'contracts_commodity_relation_outgoing' => 'само предавателни протоколи',
  'contracts_commodity_relation_both' => 'приемателни и предавателни протоколи',
  'contracts_types_principal' => 'Възложител',
  'contracts_types_executor' => 'Изпълнител',
  'contracts_types_related_customers_types' => 'Типове контрагенти за АК',
  'contracts_types_company' => 'Фирма',
  'contracts_types_VAT' => 'Финансово отношение',
  'contracts_types_no_VAT' => 'Не се начислява ДДС',
  'contracts_types_cstm_VAT' => 'Начислява се ДДС само при фактуриране',
  'contracts_types_include_VAT' => 'Начислява се ДДС',
  'contracts_types_default_VAT' => 'Ставка по подразбиране',
  'contracts_types_company_no_VAT' => 'Няма регистрация по ДДС',
  'contracts_types_add' => 'Добавяне на тип договор',
  'contracts_types_edit' => 'Редакция на тип договор',
  'contracts_types_translate' => 'Превод на тип договор',
  'contracts_types_view' => 'Разглеждане на тип договор',
  'contracts_layouts_status' => 'Статус',
  'contracts_layouts_type' => 'Тип',
  'contracts_layouts_num' => 'Договор №',
  'contracts_layouts_custom_num' => 'Номер от контрагент',
  'contracts_layouts_name' => 'Относно',
  'contracts_layouts_customer' => 'Контрагент',
  'contracts_layouts_project' => 'Проект',
  'contracts_layouts_company' => 'Фирма',
  'contracts_layouts_office' => 'Офис',
  'contracts_layouts_employee' => 'Служител',
  'contracts_layouts_date_sign' => 'Дата на подписване на договора',
  'contracts_layouts_date_start' => 'Дата на влизане в сила на договора',
  'contracts_layouts_date_validity' => 'Дата на изтичане на договора',
  'contracts_layouts_date_end' => 'Дата на опция за прекратяване на договора',
  'contracts_layouts_date_sign_subtype' => 'Дата на подписване',
  'contracts_layouts_date_start_subtype' => 'Дата на влизане в сила на споразумението',
  'contracts_layouts_date_end_subtype' => 'Дата на изтичане на споразумението',
  'contracts_layouts_referers' => 'Свързани договори',
  'contracts_layouts_description' => 'Описание',
  'contracts_layouts_notes' => 'Бележки',
  'contracts_layouts_forward' => 'Разпределяне',
  'contracts_types_fiscal' => 'Издава финансов документ',
  'contracts_types_fiscal_start' => 'Издаване на финансови документи при влизане в сила на договореност',
  'contracts_types_calculated_price' => 'Цена за изчисленията',
  'contracts_types_gt2_price' => 'Продажна цена',
  'contracts_types_gt2_last_delivery_price' => 'Доставна цена',
  'contracts_types_crontab_notifications_settings' => 'Настройки за известяване на страни по договор<br /> преди влизане в сила и преди изтичане на договори и споразумения',
  'contracts_types_notify_start_users' => 'Известяване на собствена фирма преди влизане в сила',
  'contracts_types_notify_start_customers' => 'Известяване на контрагент преди влизане в сила',
  'contracts_types_before_start_contract' => 'Брой дни преди влизане в сила (разделени със запетаи)',
  'contracts_types_notify_end_users' => 'Известяване на собствена фирма преди изтичане',
  'contracts_types_notify_end_customers' => 'Известяване на контрагент преди изтичане',
  'contracts_types_before_end_contract' => 'Брой дни преди изтичане (разделени със запетаи)',
  'message_contracts_types_add_success' => 'Успешно добавяне на тип договор',
  'message_contracts_types_edit_success' => 'Успешно редактиране на тип договор',
  'message_contracts_types_translate_success' => 'Успешен превод на тип договор',
  'error_contracts_types_edit_failed' => 'НЕУСПЕШНО редактиране на тип договор:',
  'error_contracts_types_add_failed' => 'НЕУСПЕШНО добавяне на тип договор:',
  'error_contracts_types_translate_failed' => 'НЕУСПЕШЕН превод на тип договор:',
  'error_no_such_contract_type' => 'Нямате възможност да прегледате този запис!',
  'error_no_name_specified' => 'Моля, въведете име!',
  'error_no_typename_plural_specified' => 'Моля, въведете име (за списък и меню)!',
  'error_no_type_specified' => 'Изберете тип на договора!',
  'error_no_party_specified' => 'Изберете роля по договора!',
  'error_inheritance_packagedeal' => 'Рамков договор може да бъде само първичен!',
  'error_no_code' => 'Въведете код!',
  'error_code_not_unique' => 'Този код се използва. Моля, въведете друг код!',
  'error_no_gt2_layout_specified' => 'Моля, изберете секция за груповата таблица!',
  'error_no_before_start_contract' => 'Моля, въведете брой дни преди влизане в сила!',
  'error_wrong_before_start_contract' => 'Моля, въведете валиден брой дни преди влизане в сила!',
  'error_no_before_end_contract' => 'Моля, въведете брой дни преди изтичане!',
  'error_wrong_before_end_contract' => 'Моля, въведете валиден брой дни преди изтичане!',
  'help_contracts_types_name' => '',
  'help_contracts_types_name_plural' => '',
  'help_contracts_types_status' => '',
  'help_contracts_types_status_active' => '',
  'help_contracts_types_status_inactive' => '',
  'help_contracts_types_description' => '',
  'help_contracts_types_inheritance' => '',
  'help_contracts_types_inheritance_primary' => '',
  'help_contracts_types_inheritance_secondary' => '',
  'help_contracts_types_party' => '',
  'help_contracts_types_count_contracts' => '',
  'help_contracts_types_opened_requires_comment' => 'Изискване на коментар при избиране на статус Отворен',
  'help_contracts_types_locked_requires_comment' => 'Изискване на коментар при избиране на статус Заключен',
  'help_contracts_types_closed_requires_comment' => 'Изискване на коментар при избиране на статус Затворен',
  'help_contracts_types_default_name' => 'Полето дефинира име по подразбиране при добавяне на договор от съответния тип. В името могат да се включват следните променливи, които при въвеждане на нов договор ще бъдат заместени със съответните стойности: <br /><strong>[customer_name]</strong> - име на клиента<br /><strong>[office_name]</strong> - име на офиса, за който се отнася договорът.',
  'help_contracts_types_invoice_issue_auto_message' => 'Информация, записвана за периода на фактуриране на артикули във фактура, издадена от договора. Могат да се включват следните променливи, които ще бъдат заместени със съответните стойности: <br /><strong>[date_from]</strong> - начало на период<br /><strong>[date_to]</strong> - край на период<br /><strong>[contract_num]</strong> - номер на договор<br /><strong>[contract_date_sign]</strong> - дата на подписване на договор<br /><strong>[contract_date_start]</strong> - дата на влизане в сила на договор<br /><strong>[contract_date_validity]</strong> - дата на изтичане на договор',
  'help_contracts_types_department' => '',
  'help_contracts_types_group' => '',
  'help_contracts_types_code' => 'Кодът на типа договор се използва при броячите на договори. Препоръчително е кодът да се състои от латински символи и да не бъде повече от 3-4 символа.',
  'help_contracts_transformations' => '',
  'help_contracts_types_gt2_layout' => 'Изберете секцията, в която да се постави таблицата от новото поколоние. Имате възможност и да добавите секция. Новодобавената секция ще бъде с включени права за редакция и разглеждане за група "Всички".',
  'help_contracts_types_validate' => '',
  'help_contracts_types_validate_unique' => 'Валидацията се изпълнява само за договори от подвид "Договорно споразумение".',
  'help_contracts_types_validate_unique_current_year' => 'Дали се проверява за уникалност на настроените полета спрямо записи, добавени през текущата година, или спрямо всички.',
  'help_contracts_types_assignment_types' => '',
  'help_contracts_types_requires_completed_minitasks' => 'Изискване да няма неизпълнени мини задачи към записа, за да може да бъде приключен.',
  'help_contracts_types_related_customers_types' => '',
); ?>