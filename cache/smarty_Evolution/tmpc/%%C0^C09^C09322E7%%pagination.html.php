<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:45
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html', 4, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html', 34, false),array('function', 'rpp_menu', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html', 52, false),array('function', 'page_menu', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html', 139, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html', 8, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html', 12, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/pagination.html', 32, false),)), $this); ?>
<?php if (empty ( $this->_tpl_vars['session_param'] ) && ! empty ( $this->_tpl_vars['pagination'] ) && $this->_tpl_vars['pagination']['session_param']): ?><?php $this->assign('session_param', $this->_tpl_vars['pagination']['session_param']); ?><?php endif; ?>
<?php if (( $this->_tpl_vars['module'] == 'customers' && $this->_tpl_vars['controller'] == 'customers' && empty ( $this->_tpl_vars['filter_contactpersons'] ) || $this->_tpl_vars['module'] == 'tasks' && $this->_tpl_vars['controller'] == 'tasks' || $this->_tpl_vars['module'] == 'events' && $this->_tpl_vars['controller'] == 'events' ) && preg_match ( '/^(list|search|filter|dashlet|subpanel|my(assigned|observer|records|responsible))$/' , $this->_tpl_vars['action'] )): ?>
  <?php if (! $this->_tpl_vars['hide_stats']): ?>
    <?php echo smarty_function_counter(array('name' => 'stat_items_sequence','assign' => 'stat_items_sequence'), $this);?>

    <table border="0" width="100%" align="center">
      <tr>
        <td>
          <?php echo ((is_array($_tmp=$this->_config[0]['vars']['pagination_displayed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <strong>
          <?php if ($this->_tpl_vars['found'] > $this->_tpl_vars['rpp']): ?>
            <?php echo $this->_tpl_vars['found']-1; ?>

          <?php else: ?>
            <?php echo ((is_array($_tmp=@$this->_tpl_vars['found'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>

          <?php endif; ?></strong> 
          <?php echo ((is_array($_tmp=$this->_config[0]['vars']['pagination_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          <strong>
          <?php if ($this->_tpl_vars['found'] > $this->_tpl_vars['rpp']): ?>
            <span id="totalItemsFound_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
" class="<?php echo $this->_tpl_vars['session_param']; ?>
_total">
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/indicator.gif" alt="" />
              <script type="text/javascript">
                getTotalResults({'module': '<?php echo $this->_tpl_vars['module']; ?>
', 'controller': '<?php echo $this->_tpl_vars['controller']; ?>
', 'action': '<?php echo $this->_tpl_vars['action']; ?>
', 'session_param': '<?php echo $this->_tpl_vars['session_param']; ?>
', 'total_container': 'totalItemsFound_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
', 'pages_container' : 'totalPagesFound_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
'});
              </script>
            </span>
          <?php else: ?>
            <?php ob_start(); ?><?php echo $this->_tpl_vars['page']*$this->_tpl_vars['rpp']-$this->_tpl_vars['rpp']+$this->_tpl_vars['found']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('total', ob_get_contents());ob_end_clean(); ?><?php echo $this->_tpl_vars['total']; ?>

          <?php endif; ?>
          </strong>
          <?php if (! $this->_tpl_vars['hide_selection_stats']): ?>
            /
            <?php echo ((is_array($_tmp=$this->_config[0]['vars']['count_selected_items'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:
            <span id="selectedItemsCount_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
" class="selected_items_span">
            <?php if ($this->_tpl_vars['selected_items']['ids']): ?>
              <?php echo count($this->_tpl_vars['selected_items']['ids']); ?>

            <?php elseif ($this->_tpl_vars['selected_items']['select_all'] && $this->_tpl_vars['total'] > 0): ?>
              <?php echo smarty_function_math(array('equation' => "all - ignored",'all' => $this->_tpl_vars['total'],'ignored' => count($this->_tpl_vars['selected_items']['ignore_ids'])), $this);?>

            <?php else: ?>
              0
            <?php endif; ?>
            </span>
          <?php endif; ?>
        </td>
        <td align="right">
          <?php echo ((is_array($_tmp=$this->_config[0]['vars']['pagination_page'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <strong><?php echo ((is_array($_tmp=@$this->_tpl_vars['page'])) ? $this->_run_mod_handler('default', true, $_tmp, 1) : smarty_modifier_default($_tmp, 1)); ?>
/<?php if ($this->_tpl_vars['found'] > $this->_tpl_vars['rpp']): ?><span id="totalPagesFound_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
" class="<?php echo $this->_tpl_vars['session_param']; ?>
_pages"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/indicator.gif" alt="" /></span><?php else: ?><?php echo $this->_tpl_vars['page']; ?>
<?php endif; ?></strong>
        </td>
      </tr>
    </table>
  <?php endif; ?>
  <?php if (! $this->_tpl_vars['hide_menu'] && ( $this->_tpl_vars['found'] > 5 || $this->_tpl_vars['page'] > 1 )): ?>
    <table border="0" width="100%" align="center">
      <tr>
      <?php if (! $this->_tpl_vars['hide_rpp']): ?>
        <td>
        <?php echo smarty_function_rpp_menu(array('pages' => $this->_tpl_vars['pages'],'rpp' => $this->_tpl_vars['rpp'],'link' => $this->_tpl_vars['link'],'label_display_menu' => $this->_config[0]['vars']['display'],'use_ajax' => $this->_tpl_vars['use_ajax'],'session_param' => $this->_tpl_vars['session_param']), $this);?>

        </td>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['page'] != 1 || $this->_tpl_vars['found'] > $this->_tpl_vars['rpp']): ?>
        <td align="right">
        <?php if ($this->_tpl_vars['page'] != 1): ?>
          <?php if ($this->_tpl_vars['use_ajax']): ?>
            <span class="page_menu_link page_menu_link__special" onclick="ajaxUpdater({link:'<?php echo $this->_tpl_vars['link']; ?>
1&amp;source=ajax&amp;session_param=<?php echo $this->_tpl_vars['session_param']; ?>
', target:'<?php echo ((is_array($_tmp=@$this->_tpl_vars['target'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['session_param'])); ?>
'});"><?php echo $this->_config[0]['vars']['first_page']; ?>
</span>
            <span class="page_menu_link page_menu_link__special" onclick="ajaxUpdater({link:'<?php echo $this->_tpl_vars['link']; ?>
<?php echo $this->_tpl_vars['page']-1; ?>
&amp;source=ajax&amp;session_param=<?php echo $this->_tpl_vars['session_param']; ?>
', target:'<?php echo ((is_array($_tmp=@$this->_tpl_vars['target'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['session_param'])); ?>
'});"><?php echo $this->_config[0]['vars']['previous_page']; ?>
</span>
          <?php else: ?>
            <a class="page_menu_link page_menu_link__special" href="<?php echo $this->_tpl_vars['link']; ?>
1"><?php echo $this->_config[0]['vars']['first_page']; ?>
</a>
            <a class="page_menu_link page_menu_link__special" href="<?php echo $this->_tpl_vars['link']; ?>
<?php echo $this->_tpl_vars['page']-1; ?>
"><?php echo $this->_config[0]['vars']['previous_page']; ?>
</a>
          <?php endif; ?>
        <?php else: ?>
          <span class="page_menu_link page_menu_link__special dimmed"><?php echo $this->_config[0]['vars']['first_page']; ?>
</span>
          <span class="page_menu_link page_menu_link__special dimmed"><?php echo $this->_config[0]['vars']['previous_page']; ?>
</span>
        <?php endif; ?>
        <span class="page_menu_current_page" style="display: none;"><?php echo ((is_array($_tmp=@$this->_tpl_vars['page'])) ? $this->_run_mod_handler('default', true, $_tmp, 1) : smarty_modifier_default($_tmp, 1)); ?>
</span>
        <?php if ($this->_tpl_vars['found'] > $this->_tpl_vars['rpp']): ?>
          <?php if ($this->_tpl_vars['use_ajax']): ?>
            <span class="page_menu_link page_menu_link__special" onclick="ajaxUpdater({link:'<?php echo $this->_tpl_vars['link']; ?>
<?php echo $this->_tpl_vars['page']+1; ?>
&amp;source=ajax&amp;session_param=<?php echo $this->_tpl_vars['session_param']; ?>
', target:'<?php echo ((is_array($_tmp=@$this->_tpl_vars['target'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['session_param'])); ?>
'});"><?php echo $this->_config[0]['vars']['next_page']; ?>
</span>
            <span class="page_menu_link page_menu_link__special" id="pagination_last_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
" onclick="ajaxUpdater({link:'<?php echo $this->_tpl_vars['link']; ?>
last&amp;source=ajax&amp;session_param=<?php echo $this->_tpl_vars['session_param']; ?>
', target:'<?php echo ((is_array($_tmp=@$this->_tpl_vars['target'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['session_param'])); ?>
'});"><?php echo $this->_config[0]['vars']['last_page']; ?>
</span>
          <?php else: ?>
            <a class="page_menu_link page_menu_link__special" href="<?php echo $this->_tpl_vars['link']; ?>
<?php echo $this->_tpl_vars['page']+1; ?>
"><?php echo $this->_config[0]['vars']['next_page']; ?>
</a>
            <a class="page_menu_link page_menu_link__special" id="pagination_last_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
" href="<?php echo $this->_tpl_vars['link']; ?>
last"><?php echo $this->_config[0]['vars']['last_page']; ?>
</a>
          <?php endif; ?>
        <?php else: ?>
          <span class="page_menu_link page_menu_link__special dimmed"><?php echo $this->_config[0]['vars']['next_page']; ?>
</span>
          <span class="page_menu_link page_menu_link__special dimmed"><?php echo $this->_config[0]['vars']['last_page']; ?>
</span>
        <?php endif; ?>
        </td>
      <?php endif; ?>
      </tr>
    </table>
  <?php endif; ?>
<?php else: ?>
  <?php if (! $this->_tpl_vars['hide_stats']): ?>
  <?php echo smarty_function_counter(array('name' => 'stat_items_sequence','assign' => 'stat_items_sequence'), $this);?>

  <table border="0" width="100%" align="center">
    <tr>
      <td>
        <?php echo ((is_array($_tmp=$this->_config[0]['vars']['pagination_displayed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <strong>
        <?php echo ((is_array($_tmp=@$this->_tpl_vars['found'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
</strong> 
          <?php echo ((is_array($_tmp=$this->_config[0]['vars']['pagination_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          <strong>
            <span id="totalItemsFound_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
"><?php echo ((is_array($_tmp=@$this->_tpl_vars['total'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)); ?>
</span>
          </strong>
        <?php if (! $this->_tpl_vars['hide_selection_stats']): ?>
           / 
          <?php echo ((is_array($_tmp=$this->_config[0]['vars']['count_selected_items'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:
          <span id="selectedItemsCount_<?php echo $this->_tpl_vars['stat_items_sequence']; ?>
" class="selected_items_span">
          <?php if ($this->_tpl_vars['selected_items']['ids']): ?>
            <?php echo count($this->_tpl_vars['selected_items']['ids']); ?>

          <?php elseif ($this->_tpl_vars['selected_items']['select_all']): ?>
            <?php echo smarty_function_math(array('equation' => "all - ignored",'all' => $this->_tpl_vars['total'],'ignored' => count($this->_tpl_vars['selected_items']['ignore_ids'])), $this);?>

          <?php else: ?>
            0
          <?php endif; ?>
          </span>
        <?php endif; ?>
      </td>
      <td align="right">
        <?php echo ((is_array($_tmp=$this->_config[0]['vars']['pagination_page'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <strong><?php if ($this->_tpl_vars['pages']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['page'])) ? $this->_run_mod_handler('default', true, $_tmp, 1) : smarty_modifier_default($_tmp, 1)); ?>
/<?php echo ((is_array($_tmp=@$this->_tpl_vars['pages'])) ? $this->_run_mod_handler('default', true, $_tmp, 1) : smarty_modifier_default($_tmp, 1)); ?>
<?php else: ?>0/0<?php endif; ?></strong>
      </td>
    </tr>
  </table>
  <?php endif; ?>
  <?php if (! $this->_tpl_vars['hide_menu'] && ( $this->_tpl_vars['total'] >= 5 || $this->_tpl_vars['pages'] > 1 )): ?>
  <table border="0" width="100%" align="center">
    <tr>
      <?php if ($this->_tpl_vars['total'] >= 5 && ! $this->_tpl_vars['hide_rpp']): ?>
      <td><?php echo smarty_function_rpp_menu(array('pages' => $this->_tpl_vars['pages'],'rpp' => $this->_tpl_vars['rpp'],'link' => $this->_tpl_vars['link'],'label_display_menu' => $this->_config[0]['vars']['display'],'use_ajax' => $this->_tpl_vars['use_ajax'],'session_param' => $this->_tpl_vars['session_param']), $this);?>
</td>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['pages'] > 1): ?>
      <td align="right">
        <?php echo smarty_function_page_menu(array('pages' => $this->_tpl_vars['pages'],'rpp' => $this->_tpl_vars['rpp'],'selected' => $this->_tpl_vars['page'],'link' => $this->_tpl_vars['link'],'target' => $this->_tpl_vars['target'],'jump_menu' => true,'label' => $this->_config[0]['vars']['pagination_page'],'use_ajax' => $this->_tpl_vars['use_ajax'],'session_param' => $this->_tpl_vars['session_param']), $this);?>

      </td>
      <?php endif; ?>
    </tr>
  </table>
  <?php endif; ?>
<?php endif; ?>