<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:33
         compiled from _customers_info_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '_customers_info_side_panel.html', 5, false),array('modifier', 'escape', '_customers_info_side_panel.html', 9, false),array('modifier', 'nl2br', '_customers_info_side_panel.html', 33, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
  <tr>
    <td class="labelbox" style="width: 180px! important">
      <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['customers_name']), $this);?>

    </td>
    <td class="required" style="width: 10px! important">&nbsp;</td>
    <td>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['customers_info']->get('id'); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['customers_info']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if ($this->_tpl_vars['customers_info']->get('lastname')): ?> <?php echo ((is_array($_tmp=$this->_tpl_vars['customers_info']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?> (<?php echo ((is_array($_tmp=$this->_tpl_vars['customers_info']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)</a>
    </td>
  </tr>
  <?php if ($this->_tpl_vars['customers_info']->get('is_company')): ?>
  <tr>
    <td class="labelbox">
      <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_branch<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('branch', ob_get_contents());ob_end_clean(); ?>
      <?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['customers_info']->getBranchLabels($this->_tpl_vars['branch'])), $this);?>

    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_branch_name">
        <?php echo ((is_array($_tmp=$this->_tpl_vars['customers_info']->get('main_branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </span>
    </td>
  </tr>
  <tr>
    <td class="labelbox">
      <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_branch_address<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('branch_address', ob_get_contents());ob_end_clean(); ?>
      <?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['customers_info']->getBranchLabels($this->_tpl_vars['branch_address'])), $this);?>

    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_branch_address">
        <?php echo ((is_array($_tmp=$this->_tpl_vars['customers_info']->get('branch_address'))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)); ?>

      </span>
    </td>
  </tr>
  <tr>
    <td class="labelbox">
      <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_main_contact_person<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('main_contact_person', ob_get_contents());ob_end_clean(); ?>
      <?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['customers_info']->getBranchLabels($this->_tpl_vars['main_contact_person']))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>

    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_contact_person">
        <?php echo ((is_array($_tmp=$this->_tpl_vars['customers_info']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </span>
    </td>
  </tr>
  <?php endif; ?>
  <tr>
    <td class="labelbox">
      <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_customers_contacts<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('customers_contacts', ob_get_contents());ob_end_clean(); ?>
      <?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars'][$this->_tpl_vars['customers_contacts']]), $this);?>

    </td>
    <td>&nbsp;</td>
    <td>
      <span id="customers_info_contacts">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_customers_contacts_data.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </span>
    </td>
  </tr>
</table>
<?php endif; ?>