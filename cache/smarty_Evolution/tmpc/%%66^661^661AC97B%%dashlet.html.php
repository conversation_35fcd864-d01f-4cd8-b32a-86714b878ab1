<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:46
         compiled from /var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/lazy_reporting/templates/dashlet.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/lazy_reporting/templates/dashlet.html', 70, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/lazy_reporting/templates/dashlet.html', 71, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/dashlets/plugins/lazy_reporting/templates/dashlet.html', 82, false),)), $this); ?>
<?php echo '
  <style>
    /* Default button CSS for the current dashlet */
    .dashlet_lazy_reporting_button {
      width: 220px !important;
      height: 60px !important;
      padding: 0px !important;
      color: #FFFFFF !important;
      text-decoration: none !important;
      -moz-border-radius: 5px !important;
      -webkit-border-radius: 5px !important;
      -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.5) !important;
      -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.5) !important;
      text-shadow: 0 -1px 1px rgba(0,0,0,0.25) !important;
      position: relative !important;
      border-radius: 10px !important;
      float: left !important;
      margin: 3px !important;
      font-size: 14px;
      line-height: 1;
      text-shadow: 0 -1px 1px rgba(0,0,0,0.25);
      overflow: hidden;
    }
    .dashlet_lazy_reporting_button hr {
      background-color: #EDEDED;
      border: 0 none;
      height: 1px;
      margin: 2px 10px;
    }
    .dashlet_lazy_reporting_button:active {
      top: 1px;
    }
    /* Remove dotted border which appears over a selected button in Firefox */
    .dashlet_lazy_reporting_button::-moz-focus-inner {
      border: 0;
    }
    /* This is used for start buttons */
    .dashlet_lazy_reporting_button_start {
      cursor: pointer;
      border-bottom: 1px solid rgba(0,0,0,0.25) !important;
    }
    /* This is used for stop buttons */
    .dashlet_lazy_reporting_button_stop {
      cursor: auto;
      border: 4px solid white;
      box-shadow: 0px 0px 0px 1px grey;
    }
    /* Buttons colors */
    .green.dashlet_lazy_reporting_button                                                      { background-color: #91BD09; }
    .green.dashlet_lazy_reporting_button:hover, .green.dashlet_lazy_reporting_button_stop     { background-color: #749A02; }
    .blue.dashlet_lazy_reporting_button                                                       { background-color: #2DAEBF; }
    .blue.dashlet_lazy_reporting_button:hover, .blue.dashlet_lazy_reporting_button_stop       { background-color: #007D9A; }
    .red.dashlet_lazy_reporting_button                                                        { background-color: #E33100; }
    .red.dashlet_lazy_reporting_button:hover, .red.dashlet_lazy_reporting_button_stop         { background-color: #872300; }
    .magenta.dashlet_lazy_reporting_button                                                    { background-color: #A9014B; }
    .magenta.dashlet_lazy_reporting_button:hover, .magenta.dashlet_lazy_reporting_button_stop { background-color: #630030; }
    .orange.dashlet_lazy_reporting_button                                                     { background-color: #FF5C00; }
    .orange.dashlet_lazy_reporting_button:hover, .orange.dashlet_lazy_reporting_button_stop   { background-color: #D45500; }
    .yellow.dashlet_lazy_reporting_button                                                     { background-color: #FFB515; }
    .yellow.dashlet_lazy_reporting_button:hover, .yellow.dashlet_lazy_reporting_button_stop   { background-color: #FC9200; }
    .gray.dashlet_lazy_reporting_button                                                       { background-color: #C6C6C6; }
    .gray.dashlet_lazy_reporting_button:hover, .gray.dashlet_lazy_reporting_button_stop       { background-color: #999999; }
  </style>
'; ?>

<script src="<?php echo $this->_tpl_vars['scripts_url']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
" defer="defer"></script>

<div id="dashlet_lazy_reporting_buttons_container">
  <?php $this->assign('current_active_task_id', ''); ?>
  <?php $_from = $this->_tpl_vars['tasks']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['task_id'] => $this->_tpl_vars['task']):
?>
    <?php ob_start(); ?><?php echo smarty_function_cycle(array('values' => 'green,blue,red,magenta,orange,yellow'), $this);?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_btn_color', ob_get_contents());ob_end_clean(); ?>
    <button id="stopwatch_task_<?php echo $this->_tpl_vars['task_id']; ?>
"  class="<?php echo $this->_tpl_vars['current_btn_color']; ?>
 dashlet_lazy_reporting_button dashlet_lazy_reporting_button_stop" <?php if (! $this->_tpl_vars['task']['watch_is_active']): ?> style="display: none;"<?php endif; ?> title="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']['task_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['task']['customer_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<hr><?php echo ((is_array($_tmp=$this->_tpl_vars['task']['task_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
    <button id="startwatch_task_<?php echo $this->_tpl_vars['task_id']; ?>
" class="<?php echo $this->_tpl_vars['current_btn_color']; ?>
 dashlet_lazy_reporting_button dashlet_lazy_reporting_button_start"<?php if ($this->_tpl_vars['task']['watch_is_active']): ?> style="display: none;"<?php endif; ?> onclick="dashletLazyReporting.activate(this, <?php echo $this->_tpl_vars['task_id']; ?>
);" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['task']['task_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['task']['customer_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<hr><?php echo ((is_array($_tmp=$this->_tpl_vars['task']['task_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
    <?php if ($this->_tpl_vars['task']['watch_is_active']): ?>
      <?php $this->assign('current_active_task_id', $this->_tpl_vars['task_id']); ?>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
  <button id="stopwatch_task_rest"  class="gray dashlet_lazy_reporting_button dashlet_lazy_reporting_button_stop" style="float: left;<?php if ($this->_tpl_vars['current_active_task_id']): ?> display: none;<?php endif; ?>"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_customers_requests_rest'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
  <button id="startwatch_task_rest" class="gray dashlet_lazy_reporting_button dashlet_lazy_reporting_button_start"<?php if (! $this->_tpl_vars['current_active_task_id']): ?> style="display: none;"<?php endif; ?> onclick="dashletLazyReporting.activate(this, 'rest');"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['plugin_customers_requests_rest'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
</div>

<script type="text/javascript" defer="defer">
    dashletLazyReporting.setDefaults("<?php echo $this->_tpl_vars['dashlet']->get('id'); ?>
", "<?php echo ((is_array($_tmp=@$this->_tpl_vars['current_active_task_id'])) ? $this->_run_mod_handler('default', true, $_tmp, 'rest') : smarty_modifier_default($_tmp, 'rest')); ?>
");
</script>