<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:55
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/_tools.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/_tools.html', 4, false),)), $this); ?>
<?php echo '
<script id="grid-col-tools" type="text/x-template">
    <div class="nz-grid-tools-wrapper">
        <a href="${if(rights.edit)}${Nz.getEditUrl(properties.id)}${else}#${/if}" class="material-icons nz-icon-button${if(!rights.edit)} nz--disabled${/if} nz-edit-button" title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '">edit</a>
        <a href="${if(rights.view)}${Nz.getViewUrl(properties.id)}${else}#${/if}" class="material-icons nz-icon-button${if(!rights.view)} nz--disabled${/if} nz-view-button" title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '">search</a>
        <i class="material-icons nz-tooltip-trigger nz-tooltip-autoinit nz-tooltip-helpcursor"
           data-tooltip-element="#'; ?>
<?php echo $this->_tpl_vars['short_module_name']; ?>
<?php echo '-info-${properties.id}"
           data-tooltip-title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '"
           data-tooltip-position="panel: bottom right at: bottom left">info</i>
        <div id="'; ?>
<?php echo $this->_tpl_vars['short_module_name']; ?>
<?php echo '-info-${properties.id}" class="nz-gridrow-info nz-tooltip-content nz-tooltip-notch__right-bottom">
            '; ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info_grid.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo '
        </div>
    </div>
</script>
'; ?>
