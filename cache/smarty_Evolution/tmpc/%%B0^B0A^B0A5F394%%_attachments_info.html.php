<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:50
         compiled from _attachments_info.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '_attachments_info.html', 7, false),array('modifier', 'replace', '_attachments_info.html', 33, false),)), $this); ?>
<?php echo ''; ?><?php if ($this->_tpl_vars['module'] && $this->_tpl_vars['controller']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['controller'] != $this->_tpl_vars['module']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['controller_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['controller']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['module']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('controller_action_string', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>

<?php if ($this->_tpl_vars['files']): ?>
  <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['fileAction'])) ? $this->_run_mod_handler('default', true, $_tmp, 'viewfile') : smarty_modifier_default($_tmp, 'viewfile')); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('fileAction', ob_get_contents());ob_end_clean(); ?>
  <section class="nz-attachments-info">
    <ul class="nz-attachments-info_list">
      <?php if ($this->_tpl_vars['files']['attachments']): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_attachments_info_items.html", 'smarty_include_vars' => array('filesList' => $this->_tpl_vars['files']['attachments'],'fileAction' => $this->_tpl_vars['fileAction'],'module' => $this->_tpl_vars['module'],'controller_action_string' => $this->_tpl_vars['controller_action_string'],'model_id' => $this->_tpl_vars['model_id'],'archive' => $this->_tpl_vars['archive'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['files']['generated']): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_attachments_info_items.html", 'smarty_include_vars' => array('filesList' => $this->_tpl_vars['files']['generated'],'fileAction' => $this->_tpl_vars['fileAction'],'module' => $this->_tpl_vars['module'],'controller_action_string' => $this->_tpl_vars['controller_action_string'],'model_id' => $this->_tpl_vars['model_id'],'archive' => $this->_tpl_vars['archive'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
    </ul>
    <?php if ($this->_tpl_vars['files_more'] > 0): ?>
      <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['controller_action_string']; ?>
=attachments&amp;attachments=<?php echo $this->_tpl_vars['model_id']; ?>
<?php if ($this->_tpl_vars['archive']): ?>&amp;archive=1<?php endif; ?>">
        <?php echo ((is_array($_tmp=$this->_config[0]['vars']['attachments_files_more'])) ? $this->_run_mod_handler('replace', true, $_tmp, '[files_more]', $this->_tpl_vars['files_more']) : smarty_modifier_replace($_tmp, '[files_more]', $this->_tpl_vars['files_more'])); ?>

      </a>
    <?php endif; ?>
  </section>
<?php endif; ?>