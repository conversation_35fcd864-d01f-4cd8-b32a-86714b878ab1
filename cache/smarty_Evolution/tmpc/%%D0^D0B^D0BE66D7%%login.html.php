<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:39
         compiled from /var/www/Nzoom-Hella/_libs/modules/auth/view/templates/login.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/auth/view/templates/login.html', 31, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/auth/view/templates/login.html', 47, false),)), $this); ?>
<link rel="stylesheet" href="<?php echo $this->_tpl_vars['view_url']; ?>
css/login.css?<?php echo $this->_tpl_vars['system_options']['build']; ?>
" />
<?php echo $this->_tpl_vars['version']; ?>

<div class="sec-login-wrapper">
<section class="sec-login nz-rounded nz-surface nz-elevation nz-elevation--z20">
    <div class="login-logo">
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
logo.png" alt="nZoom logo" width="148" height="38" />
        <h2 class="nz-slogan"><?php echo $this->_config[0]['vars']['slogan']; ?>
</h2>
    </div>
    <form name="loginform"  class="nz-form form--login" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
            

        <div class="login-input-wrapper nz-input-withlabel" >
            <label for="username" id="error_username" class="nz-label"
                  <?php if ($this->_tpl_vars['messages']->getErrors('username')): ?>
                class="error"<?php endif; ?>><?php echo $this->_config[0]['vars']['auth_username']; ?>
:<?php echo $this->_config[0]['vars']['required']; ?>
</label>
            <div class="nz-input-wrapper">
                <input type="text" class="nz-input nz-textbox" name="username" id="username"
                       value="<?php if (! empty ( $_POST['username'] )): ?><?php echo $_POST['username']; ?>
<?php endif; ?>"
                       title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_username'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
            </div>
        </div>
        <div class="login-input-wrapper nz-input-withlabel">
            <label for="password" id="error_password" class="nz-label"<?php if ($this->_tpl_vars['messages']->getErrors('password')): ?>
                class="error"<?php endif; ?>><?php echo $this->_config[0]['vars']['auth_password']; ?>
:<?php echo $this->_config[0]['vars']['required']; ?>
</label>
            <div class="nz-input-wrapper">
                <input type="password" class="nz-input nz-textbox" name="password" id="password"
                       title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_password'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"/>
            </div>
        </div>

        <?php if (! empty ( $this->_tpl_vars['captcha'] )): ?>
        <div class="nz-login-captcha">
          <div class="labelbox"  ><a id="error_captcha"><label
                  for="captcha"<?php if ($this->_tpl_vars['messages']->getErrors('captcha')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'captcha'), $this);?>
</label></a></div>
          <div><?php echo $this->_config[0]['vars']['required']; ?>
</div>
          <div>
            <img src="<?php echo $this->_tpl_vars['captcha']; ?>
" id="captcha" alt="" /><br /><br />
            <input type="text" class="txtbox" name="captcha" id="captcha" value=""
                   title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_captcha'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
refresh.png" alt="" onclick="$('captcha').src=$('captcha').src+'#'" />
          </div>
        </div>
        <?php endif; ?>

        <div class="login-options">
            <div class="login-options--rememberme"><input type="checkbox" name="rememberme" id="rememberme" value="1"<?php if (! empty ( $_POST['rememberme'] ) && $_POST['rememberme'] == 1): ?>
                 checked="checked"<?php endif; ?> /> <label class="labelbox" for="rememberme"
                 title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_rememberme'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_rememberme'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
            </div>
            <?php if (! $this->_tpl_vars['disable_lost_password']): ?>
            <div class="login-options--forgottenpassword">
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=lost_password"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_lost_password'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></div>
            <?php endif; ?>
        </div>

        <div class="nz-form--controls">
          <button type="submit" class="nz-button" name="loginButton" id="loginButton"
                  ><?php echo ((is_array($_tmp=$this->_config[0]['vars']['auth_login_button'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
        </div>

    </form>
</section>
</div>
<?php echo '
<script type="text/javascript">
    nz_ready().then(()=>{
        document.onload = focusLogin();
    });
</script>
'; ?>
