<?php /* Smarty version 2.6.33, created on 2025-05-27 12:01:33
         compiled from _timesheet_stopwatch_button.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'strtolower', '_timesheet_stopwatch_button.html', 4, false),array('modifier', 'escape', '_timesheet_stopwatch_button.html', 5, false),)), $this); ?>
<?php if ($this->_tpl_vars['model']->checkPermissions('addtimesheet')): ?>
<div class="stopwatch_div<?php if ($this->_tpl_vars['model']->getStopWatchData()): ?> nz--active<?php endif; ?>">
  <a href="javascript:void(0);"
     onclick="confirmAction('stop_watch', function(el) { stopWatch(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['model']->modelName)) ? $this->_run_mod_handler('strtolower', true, $_tmp) : strtolower($_tmp)); ?>
', <?php echo $this->_tpl_vars['model']->get('id'); ?>
); }, this)"
     class="nz-icon-button stopwatch-stop" id="stopwatch" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['stop_watch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['theme']->getIconForAction('stoptimer'); ?>
</a>
  <a href="javascript:void(0);"
     onclick="confirmAction('start_watch', function(el) { startWatch(el, '<?php echo ((is_array($_tmp=$this->_tpl_vars['model']->modelName)) ? $this->_run_mod_handler('strtolower', true, $_tmp) : strtolower($_tmp)); ?>
', <?php echo $this->_tpl_vars['model']->get('id'); ?>
); }, this)"
     class="nz-icon-button stopwatch-start" id="startwatch" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['start_watch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['theme']->getIconForAction('starttimer'); ?>
</a>
</div>
<?php endif; ?>