<?php /* Smarty version 2.6.33, created on 2025-05-21 13:09:42
         compiled from message.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'count', 'message.html', 4, false),array('modifier', 'regex_replace', 'message.html', 15, false),array('modifier', 'replace', 'message.html', 22, false),array('function', 'counter', 'message.html', 11, false),)), $this); ?>
<?php echo ''; ?><?php $this->assign('max_shown', 50); ?><?php echo ''; ?><?php $this->assign('wrap_at', 200); ?><?php echo ''; ?><?php $this->assign('items_count', count($this->_tpl_vars['items'])); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo $this->_tpl_vars['display']; ?><?php echo 's'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('message_type', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

<?php if ($this->_tpl_vars['items']): ?>
  <div class="message_container nz-elevation--z8">
  <?php if (is_array ( $this->_tpl_vars['items'] )): ?>
      <ul class="nz-message-list__<?php echo $this->_tpl_vars['display']; ?>
">
    <?php echo smarty_function_counter(array('assign' => 'msg_items','print' => false,'start' => 0), $this);?>

    <?php $_from = $this->_tpl_vars['items']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['msg']):
        $this->_foreach['i']['iteration']++;
?>
      <?php echo smarty_function_counter(array('assign' => 'msg_items','print' => false), $this);?>

      <?php ob_start(); ?><?php echo $this->_tpl_vars['msg']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('msg', ob_get_contents());ob_end_clean(); ?>
          <li class="<?php echo $this->_tpl_vars['display']; ?>
"><?php if (! is_numeric ( $this->_tpl_vars['key'] )): ?><a href="#error_<?php echo ((is_array($_tmp=$this->_tpl_vars['key'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '|#*$|', '') : smarty_modifier_regex_replace($_tmp, '|#*$|', '')); ?>
"><?php echo $this->_tpl_vars['msg']; ?>
</a><?php else: ?><?php echo $this->_tpl_vars['msg']; ?>
<?php endif; ?></li>
      <?php if ($this->_tpl_vars['msg_items'] == $this->_tpl_vars['max_shown']): ?>
          <span id="<?php echo $this->_tpl_vars['display']; ?>
_container" style="display: none;">
      <?php endif; ?>
      <?php if ($this->_tpl_vars['msg_items'] > $this->_tpl_vars['max_shown']): ?>
        <?php if (($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?>
            </span>
            <li id="liToggleAll" class="<?php echo $this->_tpl_vars['display']; ?>
 pointer" onclick="toggleMessages('<?php echo $this->_tpl_vars['display']; ?>
', <?php echo count($this->_tpl_vars['items']); ?>
, <?php echo $this->_tpl_vars['max_shown']; ?>
)">(<?php echo ((is_array($_tmp=$this->_config[0]['vars']['messages_expand_items'])) ? $this->_run_mod_handler('replace', true, $_tmp, '%d', $this->_tpl_vars['items_count']) : smarty_modifier_replace($_tmp, '%d', $this->_tpl_vars['items_count'])); ?>
 <?php echo $this->_config[0]['vars'][$this->_tpl_vars['message_type']]; ?>
)</li>
            <li id="liToggleMax" style="display: none;" class="<?php echo $this->_tpl_vars['display']; ?>
 pointer" onclick="toggleMessages('<?php echo $this->_tpl_vars['display']; ?>
', <?php echo count($this->_tpl_vars['items']); ?>
, <?php echo $this->_tpl_vars['max_shown']; ?>
)">(<?php echo ((is_array($_tmp=$this->_config[0]['vars']['messages_collapse_items'])) ? $this->_run_mod_handler('replace', true, $_tmp, '%d', $this->_tpl_vars['max_shown']) : smarty_modifier_replace($_tmp, '%d', $this->_tpl_vars['max_shown'])); ?>
 <?php echo $this->_config[0]['vars'][$this->_tpl_vars['message_type']]; ?>
)</li>
        <?php endif; ?>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
      </ul>
  <?php else: ?>
    <ul>
      <li class="<?php echo $this->_tpl_vars['display']; ?>
"><?php echo $this->_tpl_vars['items']; ?>
</li>
    </ul>
  <?php endif; ?>
  </div>
<?php endif; ?>