<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_status.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_status.html', 5, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_status.html', 45, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_status.html', 96, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_status.html', 34, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_status.html', 96, false),array('function', 'uniqid', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_status.html', 99, false),)), $this); ?>
<?php if (! $this->_tpl_vars['contract']): ?>
  <?php $this->assign('contract', $this->_tpl_vars['available_action']['contract']); ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['available_action']['show_form']): ?>
  <form method="<?php echo ((is_array($_tmp=@$this->_tpl_vars['available_action']['options']['form_method'])) ? $this->_run_mod_handler('default', true, $_tmp, 'get') : smarty_modifier_default($_tmp, 'get')); ?>
" action="<?php echo $_SERVER['PHP_SELF']; ?>
" id="<?php echo $this->_tpl_vars['available_action']['action']; ?>
_form">
    <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['module_param']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" />
    <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" />
    <?php if ($this->_tpl_vars['available_action']['model_id']): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['action']; ?>
" value="<?php echo $this->_tpl_vars['available_action']['model_id']; ?>
" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['available_action']['model_lang']): ?>
      <input type="hidden" name="model_lang" value="<?php echo $this->_tpl_vars['available_action']['model_lang']; ?>
" />
    <?php endif; ?>
    <?php if ($this->_tpl_vars['available_action']['name'] == 'search' || $this->_tpl_vars['available_action']['name'] == 'filter'): ?>
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['session_param']; ?>
" value="1" />
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_module" value="<?php echo $this->_tpl_vars['available_action']['module']; ?>
" />
      <input type="hidden" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
_controller" value="<?php echo $this->_tpl_vars['available_action']['controller']; ?>
" />
      <?php if ($this->_tpl_vars['event']): ?>
      <input type="hidden" name="event" value="<?php echo $this->_tpl_vars['event']; ?>
" />
      <?php endif; ?>
      <?php if ($this->_tpl_vars['form_name']): ?>
      <input type="hidden" name="form_name" value="<?php echo $this->_tpl_vars['form_name']; ?>
" />
      <?php endif; ?>
    <?php endif; ?>
    <?php $this->assign('lb_suffix', '_'); ?>
<?php endif; ?>

  <table border="0" cellpadding="3" cellspacing="3" width="100%">
    <tr>
      <td style="vertical-align: top;">
        <table cellpadding="0" cellspacing="0" border="0">
          <tr>
            <?php if (! $this->_tpl_vars['hide_status_label']): ?>
              <td class="labelbox"><a name="error_status"><label for="status"<?php if ($this->_tpl_vars['messages']->getErrors('status')): ?> class="error"<?php endif; ?>><?php if ($this->_tpl_vars['available_action']['show_form']): ?><?php echo $this->_config[0]['vars']['contracts_status']; ?>
<?php else: ?><?php echo smarty_function_help(array('label' => 'status'), $this);?>
<?php endif; ?></label></a></td>
              <td>&nbsp;</td>
            <?php endif; ?>
              <td class="databox" nowrap="nowrap">
                <?php ob_start(); ?><?php echo $this->_tpl_vars['contract']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_status', ob_get_contents());ob_end_clean(); ?>
                <?php ob_start(); ?><?php echo $this->_tpl_vars['contract']->get('substatus'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_substatus', ob_get_contents());ob_end_clean(); ?>
                <?php $this->assign('substatuses', $this->_tpl_vars['contract']->get('substatuses')); ?>
                <input type="hidden" name="current_status_base" value="<?php echo $this->_tpl_vars['current_status']; ?>
" id="current_status_base" class="current_status_base" />
                <input type="hidden" name="current_substatus_base" value="<?php echo $this->_tpl_vars['current_substatus']; ?>
" id="current_substatus_base" class="current_substatus_base" />
                <input type="hidden" name="statuses_unlock" value="<?php echo $this->_tpl_vars['contract']->checkPermissions('setstatus_unlock'); ?>
" id="statuses_unlock" class="statuses_unlock" />
                <input type="hidden" name="current_selected_status" value="<?php if ($this->_tpl_vars['current_substatus']): ?>substatus_<?php echo $this->_tpl_vars['current_substatus']; ?>
<?php endif; ?>" id="current_selected_status" class="current_selected_status" />
                <input type="radio" name="status" id="status_opened<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="status status_opened" onclick="if (validateStatusChange(this)){toggleStatuses(this);} else alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_status_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" value="opened"<?php if ($this->_tpl_vars['contract']->get('status') == 'opened' || ! $this->_tpl_vars['contract']->get('status')): ?> checked="checked"<?php endif; ?> />
                <input type="hidden" name="requires_comment_opened" id="requires_comment_opened" class="requires_comment_opened" value="<?php echo $this->_tpl_vars['contract']->get('opened_requires_comment'); ?>
" />
                <label for="status_opened<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="contracts_status opened"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_opened'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
                <?php if ($this->_tpl_vars['substatuses']['opened']): ?>
                  <blockquote id="substatus_opened" class="block_quote_status substatus_opened">
                    <?php $_from = $this->_tpl_vars['substatuses']['opened']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['substat_properties']):
?>
                      <input type="radio" name="substatus" id="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" value="<?php echo $this->_tpl_vars['substat_properties']['parent_status']; ?>
_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" onclick="if (! validateSubstatusChange(this, '<?php echo $this->_tpl_vars['module']; ?>
')) alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_substatus_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"<?php if ($this->_tpl_vars['contract']->get('substatus') == $this->_tpl_vars['substat_properties']['id']): ?> checked="checked"<?php endif; ?> />
                      <?php if ($this->_tpl_vars['substat_properties']['icon_name']): ?><img src="<?php echo @PH_CONTRACTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['substat_properties']['icon_name']; ?>
" alt="" title="" style="width: 14px; height: 14px;" /><?php endif; ?><label for="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="contracts_status_substatus"><?php echo $this->_tpl_vars['substat_properties']['name']; ?>
</label><br />
                      <input type="hidden" name="requires_comment_opened_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" id="requires_comment_opened_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" class="requires_comment_opened_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" value="<?php echo $this->_tpl_vars['substat_properties']['requires_comment']; ?>
" />
                    <?php endforeach; endif; unset($_from); ?>
                  </blockquote>
                <?php endif; ?>
                <input type="radio" name="status" id="status_locked<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="status status_locked" onclick="if (validateStatusChange(this)){toggleStatuses(this);} else alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_status_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" value="locked"<?php if ($this->_tpl_vars['contract']->get('status') == 'locked'): ?> checked="checked"<?php endif; ?> />
                <input type="hidden" name="requires_comment_locked" id="requires_comment_locked" class="requires_comment_locked" value="<?php echo $this->_tpl_vars['contract']->get('locked_requires_comment'); ?>
" />
                <label for="status_locked<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="contracts_status locked"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_locked'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
                <?php if ($this->_tpl_vars['substatuses']['locked']): ?>
                  <blockquote id="substatus_locked" class="block_quote_status substatus_locked">
                    <?php $_from = $this->_tpl_vars['substatuses']['locked']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['substat_properties']):
?>
                      <input type="radio" name="substatus" id="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" value="<?php echo $this->_tpl_vars['substat_properties']['parent_status']; ?>
_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" onclick="if (! validateSubstatusChange(this, '<?php echo $this->_tpl_vars['module']; ?>
')) alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_substatus_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"<?php if ($this->_tpl_vars['contract']->get('substatus') == $this->_tpl_vars['substat_properties']['id']): ?> checked="checked"<?php endif; ?> />
                      <?php if ($this->_tpl_vars['substat_properties']['icon_name']): ?><img src="<?php echo @PH_CONTRACTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['substat_properties']['icon_name']; ?>
" alt="" title="" style="width: 14px; height: 14px;" /><?php endif; ?><label for="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="contracts_status_substatus"><?php echo $this->_tpl_vars['substat_properties']['name']; ?>
</label><br />
                      <input type="hidden" name="requires_comment_locked_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" id="requires_comment_locked_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" class="requires_comment_locked_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" value="<?php echo $this->_tpl_vars['substat_properties']['requires_comment']; ?>
" />
                    <?php endforeach; endif; unset($_from); ?>
                  </blockquote>
                <?php endif; ?>
                <input type="radio" name="status" id="status_closed<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="status status_closed" onclick="if (validateStatusChange(this)){toggleStatuses(this);} else alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_status_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');" value="closed"<?php if ($this->_tpl_vars['contract']->get('status') == 'closed'): ?> checked="checked"<?php endif; ?> />
                <input type="hidden" name="requires_comment_closed" id="requires_comment_closed" class="requires_comment_closed" value="<?php echo $this->_tpl_vars['contract']->get('closed_requires_comment'); ?>
" />
                <label for="status_closed<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="contracts_status closed"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status_closed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
                <?php if ($this->_tpl_vars['substatuses']['closed']): ?>
                  <blockquote id="substatus_closed" class="block_quote_status substatus_closed">
                    <?php $_from = $this->_tpl_vars['substatuses']['closed']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['substat_properties']):
?>
                      <input type="radio" name="substatus" id="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" value="<?php echo $this->_tpl_vars['substat_properties']['parent_status']; ?>
_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" onclick="if (! validateSubstatusChange(this, '<?php echo $this->_tpl_vars['module']; ?>
')) alert('<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['error_substatus_change'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
');"<?php if ($this->_tpl_vars['contract']->get('substatus') == $this->_tpl_vars['substat_properties']['id']): ?> checked="checked"<?php endif; ?> />
                      <?php if ($this->_tpl_vars['substat_properties']['icon_name']): ?><img src="<?php echo @PH_CONTRACTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['substat_properties']['icon_name']; ?>
" alt="" title="" style="width: 14px; height: 14px;" /><?php endif; ?><label for="substatus_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
<?php echo $this->_tpl_vars['lb_suffix']; ?>
" class="contracts_status_substatus"><?php echo $this->_tpl_vars['substat_properties']['name']; ?>
</label><br />
                      <input type="hidden" name="requires_comment_closed_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" id="requires_comment_closed_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" class="requires_comment_closed_<?php echo $this->_tpl_vars['substat_properties']['id']; ?>
" value="<?php echo $this->_tpl_vars['substat_properties']['requires_comment']; ?>
" />
                    <?php endforeach; endif; unset($_from); ?>
                  </blockquote>
                <?php endif; ?>
              </td>
          </tr>
        </table>
      </td>
      <td align="right" style="vertical-align: top;">
        <?php ob_start(); ?><?php echo $this->_tpl_vars['contract']->get('status'); ?>
_requires_comment<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_status_requires_comment_name', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['contract']->get($this->_tpl_vars['current_status_requires_comment_name']); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_status_requires_comment', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?><?php if ($this->_tpl_vars['contract']->get('substatus')): ?><?php $_from = $this->_tpl_vars['substatuses'][$this->_tpl_vars['current_status']]; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['substatus']):
?><?php if ($this->_tpl_vars['substatus']['id'] == $this->_tpl_vars['contract']->get('substatus')): ?><?php echo $this->_tpl_vars['substatus']['requires_comment']; ?>
<?php endif; ?><?php endforeach; endif; unset($_from); ?><?php else: ?>0<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('current_substatus_requires_comment', ob_get_contents());ob_end_clean(); ?>
        <table id="available_comment_table" class="available_comment_table" style="visibility: <?php if ($this->_tpl_vars['current_substatus_requires_comment']): ?><?php if ($this->_tpl_vars['current_substatus_requires_comment'] == 'requires_comment' || $this->_tpl_vars['current_substatus_requires_comment'] == 'optional_comment'): ?>visible<?php else: ?>hidden<?php endif; ?><?php else: ?><?php if ($this->_tpl_vars['current_status_requires_comment'] == 'requires_comment' || $this->_tpl_vars['current_status_requires_comment'] == 'optional_comment'): ?>visible<?php else: ?>hidden<?php endif; ?><?php endif; ?>;">
          <tr>
            <td class="required required_comment" id="required_comment" rowspan="2" style="visibility: <?php if ($this->_tpl_vars['current_substatus_requires_comment']): ?><?php if ($this->_tpl_vars['current_substatus_requires_comment'] == 'requires_comment'): ?>visible<?php else: ?>hidden<?php endif; ?><?php else: ?><?php if ($this->_tpl_vars['current_status_requires_comment'] == 'requires_comment'): ?>visible<?php else: ?>hidden<?php endif; ?><?php endif; ?>;"><?php echo $this->_config[0]['vars']['required']; ?>
<input type="hidden" name="requires_comment" id="requires_comment" class="requires_comment" value="<?php if ($this->_tpl_vars['current_substatus_requires_comment']): ?><?php if ($this->_tpl_vars['current_substatus_requires_comment'] == 'requires_comment'): ?>1<?php else: ?>0<?php endif; ?><?php else: ?><?php if ($this->_tpl_vars['current_status_requires_comment'] == 'requires_comment'): ?>1<?php else: ?>0<?php endif; ?><?php endif; ?>" /></td>
            <td class="labelbox"><a name="error_comment"><label for="comment<?php echo $this->_tpl_vars['lb_suffix']; ?>
"<?php if ($this->_tpl_vars['messages']->getErrors('comment')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'comment'), $this);?>
</label></a></td>
          </tr>
          <tr>
            <td>
              <textarea class="areabox comment" name="comment" id="comment<?php echo $this->_tpl_vars['lb_suffix']; ?>
" style="height: <?php echo smarty_function_math(array('equation' => '(2+x+y+z)*18','x' => count($this->_tpl_vars['substatuses']['opened']),'y' => count($this->_tpl_vars['substatuses']['locked']),'z' => count($this->_tpl_vars['substatuses']['closed'])), $this);?>
px;"></textarea>
              <?php if ($this->_tpl_vars['include_portal_users_option'] && ! $this->_tpl_vars['currentUser']->get('is_portal')): ?>
                <br />
                <?php ob_start(); ?>_<?php echo smarty_function_uniqid(array(), $this);?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_portal_suffix', ob_get_contents());ob_end_clean(); ?>
                <input type="radio" name="is_portal" id="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! isset ( $this->_tpl_vars['available_action']['default_portal_comment'] ) || $this->_tpl_vars['available_action']['default_portal_comment']): ?> checked="checked"<?php endif; ?> /><label for="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
                <input type="radio" name="is_portal" id="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (isset ( $this->_tpl_vars['available_action']['default_portal_comment'] ) && ! $this->_tpl_vars['available_action']['default_portal_comment']): ?> checked="checked"<?php endif; ?> /><label for="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
              <?php endif; ?>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    <tr>
      <td colspan="2" class="nopadding">
      <?php if ($this->_tpl_vars['available_action']['started_id'] && $this->_tpl_vars['available_action']['waiting_id']): ?>
        <?php $this->assign('confirm_message', $this->_config[0]['vars']['message_contracts_confirm_cancel__agreement_and_waiting_agreements']); ?>
      <?php elseif ($this->_tpl_vars['available_action']['started_id']): ?>
        <?php $this->assign('confirm_message', $this->_config[0]['vars']['message_contracts_confirm_cancel_agreement']); ?>
      <?php elseif ($this->_tpl_vars['available_action']['waiting_id']): ?>
        <?php $this->assign('confirm_message', $this->_config[0]['vars']['message_contracts_confirm_cancel_waiting_agreements']); ?>
      <?php else: ?>
        <?php $this->assign('confirm_message', ''); ?>
      <?php endif; ?>
      <div id="status_div"></div>
        <button type="submit" class="button" style="margin: 3px;" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['options']['label']; ?>
" onclick="if (checkRequiredComment(this)<?php if ($this->_tpl_vars['confirm_message']): ?> && cancelAgreement('<?php echo $this->_tpl_vars['confirm_message']; ?>
')<?php endif; ?>){<?php if ($this->_tpl_vars['available_action']['add_notices']): ?> checkAgreementsDifferences(this.form, '<?php echo $this->_tpl_vars['contract']->get('id'); ?>
'); return false;<?php endif; ?>}else return false;"><?php echo $this->_tpl_vars['available_action']['options']['label']; ?>
</button>
      </td>
    </tr>
  </table>

<?php if ($this->_tpl_vars['available_action']['show_form']): ?>
  </form>
<?php endif; ?>