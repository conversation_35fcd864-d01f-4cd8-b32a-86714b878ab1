<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:22
         compiled from /var/www/Nzoom-Hella/_libs/modules/assignments/templates/_assignments_configurator_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/_assignments_configurator_panel.html', 7, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/_assignments_configurator_panel.html', 26, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/_assignments_configurator_panel.html', 26, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/_assignments_configurator_panel.html', 32, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/_assignments_configurator_panel.html', 19, false),)), $this); ?>
<?php if (! $this->_tpl_vars['exclude_div']): ?>
<div id="assignments_configurator" style="position: absolute; display: none; min-width: 250px;">
<?php endif; ?>
  <table cellspacing="0" cellpadding="0" class="t_table t_table_border">
    <tr>
      <td class="t_caption3 drag" id="assignments_configurator_title">
        <div class="t_caption3_title floatl"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/move.png" alt="" width="10" height="10" title="<?php echo $this->_config[0]['vars']['draggable']; ?>
" />&nbsp;<?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_configurator_load_save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
&nbsp;</div>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
close_window.png" class="vtop pointer floatr" width="12" height="12" onclick="$('assignments_configurator').style.display = 'none';" alt="<?php echo $this->_config[0]['vars']['close']; ?>
" title="<?php echo $this->_config[0]['vars']['close']; ?>
" />
      </td>
    </tr>
    <tr class="hidden">
      <td class="nopadding" id="assignments_messages_container"></td>
    </tr>
    <tr>
      <td class="nopadding">
        <div style="max-height: 120px; overflow: auto;">
          <table style="width: 100%; border: 0px none;">
            <?php $_from = $this->_tpl_vars['config_templates']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['templ']):
?>
            <tr class="<?php echo smarty_function_cycle(array('name' => 'assignments_cycle','values' => 't_even,t_odd'), $this);?>
">
              <td>
                <?php if ($this->_tpl_vars['templ']->get('model') != 'all' && $this->_tpl_vars['templ']->get('model') != 'noone'): ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" class="vtop pointer floatr" width="12" height="12" onclick="manageAssignmentsConfigurator('<?php echo $this->_tpl_vars['templ']->get('id'); ?>
', 'assignments_configurator', 'delete');" alt="<?php echo $this->_config[0]['vars']['delete']; ?>
" title="<?php echo $this->_config[0]['vars']['delete']; ?>
" />
                <?php endif; ?>
                <div class="pointer" style="display: inline-block; width: 200px!important; color: #666666;" onclick="loadSavedAssignments('<?php echo $this->_tpl_vars['templ']->get('params'); ?>
');" title="<?php echo $this->_config[0]['vars']['config_load']; ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['templ']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
                  <?php if ($this->_tpl_vars['templ']->get('model') == 'all' || $this->_tpl_vars['templ']->get('model') == 'noone'): ?><strong><?php endif; ?>
                  <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['templ']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 30, '...', true) : smarty_modifier_mb_truncate($_tmp, 30, '...', true)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

                  <?php if ($this->_tpl_vars['templ']->get('model') == 'all' || $this->_tpl_vars['templ']->get('model') == 'noone'): ?></strong><?php endif; ?>
                </div>
              </td>
            </tr>
            <?php endforeach; endif; unset($_from); ?>
            <tr class="<?php if (( count($this->_tpl_vars['config_templates']) % 2 )): ?>t_odd<?php else: ?>t_even<?php endif; ?>">
              <td>
                <img id="save_assignments_config" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
download.png" class="vtop pointer floatr" width="12" height="12" onclick="manageAssignmentsConfigurator($('assignments_configurator_name').value, 'assignments_configurator', 'save');" alt="<?php echo $this->_config[0]['vars']['save']; ?>
" title="<?php echo $this->_config[0]['vars']['save']; ?>
" />
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_text.html', 'smarty_include_vars' => array('name' => 'assignments_configurator_name','standalone' => true,'width' => 200,'label' => $this->_config[0]['vars']['assignments_configurator_name'],'show_placeholder' => 'label','onkeypress' => 'return saveAssignmentsConfiguratorOnEnter(event);')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'input_hidden.html', 'smarty_include_vars' => array('name' => 'assignments_configurator_type','standalone' => true,'value' => $this->_tpl_vars['assignment_type'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
            </tr>
          </table>
        </div>
      </td>
    </tr>
  </table>
<?php if (! $this->_tpl_vars['exclude_div']): ?>
</div>
<?php endif; ?>
<script type="text/javascript">
  new Draggable('assignments_configurator', {handle: 'assignments_configurator_title'});
</script>