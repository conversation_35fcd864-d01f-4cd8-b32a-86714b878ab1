<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:04
         compiled from /var/www/Nzoom-Hella/_libs/modules/roles/templates/_permissions_index.html */ ?>
<div id="<?php echo $this->_tpl_vars['section_name']; ?>
_index_holder" class="permissions_index_holder">
  <?php $_from = $this->_tpl_vars['link_elements']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['module_name'] => $this->_tpl_vars['module_value']):
?>
    <?php if (! empty ( $this->_tpl_vars['module_value']['types_labels'] )): ?>
      <a style="float: left" class="group_role_documents" href="#<?php echo $this->_tpl_vars['module_name']; ?>
_index"><?php echo $this->_tpl_vars['module_value']['module_label']; ?>
&nbsp;&nbsp;</a>
      <div style="cursor: pointer; margin-left: 2px" class="switch_expand" title="<?php echo $this->_config[0]['vars']['index_show_types']; ?>
" onclick="indexShowHideTypes(this, '<?php echo $this->_tpl_vars['module_name']; ?>
');"></div>
      <div style="display: block">&nbsp;</div>
      <div style="padding: 0px 0px 0px 30px; margin: 0px; display: none; background-color: #F1F1F1" class="index_<?php echo $this->_tpl_vars['module_name']; ?>
_module_types_cell">
        <?php $_from = $this->_tpl_vars['module_value']['types_labels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['module_type'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['module_type']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['module_type_key'] => $this->_tpl_vars['module_type_label']):
        $this->_foreach['module_type']['iteration']++;
?>
          <a style="display: block" class="group_role_documents_module_type" onclick="expandListPermissions('<?php echo $this->_tpl_vars['module_type_key']; ?>
_index')" href="javascript:void(0)"><?php echo $this->_tpl_vars['module_type_label']; ?>
</a>
        <?php endforeach; endif; unset($_from); ?>
      </div>
    <?php else: ?>
      <a style="display: block" class="group_role_documents" onclick="expandListPermissions('<?php echo $this->_tpl_vars['module_name']; ?>
_index')" href="javascript:void(0)"><?php echo $this->_tpl_vars['module_value']['module_label']; ?>
</a>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
</div>