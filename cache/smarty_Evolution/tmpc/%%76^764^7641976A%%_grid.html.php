<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:55
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_grid.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'sprintf', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_grid.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_grid.html', 11, false),array('modifier', 'substr', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_grid.html', 21, false),)), $this); ?>
<div class="nz-grid-wrapper">
    <div class="nz-grid-toobar">
        <span id="nz-grid-selectionMenu-trigger" class="nz-grid-multi-actions-select-wrapper"><span class="nz-grid-multi-actions-selectedcount"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['selected'])) ? $this->_run_mod_handler('sprintf', true, $_tmp, 0) : sprintf($_tmp, 0)); ?>
</span> <i class="material-icons">arrow_drop_down</i></span>
        <div class="multiple_actions_placeholder" data-endpoint="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&<?php echo $this->_tpl_vars['action_param']; ?>
=getListMultiActionsPanel&use_ajax=1"></div>
        <div class="nz-grid-pagination-wrapper"></div>
    </div>
    <div class="clear"></div>
    <div id="nz-grid" class="nz-list-grid"
         data-endpoint="<?php echo $this->_tpl_vars['listData']; ?>
"
         data-columns-endpoint="<?php echo $this->_tpl_vars['columnsDefinitions']; ?>
"
         data-rpp="<?php echo ((is_array($_tmp=@$this->_tpl_vars['pagination']['rpp'])) ? $this->_run_mod_handler('default', true, $_tmp, 30) : smarty_modifier_default($_tmp, 30)); ?>
"
         data-currentPage="<?php echo $this->_tpl_vars['pagination']['page']; ?>
"
         data-lastPage="<?php echo $this->_tpl_vars['pagination']['pages']; ?>
"
         data-key="<?php echo $this->_tpl_vars['searchKey']; ?>
"
         data-module-controller="<?php echo $this->_tpl_vars['available_actions']['list']['module']; ?>
|<?php if ($this->_tpl_vars['available_actions']['list']['controller']): ?><?php echo $this->_tpl_vars['available_actions']['list']['controller']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_actions']['list']['module']; ?>
<?php endif; ?>"
         data-outlooks-feature="<?php echo ((is_array($_tmp=@$this->_tpl_vars['manageOutlooksFeature'])) ? $this->_run_mod_handler('default', true, $_tmp, 1) : smarty_modifier_default($_tmp, 1)); ?>
"
    ></div>
</div>

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."grid/_paginator.html", 'smarty_include_vars' => array('pageSize' => $this->_tpl_vars['pageSize'],'totalRecordsCount' => $this->_tpl_vars['totalRecordsCount'],'currentPage' => $this->_tpl_vars['currentPage'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."grid/cols/_tools.html", 'smarty_include_vars' => array('short_module_name' => ((is_array($_tmp=$this->_tpl_vars['module_name'])) ? $this->_run_mod_handler('substr', true, $_tmp, 0, 3) : substr($_tmp, 0, 3)),'basic_vars_labels' => $this->_tpl_vars['basic_vars_labels'],'module_name' => $this->_tpl_vars['module_name'],'theme' => $this->_tpl_vars['theme'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."grid/_settings.html", 'smarty_include_vars' => array('module_param' => $this->_tpl_vars['module_param'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
