<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_submenu_actions_box.html */ ?>
<?php if ($this->_tpl_vars['show_divider']): ?>
<div class="t_footer" style="border:1px solid #CCCCCC; border-bottom: 0"></div>
<?php endif; ?>
<?php if ($this->_tpl_vars['available_actions_left'] || $this->_tpl_vars['available_actions_right']): ?>
<div class="nz-action_compatible nz-actions-wrapper<?php if (isset ( $this->_tpl_vars['onlyIcons'] )): ?> nz-actions-only-icons<?php endif; ?><?php if (isset ( $this->_tpl_vars['tabs'] )): ?> nz-actions--tabs<?php endif; ?>">
  <ul class="nz-actions-list">
  <?php if ($this->_tpl_vars['available_actions_left']): ?>
    <?php $_from = $this->_tpl_vars['available_actions_left']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
        <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
      </ul>
      <ul class="nz-actions-list">
        <?php else: ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box_item.html", 'smarty_include_vars' => array('action' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
  <?php if ($this->_tpl_vars['available_actions_right']): ?>
    <?php $_from = $this->_tpl_vars['available_actions_right']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
        <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
      </ul>
      <ul class="nz-actions-list">
        <?php else: ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box_item.html", 'smarty_include_vars' => array('action' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
  </ul>
</div>

<?php endif; ?>
<?php echo '
<script>
  nz_ready().then(() => {
    const titleWrapper = document.createElement(\'div\');
    titleWrapper.classList.add(\'nz-page-title\');
    const h1 = document.querySelector(\'.nz-content-wrapper h1\');
    h1.before(titleWrapper);
    titleWrapper.append(h1);
    h1.after(document.querySelector(\'.nz-actions-wrapper:not(.nz-action_compatible)\'));
  });
</script>
'; ?>
