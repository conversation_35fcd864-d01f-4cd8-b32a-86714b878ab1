<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from layout/main_messages.html */ ?>
<!-- Begin Messages Reports -->
<div id="messages_container_real" class="nz-main-messages-wrapper<?php if ($this->_tpl_vars['messages']->hasMessages()): ?> nz--has-content<?php endif; ?>" data-has-messages="<?php echo $this->_tpl_vars['messages']->hasMessages(); ?>
">
  <div class="nz-main-messages-header">
    <span class="nz-toggle nz-toggle-autoinit"
          data-toggle-target=".nz-main-messages-wrapper"
          data-toggle-toggleClass="nz--opened">
      <span class="material-icons nz-toggle__inactive">expand_more</span>
      <span class="material-icons nz-toggle__active">expand_less</span>
    </span>
    <span class="nz-main-messages-title">
      <?php if ($this->_tpl_vars['messages']->hasMessages('errors')): ?><i class="material-icons msg-error">highlight_off</i>
      <?php elseif ($this->_tpl_vars['messages']->hasMessages('warnings')): ?><i class="material-icons msg-warning">warning</i>
      <?php elseif ($this->_tpl_vars['messages']->hasMessages('messages')): ?><i class="material-icons msg-message">check_circle</i>
      <?php endif; ?>
    </span>
    <span class="nz-main-messages-counter">00:00</span>
  </div>
  <div class="nz-main-messages-body">
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layout/messages_lists.html', 'smarty_include_vars' => array('messages' => $this->_tpl_vars['messages'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </div>
</div>
<div id="messages_container"></div>
<!-- End Messages Reports -->