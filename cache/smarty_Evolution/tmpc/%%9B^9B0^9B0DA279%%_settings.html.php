<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:55
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_settings.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/_settings.html', 8, false),)), $this); ?>
<?php echo '
<script id="grid-settings-template" type="text/x-template">
    <aside class="nz-grid-settings-panel nz-popout-panel nz-pointer-top-right nz-modal">
        <div class="nz-popout-surface nz-surface nz-elevation--z6">
            <div class="nz-grid-settings-wrapper">
                <div class="nz-grid-settings-wrapper">
                    <div class="nz-grid-settings-title">
                        <h2>'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['outlooks_personal_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '</h2>
                    </div>
                    <form name="outlooks" action="'; ?>
<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=outlooks&outlooks=manage_outlook<?php echo '" method="post" enctype="multipart/form-data" class="nz-grid-settings-body"></form>
                    <div class="nz-grid-settings-footer">
                        <button class="nz-button nz-grid-settings-undo"><i class="material-icons">undo</i> '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['load_defaults'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '</button>
                    </div>
                </div>
            </div>
        </div>
    </aside>
</script>
'; ?>
