<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:55
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/relatives_parent.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/relatives_parent.html', 10, false),)), $this); ?>
<?php echo '
<script id="grid-col-relatives_parent" type="text/x-template">
  <div class="nz-grid-cell-wrapper">
    ${if(properties.relatives_parent.length>0)}
    <div class="nz-grid-cell-expandable">
      ${for(r of properties.relatives_parent)}
      <a href="${Nz.getViewUrl(properties.relatives_parent[rIndex].id)}"
         class=" ${getRecordDirectionClass(properties.relatives_parent[rIndex].direction)} nz-tooltip-trigger nz-tooltip-autoinit"
         data-tooltip-element="#doc-rel-parent-${properties.id}-${r.id}"
         data-tooltip-title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_relative_document_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '"
         data-tooltip-position="panel: top center at: bottom center">${getRecordDirectionIcon(properties.relatives_parent[rIndex].direction)} ${r.full_num}</a>
      <div id="doc-rel-parent-${properties.id}-${r.id}" class="nz-tooltip-content nz-tooltip-notch__top-center">
        <div>${r.name}</div>
      </div>
      <br />
      ${/for}
    </div>
      ${if(properties.relatives_parent.length>2)}
      <div class="nz-grid-cell-centered">
        <a href="javascript:void(0)" class="nz-popout-trigger nz-popout-autoinit"
           data-popout-position="panel: right middle at: left middle"
           data-popout-element="#doc-rel-parents-popout-${properties.id}"
           data-container-attr="{&quot;class&quot;:&quot;nz-grid-popout nz-grid-relatives-popout nz-pointer-middle-right nz-modal&quot;}">
          <i class="material-icons nz-glyph">'; ?>
<?php echo $this->_tpl_vars['theme']->getIconForAction('more'); ?>
<?php echo '</i> ${properties.relatives_parent.length}
        </a>
      </div>
      <div id="doc-rel-parents-popout-${properties.id}" >
        ${for(r of properties.relatives_parent)}
        <a href="${Nz.getViewUrl(properties.relatives_parent[rIndex].id)}"
           class=" ${getRecordDirectionClass(properties.relatives_parent[rIndex].direction)} nz-tooltip-trigger nz-tooltip-autoinit"
           data-tooltip-element="#doc-rel-parent-${properties.id}-${r.id}_popout"
           data-tooltip-title="'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_relative_document_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '"
           data-tooltip-position="panel: top center at: bottom center">${getRecordDirectionIcon(properties.relatives_parent[rIndex].direction)} ${r.full_num}</a>
        <div id="doc-rel-parent-${properties.id}-${r.id}_popout" class="nz-tooltip-content nz-tooltip-notch__top-center">
          <div>${r.name}</div>
        </div>
        <br />
        ${/for}
      </div>
      ${/if}
    ${/if}
  </div>
</script>
'; ?>
