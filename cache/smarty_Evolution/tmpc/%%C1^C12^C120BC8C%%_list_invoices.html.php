<?php /* Smarty version 2.6.33, created on 2025-05-21 16:47:00
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 4, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 31, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 70, false),array('modifier', 'string_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 74, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 167, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 50, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 67, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_list_invoices.html', 70, false),)), $this); ?>
<?php if ($this->_tpl_vars['mode'] != 'credit_debit'): ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_grouping_table t_table" style="margin-top: 0; width: 90%;">
  <tr>
    <th class="t_border" nowrap="nowrap"><div style="width: 60px;"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></th>
    <th class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
    <th class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
    <th class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_list_invoices_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
    <?php if ($this->_tpl_vars['mode'] == 'invoice'): ?>
      <th class="t_border" nowrap="nowrap"><?php echo $this->_config[0]['vars']['contracts_list_invoices_paid']; ?>
</th>
      <th class="t_border" nowrap="nowrap"><?php echo $this->_config[0]['vars']['contracts_list_invoices_not_paid']; ?>
</th>
    <?php endif; ?>
    <th class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
    <th nowrap="nowrap">&nbsp;</th>
  </tr>
<?php else: ?>
  <tr class="cd_list_back cd_list_<?php echo $this->_tpl_vars['parent_iteration']; ?>
" style="display: none">
    <td class="t_border" nowrap="nowrap">&nbsp;</td>
    <td class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    <td class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    <td class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_list_invoices_total'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    <td class="t_border" nowrap="nowrap"><?php echo $this->_config[0]['vars']['contracts_list_invoices_paid']; ?>
</td>
    <td class="t_border" nowrap="nowrap"><?php echo $this->_config[0]['vars']['contracts_list_invoices_not_paid']; ?>
</td>
    <td class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    <td nowrap="nowrap">&nbsp;</td>
  </tr>
<?php endif; ?>
    <?php $_from = $this->_tpl_vars['records']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach[$this->_tpl_vars['mode']] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach[$this->_tpl_vars['mode']]['total'] > 0):
    foreach ($_from as $this->_tpl_vars['record']):
        $this->_foreach[$this->_tpl_vars['mode']]['iteration']++;
?>
    <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['record']->get('name')): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['finance_incomes_reasons_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['record']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['record']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['record']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['record']->get('translations')): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['record']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['record']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

    <?php if ($this->_tpl_vars['record']->modelName == 'Finance_Incomes_Reason'): ?>
      <?php $this->assign('link_controller', 'incomes_reasons'); ?>
      <?php $this->assign('paid', $this->_tpl_vars['record']->get('paid_invoices_amount')); ?>
    <?php else: ?>
      <?php $this->assign('link_controller', 'expenses_reasons'); ?>
      <?php $this->assign('paid', $this->_tpl_vars['record']->get('paid_amount')); ?>
    <?php endif; ?>
    <tr class="<?php echo smarty_function_cycle(array('name' => "cycle_".($this->_tpl_vars['mode'])."_".($this->_tpl_vars['parent_iteration']),'values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['record']->get('annulled_by')): ?> t_strike<?php endif; ?> pointer<?php if ($this->_tpl_vars['mode'] == 'credit_debit'): ?> cd_list_<?php echo $this->_tpl_vars['parent_iteration']; ?>
<?php endif; ?>" style="<?php if ($this->_tpl_vars['mode'] == 'credit_debit'): ?>display: none;<?php endif; ?>" id="<?php echo $this->_tpl_vars['mode']; ?>
_<?php echo $this->_tpl_vars['record']->get('id'); ?>
">
      <td class="t_border hright" onclick="toggleInvoicePreview2({template: <?php echo $this->_tpl_vars['record']->get('id'); ?>
, mode: '<?php echo $this->_tpl_vars['mode']; ?>
', parent_doc: '<?php echo $this->_foreach[$this->_tpl_vars['mode']]['iteration']; ?>
'})">
        <div class="switch_expand" id="switch_<?php echo $this->_tpl_vars['mode']; ?>
_<?php echo $this->_tpl_vars['record']->get('id'); ?>
"></div>
        <?php if ($this->_tpl_vars['record']->get('files_count')): ?>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=<?php echo $this->_tpl_vars['link_controller']; ?>
&amp;<?php echo $this->_tpl_vars['link_controller']; ?>
=attachments&amp;attachments=<?php echo $this->_tpl_vars['record']->get('id'); ?>
">
            <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                 onmouseover="showFiles(this, 'finance', '<?php echo $this->_tpl_vars['link_controller']; ?>
', <?php echo $this->_tpl_vars['record']->get('id'); ?>
)"
                 onmouseout="mclosetime()" style="float: left" />
          </a>
        <?php endif; ?>
        <a name="<?php echo $this->_tpl_vars['mode']; ?>
_<?php echo $this->_tpl_vars['record']->get('id'); ?>
"></a>
        <?php if ($this->_tpl_vars['record']->get('credit_debit')): ?>
          <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
tree1.png" class="floatl" alt="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_issued_creditdebit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
        <?php endif; ?>
        <?php if ($this->_tpl_vars['mode'] != 'invoice'): ?>
          <?php echo $this->_foreach[$this->_tpl_vars['mode']]['iteration']; ?>

        <?php else: ?>
          <?php echo smarty_function_math(array('equation' => 'x-(y*(z-1))-(c-1)','x' => $this->_tpl_vars['all_count'],'y' => $this->_tpl_vars['rpp'],'z' => $this->_tpl_vars['page'],'c' => $this->_foreach[$this->_tpl_vars['mode']]['iteration']), $this);?>

        <?php endif; ?>
      </td>
      <td class="t_border"><a href="<?php if ($this->_tpl_vars['mode'] != 'template'): ?><?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=<?php echo $this->_tpl_vars['link_controller']; ?>
&amp;<?php echo $this->_tpl_vars['link_controller']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['record']->get('id'); ?>
<?php else: ?>#<?php echo $this->_tpl_vars['mode']; ?>
_<?php echo $this->_tpl_vars['record']->get('id'); ?>
<?php endif; ?>" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_tpl_vars['record']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo ((is_array($_tmp=@$this->_tpl_vars['record']->get('num'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['no_number']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['no_number'])); ?>
</a></td>
      <td class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
      <td class="t_border hright" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('total_with_vat'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
      <?php if ($this->_tpl_vars['mode'] != 'template'): ?>
        <td class="t_border hright" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['paid'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)))) ? $this->_run_mod_handler('string_format', true, $_tmp, "%.2f") : smarty_modifier_string_format($_tmp, "%.2f")); ?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
        <td class="t_border hright" nowrap="nowrap"><?php echo smarty_function_math(array('equation' => "x-y",'x' => $this->_tpl_vars['record']->get('total_with_vat'),'y' => ((is_array($_tmp=@$this->_tpl_vars['paid'])) ? $this->_run_mod_handler('default', true, $_tmp, 0) : smarty_modifier_default($_tmp, 0)),'format' => "%.2f"), $this);?>
 <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('currency'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
        <td class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        <td class="hcenter" nowrap="nowrap">
        <?php if ($this->_tpl_vars['record']->modelName == 'Finance_Incomes_Reason' && $this->_tpl_vars['currentUser']->checkRights('finance_incomes_reasons','print')): ?>
          <?php if ($this->_tpl_vars['record']->get('type') == @PH_FINANCE_TYPE_PRO_INVOICE): ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['proforma_invoice']); ?><?php elseif ($this->_tpl_vars['record']->get('type') == @PH_FINANCE_TYPE_INVOICE): ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['invoice']); ?><?php elseif ($this->_tpl_vars['record']->get('type') == @PH_FINANCE_TYPE_CREDIT_NOTICE): ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['credit']); ?><?php elseif ($this->_tpl_vars['record']->get('type') == @PH_FINANCE_TYPE_DEBIT_NOTICE): ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['debit']); ?><?php endif; ?>
          <?php $this->assign('record_company', $this->_tpl_vars['record']->get('company')); ?>
          <?php $this->assign('record_for_printform', $this->_tpl_vars['record']->checkForPrintForm()); ?>
          <?php $_from = $this->_tpl_vars['pats']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pattern']):
?>
            <?php if ($this->_tpl_vars['pattern']['for_printform'] == $this->_tpl_vars['record_for_printform'] && ( $this->_tpl_vars['pattern']['company'] == $this->_tpl_vars['record_company'] || $this->_tpl_vars['pattern']['company'] == '0' )): ?><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=incomes_reasons&amp;incomes_reasons=print&amp;print=<?php echo $this->_tpl_vars['record']->get('id'); ?>
&amp;pattern=<?php echo $this->_tpl_vars['pattern']['id']; ?>
" target="_blank" title="<?php echo $this->_config[0]['vars']['print']; ?>
: <?php echo $this->_tpl_vars['pattern']['name']; ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
pdf.png" alt="" border="0" /></a><?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        <?php endif; ?>
        </td>
      <?php else: ?>
        <td class="t_border" nowrap="nowrap"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['record']->get('template_issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        <td class="hcenter" nowrap="nowrap">
          <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_incomes_reasons','print')): ?>
            <?php if ($this->_tpl_vars['record']->get('type') == @PH_FINANCE_TYPE_PRO_INVOICE): ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['proforma_invoice']); ?><?php elseif ($this->_tpl_vars['record']->get('type') == @PH_FINANCE_TYPE_INVOICE): ?><?php $this->assign('pats', $this->_tpl_vars['patterns']['invoice']); ?><?php endif; ?>
            <?php $this->assign('record_company', $this->_tpl_vars['record']->get('company')); ?>
            <?php $_from = $this->_tpl_vars['pats']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pattern']):
?>
              <?php if (! $this->_tpl_vars['pattern']['for_printform'] && ( $this->_tpl_vars['pattern']['company'] == $this->_tpl_vars['record_company'] || $this->_tpl_vars['pattern']['company'] == '0' ) && ( ! $this->_tpl_vars['record']->get('pattern') || $this->_tpl_vars['pattern']['id'] == $this->_tpl_vars['record']->get('pattern') )): ?>
                <?php if (empty ( $this->_tpl_vars['tpl_errors'][$this->_tpl_vars['key']] )): ?>
                  <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=invoices_templates&amp;invoices_templates=preview&amp;preview=<?php echo $this->_tpl_vars['record']->get('id'); ?>
&amp;pattern=<?php echo $this->_tpl_vars['pattern']['id']; ?>
" target="_blank" title="<?php echo $this->_config[0]['vars']['preview']; ?>
: <?php echo $this->_tpl_vars['pattern']['name']; ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
pdf.png" alt="" border="0" /></a>
                <?php else: ?>
                  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
pdf.png" alt="" border="0" class="dimmed" />
                <?php endif; ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php endif; ?>
          <?php if ($this->_tpl_vars['record']->get('container_id')): ?>
            <?php if ($this->_tpl_vars['currentUser']->checkRights('contracts','addinvoice') && $this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve') && ( $this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_issue_date') || $this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_future_issue_date') )): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
issue_invoice.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" onclick="showIssueDateForm(<?php echo $this->_tpl_vars['record']->get('id'); ?>
, 'issue_invoice', '<?php echo ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, "%Y-%m-%d") : smarty_modifier_date_format($_tmp, "%Y-%m-%d")); ?>
', '<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
', '<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_issue_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" style="cursor:pointer;" />
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
issue_invoice.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" class="dimmed" />
            <?php endif; ?>
            <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve') && ( $this->_tpl_vars['record']->get('observer_response') == 'cancel' || $this->_tpl_vars['record']->get('observer_response') == 'none' )): ?>
              <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_issue_date') || $this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve_future_issue_date')): ?>
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
approve.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" onclick="showIssueDateForm(<?php echo $this->_tpl_vars['record']->get('id'); ?>
, 'approve', '<?php echo $this->_tpl_vars['record']->get('issue_date'); ?>
', '<?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
', '<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_approve_title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
')" style="cursor:pointer;" />
              <?php else: ?>
                <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=invoices_templates&amp;invoices_templates=approve&amp;items=<?php echo $this->_tpl_vars['record']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
approve.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /></a>
              <?php endif; ?>
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
approve.png" border="0" class="dimmed" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['approve'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
            <?php endif; ?>
            <?php if ($this->_tpl_vars['currentUser']->checkRights('finance_invoices_templates','approve') && $this->_tpl_vars['record']->get('observer_response') != 'cancel'): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=finance&amp;controller=invoices_templates&amp;invoices_templates=disapprove&amp;items=<?php echo $this->_tpl_vars['record']->get('id'); ?>
"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
disapprove.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['disapprove'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['disapprove'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" /></a>
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
disapprove.png" border="0" class="dimmed" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['disapprove'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['disapprove'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
            <?php endif; ?>
          <?php else: ?>
            <?php if ($this->_tpl_vars['currentUser']->checkRights('contracts','addinvoice')): ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
issue_invoice.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" onclick="issueFinal(<?php echo $this->_tpl_vars['record']->get('contract_id'); ?>
)" style="cursor:pointer;" />
            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
issue_invoice.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" class="dimmed" />
            <?php endif; ?>
          <?php endif; ?>
        </td>
      <?php endif; ?>
    </tr>
    <tr id="<?php echo $this->_tpl_vars['mode']; ?>
_<?php echo $this->_tpl_vars['record']->get('id'); ?>
_preview" style="display:none;"<?php if ($this->_tpl_vars['mode'] == 'credit_debit'): ?> class="cd_list_<?php echo $this->_tpl_vars['parent_iteration']; ?>
"<?php endif; ?>>
      <td colspan="8" style="padding: 20px;">
        <?php if ($this->_tpl_vars['record']->checkPermissions('view')): ?>
          <?php $this->assign('gt2', $this->_tpl_vars['record']->get('grouping_table_2')); ?>
          <?php if (empty ( $this->_tpl_vars['gt2']['values'] )): ?>
            <span class="warning">
            <?php if ($this->_tpl_vars['mode'] == 'template'): ?>
              <?php echo $this->_config[0]['vars']['contracts_templates_canceled_period']; ?>

            <?php else: ?>
              <?php echo $this->_config[0]['vars']['contracts_invoices_annulled_document']; ?>

            <?php endif; ?>
            </span>
          <?php else: ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_gt2_view.html", 'smarty_include_vars' => array('model' => $this->_tpl_vars['record'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php endif; ?>
        <?php else: ?>
          <span class="red"><?php echo $this->_config[0]['vars']['error_preview_notallowed']; ?>
</span>
        <?php endif; ?>
      </td>
    </tr>
    <?php if ($this->_tpl_vars['record']->get('credit_debit')): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_list_invoices.html", 'smarty_include_vars' => array('records' => $this->_tpl_vars['record']->get('credit_debit'),'mode' => 'credit_debit','parent_iteration' => $this->_foreach[$this->_tpl_vars['mode']]['iteration'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
    <?php endforeach; else: ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
      <td class="error" colspan="8"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
    <?php endif; unset($_from); ?>
<?php if ($this->_tpl_vars['mode'] != 'credit_debit'): ?>
  <tr><td colspan="8" class="t_footer"></td></tr>
</table>
  <?php if ($this->_tpl_vars['records'] && $this->_tpl_vars['mode'] == 'invoice'): ?>
    <div class="pagemenu floatr">
      <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=<?php echo $this->_tpl_vars['action']; ?>
&amp;<?php echo $this->_tpl_vars['action']; ?>
=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => count($this->_tpl_vars['records']),'total' => $this->_tpl_vars['all_count'],'rpp' => $this->_tpl_vars['rpp'],'page' => $this->_tpl_vars['page'],'pages' => $this->_tpl_vars['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1,'hide_rpp' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </div>
  <?php endif; ?>
<div style="clear: both;"></div>
<?php endif; ?>