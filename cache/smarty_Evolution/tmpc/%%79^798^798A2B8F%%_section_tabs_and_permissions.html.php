<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/modules/roles/templates/_section_tabs_and_permissions.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/_section_tabs_and_permissions.html', 34, false),array('modifier', 'mb_upper', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/_section_tabs_and_permissions.html', 42, false),)), $this); ?>
<tr>
  <td colspan="3" style="padding-bottom: 0px;">
    <input type="hidden" name="section_loading" id="section_loading" value="" />
    <div class="action_tabs" id="sections_permissions_tabs" style="background:none!important;width:100%;border-top:none">
      <div class="zpMenuNzoom">
        <div style="text-align: left; display: block; z-index: 0; " class="zpMenu-top zpMenuContainer zpMenu-horizontal-mode" id="zpMenu4Container0"><div class="zpMenu zpMenu-lines-t" style="width: 616px; ">
          <?php $this->assign('counter', "-1"); ?>
          <?php $_from = $this->_tpl_vars['role']->getSectionsPermissionsMenu(); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['module'] => $this->_tpl_vars['name']):
?>
            <?php $this->assign('counter', $this->_tpl_vars['counter']+1); ?>
            <div id="<?php echo $this->_tpl_vars['module']; ?>
" style="font-weight: normal!important;" onclick="showHideRoleSections(this, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['role']->get('id'); ?>
')" class="zpMenu-lines-c zpMenu-item-first zpMenu-item zpMenu-level-1 zpMenu-item-odd section_menu_element" onmouseover="addClass(this, 'menu-path');" onmouseout="removeClass(this, 'menu-path');">
              <table class="zpMenu-table" cellspacing="0" cellpadding="0">
                <tr>
                  <td class="tgb icon">
                    <div>
                      <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
<?php if ($this->_tpl_vars['module'] != 'others'): ?><?php echo $this->_tpl_vars['module']; ?>
<?php else: ?>empty<?php endif; ?>.png" width="12" height="12" alt="" title="<?php echo $this->_tpl_vars['name']; ?>
" />
                    </div>
                  </td>
                  <td style="white-space: nowrap; " class="zpMenu-label" title="<?php echo $this->_tpl_vars['name']; ?>
">
                    <div><?php echo $this->_tpl_vars['name']; ?>
</div>
                  </td>
                </tr>
              </table>
            </div>
          <?php endforeach; endif; unset($_from); ?>
        </div>
      </div></div>
    </div>
  </td>
</tr>
<tr>
  <td colspan="3" class="t_caption3">
    <a name="vars_index"></a>
    <div class="t_caption2_title strong" style="color:black">
      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['permissions_index'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    </div>
  </td>
</tr>
<tr>
  <td colspan="3" class="index_class" style="background-color: #E8E8E8"></td>
</tr>
<tr>
  <td class="t_caption3 strong" width="65%" colspan="2"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_permissions'])) ? $this->_run_mod_handler('mb_upper', true, $_tmp) : smarty_modifier_mb_upper($_tmp)); ?>
</td>
  <td class="t_caption3 hright" width="30%">
    <span class="pointer" onclick="toggleModulePermissionsAll(this, 'expand')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['expand_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
    <span class="pointer" onclick="toggleModulePermissionsAll(this, 'collapse')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['collapse_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
    <script type="text/javascript">
      onReady().then(() => {
        showHideRoleSections($('<?php echo $this->_tpl_vars['selected_section_name']; ?>
'), '<?php echo $this->_tpl_vars['selected_section_name']; ?>
', '<?php echo $this->_tpl_vars['role']->get('id'); ?>
');
      });
    </script>
  </td>
</tr>
<?php $_from = $this->_tpl_vars['role']->rolesGroups; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['section_group_name'] => $this->_tpl_vars['section_group_values']):
?>
  <tr>
    <td id="<?php echo $this->_tpl_vars['section_group_name']; ?>
_permissions_container" colspan="3" class="nopadding"></td>
  </tr>
<?php endforeach; endif; unset($_from); ?>