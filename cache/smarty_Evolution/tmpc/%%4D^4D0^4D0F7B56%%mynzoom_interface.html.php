<?php /* Smarty version 2.6.33, created on 2025-05-21 16:41:59
         compiled from /var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html', 5, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html', 10, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html', 205, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html', 10, false),array('function', 'array', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html', 109, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html', 111, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/users/templates/mynzoom_interface.html', 119, false),)), $this); ?>
<script type="text/javascript">
  timezones = <?php echo $this->_tpl_vars['timezones_obj']; ?>
;
</script>
    <input type="hidden" name="layout" id="layout" value="<?php echo $this->_tpl_vars['layout']; ?>
" />
    <input type="hidden" name="timezones" id="timezones" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['timezones_obj'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
    <table cellspacing="0" cellpadding="0" border="0" class="t_table">
      <tr>
        <td class="t_caption3">
          <div class="t_caption2_title">
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/info.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=@$this->_tpl_vars['layouts'][$this->_tpl_vars['layout']]['description'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')),'caption' => $this->_config[0]['vars']['system_info']), $this);?>
 />
            <?php echo ((is_array($_tmp=$this->_tpl_vars['layouts'][$this->_tpl_vars['layout']]['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </div>
        </td>
      </tr>

      <tr>
        <td style="padding: 5px;">
          <input type="checkbox" name="action_labels" id="action_labels" value="1"<?php if ($this->_tpl_vars['settings']['interface']['action_labels']): ?> checked="checked"<?php endif; ?> /><label for="action_labels"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_action_labels'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
        </td>
      </tr>
      <?php if ($this->_tpl_vars['allow_alternative_lang_switch']): ?>
        <tr>
          <td class="t_caption2">
            <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_alternative_lang_switch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
          </td>
        </tr>
        <tr>
          <td style="padding: 5px;">
            <input type="checkbox" name="alternative_lang_switch" id="alternative_lang_switch" value="1"<?php if ($this->_tpl_vars['settings']['interface']['alternative_lang_switch']): ?> checked="checked"<?php endif; ?> onclick="$('alternative_keyboard_inputs_container').style.display=(this.checked)?'':'none'" /><label for="alternative_lang_switch"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_allow_alternative_lang_switch'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />

            <div id="alternative_keyboard_inputs_container" <?php if (! $this->_tpl_vars['settings']['interface']['alternative_lang_switch']): ?> style="display: none"<?php endif; ?>>
              <ol id="alternative_keyboard_inputs" class="alternative_keyboard_inputs">
              <?php $_from = $this->_tpl_vars['alternative_keyboard_inputs']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['kinput']):
?>
                <li class="sortable pointer">
                  <input type="checkbox" name="alternative_keyboard_inputs[]" id="alternative_keyboard_input_<?php echo $this->_tpl_vars['kinput']['abbr']; ?>
" value="<?php echo $this->_tpl_vars['kinput']['abbr']; ?>
"<?php if ($this->_tpl_vars['kinput']['selected']): ?> checked="checked"<?php endif; ?> /><label for="alternative_keyboard_input_<?php echo $this->_tpl_vars['kinput']['abbr']; ?>
"><span class="langLinkSample <?php echo $this->_tpl_vars['kinput']['abbr']; ?>
"><?php echo $this->_tpl_vars['kinput']['abbr']; ?>
</span><?php echo $this->_tpl_vars['kinput']['description']; ?>
</label>
                </li>
              <?php endforeach; endif; unset($_from); ?>
              </ol>
              <?php echo $this->_config[0]['vars']['users_mynzoom_settings_alternative_lang_switch_disclaimer']; ?>

            </div>
            <script type="text/javascript">
              Position.includeScrollOffsets = true;
              Sortable.create('alternative_keyboard_inputs', {tag: 'LI',
                                                        containment: 'alternative_keyboard_inputs',
                                                        constraint: 'vertical',
                                                        only: 'sortable',
                                                        scroll: 'alternative_keyboard_inputs_container'
                                                       });
            </script>
          </td>
        </tr>
      <?php endif; ?>

      <?php if ($this->_tpl_vars['userCanSelectTheme']): ?>
      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_theme'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
        </td>
      </tr>
      <tr>
        <td style="padding: 5px;">
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'theme','label' => ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_theme'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'value' => ((is_array($_tmp=@$this->_tpl_vars['settings']['interface']['theme'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')),'sequences' => '','required' => '','disabled' => '','standalone' => 1,'options' => $this->_tpl_vars['themes'],'index' => 1,'width' => 200)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
      </tr>
      <?php endif; ?>

      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_timezone'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
        </td>
      </tr>
        <tr>
          <td nowrap="nowrap" id="timers_1_1" style="padding: 5px;">
            <select name="timezone_area" id="areas_1" onchange="setTimezones(this);" class="selbox">
            <?php $_from = $this->_tpl_vars['timezones_all']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['zones_area'] => $this->_tpl_vars['zones']):
?>
            <option value="<?php echo $this->_tpl_vars['zones_area']; ?>
"<?php if ($this->_tpl_vars['zones_area'] == $this->_tpl_vars['timezone_area']): ?> selected="selected"<?php $this->assign('timezones', $this->_tpl_vars['zones']); ?><?php endif; ?>><?php echo $this->_tpl_vars['zones_area']; ?>
</option>
            <?php endforeach; endif; unset($_from); ?>
            </select>
            <span id="timers_1_2">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_dropdown.html", 'smarty_include_vars' => array('name' => 'timezones','label' => $this->_config[0]['vars']['timezones'],'value' => $this->_tpl_vars['timezone'],'sequences' => '','required' => 1,'disabled' => 0,'standalone' => 1,'options' => $this->_tpl_vars['timezones'],'index' => 1,'width' => 200)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </span>
          </td>
        </tr>
      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_rpp'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
        </td>
      </tr>
      <?php echo smarty_function_array(array('assign' => 'rpp_vals','eval' => 'array(5,10,25,50,75,100)'), $this);?>

      <?php $this->assign('cols', 3); ?>
      <?php echo smarty_function_math(array('assign' => 'dcols','equation' => 'a*2','a' => $this->_tpl_vars['cols']), $this);?>

      <?php echo smarty_function_math(array('assign' => 'cwidth','equation' => '80/a','a' => $this->_tpl_vars['cols'],'format' => '%d'), $this);?>

      <tr>
        <td>
          <table class="t_table" cellspacing="0" cellpadding="0" border="0" id="interface_rpp">
            <?php $_from = $this->_tpl_vars['modules']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['mi'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['mi']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['mk'] => $this->_tpl_vars['mod']):
        $this->_foreach['mi']['iteration']++;
?>
            <?php $this->assign('idx', $this->_foreach['mi']['iteration']); ?>
            <?php $this->assign('var_name', $this->_tpl_vars['mod']['var_name']); ?>
            <?php if (! ( $this->_tpl_vars['mk'] % $this->_tpl_vars['cols'] )): ?><tr class="<?php echo smarty_function_cycle(array('values' => 't_odd1, t_even1'), $this);?>
"><?php endif; ?>
            <td style="width: <?php echo $this->_tpl_vars['cwidth']; ?>
%;"><?php echo $this->_tpl_vars['mod']['module_name']; ?>
</td>
            <td>
              <select class="selbox small" name="<?php echo $this->_tpl_vars['var_name']; ?>
" id="<?php echo $this->_tpl_vars['var_name']; ?>
" <?php if (($this->_foreach['mi']['iteration'] <= 1)): ?>title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_rpp'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onchange="return confirmAction('all_default_rpp', function(el) { setAllDefaultRPP(el); }, this);"<?php endif; ?>>
                <?php $_from = $this->_tpl_vars['rpp_vals']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['rpp']):
?>
                <option value="<?php echo $this->_tpl_vars['rpp']; ?>
"<?php if ($this->_tpl_vars['settings']['interface'][$this->_tpl_vars['var_name']] == $this->_tpl_vars['rpp'] || $this->_tpl_vars['rpp'] == 10 && ! $this->_tpl_vars['settings']['interface'][$this->_tpl_vars['var_name']]): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['rpp']; ?>
</option>
                <?php endforeach; endif; unset($_from); ?>
              </select>
            </td>
            <?php if (($this->_foreach['mi']['iteration'] == $this->_foreach['mi']['total']) && $this->_tpl_vars['mk'] % $this->_tpl_vars['cols'] != $this->_tpl_vars['cols'] - 1): ?><td colspan="<?php echo smarty_function_math(array('equation' => '2*(b-(a+1)%b)','a' => $this->_tpl_vars['mk'],'b' => $this->_tpl_vars['cols']), $this);?>
">&nbsp;</td><?php endif; ?>
            <?php if (( $this->_tpl_vars['mk'] % $this->_tpl_vars['cols'] ) == $this->_tpl_vars['cols'] - 1 || ($this->_foreach['mi']['iteration'] == $this->_foreach['mi']['total'])): ?></tr><?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
            <tr>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
" class="t_caption2">
                <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_rpp_communications'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
              </td>
            </tr>
            <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd1, t_even1'), $this);?>
">
              <td><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
-1">
                <select class="selbox small" name="list_communications" id="list_communications">
                  <?php $_from = $this->_tpl_vars['rpp_vals']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['rpp']):
?>
                  <option value="<?php echo $this->_tpl_vars['rpp']; ?>
"<?php if ($this->_tpl_vars['settings']['interface']['list_communications'] == $this->_tpl_vars['rpp'] || $this->_tpl_vars['rpp'] == 10 && ! $this->_tpl_vars['settings']['interface']['list_communications']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['rpp']; ?>
</option>
                  <?php endforeach; endif; unset($_from); ?>
                </select>
              </td>
            </tr>
            <tr>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
" class="t_caption2">
                <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_rpp_history'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
              </td>
            </tr>
            <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd1, t_even1'), $this);?>
">
              <td><?php echo ((is_array($_tmp=$this->_config[0]['vars']['history'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
-1">
                <select class="selbox small" name="list_history" id="list_history">
                  <?php $_from = $this->_tpl_vars['rpp_vals']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['rpp']):
?>
                  <option value="<?php echo $this->_tpl_vars['rpp']; ?>
"<?php if ($this->_tpl_vars['settings']['interface']['list_history'] == $this->_tpl_vars['rpp'] || $this->_tpl_vars['rpp'] == 10 && ! $this->_tpl_vars['settings']['interface']['list_history']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['rpp']; ?>
</option>
                  <?php endforeach; endif; unset($_from); ?>
                </select>
              </td>
            </tr>
            <tr>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
" class="t_caption2">
                <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_rpp_timesheets'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
              </td>
            </tr>
            <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd1, t_even1'), $this);?>
">
              <td><?php echo ((is_array($_tmp=$this->_config[0]['vars']['timesheets'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
-1">
                <select class="selbox small" name="list_timesheets" id="list_timesheets">
                  <?php $_from = $this->_tpl_vars['rpp_vals']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['rpp']):
?>
                  <option value="<?php echo $this->_tpl_vars['rpp']; ?>
"<?php if ($this->_tpl_vars['settings']['interface']['list_timesheets'] == $this->_tpl_vars['rpp'] || $this->_tpl_vars['rpp'] == 10 && ! $this->_tpl_vars['settings']['interface']['list_timesheets']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['rpp']; ?>
</option>
                  <?php endforeach; endif; unset($_from); ?>
                </select>
              </td>
            </tr>
            <tr>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
" class="t_caption2">
                <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_rpp_reminders'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
              </td>
            </tr>
            <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd1, t_even1'), $this);?>
">
              <td><?php echo ((is_array($_tmp=$this->_config[0]['vars']['remind'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
              <td colspan="<?php echo $this->_tpl_vars['dcols']; ?>
-1">
                <select class="selbox small" name="list_reminders" id="list_reminders">
                  <?php $_from = $this->_tpl_vars['rpp_vals']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['rpp']):
?>
                  <option value="<?php echo $this->_tpl_vars['rpp']; ?>
"<?php if ($this->_tpl_vars['settings']['interface']['list_reminders'] == $this->_tpl_vars['rpp'] || $this->_tpl_vars['rpp'] == 10 && ! $this->_tpl_vars['settings']['interface']['list_reminders']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['rpp']; ?>
</option>
                  <?php endforeach; endif; unset($_from); ?>
                </select>
              </td>
            </tr>
          </table>
        </td>
      </tr>
      <tr>
        <td class="t_caption2">
          <div class="t_caption2_title floatl"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['users_mynzoom_settings_confirm_required'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
          <div class="floatr">
            <span onclick="toggleCheckboxes(this, 'confirm', true, 'settings_confirm')" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
            <span onclick="toggleCheckboxes(this, 'confirm', false, 'settings_confirm')" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
          </div>
        </td>
      </tr>
      <tr id="settings_confirm">
        <td>
          <?php $this->assign('num_cols', count($this->_tpl_vars['confirm_actions'])); ?>
          <?php if ($this->_tpl_vars['num_cols'] > $this->_tpl_vars['cols']): ?><?php $this->assign('num_cols', $this->_tpl_vars['cols']); ?><?php endif; ?>
          <?php $_from = $this->_tpl_vars['confirm_actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ci'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ci']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['confirm_actions_part']):
        $this->_foreach['ci']['iteration']++;
?>
          <div class="vtop floatl" style="min-height: 180px; width: <?php echo smarty_function_math(array('equation' => '100/a','a' => $this->_tpl_vars['num_cols'],'format' => '%.2f'), $this);?>
%;<?php if ($this->_foreach['ci']['iteration'] % $this->_tpl_vars['num_cols'] == 1): ?> clear: both;<?php endif; ?>">
            <div class="t_caption3 t_caption3_title"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['confirm_actions_part']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</div>
            <?php $_from = $this->_tpl_vars['confirm_actions_part']['actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['act'] => $this->_tpl_vars['lbl']):
?>
            <input type="checkbox"<?php if (! in_array ( $this->_tpl_vars['act'] , $this->_tpl_vars['settings']['interface']['skip_confirm'] )): ?> checked="checked"<?php endif; ?> name="confirm[]" id="confirm_<?php echo $this->_tpl_vars['act']; ?>
" value="<?php echo $this->_tpl_vars['act']; ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['lbl'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <label for="confirm_<?php echo $this->_tpl_vars['act']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['lbl'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label><br />
            <?php endforeach; endif; unset($_from); ?>
          </div>
          <?php if (($this->_foreach['ci']['iteration'] == $this->_foreach['ci']['total']) && $this->_foreach['ci']['iteration'] % $this->_tpl_vars['num_cols']): ?>
          <div class="vtop floatl" style="min-height: 180px; width: <?php echo smarty_function_math(array('equation' => '(a-b%a)*100/a','a' => $this->_tpl_vars['num_cols'],'b' => $this->_foreach['ci']['iteration'],'format' => '%.2f'), $this);?>
%;">
            <div class="t_caption3 t_caption3_title">&nbsp;</div>
          </div>
          <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        </td>
      </tr>
    </table>