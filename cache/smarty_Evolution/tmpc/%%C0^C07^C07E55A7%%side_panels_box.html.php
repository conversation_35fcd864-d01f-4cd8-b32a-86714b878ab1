<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:32
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/side_panels_box.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/side_panels_box.html', 9, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/side_panels_box.html', 15, false),)), $this); ?>
<input type="hidden" name="side_panel_model_type" id="side_panel_model_type" value="<?php echo $this->_tpl_vars['model_type']; ?>
" />
<div class="side_panel_drag-wrapper">
<?php $_from = $this->_tpl_vars['side_panels']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['pos'] => $this->_tpl_vars['panel']):
        $this->_foreach['i']['iteration']++;
?>
  <?php if (! empty ( $this->_tpl_vars['panel'] )): ?>
      <section id="side_panel_<?php echo $this->_tpl_vars['panel']; ?>
" class="nz-side-panel draggable drag_side_panel nz-elevation--z3 hidden nz-drag-element">
        <div id="title_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
" class="side_panel_head">
          <span id="move_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
">
            <a href="javascript:void()">
              <i class="material-icons move_side_panel_icon nz-drag-handle" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['draggable'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">drag_indicator</i>
            </a>
          </span>
          <span class="side_panel_title">
            <span class="title_side_panel"><span class="material-icons nz-side-panel-graphic"><?php echo $this->_tpl_vars['side_panels_icons'][$this->_tpl_vars['panel']]; ?>
</span>
              <?php if ($this->_tpl_vars['side_panels_urls'][$this->_tpl_vars['panel']]): ?>
                <a href="<?php echo $this->_tpl_vars['side_panels_urls'][$this->_tpl_vars['panel']]; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['side_panels_titles'][$this->_tpl_vars['panel']])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 90, "...") : smarty_modifier_mb_truncate($_tmp, 90, "...")))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a>
              <?php else: ?>
                <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['side_panels_titles'][$this->_tpl_vars['panel']])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 90, "...") : smarty_modifier_mb_truncate($_tmp, 90, "...")))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

              <?php endif; ?>
            </span>
          </span>
          <span class="nz-icon-button" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="hideSidePanel('<?php echo $this->_tpl_vars['panel']; ?>
');">
            clear
          </span>
        </div>
        <div id="content_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
" class="side_panel_loader side_panel_content">
          <script type="text/javascript">
          sidePanelsLoad('content_side_panel_<?php echo $this->_tpl_vars['panel']; ?>
', '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', '<?php echo $this->_tpl_vars['model_id']; ?>
', '<?php echo $this->_tpl_vars['panel']; ?>
'<?php if ($this->_tpl_vars['archive']): ?>, 1<?php endif; ?>);
          </script>
        </div>
      </section>
  <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
</div>