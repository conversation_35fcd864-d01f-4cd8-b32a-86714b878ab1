<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from /var/www/Nzoom-Hella/_libs/modules/index/view/templates/frontend.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/index/view/templates/frontend.html', 7, false),array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/index/view/templates/frontend.html', 24, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/index/view/templates/frontend.html', 24, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/modules/index/view/templates/frontend.html', 24, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/index/view/templates/frontend.html', 24, false),)), $this); ?>
<link rel="stylesheet" href="<?php echo $this->_tpl_vars['view_url']; ?>
css/frontend.css?<?php echo $this->_tpl_vars['system_options']['build']; ?>
" />
<div class="clear" id="index_page_clear_flag"></div>
<div class="nz-dashboard nz-drag-list">
<?php echo smarty_function_counter(array('name' => 'idx','assign' => 'empty_idx','start' => 0,'print' => false), $this);?>

<?php $_from = $this->_tpl_vars['dashletByPosition']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['rowNum'] => $this->_tpl_vars['rowData']):
        $this->_foreach['i']['iteration']++;
?>
  <?php $_from = $this->_tpl_vars['rowData']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['col'] => $this->_tpl_vars['positionDashlets']):
        $this->_foreach['i']['iteration']++;
?>
    <div class="nz-drag-placeholder nz-dashboard-gridPosition row-<?php echo $this->_tpl_vars['rowNum']; ?>
 col-<?php echo $this->_tpl_vars['col']; ?>
" data-position="<?php echo $this->_tpl_vars['rowNum']; ?>
<?php echo $this->_tpl_vars['col']; ?>
"><?php echo ''; ?><?php $_from = $this->_tpl_vars['positionDashlets']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key']):
        $this->_foreach['i']['iteration']++;
?><?php echo ''; ?><?php $this->assign('dashlet', $this->_tpl_vars['dashlets'][$this->_tpl_vars['key']]); ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'content_dashlet_'; ?><?php echo $this->_tpl_vars['dashlet']->get('id'); ?><?php echo '_box'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('dashlet_id_cookie', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'dashlet_'; ?><?php echo $this->_tpl_vars['dashlet']->get('id'); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('dashlet_id', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['dashlet'] )): ?><?php echo '<div class="nz-dashlet-wrapper nz-drag-element position-'; ?><?php echo $this->_tpl_vars['position']; ?><?php echo ''; ?><?php if ($this->_tpl_vars['dashlet']->get('full_width') == 1): ?><?php echo ' full_width'; ?><?php endif; ?><?php echo '"data-dashlet-id="'; ?><?php echo $this->_tpl_vars['dashlet_id']; ?><?php echo '"data-position="'; ?><?php echo $this->_tpl_vars['position']; ?><?php echo '"><section id="'; ?><?php echo $this->_tpl_vars['dashlet_id']; ?><?php echo '" class="nz-dashlet nz-elevation--z3'; ?><?php if ($this->_tpl_vars['dashlet']->get('full_width') == 1): ?><?php echo ' full_width'; ?><?php endif; ?><?php echo '"><div class="nz-dashlet__titlebar"><div class="nz-dashlet__titlebar__tools"><div class="nz-drag-handle material-icons">drag_indicator</div>'; ?><?php if ($this->_tpl_vars['dashlet']->get('description') !== ''): ?><?php echo ''; ?><?php echo smarty_function_help(array('text_content' => ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['dashlet']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp, 'html') : smarty_modifier_escape($_tmp, 'html')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')),'labbel' => 'system_info'), $this);?><?php echo ''; ?><?php endif; ?><?php echo '</div><div class="nz-dashlet__titlebar__caption"><h3>'; ?><?php echo $this->_tpl_vars['dashlet']->get('name'); ?><?php echo '</h3>'; ?><?php if ($this->_tpl_vars['dashlet']->get('module') != 'plugin'): ?><?php echo ''; ?><?php if ($this->_tpl_vars['dashlet']->get('module') == 'reports'): ?><?php echo '<a href="'; ?><?php echo $this->_tpl_vars['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '=reports&amp;report_type='; ?><?php echo $this->_tpl_vars['dashlet']->get('controller'); ?><?php echo '"class="nz-icon-button">'; ?><?php echo $this->_tpl_vars['theme']->getIconForRecord('reports'); ?><?php echo '</a>'; ?><?php elseif (isset ( $this->_tpl_vars['dashlet']->linkUrl )): ?><?php echo '<a href="'; ?><?php echo $this->_tpl_vars['dashlet']->linkUrl; ?><?php echo '"class="nz-icon-button"title="'; ?><?php echo $this->_config[0]['vars']['search']; ?><?php echo '">'; ?><?php echo $this->_tpl_vars['theme']->getIconForRecord($this->_tpl_vars['dashlet']->get('module')); ?><?php echo '<span class="nz-glyph-sub">search</span></a>'; ?><?php else: ?><?php echo '<a class="nz-icon-button"href="'; ?><?php echo $this->_tpl_vars['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['dashlet']->get('module'); ?><?php echo '&amp;'; ?><?php if ($this->_tpl_vars['dashlet']->get('module') != $this->_tpl_vars['dashlet']->get('controller')): ?><?php echo 'controller='; ?><?php echo $this->_tpl_vars['dashlet']->get('controller'); ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['dashlet']->get('controller'); ?><?php echo '='; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['dashlet']->get('module'); ?><?php echo '='; ?><?php endif; ?><?php echo 'search&amp;session_param_prefix=dashlets_'; ?><?php echo $this->_tpl_vars['dashlet']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['dashlet_filters']['display']): ?><?php echo '&amp;display='; ?><?php echo $this->_tpl_vars['dashlet_filters']['display']; ?><?php echo ''; ?><?php endif; ?><?php echo '">'; ?><?php echo $this->_tpl_vars['theme']->getIconForRecord($this->_tpl_vars['dashlet']->get('module')); ?><?php echo '<span class="nz-glyph-sub">search</span></a><a href="'; ?><?php echo $this->_tpl_vars['dashlet']->linkUrl; ?><?php echo '"class="nz-icon-button"title="'; ?><?php echo $this->_config[0]['vars']['search']; ?><?php echo '">'; ?><?php echo $this->_tpl_vars['theme']->getIconForRecord($this->_tpl_vars['dashlet']->get('module')); ?><?php echo '<span class="nz-glyph-sub">search</span></a>'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php if ($this->_tpl_vars['dashlet']->get('controller') == 'calendar'): ?><?php echo '<a href="'; ?><?php echo $this->_tpl_vars['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '=calendars"class="nz-icon-button">'; ?><?php echo $this->_tpl_vars['theme']->getIconForRecord('calendars'); ?><?php echo '</a>'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo '</div><div class="nz-dashlet__titlebar__toggle nz-toggle nz-toggle-autoinit"data-toggle-target="#'; ?><?php echo $this->_tpl_vars['dashlet_id']; ?><?php echo '"data-toggle-toggleClass="nz--closed"data-toggle-personalsettings-section="switch"data-toggle-personalsettings-name="'; ?><?php echo $this->_tpl_vars['dashlet_id_cookie']; ?><?php echo '"data-toggle-personalsettings-value="off"><button type="button" class="nz-icon-button nz-toggle__inactive">expand_more</button><button type="button" class="nz-icon-button nz-toggle__active">expand_less</button></div></div><div id="content_dashlet_'; ?><?php echo $this->_tpl_vars['dashlet']->get('id'); ?><?php echo '" class="nz-dashlet__body dashlet_content"><script type="text/javascript">nz_ready().then(() => '; ?>{<?php echo 'dashletsLoad(\'#content_dashlet_'; ?><?php echo $this->_tpl_vars['dashlet']->get('id'); ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['dashlet']->get('module'); ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['dashlet']->get('controller'); ?><?php echo '\', \''; ?><?php echo $this->_tpl_vars['dashlet']->get('id'); ?><?php echo '\');'; ?>}<?php echo ');</script></div></section></div>'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>
</div>
  <?php endforeach; endif; unset($_from); ?>
<?php endforeach; endif; unset($_from); ?>
</div>
<div class="clear"></div>
<script type="text/javascript">
  nz_ready().then(() => {
    initDashboard();
  });
</script>