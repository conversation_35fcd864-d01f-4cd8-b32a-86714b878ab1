<?php /* Smarty version 2.6.33, created on 2025-05-27 12:09:21
         compiled from view_checkbox_group.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', 'view_checkbox_group.html', 3, false),array('modifier', 'default', 'view_checkbox_group.html', 12, false),array('modifier', 'escape', 'view_checkbox_group.html', 12, false),)), $this); ?>
<?php if (! $this->_tpl_vars['standalone']): ?>
        <tr<?php if ($this->_tpl_vars['var']['hidden']): ?> style="display: none;"<?php endif; ?>>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['var']['label'],'text_content' => $this->_tpl_vars['var']['help']), $this);?>
</td>
          <td class="required"><?php if ($this->_tpl_vars['var']['required']): ?><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?>&nbsp;<?php endif; ?></td>
          <td>
<?php endif; ?>
<?php $this->assign('cb_has_value', false); ?>
<?php if ($this->_tpl_vars['var']['options']): ?>
  <?php $_from = $this->_tpl_vars['var']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['cb']['iteration']++;
?>
    <?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['option']['value']): ?>
      <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?><span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*<?php endif; ?>
      <?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?><?php if (! empty ( $this->_tpl_vars['var']['options_align'] ) && $this->_tpl_vars['var']['options_align'] == 'horizontal'): ?>&nbsp;<?php else: ?><br /><?php endif; ?><?php else: ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" /><?php endif; ?>
      <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?></span><?php endif; ?>
      <?php $this->assign('cb_has_value', true); ?>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
<?php elseif ($this->_tpl_vars['options']): ?>
  <?php if (is_array ( $this->_tpl_vars['value'] ) && count ( $this->_tpl_vars['value'] ) > 10): ?><div class="scroll_box" style="width:100%!important;"><?php endif; ?>
      <?php $_from = $this->_tpl_vars['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['cb']['iteration']++;
?>
        <?php if (is_array ( $this->_tpl_vars['value'] ) && in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] )): ?>
          <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?><span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*<?php endif; ?>
          <?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?><?php if (empty ( $this->_tpl_vars['var']['options_align'] ) && $this->_tpl_vars['var']['options_align'] == 'horizontal'): ?>&nbsp;<?php else: ?><br /><?php endif; ?><?php else: ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" /><?php endif; ?>
          <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?></span><?php endif; ?>
          <?php $this->assign('cb_has_value', true); ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
  <?php if (is_array ( $this->_tpl_vars['value'] ) && count ( $this->_tpl_vars['value'] ) > 10): ?></div><?php endif; ?>
<?php elseif ($this->_tpl_vars['var']['optgroups']): ?>
  <?php $_from = $this->_tpl_vars['var']['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['optgroup_name'] => $this->_tpl_vars['optgroup']):
?>
    <?php $_from = $this->_tpl_vars['optgroup']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
      <?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['option']['value']): ?>
        <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?><span class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
">*<?php endif; ?>
        <?php if (trim ( $this->_tpl_vars['option']['label'] )): ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?><?php if (empty ( $this->_tpl_vars['var']['options_align'] ) && $this->_tpl_vars['var']['options_align'] == 'horizontal'): ?>&nbsp;<?php else: ?><br /><?php endif; ?><?php else: ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" border="0" alt="" /><?php endif; ?>
        <?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?></span><?php endif; ?>
        <?php $this->assign('cb_has_value', true); ?>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  <?php endforeach; endif; unset($_from); ?>
<?php endif; ?>

<?php if ($this->_tpl_vars['cb_has_value']): ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('back_label' => $this->_tpl_vars['var']['back_label'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php endif; ?>
<?php if (! $this->_tpl_vars['standalone']): ?>
          </td>
        </tr>
<?php endif; ?>