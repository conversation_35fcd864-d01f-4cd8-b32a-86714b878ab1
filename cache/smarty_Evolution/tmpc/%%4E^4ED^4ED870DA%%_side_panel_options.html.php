<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:32
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_side_panel_options.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_side_panel_options.html', 4, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_side_panel_options.html', 6, false),)), $this); ?>
<?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['model_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('sidepanelKey', ob_get_contents());ob_end_clean(); ?>
<?php $this->assign('sidepanelCollapsed', $this->_tpl_vars['currentUser']->getPersonalSettings('switch',$this->_tpl_vars['sidepanelKey'],true)); ?>
<div class="nz-side-panel-tools nz-elevation--z1">
  <span id="side_panel_settings_show_div" class="nz-icon-button nz-popout-trigger" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_personal_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">handyman</span>
  <span class="nz-toggle nz-side-panel-collapse-toggle nz-toggle-autoinit<?php if (empty ( $this->_tpl_vars['sidepanelCollapsed'] )): ?> nz--active<?php endif; ?>"
        data-toggle-target="<?php if (((is_array($_tmp=@$this->_tpl_vars['dont_wrap_content'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))): ?>.nz-page-wrapper<?php else: ?>.nz-content-wrapper<?php endif; ?>"
        data-toggle-toggleClass="nz-side-panel-collapse"
        data-toggle-personalsettings-section="switch"
        data-toggle-personalsettings-name="<?php echo $this->_tpl_vars['sidepanelKey']; ?>
"
        data-toggle-personalsettings-value="1"><span
          class="nz-toggle__inactive">Скриване <span class="material-icons">keyboard_double_arrow_right</span></span
            ><span class="nz-icon-button nz-toggle__active">keyboard_double_arrow_left</span>
  </span>
</div>
   <script id="nz-side-panels-template" type="text/x-template">
  <aside id="side_panel_options" class="nz-popout-panel nz-pointer-top-right nz-modal">
    <div class="nz-popout-surface nz-surface nz-elevation--z6">
        <div class="nz-popout-title">
          <h2><?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_personal_settings'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h2>
        </div>
        <form name="side_panels" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=users&amp;users=ajax_mynzoom&amp;ajax_save=1" method="post"  class="">
          <div id="nz-side_panel_all_options" class="nz-popout-body side_panel_all_options"></div>
          <div id="side_panel_options_buttons" class="nz-popout-footer side_panel_options_buttons">
            <div id="side_panel_restore_defaults" class="">
              <input type="checkbox" id="update_all_actions" name="update_all_actions" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_update_all_actions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <label for="update_all_actions"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_update_all_actions'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
              <br />
              <input type="checkbox" id="restore_defaults" name="restore_defaults" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_restore_defaults'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /> <label for="restore_defaults"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['side_panels_restore_defaults'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
            </div>
            <button type="submit" name="saveButton1" class="nz-button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
            <button type="button" name="cancel" class="nz-button nz-popout--close"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['cancel'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button>
          </div>
        </form>
    </div>
  </aside>
</script>
                 