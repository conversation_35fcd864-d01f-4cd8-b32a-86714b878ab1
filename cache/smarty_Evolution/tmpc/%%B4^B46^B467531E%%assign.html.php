<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:22
         compiled from /var/www/Nzoom-Hella/_libs/modules/assignments/templates/assign.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/assign.html', 1, false),array('modifier', 'lower', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/assign.html', 2, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/assignments/templates/assign.html', 11, false),)), $this); ?>
<h1><?php echo ((is_array($_tmp=$this->_tpl_vars['title'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</h1>
<?php $this->assign(((is_array($_tmp=$this->_tpl_vars['model']->modelName)) ? $this->_run_mod_handler('lower', true, $_tmp) : smarty_modifier_lower($_tmp)), $this->_tpl_vars['model']); ?>
<div id="form_container">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assignments_configurator_panel.html", 'smarty_include_vars' => array('config_templates' => $this->_tpl_vars['assignments_settings']['config_templates'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<form name="assignments" id="assignments" action="<?php echo $this->_tpl_vars['submitLink']; ?>
" method="post">
<input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['model']->get('id'); ?>
" />
<input type="hidden" name="model_lang" id="model_lang" value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['model']->get('model_lang'))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lang']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lang'])); ?>
" />
<input type="hidden" name="model_name" id="model_name" value="<?php echo $this->_tpl_vars['model']->modelName; ?>
" />
<input type="hidden" name="model_type" id="model_type" value="<?php echo $this->_tpl_vars['model']->get('type'); ?>
" />

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <?php if ($this->_tpl_vars['module'] == $this->_tpl_vars['controller'] || ! $this->_tpl_vars['controller']): ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['module_templates_path'])."_info_header.html", 'smarty_include_vars' => array('templatesDir' => $this->_tpl_vars['module_templates_path'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php else: ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['module_templates_path'])."_".($this->_tpl_vars['controller'])."_info_header.html", 'smarty_include_vars' => array('templatesDir' => $this->_tpl_vars['module_templates_path'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
        <?php if (! empty ( $this->_tpl_vars['assignments_settings'] )): ?>
          <tr>
            <td colspan="3" style="padding: 0px">
              <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
                <tr>
                  <?php if (! empty ( $this->_tpl_vars['assignments_settings']['owner']['users'] )): ?>
                    <td class="t_caption3 pointer">
                      <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_assign_owner'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assignments_group.html", 'smarty_include_vars' => array('type' => 'owner','users' => $this->_tpl_vars['assignments_settings']['owner']['users'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    </td>
                  <?php endif; ?>
                  <?php if (! empty ( $this->_tpl_vars['assignments_settings']['responsible']['users'] )): ?>
                    <td class="t_caption3 pointer">
                      <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_assign_responsible'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assignments_group.html", 'smarty_include_vars' => array('type' => 'responsible','users' => $this->_tpl_vars['assignments_settings']['responsible']['users'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    </td>
                  <?php endif; ?>
                  <?php if (! empty ( $this->_tpl_vars['assignments_settings']['observer']['users'] )): ?>
                    <td class="t_caption3 pointer">
                      <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_assign_observer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assignments_group.html", 'smarty_include_vars' => array('type' => 'observer','users' => $this->_tpl_vars['assignments_settings']['observer']['users'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    </td>
                  <?php endif; ?>
                  <?php if (! empty ( $this->_tpl_vars['assignments_settings']['decision']['users'] )): ?>
                    <td class="t_caption3 pointer">
                      <div class="t_caption2_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_assign_decision'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assignments_group.html", 'smarty_include_vars' => array('type' => 'decision','users' => $this->_tpl_vars['assignments_settings']['decision']['users'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                    </td>
                  <?php endif; ?>
                </tr>
                <tr>
                <?php $_from = $this->_tpl_vars['assignments_settings']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['at'] => $this->_tpl_vars['as']):
?>
                  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assign.html", 'smarty_include_vars' => array('type' => $this->_tpl_vars['at'],'data' => $this->_tpl_vars['as'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <?php endforeach; endif; unset($_from); ?>
                </tr>
              </table>
            </td>
          </tr>
        <?php endif; ?>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <?php if (! $this->_tpl_vars['model']->get('archived_by')): ?>
          <tr>
            <td colspan="3">
              <button type="submit" name="saveButton1" class="button"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</button><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."cancel_button.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            </td>
          </tr>
        <?php endif; ?>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."after_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</form>
</div>