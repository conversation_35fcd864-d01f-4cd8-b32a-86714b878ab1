<?php /* Smarty version 2.6.33, created on 2025-05-21 13:23:50
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/actions_box.html */ ?>
<?php if ($this->_tpl_vars['available_actions']): ?>
<div class="nz-actions-wrapper<?php if (isset ( $this->_tpl_vars['onlyIcons'] )): ?> nz-actions-only-icons<?php endif; ?><?php if (isset ( $this->_tpl_vars['tabs'] )): ?> nz-actions--tabs<?php endif; ?>">
  <ul class="nz-actions-list">
    <?php $_from = $this->_tpl_vars['available_actions']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
      <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
  </ul>
  <ul class="nz-actions-list">
    <?php else: ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box_item.html", 'smarty_include_vars' => array('action' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
  </ul>
</div>
<?php endif; ?>
<?php echo '
<script>
  function prep_compatibility(actionName, element, isAjax, model=0) {
    if (null === $(`${actionName}Go`))
      if (isAjax) {
        getActionOptions(element, env.module_name, env.controller_name, actionName, model, {});
      }
  }
</script>
'; ?>


<?php if (! empty ( $this->_tpl_vars['available_actions_filter'] )): ?>
  <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_filter_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  <div class="clear"></div>
<?php endif; ?>

<?php if (! empty ( $this->_tpl_vars['available_actions_upper_right'] )): ?>
  <div class="nz-actions-wrapper action_tabs_submenu_upper_right nz-actions-only-icons">
    <ul class="nz-actions-list">
      <?php $_from = $this->_tpl_vars['available_actions_upper_right']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['available_action']):
?>
      <?php if ($this->_tpl_vars['available_action']['name'] == '|'): ?>
    </ul>
    <ul class="nz-actions-list">
      <?php else: ?>
        <?php if ($this->_tpl_vars['available_action']['action'] == 'manage_outlooks'): ?>
          <li class="nz-actions-list-item">
            <i class="nz-icon-button nz-glyph" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" onclick="showAvailableOutlookOptions('<?php echo $this->_tpl_vars['available_action']['module']; ?>
|<?php if ($this->_tpl_vars['available_action']['controller']): ?><?php echo $this->_tpl_vars['available_action']['controller']; ?>
<?php else: ?><?php echo $this->_tpl_vars['available_action']['module']; ?>
<?php endif; ?>', '<?php echo $this->_tpl_vars['custom_template_options']['name']; ?>
', '<?php echo $this->_tpl_vars['custom_template_options']['value']; ?>
')">handyman</i>
          </li>
          <?php $this->assign('show_manage_outlook_form', '1'); ?>
        <?php else: ?>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box_item.html", 'smarty_include_vars' => array('action' => $this->_tpl_vars['available_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        <?php endif; ?>
      <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    </ul>
    <?php if ($this->_tpl_vars['show_manage_outlook_form']): ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_manage_outlooks_options.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
  </div>
<?php endif; ?>