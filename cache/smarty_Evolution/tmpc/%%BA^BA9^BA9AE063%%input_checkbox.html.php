<?php /* Smarty version 2.6.33, created on 2025-05-21 13:03:02
         compiled from input_checkbox.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_checkbox.html', 58, false),array('modifier', 'escape', 'input_checkbox.html', 65, false),array('modifier', 'strip_tags', 'input_checkbox.html', 66, false),)), $this); ?>
<?php if ($this->_tpl_vars['index']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">&nbsp;</td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>

        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>

        <input
      type="checkbox"
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      onfocus="highlight(this)"
      onblur="unhighlight(this)"
      <?php if ($this->_tpl_vars['custom_class']): ?>class="<?php echo $this->_tpl_vars['custom_class']; ?>
"<?php endif; ?>
      <?php if ($this->_tpl_vars['option_value'] == $this->_tpl_vars['value']): ?> checked="checked"<?php endif; ?>
      <?php if ($this->_tpl_vars['onclick']): ?> onclick="<?php echo $this->_tpl_vars['onclick']; ?>
"<?php endif; ?>
      <?php if ($this->_tpl_vars['hidden']): ?> style="display:none"<?php endif; ?>
      <?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?> />
      <?php if ($this->_tpl_vars['label']): ?><label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"<?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?> class="inactive_option" title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?><?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 )): ?>* <?php endif; ?><?php echo $this->_tpl_vars['label']; ?>
</label><?php endif; ?>
      <?php if ($this->_tpl_vars['standalone'] && ! $this->_tpl_vars['no_br'] && ! $this->_tpl_vars['hidden']): ?><br /><?php endif; ?>

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => $this->_tpl_vars['custom_id'],'name' => $this->_tpl_vars['name'],'back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>