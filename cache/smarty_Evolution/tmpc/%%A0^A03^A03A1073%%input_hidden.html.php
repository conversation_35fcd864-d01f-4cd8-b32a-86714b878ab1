<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:46
         compiled from input_hidden.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_hidden.html', 48, false),array('modifier', 'escape', 'input_hidden.html', 51, false),)), $this); ?>
<?php if ($this->_tpl_vars['index']): ?><?php ob_start(); ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo $this->_tpl_vars['index']; ?>
<?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php else: ?><?php echo $this->_tpl_vars['index']-1; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php endif; ?>
<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>>
    <td>&nbsp;</td>

    <td>&nbsp;</td>

    <td nowrap="nowrap">
<?php endif; ?>

        <input
      type="hidden"
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      <?php if ($this->_tpl_vars['disabled']): ?>disabled="disabled"<?php endif; ?>
      <?php if ($this->_tpl_vars['custom_class']): ?>class="<?php echo $this->_tpl_vars['custom_class']; ?>
"<?php endif; ?>
      value="<?php echo ((is_array($_tmp=$this->_tpl_vars['value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />

<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>