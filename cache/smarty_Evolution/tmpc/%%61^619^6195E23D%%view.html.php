<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:25
         compiled from /var/www/Nzoom-Hella/_libs/modules/roles/templates/view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/view.html', 18, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/view.html', 21, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/view.html', 21, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/view.html', 21, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/view.html', 28, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/view.html', 28, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<div id="form_container">

<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."translate_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_submenu_actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<input type="hidden" name="id" id="id" value="<?php echo $this->_tpl_vars['role']->get('id'); ?>
" />

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td valign="top" colspan="3" class="nopadding">
            <table cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td class="labelbox"><?php echo smarty_function_help(array('label' => 'name'), $this);?>
</td>
                <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
                <td nowrap="nowrap">
                  <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['role']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

                </td>
              </tr>
              <tr>
                <td class="labelbox"><?php echo smarty_function_help(array('label' => 'description'), $this);?>
</td>
                <td>&nbsp;</td>
                <td nowrap="nowrap">
                  <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['role']->get('description'))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp) : smarty_modifier_mb_wordwrap($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)); ?>

                </td>
              </tr>
            </table>
          </td>
        </tr>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_section_tabs_and_permissions.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </table>
    </td>
  </tr>
</table>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."help_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."system_settings_box.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['role'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
</div>