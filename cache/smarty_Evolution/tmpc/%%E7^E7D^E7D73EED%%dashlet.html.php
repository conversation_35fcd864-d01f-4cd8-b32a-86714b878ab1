<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:47
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 3, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 8, false),array('modifier', 'numerate', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 22, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 23, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 25, false),array('modifier', 'count', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 57, false),array('modifier', 'replace', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 131, false),array('modifier', 'nl2br', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 166, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 166, false),array('modifier', 'regex_replace', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 168, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 16, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 55, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 81, false),array('function', 'array', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 175, false),array('function', 'math', '/var/www/Nzoom-Hella/_libs/modules/documents/templates/dashlet.html', 244, false),)), $this); ?>
<table cellpadding="0" border="0" cellspacing="0" width="100%" class="t_table t_list">
  <tr>
    <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <?php $_from = $this->_tpl_vars['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['column']):
?>
    <td class="t_caption t_border <?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['class']; ?>
" nowrap="nowrap">
      <div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['link']; ?>
">
        <?php ob_start(); ?>documents_<?php echo $this->_tpl_vars['column']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('column_name', ob_get_contents());ob_end_clean(); ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['vars_labels'][$this->_tpl_vars['column']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars'][$this->_tpl_vars['column_name']]) : smarty_modifier_default($_tmp, @$this->_config[0]['vars'][$this->_tpl_vars['column_name']])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </div>
    </td>
    <?php endforeach; endif; unset($_from); ?>
    <td class="t_caption" nowrap="nowrap">
      &nbsp;
    </td>
  </tr>
  <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

  <?php $_from = $this->_tpl_vars['documents']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['document']):
        $this->_foreach['i']['iteration']++;
?>
  <?php $this->assign('assoc_vars', $this->_tpl_vars['document']->get('assoc_vars')); ?>
  <?php $this->assign('layouts_view', $this->_tpl_vars['document']->getPermittedLayouts('view')); ?>
  <?php echo ''; ?><?php ob_start(); ?><?php echo '<strong><u>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['vars_labels']['full_num'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_full_num']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_full_num'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</u></strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['document']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['document']->get('direction'))); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['vars_labels']['name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_name']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_name'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, @PH_MAX_TRUNCATE_ABOUT) : smarty_modifier_mb_truncate($_tmp, @PH_MAX_TRUNCATE_ABOUT)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=@$this->_tpl_vars['vars_labels']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['documents_type']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['documents_type'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['status_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('status_modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('status_modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['document']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('archived_by')): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['archived'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('archived'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['document']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['document']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span><br />'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('status') == 'opened'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_opened']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['document']->get('status') == 'locked'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_locked']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['document']->get('status') == 'closed'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_documents_status_closed']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['document']->get('substatus_name')): ?><?php echo '<br />'; ?><?php echo $this->_config[0]['vars']['help_documents_substatus']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['document']->get('substatus_name'); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_status', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

  <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."row_link_action.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('row_link', ob_get_contents()); ob_end_clean();
 ?>
  <?php ob_start(); ?><?php if ($this->_tpl_vars['row_link']): ?>pointer<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('row_link_class', ob_get_contents());ob_end_clean(); ?>
  <?php if (! $this->_tpl_vars['document']->checkPermissions('list')): ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
      <td class="t_border hright dimmed" nowrap="nowrap"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
      <td colspan="<?php echo count($this->_tpl_vars['columns']); ?>
" class="t_border dimmed"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_right_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
      <td>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'],'disabled' => 'edit,delete,view')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
  <?php else: ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (! $this->_tpl_vars['document']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['document']->get('archived_by')): ?> attention<?php endif; ?><?php if ($this->_tpl_vars['document']->get('deleted_by')): ?> t_deleted<?php endif; ?>">
      <td class="t_border hright" nowrap="nowrap">
        <?php if ($this->_tpl_vars['document']->get('files_count')): ?>
          <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=attachments&amp;attachments=<?php echo $this->_tpl_vars['document']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>">
            <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                     onmouseover="showFiles(this, 'documents', 'documents', <?php echo $this->_tpl_vars['document']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>, '', 1<?php endif; ?>)"
                     onmouseout="mclosetime()" />
          </a>
        <?php endif; ?>
        <?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>

      </td>
      <?php $_from = $this->_tpl_vars['columns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['column']):
?>
      <?php if (in_array ( $this->_tpl_vars['column'] , array ( 'comments' , 'emails' , 'history_activity' , 'timesheet_time' ) )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/outlooks/templates/td/default_".($this->_tpl_vars['column']).".html", 'smarty_include_vars' => array('single' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php else: ?>
      <td class="t_border <?php echo $this->_tpl_vars['sort'][$this->_tpl_vars['column']]['isSorted']; ?>
<?php if (in_array ( $this->_tpl_vars['column'] , array ( 'relatives_children' , 'relatives_parent' ) )): ?> nopadding<?php endif; ?><?php if (! in_array ( $this->_tpl_vars['column'] , array ( 'status' , 'full_num' , 'tags' , 'owner' , 'responsible' , 'observer' , 'decision' , 'relatives_children' , 'relatives_parent' ) )): ?> <?php echo $this->_tpl_vars['row_link_class']; ?>
<?php endif; ?>"<?php if (! in_array ( $this->_tpl_vars['column'] , array ( 'status' , 'full_num' , 'tags' , 'owner' , 'responsible' , 'observer' , 'decision' , 'relatives_children' , 'relatives_parent' ) )): ?><?php echo $this->_tpl_vars['row_link']; ?>
<?php endif; ?>>
      <?php if ($this->_tpl_vars['column'] == 'status'): ?>
        <?php ob_start(); ?>
          <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['help_documents_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
<?php if ($this->_tpl_vars['document']->checkPermissions('setstatus')): ?> onclick="changeStatus(<?php echo $this->_tpl_vars['document']->get('id'); ?>
, 'documents')" style="cursor:pointer;"<?php endif; ?>
        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('popup_and_onclick', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>
          <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
            <?php echo $this->_config[0]['vars']['documents_expired_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>!
          <?php endif; ?>
          <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
            <?php echo $this->_tpl_vars['documents_expired']; ?>
 <?php echo $this->_config[0]['vars']['documents_expired_validity_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>!
          <?php endif; ?>
        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired', ob_get_contents());ob_end_clean(); ?>
        <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && ( ( $this->_tpl_vars['document']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) ) || ( $this->_tpl_vars['document']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) ) )): ?>
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" alt="" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
 />
        <?php endif; ?>
        <?php if ($this->_tpl_vars['document']->get('substatus_name')): ?>
          <?php if ($this->_tpl_vars['document']->get('icon_name')): ?>
            <img src="<?php echo @PH_DOCUMENTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['document']->get('icon_name'); ?>
" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
          <?php else: ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_<?php echo $this->_tpl_vars['document']->get('status'); ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
          <?php endif; ?>
          <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo $this->_tpl_vars['document']->get('substatus_name'); ?>
</span>
        <?php else: ?>
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
documents_<?php echo $this->_tpl_vars['document']->get('status'); ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
          <?php ob_start(); ?>documents_status_<?php echo $this->_tpl_vars['document']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('status_param', ob_get_contents());ob_end_clean(); ?>
          <span <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
><?php echo $this->_config[0]['vars'][$this->_tpl_vars['status_param']]; ?>
</span>
        <?php endif; ?>
      <?php elseif (in_array ( $this->_tpl_vars['column'] , array ( 'owner' , 'responsible' , 'observer' , 'decision' ) )): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_assignments_dashlet.html", 'smarty_include_vars' => array('a_type' => "assignments_".($this->_tpl_vars['column']))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php elseif ($this->_tpl_vars['column'] == 'full_num'): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['document']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>"><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['document']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['document']->get('direction'))); ?>
</a>
      <?php elseif ($this->_tpl_vars['column'] == 'added'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'modified'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'deadline'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'validity_term'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'date'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'tags'): ?>
        <div<?php if ($this->_tpl_vars['document']->getModelTags() && $this->_tpl_vars['document']->get('available_tags_count') > 0 && $this->_tpl_vars['document']->checkPermissions('tags_view') && $this->_tpl_vars['document']->checkPermissions('tags_edit')): ?> style="padding: 3px 0 3px 0; cursor: pointer;" onclick="changeTags(<?php echo $this->_tpl_vars['document']->get('id'); ?>
, 'documents')" title="<?php echo $this->_config[0]['vars']['tags_change']; ?>
"<?php endif; ?>>
          <?php if (count($this->_tpl_vars['document']->get('model_tags')) > 0 && $this->_tpl_vars['document']->checkPermissions('tags_view')): ?>
            <?php $_from = $this->_tpl_vars['document']->get('model_tags'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ti'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ti']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['tag']):
        $this->_foreach['ti']['iteration']++;
?>
              <span class="<?php echo $this->_tpl_vars['tag']->get('color'); ?>
_pushpin" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['tag']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span><?php if (! ($this->_foreach['ti']['iteration'] == $this->_foreach['ti']['total'])): ?><br /><?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php else: ?>
            &nbsp;
          <?php endif; ?>
        </div>
      <?php elseif ($this->_tpl_vars['column'] == 'relatives_children' || $this->_tpl_vars['column'] == 'relatives_parent'): ?>
        <?php $this->assign('relative', ((is_array($_tmp=$this->_tpl_vars['column'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'relatives_', "") : smarty_modifier_replace($_tmp, 'relatives_', ""))); ?>
        <table border="0" cellspacing="0" cellpadding="0" width="100%">
          <tr>
            <td style="border: none!important;">
              <div class="collapsed" id="<?php echo $this->_tpl_vars['relative']; ?>
_<?php echo $this->_tpl_vars['document']->get('id'); ?>
" style="max-height: 36px; overflow: hidden;">
                <?php if ($this->_tpl_vars['column'] == 'relatives_children'): ?>
                  <?php $this->assign('relatives', $this->_tpl_vars['document']->getFirstLevelRelatedDocuments('child')); ?>
                <?php else: ?>
                  <?php $this->assign('relatives', $this->_tpl_vars['document']->getFirstLevelRelatedDocuments('parent')); ?>
                <?php endif; ?>
                <?php $_from = $this->_tpl_vars['relatives']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cd'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cd']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['rel']):
        $this->_foreach['cd']['iteration']++;
?>
                  <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=view&amp;view=<?php echo $this->_tpl_vars['rel']['id']; ?>
<?php if ($this->_tpl_vars['rel']['archived_by']): ?>&amp;archive=1<?php endif; ?>" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['rel']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_relative_document_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo ((is_array($_tmp=$this->_tpl_vars['rel']['full_num'])) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['rel']['direction']) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['rel']['direction'])); ?>
</a>
                  <?php if (! ($this->_foreach['cd']['iteration'] == $this->_foreach['cd']['total'])): ?>
                    <br />
                  <?php endif; ?>
                <?php endforeach; else: ?>
                  &nbsp;
                <?php endif; unset($_from); ?>
              </div>
            </td>
            <td class="hright" style="border: none!important;"><?php if (count($this->_tpl_vars['relatives']) > 3): ?><img alt="" class="pointer" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expand'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
expand1.png" onclick="toggleDocumentsListRelatives(this, '<?php echo $this->_tpl_vars['relative']; ?>
_<?php echo $this->_tpl_vars['document']->get('id'); ?>
');" /><?php else: ?>&nbsp;<?php endif; ?>
            </td>
          </tr>
        </table>
      <?php elseif ($this->_tpl_vars['column'] == 'name_full_num'): ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['module']; ?>
&amp;<?php echo $this->_tpl_vars['action_param']; ?>
=view&amp;view=<?php echo $this->_tpl_vars['document']->get('id'); ?>
<?php if ($this->_tpl_vars['document']->get('archived_by')): ?>&amp;archive=1<?php endif; ?>">&#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
      <?php elseif ($this->_tpl_vars['column'] == 'customer_name_code'): ?>
        &#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('customer_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php elseif ($this->_tpl_vars['column'] == 'project_name_code'): ?>
        <?php if ($this->_tpl_vars['document']->get('project')): ?>&#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('project_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php else: ?>&nbsp;<?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'trademark_name_code'): ?>
        <?php if ($this->_tpl_vars['document']->get('trademark')): ?>&#91;<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('trademark_code'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
&#93; <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
<?php else: ?>&nbsp;<?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'contract'): ?>
        <?php if ($this->_tpl_vars['document']->get('contract')): ?><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['document']->get('contract'); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('contract_custom_label'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a><?php else: ?>&nbsp;<?php endif; ?>
      <?php elseif ($this->_tpl_vars['column'] == 'description' || $this->_tpl_vars['column'] == 'notes'): ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get($this->_tpl_vars['column']))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('nl2br', true, $_tmp) : smarty_modifier_nl2br($_tmp)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php elseif (preg_match ( '/^a__/' , $this->_tpl_vars['column'] )): ?>
        <?php $this->assign('additional_col_name', ((is_array($_tmp=$this->_tpl_vars['column'])) ? $this->_run_mod_handler('regex_replace', true, $_tmp, '#^a__(.*)$#i', '$1') : smarty_modifier_regex_replace($_tmp, '#^a__(.*)$#i', '$1'))); ?>
        <?php if (! empty ( $this->_tpl_vars['layouts_view'] ) && is_array ( $this->_tpl_vars['layouts_view'] ) && in_array ( $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['layout_id'] , $this->_tpl_vars['layouts_view'] )): ?>
          <?php $this->assign('additional_col_type', $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['type']); ?>
          <?php $this->assign('additional_col_value', ''); ?>
          <?php if (is_array ( $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['value'] )): ?>
            <?php $this->assign('additional_col_value', $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['value']); ?>
          <?php else: ?>
            <?php echo smarty_function_array(array('assign' => 'additional_col_value','value' => $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['value']), $this);?>

          <?php endif; ?>
          <?php $this->assign('has_value', false); ?>
          <?php echo ''; ?><?php $_from = $this->_tpl_vars['additional_col_value']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['var_values'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['var_values']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['var_value']):
        $this->_foreach['var_values']['iteration']++;
?><?php echo ''; ?><?php if ($this->_tpl_vars['var_value'] || $this->_tpl_vars['var_value'] === '0'): ?><?php echo ''; ?><?php $this->assign('has_value', true); ?><?php echo ''; ?><?php if ($this->_tpl_vars['additional_col_type'] == 'date'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['additional_col_type'] == 'datetime'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?><?php echo ''; ?><?php elseif (in_array ( $this->_tpl_vars['additional_col_type'] , array ( 'checkbox_group' , 'dropdown' , 'radio' ) )): ?><?php echo ''; ?><?php $_from = $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['var_option']):
?><?php echo ''; ?><?php if ($this->_tpl_vars['var_option']['option_value'] == $this->_tpl_vars['var_value']): ?><?php echo ''; ?><?php if ($this->_tpl_vars['additional_col_type'] == 'checkbox_group' && trim ( $this->_tpl_vars['var_option']['label'] ) === ''): ?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/check_yes.png" border="0" alt="" style="margin-left: 5px;" />'; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['var_option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['var_value']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['var_value'])); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endforeach; else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; unset($_from); ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['additional_col_type'] == 'file_upload'): ?><?php echo ''; ?><?php if (! empty ( $this->_tpl_vars['var_value'] ) && is_object ( $this->_tpl_vars['var_value'] ) && ! $this->_tpl_vars['var_value']->get('deleted_by')): ?><?php echo ''; ?><?php if (! $this->_tpl_vars['var_value']->get('not_exist')): ?><?php echo '<a href="'; ?><?php echo $_SERVER['SCRIPT_NAME']; ?><?php echo '?'; ?><?php echo $this->_tpl_vars['module_param']; ?><?php echo '='; ?><?php echo $this->_tpl_vars['module']; ?><?php echo '&amp;'; ?><?php echo $this->_tpl_vars['action_param']; ?><?php echo '=viewfile&amp;viewfile='; ?><?php echo $this->_tpl_vars['var_value']->get('model_id'); ?><?php echo '&amp;file='; ?><?php echo $this->_tpl_vars['var_value']->get('id'); ?><?php echo ''; ?><?php if ($this->_tpl_vars['var_value']->get('archived_by')): ?><?php echo '&amp;archive=1'; ?><?php endif; ?><?php echo '" target="_blank">'; ?><?php endif; ?><?php echo '<img border="0" width="16" height="16" src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo ''; ?><?php echo $this->_tpl_vars['var_value']->getIconName(); ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['var_value']->get('filename'); ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['var_value']->get('filename'); ?><?php echo '" class="'; ?><?php if ($this->_tpl_vars['var_value']->get('not_exist')): ?><?php echo 'dimmed'; ?><?php else: ?><?php echo 'pointer'; ?><?php endif; ?><?php echo '" />'; ?><?php if (! $this->_tpl_vars['var_value']->get('not_exist')): ?><?php echo '</a>'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '&nbsp;'; ?><?php if (($this->_foreach['var_values']['iteration'] <= 1)): ?><?php echo ''; ?><?php $this->assign('has_value', false); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['additional_col_type'] == 'autocompleter'): ?><?php echo ''; ?><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => $this->_tpl_vars['var_value'],'value_id' => $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['value_id'],'view_mode_url' => $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['autocomplete']['view_mode_url'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['var_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php elseif (count ( $this->_tpl_vars['additional_col_value'] ) > 1 && $this->_tpl_vars['additional_col_type'] != 'file_upload'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['no_data']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if (! ($this->_foreach['var_values']['iteration'] == $this->_foreach['var_values']['total']) && $this->_tpl_vars['additional_col_type'] != 'file_upload'): ?><?php echo ',<br />'; ?><?php endif; ?><?php echo ''; ?><?php endforeach; endif; unset($_from); ?><?php echo ''; ?>

          <?php if ($this->_tpl_vars['has_value'] && $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['back_label']): ?><?php echo $this->_tpl_vars['assoc_vars'][$this->_tpl_vars['additional_col_name']]['back_label']; ?>
<?php endif; ?>
        <?php else: ?>
          &nbsp;
        <?php endif; ?>
      <?php else: ?>
        <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get($this->_tpl_vars['all_columns'][$this->_tpl_vars['column']]))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

      <?php endif; ?>
      </td>
      <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
      <td class="hcenter" nowrap="nowrap">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      </td>
    </tr>
    <?php endif; ?>
  <?php endforeach; else: ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
      <td class="error" colspan="<?php echo smarty_function_math(array('equation' => 'count+2','count' => count($this->_tpl_vars['columns'])), $this);?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
  <?php endif; unset($_from); ?>
  <tr>
    <td colspan="<?php echo smarty_function_math(array('equation' => 'count+2','count' => count($this->_tpl_vars['columns'])), $this);?>
" class="t_footer"></td>
  </tr>
</table>
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=documents&amp;documents=dashlet&amp;dashlet=<?php echo $this->_tpl_vars['dashlet_id']; ?>
&amp;page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<?php ob_start(); ?>content_dashlet_<?php echo $this->_tpl_vars['dashlet_id']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('container', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'target' => $this->_tpl_vars['container'],'link' => $this->_tpl_vars['link'],'use_ajax' => 1,'hide_rpp' => 1,'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>