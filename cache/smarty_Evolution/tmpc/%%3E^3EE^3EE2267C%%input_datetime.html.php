<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from input_datetime.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_datetime.html', 35, false),array('modifier', 'date_format', 'input_datetime.html', 79, false),array('modifier', 'strip_tags', 'input_datetime.html', 83, false),array('modifier', 'escape', 'input_datetime.html', 83, false),array('function', 'help', 'input_datetime.html', 57, false),)), $this); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php ob_start(); ?><?php if (((is_array($_tmp=@$this->_tpl_vars['eq_indexes'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['index']; ?>
<?php elseif (((is_array($_tmp=@$this->_tpl_vars['empty_indexes'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo $this->_tpl_vars['name_index']; ?>
<?php else: ?><?php echo $this->_tpl_vars['index']-1; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php endif; ?>
<?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['standalone']): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] )): ?><?php echo '100%'; ?><?php elseif (is_numeric ( $this->_tpl_vars['width'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['width']; ?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>
<?php ob_start(); ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo $this->_tpl_vars['height']; ?>
px<?php elseif ($this->_tpl_vars['height']): ?><?php echo $this->_tpl_vars['height']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>

<?php if (! ((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
<tr<?php if (((is_array($_tmp=@$this->_tpl_vars['hidden'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> style="display: none"<?php endif; ?> class="nz-form-input">
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
    <?php if ($this->_tpl_vars['label']): ?>
            <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
    <?php else: ?>
      &nbsp;
    <?php endif; ?>
  </td>

    <td<?php if (((is_array($_tmp=@$this->_tpl_vars['required'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>
    <span class="nz-form-input-wrapper">
        <input
      type="text" maxlength="17"
      name="<?php echo $this->_tpl_vars['name']; ?>
_formatted<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_formatted<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="txtbox datetimebox<?php if (((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> readonly<?php endif; ?><?php if ($this->_tpl_vars['custom_class']): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
      style="<?php if ($this->_tpl_vars['hidden']): ?>display: none;<?php elseif ($this->_tpl_vars['width']): ?>width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
      autocomplete="off"
      <?php if ($this->_tpl_vars['value'] && ! preg_match ( '/0000-00-00/' , $this->_tpl_vars['value'] )): ?>
        value="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['value'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('default', true, $_tmp, '  .  .    ,   :  ') : smarty_modifier_default($_tmp, '  .  .    ,   :  ')); ?>
"
      <?php else: ?>
        value="  .  .    ,   :  "
      <?php endif; ?>
      title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      onfocus="
               datetimePositionMouseCursor(this);
               <?php if (! ((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
               calendarInit(this.id,
                            this.id<?php if (! ((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> + '_trigger'<?php endif; ?>,
                            true,
                            '<?php echo ((is_array($_tmp=@$this->_tpl_vars['format'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['date_mid']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['date_mid'])); ?>
',
                            '%Y-%m-%d %H:%M:00',
                            <?php if (! empty ( $this->_tpl_vars['disallow_date_before'] ) || ! empty ( $this->_tpl_vars['js_methods']['disallow_date_before'] ) || ! empty ( $this->_tpl_vars['disallow_date_after'] ) || ! empty ( $this->_tpl_vars['js_methods']['disallow_date_after'] )): ?>disallowDates<?php else: ?>false<?php endif; ?>);
               <?php endif; ?>"
      onblur="validateDate(this, -1); validateTime(this, -1); formatDate(this);<?php if (! empty ( $this->_tpl_vars['js_methods']['onblur'] )): ?><?php echo $this->_tpl_vars['js_methods']['onblur']; ?>
<?php endif; ?>"
      <?php if (((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
        readonly="readonly"
      <?php else: ?>
        onkeydown="return isAllowedDateKey(event);<?php if (! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeydown']; ?>
<?php endif; ?>"
        onkeyup="return changeKey(this, event, filterDate);<?php if (! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeyup']; ?>
<?php endif; ?>"
      <?php endif; ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['disabled'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> disabled="disabled"<?php endif; ?> />

    <input
      type="hidden"
      name="<?php echo $this->_tpl_vars['name']; ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
      id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
      class="datetimebox txtbox"
      <?php if (! empty ( $this->_tpl_vars['js_methods']['onchange'] )): ?>onchange="<?php echo $this->_tpl_vars['js_methods']['onchange']; ?>
"<?php endif; ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['disabled'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> disabled="disabled"<?php endif; ?>
      <?php if ($this->_tpl_vars['value'] && ! preg_match ( '#0000-00-00#' , $this->_tpl_vars['value'] )): ?>
        value="<?php echo ((is_array($_tmp=$this->_tpl_vars['value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
      <?php else: ?>
        value=""
      <?php endif; ?>
      <?php if (! empty ( $this->_tpl_vars['disallow_date_before'] )): ?>
        disallow_before="<?php echo $this->_tpl_vars['disallow_date_before']; ?>
"
      <?php elseif (! empty ( $this->_tpl_vars['js_methods']['disallow_date_before'] )): ?>
        disallow_before="<?php echo $this->_tpl_vars['js_methods']['disallow_date_before']; ?>
"
      <?php endif; ?>
      <?php if (! empty ( $this->_tpl_vars['disallow_date_after'] )): ?>
        disallow_after="<?php echo $this->_tpl_vars['disallow_date_after']; ?>
"
      <?php elseif (! empty ( $this->_tpl_vars['js_methods']['disallow_date_after'] )): ?>
        disallow_after="<?php echo $this->_tpl_vars['js_methods']['disallow_date_after']; ?>
"
      <?php endif; ?>
    />&nbsp;

        <?php if (( ( ! ((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ! ((is_array($_tmp=@$this->_tpl_vars['hide_calendar_icon'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) || ((is_array($_tmp=@$this->_tpl_vars['show_calendar_icon'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) && ! ((is_array($_tmp=@$this->_tpl_vars['disabled'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ! ((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
      <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_formatted_trigger<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>_tooltop"<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('calendar_tooltip_id', ob_get_contents());ob_end_clean(); ?>
      <a href="javascript:void(0);"
                id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_formatted_trigger<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
                class="nz-input-controls calendar_trigger nz-tooltip-trigger nz-tooltip-autoinit"
                data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                data-tooltip-position="panel: bottom center at: top center"
                data-tooltip-element="#<?php echo $this->_tpl_vars['calendar_tooltip_id']; ?>
"
                title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['calendars'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                onmousedown="calendarInit(this.id.replace('_trigger', ''),
                        this.id,
                        true,
                        '<?php echo ((is_array($_tmp=@$this->_tpl_vars['format'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_config[0]['vars']['date_mid']) : smarty_modifier_default($_tmp, @$this->_config[0]['vars']['date_mid'])); ?>
',
                        '%Y-%m-%d %H:%M:00',
                        <?php if (! empty ( $this->_tpl_vars['disallow_date_before'] ) || ! empty ( $this->_tpl_vars['js_methods']['disallow_date_before'] ) || ! empty ( $this->_tpl_vars['disallow_date_after'] ) || ! empty ( $this->_tpl_vars['js_methods']['disallow_date_after'] )): ?>disallowDates<?php else: ?>false<?php endif; ?>);"
        ><i class="material-icons nz-input-icon">calendar_month</i></a>
      <div id="<?php echo $this->_tpl_vars['calendar_tooltip_id']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-center"><?php echo $this->_config[0]['vars']['calendars']; ?>
</div>
    <?php endif; ?>
        <?php if (! ((is_array($_tmp=@$this->_tpl_vars['back_label'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ((is_array($_tmp=@$this->_tpl_vars['var']['back_label'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! ((is_array($_tmp=@$this->_tpl_vars['back_label_style'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ((is_array($_tmp=@$this->_tpl_vars['var']['back_label_style'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])),'name' => $this->_tpl_vars['name'],'back_label' => ((is_array($_tmp=@$this->_tpl_vars['back_label'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'back_label_style' => ((is_array($_tmp=@$this->_tpl_vars['back_label_style'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </span>

<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>