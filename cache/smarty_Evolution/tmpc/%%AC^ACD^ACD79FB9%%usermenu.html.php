<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from layout/usermenu.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'mb_substr', 'layout/usermenu.html', 4, false),array('modifier', 'escape', 'layout/usermenu.html', 5, false),array('modifier', 'default', 'layout/usermenu.html', 24, false),array('modifier', 'regex_replace', 'layout/usermenu.html', 35, false),array('modifier', 'replace', 'layout/usermenu.html', 35, false),)), $this); ?>
<?php ob_start(); ?>/&(?![a-zA-Z]{2,5};)/<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('amp_regex', ob_get_contents());ob_end_clean(); ?>
<div class="nz-user-account">
    <div class="nz-user-account--button">
        <span class="nz-user-account--avatar nz-avatar"><?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('firstname'))) ? $this->_run_mod_handler('mb_substr', true, $_tmp, 0, 1) : mb_substr($_tmp, 0, 1)); ?>
<?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('lastname'))) ? $this->_run_mod_handler('mb_substr', true, $_tmp, 0, 1) : mb_substr($_tmp, 0, 1)); ?>
</span>
        <span class="nz-user-account--label"><?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('firstname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
    </div>
    <div class="nz-user-account--menu">
        <div class="nz-user-account--menu_surface nz-surface nz-elevation--z6">
            <div  class="nz-user-menu-section">
                <div class="nz-user-account--name"><?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('firstname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                <div class="nz-user-account--username">@<?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('username'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
                <div class="nz-user-account--role"><?php echo ((is_array($_tmp=$this->_tpl_vars['currentUser']->get('role_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
            </div>
            <?php if ($this->_tpl_vars['emails_off'] || ( is_array ( $this->_tpl_vars['lang_menu'] ) && count ( $this->_tpl_vars['lang_menu'] ) > 1 )): ?>
            <div class="nz-user-menu-section nz-user-menu-section--langs">
                <!-- Icon to show if e-mails are turned off -->
                <?php if ($this->_tpl_vars['emails_off']): ?>
                <div class="nz-emailing"><i class="material-icons nz-tooltip nz-tooltip-dir-top"
                                            data-tooltip-content="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['emailing_is_off'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">unsubscribe</i></div>
                <?php endif; ?>
                <?php if (is_array ( $this->_tpl_vars['lang_menu'] ) && count ( $this->_tpl_vars['lang_menu'] ) > 1): ?>
                <div class="nz-lang-menu<?php if ($this->_tpl_vars['include_keyboard_inputs_toggler']): ?><?php endif; ?>">
                    <?php $_from = $this->_tpl_vars['lang_menu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['lang']):
        $this->_foreach['i']['iteration']++;
?>
                    <a href="<?php echo ((is_array($_tmp=@$this->_tpl_vars['lang']['url'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')); ?>
"
                       class="nz-lang-menu--item <?php if ($this->_tpl_vars['lang']['selected']): ?>nz--active<?php endif; ?>"><?php echo $this->_tpl_vars['lang']['lang']; ?>
</a>
                    <?php endforeach; endif; unset($_from); ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <div class="nz-user-menu-section">
                <nav>
                    <?php $_from = $this->_tpl_vars['menu']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['item']):
        $this->_foreach['i']['iteration']++;
?>
                    <a href="<?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['item']->getUrl())) ? $this->_run_mod_handler('regex_replace', true, $_tmp, $this->_tpl_vars['amp_regex'], '&amp;') : smarty_modifier_regex_replace($_tmp, $this->_tpl_vars['amp_regex'], '&amp;')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('replace', true, $_tmp, '&amp;', '&') : smarty_modifier_replace($_tmp, '&amp;', '&')); ?>
"
                       class="nz-user-menu--action"
                       title="<?php echo $this->_tpl_vars['item']->getLegend(); ?>
"
                       target="<?php echo $this->_tpl_vars['item']->getTarget(); ?>
"
                    ><span class="nz-user-menu-item--graphic"><?php echo $this->_tpl_vars['item']->renderIcon(); ?>
</span>
                        <span class="nz-user-menu-item--label"><?php echo $this->_tpl_vars['item']->getI18n(); ?>
</span></a>
                    <?php endforeach; endif; unset($_from); ?>
                                    </nav>
            </div>
        </div>
    </div>
</div>