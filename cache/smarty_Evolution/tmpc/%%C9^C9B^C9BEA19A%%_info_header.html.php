<?php /* Smarty version 2.6.33, created on 2025-05-27 12:01:49
         compiled from /var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 5, false),array('function', 'mb_truncate_overlib', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 56, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 157, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 8, false),array('modifier', 'numerate', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 16, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 24, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 33, false),array('modifier', 'mb_wordwrap', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 188, false),array('modifier', 'url2href', '/var/www/Nzoom-Hella/_libs/modules/documents/view/templates/_info_header.html', 188, false),)), $this); ?>
  <?php $_from = $this->_tpl_vars['document']->getLayoutsDetails(); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['lkey'] => $this->_tpl_vars['layout']):
?>
    <?php if ($this->_tpl_vars['layout']['info_header_visibility'] && $this->_tpl_vars['layout']['view']): ?>
      <?php if ($this->_tpl_vars['lkey'] == 'type'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'full_num'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('full_num'))) ? $this->_run_mod_handler('numerate', true, $_tmp, $this->_tpl_vars['document']->get('direction')) : smarty_modifier_numerate($_tmp, $this->_tpl_vars['document']->get('direction'))); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'custom_num'): ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
            <td class="unrequired">&nbsp;</td>
            <td>
              <?php echo ((is_array($_tmp=@$this->_tpl_vars['document']->get('custom_num'))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            </td>
          </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'date'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('date')): ?>
              <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'name'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <div class="nz-record-status">
              <span class="nz-graphic nz-tooltip-trigger nz-tooltip-autoinit"
                    data-tooltip-position="panel: bottom left at: top left"
                    data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                    data-tooltip-element="#tooltip_status">
                <?php if ($this->_tpl_vars['document']->get('icon_name')): ?>
                  <img src="<?php echo @PH_DOCUMENTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['document']->get('icon_name'); ?>
" alt="<?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('status_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
                <?php else: ?>
                  <?php ob_start(); ?>documents_statuses_<?php echo $this->_tpl_vars['document']->get('status'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('icon', ob_get_contents());ob_end_clean(); ?>
                  <i class="material-icons"><?php echo $this->_tpl_vars['theme']->getIconForRecord($this->_tpl_vars['icon']); ?>
</i>
                <?php endif; ?>
              </span>
              <?php echo smarty_function_mb_truncate_overlib(array('text' => ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;"))), $this);?>

              <div id="tooltip_status" class="nz-tooltip-content nz-tooltip-notch__bottom-left" style="width:500px"><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_info.html", 'smarty_include_vars' => array('document' => $this->_tpl_vars['document'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?></div>
            </div>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'customer'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=view&amp;view=<?php echo $this->_tpl_vars['document']->get('customer'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php if ($this->_tpl_vars['document']->get('branch')): ?>
              <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('documents_branch'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('branch_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
            <?php if ($this->_tpl_vars['document']->get('contact_person')): ?>
              <span class="labelbox"><?php echo smarty_function_help(array('label_content' => ((is_array($_tmp=$this->_tpl_vars['document']->getBranchLabels('documents_contact_person'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
</span> <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('contact_person_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'trademark'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('trademark')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures&amp;nomenclatures=view&amp;view=<?php echo $this->_tpl_vars['document']->get('trademark'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('trademark_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'contract'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('contract')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['document']->get('contract'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('contract_custom_label'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('contract_custom_label'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'project'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('project')): ?>
              <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=projects&amp;projects=view&amp;view=<?php echo $this->_tpl_vars['document']->get('project'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a>
            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'office'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('office')): ?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('office_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'employee'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('employee')): ?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('employee_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'media'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('media')): ?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('media_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'deadline'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('deadline')): ?>
              <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('deadline') && ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
                <?php ob_start(); ?>
                  <?php echo $this->_config[0]['vars']['documents_expired_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>
                <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired', ob_get_contents());ob_end_clean(); ?>
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" class="t_info_image" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
              <?php endif; ?>
              <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('deadline'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

            <?php else: ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_no_deadline'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'validity_term'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('validity_term')): ?>
              <?php if ($this->_tpl_vars['document']->get('status') != 'closed' && $this->_tpl_vars['document']->get('validity_term') && ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso'])) < ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_iso']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_iso']))): ?>
                <?php ob_start(); ?>
                  <?php echo $this->_config[0]['vars']['documents_expired_validity_legend']; ?>
: <strong><?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>
</strong>
                <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('document_expired_validity_term', ob_get_contents());ob_end_clean(); ?>
                <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
warning.png" width="16" height="16" border="0" class="t_info_image" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['document_expired_validity_term'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['documents_validity_term_expired'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))), $this);?>
 />
              <?php endif; ?>
              <?php echo ((is_array($_tmp=$this->_tpl_vars['document']->get('validity_term'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

            <?php else: ?>
              <?php echo ((is_array($_tmp=$this->_config[0]['vars']['documents_no_validity_term'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            <?php endif; ?>
          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'description'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('description'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'notes'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('notes'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('mb_wordwrap', true, $_tmp, 70) : smarty_modifier_mb_wordwrap($_tmp, 70)))) ? $this->_run_mod_handler('url2href', true, $_tmp) : smarty_modifier_url2href($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

          </td>
        </tr>
      <?php elseif ($this->_tpl_vars['lkey'] == 'department'): ?>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['layout']['name']), $this);?>
</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <?php if ($this->_tpl_vars['document']->get('department')): ?>
              <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['document']->get('department_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

            <?php else: ?>
              &nbsp;
            <?php endif; ?>
          </td>
        </tr>
      <?php endif; ?>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>