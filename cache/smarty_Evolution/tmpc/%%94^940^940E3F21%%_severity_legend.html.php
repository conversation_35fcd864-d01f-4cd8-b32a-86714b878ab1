<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:45
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_severity_legend.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_severity_legend.html', 4, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_severity_legend.html', 10, false),array('function', 'array', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_severity_legend.html', 6, false),)), $this); ?>
      <table border="0" cellspacing="2" cellpadding="5" style="margin-left: -2px;">
        <?php ob_start(); ?><?php echo $this->_tpl_vars['prefix']; ?>
_severity_legend<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('severity_legend_title', ob_get_contents());ob_end_clean(); ?>
        <tr>
          <td colspan="5" style="padding-left: 0px"><u><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['severity_legend_title']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</u></td>
        </tr>
        <?php echo smarty_function_array(array('assign' => 'severities','eval' => 'array(\'veryheavy\', \'heavy\', \'normal\', \'light\', \'verylight\')'), $this);?>

        <tr>
          <?php $_from = $this->_tpl_vars['severities']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['severity']):
?>
          <?php ob_start(); ?><?php echo $this->_tpl_vars['prefix']; ?>
_<?php echo $this->_tpl_vars['severity']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('severity_title', ob_get_contents());ob_end_clean(); ?>
          <td class="<?php echo $this->_tpl_vars['severity']; ?>
 severity_legend_cell"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['severity_title']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</td>
          <?php endforeach; endif; unset($_from); ?>
        </tr>
      </table>