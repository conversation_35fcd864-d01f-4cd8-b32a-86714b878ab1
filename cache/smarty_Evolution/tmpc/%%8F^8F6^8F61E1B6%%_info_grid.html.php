<?php /* Smarty version 2.6.33, created on 2025-05-21 13:23:50
         compiled from /var/www/Nzoom-Hella/_libs/modules/tasks/view/templates/_info_grid.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/tasks/view/templates/_info_grid.html', 4, false),)), $this); ?>
<?php echo '<div><strong>${properties.type_name}</strong>: ${properties.name}</div>
<div><strong>${statusName(properties.status, properties.substatus_name)}</strong></div>

<div><strong>'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo '</strong>: ${Nz.formatDate(properties.added)}<br />
  '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.added_by_name}</div>
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.modified)}<br />
  '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.modified_by_name}</div>
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['status_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.status_modified)}<br />
  '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.status_modified_by_name}</div>
 ${if(properties.deleted_by_name)}
<div>'; ?>
<strong><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><?php echo ': ${Nz.formatDate(properties.deleted)}<br>
  '; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ' ${properties.deleted_by_name}</div>{
${/if}

<div>
  <strong>'; ?>
<?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php echo ':</strong>
  <span class="translations">
    ${for(trans of properties.translations)}
      <img src="'; ?>
<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
flags<?php echo '/${trans}.png" alt="${trans}" title="${trans}" border="0" align="absmiddle"${if(trans==properties.model_lang)} class="selected"${/if} />
    ${/for}
  </span>
</div>
'; ?>


