<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_recurrence_legend.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_recurrence_legend.html', 1, false),)), $this); ?>
<h3><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_invoices_templates_description_vars'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</h3>
<strong>[date_from]</strong> - <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_invoices_templates_date_from'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
<strong>[date_to]</strong> - <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_invoices_templates_date_to'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
<strong>[contract_num]</strong> - <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
<strong>[contract_date_sign]</strong> - <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_date_sign'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
<strong>[contract_date_start]</strong> - <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_date_start'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />
<strong>[contract_date_validity]</strong> - <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_date_validity'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<br />