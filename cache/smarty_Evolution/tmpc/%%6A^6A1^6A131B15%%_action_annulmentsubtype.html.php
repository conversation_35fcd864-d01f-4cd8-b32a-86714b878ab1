<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_annulmentsubtype.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_action_annulmentsubtype.html', 12, false),)), $this); ?>
  <table border="0" cellpadding="3" cellspacing="3" width="100%">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_date.html", 'smarty_include_vars' => array('standalone' => false,'name' => 'date_annul_subtype','required' => 1,'label' => $this->_config[0]['vars']['contracts_date_end_subtype'],'value' => '','show_calendar_icon' => false)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <tr>
      <td colspan="3">
        <input type="hidden" name="templates_generated" id="templates_generated" value="sure" />
        <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" onclick="if ($('date_annul_subtype').value < '<?php echo $this->_tpl_vars['available_action']['from_date']; ?>
' || $('date_annul_subtype').value > '<?php echo ((is_array($_tmp=time())) ? $this->_run_mod_handler('date_format', true, $_tmp, '%Y-%m-%d') : smarty_modifier_date_format($_tmp, '%Y-%m-%d')); ?>
') {alert('<?php echo $this->_config[0]['vars']['warning_contracts_date_end_subtype']; ?>
');return false;} <?php if ($this->_tpl_vars['available_action']['model_id'] && $this->_tpl_vars['available_action']['subtype_status'] == 'started'): ?> else {checkAgreementsDifferences(this.form, '<?php echo $this->_tpl_vars['available_action']['model_id']; ?>
', 'annul');return false;}<?php else: ?>else return confirmAction('<?php echo $this->_tpl_vars['available_action']['name']; ?>
', submitForm, this);<?php endif; ?>"><?php echo $this->_config[0]['vars']['contracts_annul_subtype']; ?>
</button>
      </td>
    </tr>
  </table>