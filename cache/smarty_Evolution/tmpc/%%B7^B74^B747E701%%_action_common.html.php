<?php /* Smarty version 2.6.33, created on 2025-05-21 13:03:02
         compiled from _action_common.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '_action_common.html', 1, false),array('modifier', 'escape', '_action_common.html', 57, false),)), $this); ?>
<form method="<?php echo ((is_array($_tmp=@$this->_tpl_vars['action']['options']['form_method'])) ? $this->_run_mod_handler('default', true, $_tmp, 'get') : smarty_modifier_default($_tmp, 'get')); ?>
" action="<?php echo $_SERVER['PHP_SELF']; ?>
" id="<?php echo $this->_tpl_vars['action']['action']; ?>
_form" enctype="multipart/form-data">
  <input type="hidden" name="<?php echo $this->_tpl_vars['action']['module_param']; ?>
" value="<?php echo $this->_tpl_vars['action']['module']; ?>
" />
  <?php if ($this->_tpl_vars['action']['controller']): ?>
    <input type="hidden" name="<?php echo $this->_tpl_vars['action']['controller_param']; ?>
" value="<?php echo $this->_tpl_vars['action']['controller']; ?>
" />
  <?php endif; ?>
  <input type="hidden" name="<?php echo $this->_tpl_vars['action']['action_param']; ?>
" value="<?php echo $this->_tpl_vars['action']['action']; ?>
" />
  <?php if ($this->_tpl_vars['action']['model_id']): ?>
    <input type="hidden" name="<?php echo $this->_tpl_vars['action']['action']; ?>
" value="<?php echo $this->_tpl_vars['action']['model_id']; ?>
" />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['action']['model_lang']): ?>
    <input type="hidden" name="model_lang" value="<?php echo $this->_tpl_vars['action']['model_lang']; ?>
" />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['action']['name'] == 'search' || $this->_tpl_vars['action']['name'] == 'filter'): ?>
    <input type="hidden" name="<?php echo $this->_tpl_vars['action']['session_param']; ?>
" value="1" />
    <input type="hidden" name="<?php echo $this->_tpl_vars['action']['name']; ?>
_module" value="<?php echo $this->_tpl_vars['action']['module']; ?>
" />
    <input type="hidden" name="<?php echo $this->_tpl_vars['action']['name']; ?>
_controller" value="<?php echo $this->_tpl_vars['action']['controller']; ?>
" />
  <?php if ($this->_tpl_vars['event'] && ! is_object ( $this->_tpl_vars['event'] )): ?>
    <input type="hidden" name="event" value="<?php echo $this->_tpl_vars['event']; ?>
" />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['relation']): ?>
    <input type="hidden" name="relation" value="<?php echo $this->_tpl_vars['relation']; ?>
" />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['group_table']): ?>
    <input type="hidden" name="group_table" value="<?php echo $this->_tpl_vars['group_table']; ?>
" />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['mynzoom_settings_table']): ?>
    <input type="hidden" name="mynzoom_settings_table" value="<?php echo $this->_tpl_vars['mynzoom_settings_table']; ?>
" />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['form_name']): ?>
    <input type="hidden" name="form_name" value="<?php echo $this->_tpl_vars['form_name']; ?>
" />
  <?php endif; ?>
  <?php if ($_REQUEST['autocomplete_filter']): ?>
    <input type="hidden" name="autocomplete_filter" id="autocomplete_filter" value="session" />
  <?php endif; ?>
  <?php if ($_REQUEST['uniqid']): ?>
    <input type="hidden" name="uniqid" id="uniqid" value="<?php echo $_REQUEST['uniqid']; ?>
" />
  <?php endif; ?>
  <?php if ($this->_tpl_vars['session_param']): ?>
    <input type="hidden" name="session_param" value="<?php echo $this->_tpl_vars['session_param']; ?>
" />
  <?php endif; ?>
  <?php endif; ?>
  <?php if ($this->_tpl_vars['hidden_fields']): ?><?php echo $this->_tpl_vars['hidden_fields']; ?>
<?php endif; ?>
  <div id="td_<?php echo $this->_tpl_vars['action']['name']; ?>
_options">
    <?php if ($this->_tpl_vars['action']['show_notice']): ?>
      <span style="color: #0000FF"><?php echo $this->_tpl_vars['action']['show_notice']; ?>
</span>
    <?php endif; ?>
    <?php if ($this->_tpl_vars['action']['ajax_no'] == '1'): ?>
      <?php if ($this->_tpl_vars['action']['template']): ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['action']['template'], 'smarty_include_vars' => array('available_action' => $this->_tpl_vars['action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php else: ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => '_action_common_options.html', 'smarty_include_vars' => array('available_action' => $this->_tpl_vars['action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php endif; ?>
    <?php endif; ?>
  </div>
  <!--<table border="0" cellpadding="0" cellspacing="0" class="t_table" width="100%">
      <tr>
          <td class="t_caption"><div class="t_caption_title"><?php echo $this->_tpl_vars['action']['label']; ?>
 <?php echo ((is_array($_tmp=$this->_config[0]['vars']['options'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
      </tr>
      <tr>

      </tr>
  </table>-->
</form>
