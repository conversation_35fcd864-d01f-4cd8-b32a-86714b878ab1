<?php /* Smarty version 2.6.33, created on 2025-05-27 12:01:49
         compiled from /var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assign.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assign.html', 18, false),array('function', 'json', '/var/www/Nzoom-Hella/_libs/modules/assignments/view/templates/_assign.html', 24, false),)), $this); ?>
<?php if (! empty ( $this->_tpl_vars['data']['users'] ) || $this->_tpl_vars['ac_index'] || $this->_tpl_vars['display_always']): ?>
  <td id="assignments_<?php echo $this->_tpl_vars['type']; ?>
<?php if ($this->_tpl_vars['ac_index']): ?>_<?php echo $this->_tpl_vars['ac_index']; ?>
<?php endif; ?>" class="nz-assignments-cell<?php if (! $this->_tpl_vars['borderless']): ?> t_border t_v_border<?php endif; ?>">
    <div class="nz-assignments-input-wrapper">
      <?php ob_start(); ?><?php echo $this->_tpl_vars['type']; ?>
_ac<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_name', ob_get_contents());ob_end_clean(); ?>
      <?php if (! $this->_tpl_vars['ac_index'] && ( in_array ( $this->_tpl_vars['model']->get('status') , array ( 'finished' , 'closed' ) ) && $this->_tpl_vars['type'] == 'owner' || $this->_tpl_vars['model']->get('archived_by') )): ?>
        <?php $this->assign('ac_readonly', true); ?>
      <?php else: ?>
        <?php $this->assign('ac_readonly', false); ?>
      <?php endif; ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."input_autocompleter.html", 'smarty_include_vars' => array('name' => $this->_tpl_vars['ac_name'],'standalone' => true,'autocomplete' => $this->_tpl_vars['data']['ac_settings'],'index' => $this->_tpl_vars['ac_index'],'empty_indexes' => true,'readonly' => $this->_tpl_vars['ac_readonly'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <?php if (! $this->_tpl_vars['ac_readonly']): ?>
      <i class="material-icons nz-input-icon" onclick="loadAssignmentsConfigurator('<?php echo $this->_tpl_vars['type']; ?>
<?php if ($this->_tpl_vars['ac_index']): ?>_<?php echo $this->_tpl_vars['ac_index']; ?>
<?php endif; ?>', this);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_configurator_load_save'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">filter_alt</i>
      <i class="material-icons nz-input-icon" onclick="loadAssignmentsGroup('<?php echo $this->_tpl_vars['type']; ?>
<?php if ($this->_tpl_vars['ac_index']): ?>_<?php echo $this->_tpl_vars['ac_index']; ?>
<?php endif; ?>', this);" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['assignments_users_group'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">person_search</i>
      <?php endif; ?>
    </div>
    <script type="text/javascript">
      <?php if ($this->_tpl_vars['ac_index']): ?>
        var <?php echo $this->_tpl_vars['type']; ?>
_<?php echo $this->_tpl_vars['ac_index']; ?>
_allowed = <?php echo smarty_function_json(array('encode' => $this->_tpl_vars['data']['users']), $this);?>
;
      <?php else: ?>
        var <?php echo $this->_tpl_vars['type']; ?>
_allowed = <?php echo smarty_function_json(array('encode' => $this->_tpl_vars['data']['users']), $this);?>
;
      <?php endif; ?>
    </script>
    <?php $_from = $this->_tpl_vars['data']['users']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['u']):
?>
      <?php if ($this->_tpl_vars['u']['selected']): ?>
        <div class="aContainer" style="white-space: nowrap; margin-bottom: 5px;">
          <?php if (! $this->_tpl_vars['ac_readonly'] && empty ( $this->_tpl_vars['inactive_at'] )): ?>
            <img class="pointer" style="margin-right: 5px;" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="confirmAction('delete_row', function(el) { el.parentNode.parentNode.removeChild(el.parentNode); }, this);" />
          <?php endif; ?>
          <img style="margin-right: 5px;" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
user<?php if ($this->_tpl_vars['u']['inactive']): ?>s_inactive<?php elseif ($this->_tpl_vars['u']['is_portal']): ?>_portal<?php endif; ?>.png" alt="" />
          <input type="hidden" name="assignments_<?php echo $this->_tpl_vars['type']; ?>
<?php if ($this->_tpl_vars['ac_index']): ?>_<?php echo $this->_tpl_vars['ac_index']; ?>
<?php endif; ?>[]" id="assignments_<?php echo $this->_tpl_vars['type']; ?>
<?php if ($this->_tpl_vars['ac_index']): ?>_<?php echo $this->_tpl_vars['ac_index']; ?>
<?php endif; ?>_<?php echo $this->_tpl_vars['u']['option_value']; ?>
" value="<?php echo $this->_tpl_vars['u']['option_value']; ?>
" />
          <?php if ($this->_tpl_vars['u']['inactive']): ?><span style="color: grey;"><?php echo $this->_tpl_vars['u']['label']; ?>
</span><?php else: ?><?php echo $this->_tpl_vars['u']['label']; ?>
<?php endif; ?>
        </div>
      <?php endif; ?>
    <?php endforeach; endif; unset($_from); ?>
    <div class="aContainer" style="white-space: nowrap; margin-bottom: 5px; display: none;" name="template">
      <img class="pointer" style="margin-right: 5px;" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/delete.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="confirmAction('delete_row', function(el) { el.parentNode.parentNode.removeChild(el.parentNode); }, this);" />
      <img style="margin-right: 5px;" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
user.png" alt="" />
      <input type="hidden" disabled="disabled" name="assignments_<?php echo $this->_tpl_vars['type']; ?>
<?php if ($this->_tpl_vars['ac_index']): ?>_<?php echo $this->_tpl_vars['ac_index']; ?>
<?php endif; ?>[]" id="assignments_<?php echo $this->_tpl_vars['type']; ?>
<?php if ($this->_tpl_vars['ac_index']): ?>_<?php echo $this->_tpl_vars['ac_index']; ?>
<?php endif; ?>_[id]" value="[id]" />
      [label]
    </div>
  </td>
<?php endif; ?>