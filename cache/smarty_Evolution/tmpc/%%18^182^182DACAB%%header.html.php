<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from layout/header.html */ ?>
<header id="nz-main-header">
    <div id="nz-menu-target">
        <div class="nz-menu-target--icon"><i class="material-icons">menu</i></div>
        <div class="nz-menu-target--text"><?php echo $this->_config[0]['vars']['menu']; ?>
</div>
    </div>
    <div class="nz-main-header--title">
        <div class="nz-main-header--title-content">
        <?php if (! $this->_tpl_vars['header_info']['logo']): ?>
        <a htef="<?php echo $this->_tpl_vars['SCRIPT_NAME']; ?>
" class="header-root-link"><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
logo.png" alt="nZoom logo" style="width: 9.25rem; height: 2.375rem" /></a>
        <?php endif; ?>
        </div>
    </div>

    <div class="nz-main-header--tools">
        <?php if ($this->_tpl_vars['validLogin']): ?>
            <?php if ($this->_tpl_vars['rolePreviewMenu']->getNumberOfItems()): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layout/role_preview.html', 'smarty_include_vars' => array('menu' => $this->_tpl_vars['rolePreviewMenu']->getMenu())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['profileMenu']): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layout/usermenu.html', 'smarty_include_vars' => array('menu' => $this->_tpl_vars['profileMenu']->getMenu())));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</header>