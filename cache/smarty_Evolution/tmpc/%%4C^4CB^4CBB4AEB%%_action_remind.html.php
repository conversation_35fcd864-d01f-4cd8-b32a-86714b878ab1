<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from _action_remind.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '_action_remind.html', 6, false),array('modifier', 'escape', '_action_remind.html', 10, false),)), $this); ?>
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td class="vtop">
        <table cellpadding="0" cellspacing="0" border="0" class="t_layout_table">
          <tr>
            <td class="labelbox"><label for="reminder_type"><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['type']), $this);?>
</label></td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <input type="hidden" name="reminder_event_id" value="<?php echo $this->_tpl_vars['available_action']['options']['reminder']['reminder_event_id']; ?>
" />
              <select class="selbox" name="reminder_type" id="reminder_type" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)">
              <?php $_from = $this->_tpl_vars['available_action']['options']['reminderTypes']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['value'] => $this->_tpl_vars['reminderType']):
?>
                <option value="<?php echo $this->_tpl_vars['value']; ?>
"<?php if ($this->_tpl_vars['value'] == $this->_tpl_vars['available_action']['options']['reminder']['type']): ?> selected="selected"<?php endif; ?>><?php echo $this->_tpl_vars['reminderType']; ?>
</option>
              <?php endforeach; endif; unset($_from); ?>
              </select>
            </td>
          </tr>
          <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_datetime.html", 'smarty_include_vars' => array('standalone' => false,'required' => 1,'name' => 'reminder_date','label' => $this->_config[0]['vars']['remind'],'show_calendar_icon' => 1,'value' => $this->_tpl_vars['available_action']['options']['reminder']['date'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <tr>
            <td class="labelbox"><a name="error_custom_message"><label for="custom_message"<?php if ($this->_tpl_vars['messages']->getErrors('custom_message')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_config[0]['vars']['custom_message']), $this);?>
</label></a></td>
            <td class="unrequired">&nbsp;</td>
            <td>
              <textarea class="areabox" name="custom_message" id="custom_message" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['custom_message'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"><?php echo ((is_array($_tmp=$this->_tpl_vars['available_action']['options']['reminder']['custom_message'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</textarea>
            </td>
          </tr>
          <tr>
            <td>
              <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"><?php echo $this->_tpl_vars['available_action']['options']['label']; ?>
</button>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>