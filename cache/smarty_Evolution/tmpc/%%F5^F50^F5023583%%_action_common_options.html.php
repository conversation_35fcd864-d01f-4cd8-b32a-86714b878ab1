<?php /* Smarty version 2.6.33, created on 2025-05-21 16:41:42
         compiled from _action_common_options.html */ ?>
<?php if ($this->_tpl_vars['available_action']['template']): ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['available_action']['template']), 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
<?php elseif (! empty ( $this->_tpl_vars['available_action']['options'] )): ?>
  <table border="0" cellpadding="3" cellspacing="3">
  <?php $_from = $this->_tpl_vars['available_action']['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['i']['iteration']++;
?>
    <?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['option']['help']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['option']['help']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['option']['label']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

    <?php if ($this->_tpl_vars['option']['type']): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['option']['type']).".html", 'smarty_include_vars' => array('var' => $this->_tpl_vars['option'],'standalone' => false,'name' => $this->_tpl_vars['option']['name'],'custom_id' => $this->_tpl_vars['option']['custom_id'],'label' => $this->_tpl_vars['option']['label'],'help' => $this->_tpl_vars['option']['help'],'value' => $this->_tpl_vars['option']['value'],'options' => $this->_tpl_vars['option']['options'],'optgroups' => $this->_tpl_vars['option']['optgroups'],'optgroup_label_source' => $this->_tpl_vars['option']['optgroup_label_source'],'option_label_source' => $this->_tpl_vars['option']['option_label_source'],'option_value' => $this->_tpl_vars['option']['option_value'],'first_option_label' => $this->_tpl_vars['option']['first_option_label'],'skip_please_select' => $this->_tpl_vars['option']['skip_please_select'],'onclick' => $this->_tpl_vars['option']['onclick'],'on_change' => $this->_tpl_vars['option']['on_change'],'onchange' => $this->_tpl_vars['option']['onchange'],'sequences' => $this->_tpl_vars['option']['sequences'],'check' => $this->_tpl_vars['option']['check'],'scrollable' => $this->_tpl_vars['option']['scrollable'],'calculate' => $this->_tpl_vars['option']['calculate'],'searchable' => $this->_tpl_vars['option']['searchable'],'autocomplete' => $this->_tpl_vars['option']['autocomplete'],'value_autocomplete' => $this->_tpl_vars['option']['value_autocomplete'],'autocomplete_var_type' => 'basic','autocomplete_type' => $this->_tpl_vars['option']['autocomplete_type'],'autocomplete_buttons' => $this->_tpl_vars['option']['autocomplete_buttons'],'min_chars' => $this->_tpl_vars['option']['min_chars'],'filters_array' => $this->_tpl_vars['option']['filters'],'execute_after' => $this->_tpl_vars['option']['execute_after'],'readonly' => $this->_tpl_vars['option']['readonly'],'hidden' => $this->_tpl_vars['option']['hidden'],'required' => $this->_tpl_vars['option']['required'],'really_required' => $this->_tpl_vars['option']['really_required'],'disabled' => $this->_tpl_vars['option']['disabled'],'options_align' => $this->_tpl_vars['option']['options_align'],'js_methods' => $this->_tpl_vars['option']['js_methods'],'restrict' => $this->_tpl_vars['option']['js_filter'],'show_placeholder' => $this->_tpl_vars['option']['show_placeholder'],'text_align' => $this->_tpl_vars['option']['text_align'],'custom_class' => $this->_tpl_vars['option']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
    <tr>
      <td colspan="3">
        <button type="<?php if ($this->_tpl_vars['available_action']['disable_items_before_execute']): ?>button<?php else: ?>submit<?php endif; ?>" class="nz-button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
"<?php if ($this->_tpl_vars['available_action']['confirm'] || $this->_tpl_vars['available_action']['disable_items_before_execute']): ?>onclick="<?php if ($this->_tpl_vars['available_action']['disable_items_before_execute']): ?><?php echo $this->_tpl_vars['available_action']['disable_items_before_execute']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['available_action']['confirm']): ?>return confirmAction('<?php echo $this->_tpl_vars['available_action']['name']; ?>
', submitForm, this);<?php endif; ?>"<?php endif; ?>
        ><?php if (! empty ( $this->_tpl_vars['available_action']['buttonIcon'] )): ?><i class="material-icons"><?php echo $this->_tpl_vars['available_action']['buttonIcon']; ?>
</i> <?php endif; ?><?php echo $this->_tpl_vars['available_action']['label']; ?>
</button>
      </td>
    </tr>
  </table>
<?php endif; ?>