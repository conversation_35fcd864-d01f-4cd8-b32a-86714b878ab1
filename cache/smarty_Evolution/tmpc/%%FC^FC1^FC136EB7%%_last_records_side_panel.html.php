<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:32
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_last_records_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_last_records_side_panel.html', 4, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_last_records_side_panel.html', 24, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_last_records_side_panel.html', 9, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_last_records_side_panel.html', 11, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_last_records_side_panel.html', 41, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_project'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <?php echo smarty_function_counter(array('start' => 0,'name' => 'item_counter','print' => false), $this);?>

  <?php $_from = $this->_tpl_vars['last_records']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['contract']):
        $this->_foreach['i']['iteration']++;
?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 vtop<?php if (! $this->_tpl_vars['contract']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['contract']->get('deleted_by')): ?> t_deleted<?php endif; ?>">
      <td class="t_border">
        <?php if ($this->_tpl_vars['contract']->get('files_count')): ?>
          <a class="vtop" href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=attachments&amp;attachments=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
">
            <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" alt=""
                 onmouseover="showFiles(this, '<?php echo $this->_tpl_vars['module']; ?>
', '<?php echo $this->_tpl_vars['controller']; ?>
', <?php echo $this->_tpl_vars['contract']->get('id'); ?>
)"
                 onmouseout="mclosetime()" />
          </a>
        <?php endif; ?>
        <a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
">
          <?php if ($this->_tpl_vars['contract']->get('num')): ?><?php if ($this->_tpl_vars['contract']->get('num') == 'system'): ?><?php echo $this->_config[0]['vars']['contracts_system_num']; ?>
<?php else: ?><?php echo $this->_tpl_vars['contract']->get('num'); ?>
<?php endif; ?><?php else: ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?>
        </a>
      </td>
      <td class="t_border"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('id'); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a></td>
      <td class="t_border"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=projects&amp;projects=view&amp;view=<?php echo $this->_tpl_vars['contract']->get('project'); ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('project_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>
</a></td>
      <td>
        <?php ob_start(); ?>
          <?php if ($this->_tpl_vars['contract']->get('status') == 'opened'): ?>
            <?php echo $this->_config[0]['vars']['help_contracts_status_opened']; ?>

          <?php elseif ($this->_tpl_vars['contract']->get('status') == 'locked'): ?>
            <?php echo $this->_config[0]['vars']['help_contracts_status_locked']; ?>

          <?php elseif ($this->_tpl_vars['contract']->get('status') == 'closed'): ?>
            <?php echo $this->_config[0]['vars']['help_contracts_status_closed']; ?>

          <?php endif; ?>
          <?php if ($this->_tpl_vars['contract']->get('substatus_name')): ?>
            <br />
            <?php echo $this->_config[0]['vars']['help_contracts_substatus']; ?>
<?php echo $this->_tpl_vars['contract']->get('substatus_name'); ?>

          <?php endif; ?>
        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contract_status', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>
          <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['contract_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => $this->_config[0]['vars']['help_contracts_status'],'width' => 250), $this);?>

        <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('popup_only', ob_get_contents());ob_end_clean(); ?>
        <?php if ($this->_tpl_vars['contract']->get('substatus_name') && $this->_tpl_vars['contract']->get('icon_name')): ?>
          <img src="<?php echo @PH_CONTRACTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['contract']->get('icon_name'); ?>
" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_only']; ?>
 />
        <?php else: ?>
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
contracts_<?php echo $this->_tpl_vars['contract']->get('status'); ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_only']; ?>
 />
        <?php endif; ?>
      </td>
    </tr>
  <?php endforeach; else: ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
      <td class="error" colspan="4"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
  <?php endif; unset($_from); ?>
</table>
<?php endif; ?>