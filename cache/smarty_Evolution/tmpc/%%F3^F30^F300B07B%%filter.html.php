<?php /* Smarty version 2.6.33, created on 2025-05-21 16:50:33
         compiled from /var/www/Nzoom-Hella/_libs/modules/customers/view/templates/filter.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'json', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/filter.html', 24, false),array('function', 'counter', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/filter.html', 52, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/filter.html', 78, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/filter.html', 25, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/filter.html', 31, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/customers/view/templates/filter.html', 66, false),)), $this); ?>
<h1><?php echo $this->_tpl_vars['title']; ?>
</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
<?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers&amp;customers=filter&amp;<?php if ($this->_tpl_vars['relation']): ?>relation=<?php echo $this->_tpl_vars['relation']; ?>
&amp;<?php endif; ?><?php if ($this->_tpl_vars['event']): ?>event=<?php echo $this->_tpl_vars['event']; ?>
&amp;<?php endif; ?><?php if ($this->_tpl_vars['mynzoom_settings_table']): ?>mynzoom_settings_table=<?php echo $this->_tpl_vars['mynzoom_settings_table']; ?>
&amp;<?php endif; ?><?php if ($_GET['autocomplete_filter']): ?>autocomplete_filter=session&amp;<?php endif; ?><?php if ($_REQUEST['uniqid']): ?>uniqid=<?php echo $_REQUEST['uniqid']; ?>
&amp;<?php endif; ?><?php if ($this->_tpl_vars['session_param']): ?>session_param=<?php echo $this->_tpl_vars['session_param']; ?>
&amp;<?php endif; ?>page=<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('link', ob_get_contents());ob_end_clean(); ?>
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'],'hide_stats' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
  <tr>
    <td id="form_container">
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."actions_box.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <form name="customers" action="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=customers" method="post" enctype="multipart/form-data">
      <?php if ($_REQUEST['autocomplete_filter']): ?>
        <?php $this->assign('uniqid', $_REQUEST['uniqid']); ?>
        <?php $this->assign('autocomplete_params', $_SESSION['autocomplete_params'][$this->_tpl_vars['uniqid']]); ?>
        <?php echo smarty_function_json(array('assign' => 'autocomplete_params_json','encode' => $this->_tpl_vars['autocomplete_params']), $this);?>

        <input type="hidden" name="autocomplete_params" id="autocomplete_params" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['autocomplete_params_json'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
      <?php endif; ?>
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
          <?php if (! $this->_tpl_vars['autocomplete_params'] || $this->_tpl_vars['autocomplete_params']['select_multiple']): ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."_select_items.html", 'smarty_include_vars' => array('pages' => $this->_tpl_vars['pagination']['pages'],'total' => $this->_tpl_vars['pagination']['total'],'session_param' => ((is_array($_tmp=@$this->_tpl_vars['session_param'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['pagination']['session_param']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['pagination']['session_param'])))));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          <?php else: ?>
            <?php $this->assign('hide_selection_stats', true); ?>
          <?php endif; ?>
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <?php if ($this->_tpl_vars['event']): ?>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <?php elseif ($this->_tpl_vars['filter_contactpersons']): ?>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customer'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <?php else: ?>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['is_company']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['is_company']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <?php endif; ?>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['name']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['name']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['type']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['type']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption t_border <?php echo $this->_tpl_vars['sort']['added']['class']; ?>
" nowrap="nowrap"><div class="t_caption_title" onclick="<?php echo $this->_tpl_vars['sort']['added']['link']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      <?php echo smarty_function_counter(array('start' => $this->_tpl_vars['pagination']['start'],'name' => 'item_counter','print' => false), $this);?>

      <?php $_from = $this->_tpl_vars['customers']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['customer']):
        $this->_foreach['i']['iteration']++;
?>
      <?php echo ''; ?><?php $this->assign('salutation', ''); ?><?php echo ''; ?><?php if (! $this->_tpl_vars['customer']->get('is_company') && $this->_tpl_vars['customer']->get('salutation')): ?><?php echo ''; ?><?php $this->assign('layout_salutation', $this->_tpl_vars['customer']->getLayoutsDetails('salutation')); ?><?php echo ''; ?><?php if ($this->_tpl_vars['layout_salutation']['view']): ?><?php echo ''; ?><?php ob_start(); ?><?php echo 'salutation_'; ?><?php echo $this->_tpl_vars['customer']->get('salutation'); ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('salutation', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['salutation']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('salutation', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php ob_start(); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo $this->_tpl_vars['salutation']; ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if (! $this->_tpl_vars['customer']->get('is_company')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php if ($this->_tpl_vars['customer']->get('subtype') == 'contact'): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' ('; ?><?php if ($this->_tpl_vars['customer']->get('is_company')): ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo ')'; ?><?php endif; ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['customer']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['customer']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['customer']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['customer']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span>'; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('info', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>

        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if (! $this->_tpl_vars['customer']->get('active')): ?> t_inactive<?php endif; ?><?php if ($this->_tpl_vars['customer']->get('deleted_by')): ?> t_deleted<?php endif; ?>">
          <td class="t_border">
            <?php if ($this->_tpl_vars['autocomplete_params'] && ! $this->_tpl_vars['autocomplete_params']['select_multiple']): ?>
                <input type="checkbox" name='items[]' value="<?php echo $this->_tpl_vars['customer']->get('id'); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="return clickOnce(this);" />
            <?php else: ?>
                <input type="checkbox"
                   onclick="setCheckAllBox(params = {
                                            the_element: this,
                                            module: '<?php echo $this->_tpl_vars['module']; ?>
',
                                            controller: '<?php echo $this->_tpl_vars['controller']; ?>
',
                                            action: '<?php echo $this->_tpl_vars['action']; ?>
',
                                            button_id: '<?php echo $this->_tpl_vars['module']; ?>
_<?php echo $this->_tpl_vars['controller']; ?>
_<?php echo $this->_tpl_vars['action']; ?>
_checkall_1'
                                           });"
                   name='items[]'
                   value="<?php echo $this->_tpl_vars['customer']->get('id'); ?>
<?php if ($this->_tpl_vars['relation']): ?>_<?php if ($this->_tpl_vars['customer']->get('is_company')): ?>company<?php else: ?>person<?php endif; ?><?php endif; ?>"
                   title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_to_include'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
           <?php endif; ?>
          </td>
          <td class="t_border hright"><?php echo smarty_function_counter(array('name' => 'item_counter','print' => true), $this);?>
</td>
          <?php if ($this->_tpl_vars['event']): ?>
          <td class="t_border"><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('company_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <?php elseif ($this->_tpl_vars['filter_contactpersons']): ?>
          <td class="t_border"><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('customer_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <?php else: ?>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['is_company']['isSorted']; ?>
"><?php if ($this->_tpl_vars['customer']->get('is_company')): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_company'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></td>
          <?php endif; ?>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['name']['isSorted']; ?>
">
            <?php if ($this->_tpl_vars['filter_contactpersons']): ?>
              <?php if ($this->_tpl_vars['customer']->get('is_main')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
small/check_yes.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_person_is_main'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_person_is_main'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /><?php endif; ?>
              <?php if ($this->_tpl_vars['customer']->get('admit_VAT_credit')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
finance_fixing.png" width="12" height="12" border="0" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_financial_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_persons_financial_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" /><?php endif; ?>
            <?php endif; ?>
            <?php echo $this->_tpl_vars['salutation']; ?>
<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! $this->_tpl_vars['customer']->get('is_company')): ?> <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
            <div id="rf<?php echo $this->_tpl_vars['customer']->get('id'); ?>
" style="display: none"><?php echo $this->_tpl_vars['salutation']; ?>
<?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php if (! $this->_tpl_vars['customer']->get('is_company')): ?> <?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('lastname'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></div>
          </td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['type']['isSorted']; ?>
"><?php if ($this->_tpl_vars['customer']->get('subtype') == 'contact'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['customers_contact_person'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_tpl_vars['customer']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></td>
          <td class="t_border <?php echo $this->_tpl_vars['sort']['added']['isSorted']; ?>
"><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['customer']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
          <td class="hcenter">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."single_actions_list.html", 'smarty_include_vars' => array('object' => $this->_tpl_vars['customer'],'exclude' => "edit, view, delete")));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php endforeach; else: ?>
        <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
          <td class="error" colspan="7"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
        </tr>
      <?php endif; unset($_from); ?>
        <tr>
          <td class="t_footer" colspan="7"></td>
        </tr>
      </table>
      <br />
      <br />
      <table border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td>
          <?php echo ''; ?><?php if ($_REQUEST['autocomplete_filter']): ?><?php echo ''; ?><?php if ($this->_tpl_vars['autocomplete_params']['select_multiple']): ?><?php echo '<button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete('; ?>{<?php echo 'close_window: false'; ?>}<?php echo ');">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo '<button type="button" name="linktButton" class="button" onclick="updateParentAutocomplete('; ?>{<?php echo 'close_window: true'; ?>}<?php echo ');">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' &amp; '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php echo ''; ?><?php else: ?><?php echo '<button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, \'items\')) '; ?>{<?php echo ' return confirmAction(\'link\', function(el) '; ?>{<?php echo ''; ?><?php if (! $this->_tpl_vars['relation']): ?><?php echo 'updateReferers(el.form, 0)'; ?><?php else: ?><?php echo 'updateCustomersRelations(el.form, \''; ?><?php echo $this->_tpl_vars['relation']; ?><?php echo '\', 0)'; ?><?php endif; ?><?php echo ';'; ?>}<?php echo ', this, \''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['confirm_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); '; ?>}<?php echo 'else'; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button><button type="button" name="linktButton" class="button" onclick="if (count_checkboxes(this.form, \'items\')) '; ?>{<?php echo ' return confirmAction(\'link\', function(el) '; ?>{<?php echo ''; ?><?php if (! $this->_tpl_vars['relation']): ?><?php echo 'updateReferers(el.form, 1)'; ?><?php else: ?><?php echo 'updateCustomersRelations(el.form, \''; ?><?php echo $this->_tpl_vars['relation']; ?><?php echo '\', 1)'; ?><?php endif; ?><?php echo ';'; ?>}<?php echo ', this, \''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['confirm_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); '; ?>}<?php echo 'else'; ?>{<?php echo 'alert(\''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_config[0]['vars']['alert_link_customers'])) ? $this->_run_mod_handler('escape', true, $_tmp, 'quotes') : smarty_modifier_escape($_tmp, 'quotes')))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '\'); return false;'; ?>}<?php echo '">'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['select'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' &amp; '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '</button>'; ?><?php endif; ?><?php echo ''; ?>

          </td>
        </tr>
      </table>
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['theme']->templatesDir)."pagination.html", 'smarty_include_vars' => array('found' => $this->_tpl_vars['pagination']['found'],'total' => $this->_tpl_vars['pagination']['total'],'rpp' => $this->_tpl_vars['pagination']['rpp'],'page' => $this->_tpl_vars['pagination']['page'],'pages' => $this->_tpl_vars['pagination']['pages'],'link' => $this->_tpl_vars['link'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </td>
  </tr>
</table>