<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_translations_menu.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/_translations_menu.html', 16, false),)), $this); ?>
<div class="nz-translations-menu">
  <?php if (! empty ( $this->_tpl_vars['make_translations'] ) || ! empty ( $this->_tpl_vars['translations'] ) && count ( $this->_tpl_vars['translations'] ) > 1): ?>
  <div class="nz-quickmenu nz-quickmenu--horizontal nz-quickmenu-autoinit">
    <div class="nz-quickmenu__menu-wrapper nz-elevation-z2">
      <ul>
        <?php $_from = $this->_tpl_vars['translations']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?>
          <?php if ($this->_tpl_vars['trans']['lang']): ?>
          <li <?php if ($this->_tpl_vars['trans']['selected']): ?>class="nz--active"<?php endif; ?>><a href="<?php echo $this->_tpl_vars['trans']['url']; ?>
" title="<?php echo $this->_tpl_vars['trans']['lang_name']; ?>
" class="nz--item"><?php echo $this->_tpl_vars['trans']['lang']; ?>
</a></li>
          <?php endif; ?>
        <?php endforeach; endif; unset($_from); ?>
        <?php if ($this->_tpl_vars['make_translations'] && ! empty ( $this->_tpl_vars['model'] )): ?>
          <li class="nz-divider"></li>
          <?php $_from = $this->_tpl_vars['make_translations']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['mk_trans']):
?>
            <li class="nz-translations-add<?php if ($this->_tpl_vars['mk_trans']['lang'] == $this->_tpl_vars['model']->get('model_lang')): ?> nz--active<?php endif; ?>"><?php echo ''; ?><?php if ($this->_tpl_vars['model']->checkPermissions('translate')): ?><?php echo '<a href="'; ?><?php echo $this->_tpl_vars['mk_trans']['url']; ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['make_translation'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' ['; ?><?php echo $this->_tpl_vars['mk_trans']['lang_name']; ?><?php echo ']" class="nz--item">'; ?><?php echo $this->_tpl_vars['mk_trans']['lang']; ?><?php echo '</a>'; ?><?php else: ?><?php echo '<span title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['error_translate_notallowed'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' ['; ?><?php echo $this->_tpl_vars['mk_trans']['lang_name']; ?><?php echo ']" class="nz--item">'; ?><?php echo $this->_tpl_vars['mk_trans']['lang']; ?><?php echo '</span>'; ?><?php endif; ?><?php echo ''; ?>
</li>
          <?php endforeach; endif; unset($_from); ?>
        <?php endif; ?>
      </ul>
    </div>
  </div>
  <?php endif; ?>
</div>