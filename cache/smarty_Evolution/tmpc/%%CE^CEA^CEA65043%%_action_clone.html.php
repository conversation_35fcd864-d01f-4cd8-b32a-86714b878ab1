<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/modules/roles/templates/_action_clone.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/_action_clone.html', 6, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/roles/templates/_action_clone.html', 9, false),)), $this); ?>
  <table border="0" cellpadding="3" cellspacing="3" width="100%">
    <tr>
      <td style="vertical-align: top;">
        <table cellpadding="0" cellspacing="0" border="0">
          <tr>
              <td class="labelbox"><label for="new_name"<?php if ($this->_tpl_vars['messages']->getErrors('new_name')): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label' => 'name'), $this);?>
</label></td>
              <td>&nbsp;</td>
              <td class="databox" nowrap="nowrap">
                <input type="text" class="txtbox" name="new_name" id="new_name" value="<?php echo $this->_config[0]['vars']['roles_copy_of']; ?>
 <?php echo ((is_array($_tmp=$this->_tpl_vars['role']->get('name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['roles_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" />
              </td>
          </tr>
        </table>
      </td>
      <td align="right" style="vertical-align: top;">
      </td>
    </tr>
    <tr>
      <td colspan="2">
        <button type="submit" class="button" name="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" id="<?php echo $this->_tpl_vars['available_action']['name']; ?>
Go" title="<?php echo $this->_tpl_vars['available_action']['label']; ?>
" onclick=""><?php echo $this->_tpl_vars['available_action']['options']['label']; ?>
</button>
      </td>
    </tr>
  </table>