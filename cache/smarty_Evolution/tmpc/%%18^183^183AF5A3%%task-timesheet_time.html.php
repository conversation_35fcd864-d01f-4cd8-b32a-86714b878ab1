<?php /* Smarty version 2.6.33, created on 2025-05-21 13:23:50
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/task-timesheet_time.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/task-timesheet_time.html', 12, false),)), $this); ?>
<?php echo '
<script id="grid-col-timesheet_time" type="text/x-template">
    <div class="nz-grid-col-timesheet_time">
        ${if(properties.timesheet_time_formatted)}
            ${if(properties.timesheet_time_formatted!=\'null\')}
                ${getTimesheetIcon(properties.id, properties.timesheet_time_formatted, properties.archived_by)}
            ${/if}
        ${/if}
        ${if(rights.addtimesheet)}
            '; ?>

            <?php ob_start(); ?><?php echo $this->_tpl_vars['module']; ?>
_add_timesheet<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('label_param', ob_get_contents());ob_end_clean(); ?>
            <?php $this->assign('add_title', ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['label_param']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp))); ?>
            <?php echo '
            ${getTimesheetAddIcon(properties.id, properties.archived_by)}
        ${/if}
    </div>
</script>
'; ?>
