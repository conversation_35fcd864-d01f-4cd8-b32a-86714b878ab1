<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:44
         compiled from announcementbar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'announcementbar.html', 4, false),)), $this); ?>
<?php if ($this->_tpl_vars['commercial_announcements']): ?>
  <?php ob_start(); ?>announcements<?php echo @PH_ANNOUNCEMENT_COMMERCIAL_TYPE; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ca_type', ob_get_contents());ob_end_clean(); ?>
  <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['ca_type'],'hidebar')): ?>
  <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
close_window.png" alt="" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['announcement_hide_bar'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="hideAnnouncementBar('announcement_bar_container');" class="floatr pointer m_announcement_hidebar" />
  <?php endif; ?>
  <div class="m_announcement_wrapper" onmouseout="clearInterval(timer);timer=setInterval(&quot;scrollAnnouncementBar()&quot;, 10);" onmouseover="clearInterval(timer);">
    <div id="announcement">
      <?php $_from = $this->_tpl_vars['commercial_announcements']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['a']):
?>
      <span id="announcement_<?php echo $this->_tpl_vars['a']->get('id'); ?>
">&raquo; <a href="#" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['announcement_view_content'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onclick="displayAnnouncement(<?php echo $this->_tpl_vars['a']->get('id'); ?>
);"><?php echo ((is_array($_tmp=$this->_tpl_vars['a']->get('content_short'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a> <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['ca_type'],'closebar')): ?><img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
close.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['close'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="pointer" onclick="closeAnnouncement(<?php echo $this->_tpl_vars['a']->get('id'); ?>
);" style="margin: -5px 0;" /><?php endif; ?></span>
      <?php endforeach; endif; unset($_from); ?>
    </div>
    <script type="text/javascript">
      var announcement;
      psinit = 1200;
      pscrnt = psinit;
      timer = setInterval("scrollAnnouncementBar()", 10);
    </script>
  </div>
<?php endif; ?>