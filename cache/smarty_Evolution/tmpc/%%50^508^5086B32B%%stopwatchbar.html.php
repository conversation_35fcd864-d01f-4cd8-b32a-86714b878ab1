<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from stopwatchbar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'stopwatchbar.html', 11, false),array('modifier', 'mb_lower', 'stopwatchbar.html', 16, false),array('modifier', 'date_format', 'stopwatchbar.html', 19, false),)), $this); ?>
<span id="m_started_timers">
<?php if ($this->_tpl_vars['currentUser'] && $this->_tpl_vars['currentUser']->get('started_timers') && count ( $this->_tpl_vars['currentUser']->get('started_timers') ) > 0): ?>
    <?php $_from = $this->_tpl_vars['currentUser']->get('started_timers'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['started_model'] => $this->_tpl_vars['started_timers']):
?>
      <?php $_from = $this->_tpl_vars['started_timers']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['started_model_id'] => $this->_tpl_vars['started_model_details']):
?>
        <?php ob_start(); ?>lr_model_<?php echo $this->_tpl_vars['started_model_details']['model']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('model_label', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?><?php echo $this->_tpl_vars['started_model']; ?>
s<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('started_model_plural', ob_get_contents());ob_end_clean(); ?>
        <button class="nz-icon-button nz-tooltip-trigger nz-tooltip-autoinit"
                id="startedwatch_<?php echo $this->_tpl_vars['started_model']; ?>
_<?php echo $this->_tpl_vars['started_model_id']; ?>
"
                onclick="confirmAction('stop_watch', function(el) { stopWatch(el, '<?php echo $this->_tpl_vars['started_model']; ?>
', <?php echo $this->_tpl_vars['started_model_id']; ?>
); }, this)"
                data-tooltip-position="panel: top right at: bottom right"
                data-tooltip-title="<?php echo ((is_array($_tmp=$this->_tpl_vars['help_label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
                data-tooltip-element="#stopwatchInfo-<?php echo $this->_tpl_vars['started_model']; ?>
_<?php echo $this->_tpl_vars['started_model_id']; ?>
"><?php echo $this->_tpl_vars['theme']->getIconForAction('stoptimer'); ?>
<span class="nz-glyph-sub"><?php echo $this->_tpl_vars['theme']->getIconForRecord($this->_tpl_vars['started_model_plural']); ?>
</span></button>
        <div id="stopwatchInfo-<?php echo $this->_tpl_vars['started_model']; ?>
_<?php echo $this->_tpl_vars['started_model_id']; ?>
"
             class="nz-tooltip-content nz-tooltip-notch__top-right stopwatchbar-info">
          <?php echo $this->_config[0]['vars']['stopwatch_started_prefix']; ?>

          <?php echo ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['model_label']])) ? $this->_run_mod_handler('mb_lower', true, $_tmp) : smarty_modifier_mb_lower($_tmp)); ?>
<br />
          <strong><?php if ($this->_tpl_vars['started_model_details']['full_num']): ?>[<?php echo $this->_tpl_vars['started_model_details']['full_num']; ?>
] <?php endif; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['started_model_details']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><br />
          <?php echo $this->_config[0]['vars']['stopwatch_started_suffix']; ?>

          <?php echo ((is_array($_tmp=$this->_tpl_vars['started_model_details']['start_date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])); ?>

        </div>
      <?php endforeach; endif; unset($_from); ?>
    <?php endforeach; endif; unset($_from); ?>
<?php else: ?>
  &nbsp;
<?php endif; ?>
</span>