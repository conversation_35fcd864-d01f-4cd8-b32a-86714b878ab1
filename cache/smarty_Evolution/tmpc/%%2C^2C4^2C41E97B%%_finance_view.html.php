<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_view.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'help', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_view.html', 8, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_view.html', 12, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_view.html', 12, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_view.html', 54, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_view.html', 66, false),)), $this); ?>

<table cellspacing="0" class="finance_table_edit" cellpadding="0">
  <tr>
    <td class="finance_main_cell" style="border:none">
      <div class="error" style="display: none; background: #FFFFFF; border: 0" id="finance_invalid_data"><?php echo $this->_config[0]['vars']['error_finance_invalid_data']; ?>
</div>
      <table class="t_borderless">
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'invoices_templates_name'), $this);?>
</td>
          <td class="required">&nbsp;</td>
          <td class="">
            <?php ob_start();
$_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => (@PH_MODULES_DIR)."/finance/templates/_info.html", 'smarty_include_vars' => array('model' => $this->_tpl_vars['invoices_template'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
$this->assign('info', ob_get_contents()); ob_end_clean();
 ?>
            <span <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => ((is_array($_tmp=$this->_config[0]['vars']['system_info'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?>
><?php echo $this->_tpl_vars['invoices_template']->get('name'); ?>
</span>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'auto_issue'), $this);?>
</td>
          <td class="required">&nbsp;</td>
          <td class=""><?php if ($this->_tpl_vars['invoices_template']->get('auto_issue')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?></td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'auto_send'), $this);?>
</td>
          <td class="required">&nbsp;</td>
          <td class=""><?php if ($this->_tpl_vars['invoices_template']->get('auto_send')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?></td>
        </tr>
        <tr style="<?php if (! $this->_tpl_vars['invoices_template']->get('auto_send')): ?> display: none;<?php endif; ?>">
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'email_template'), $this);?>
</td>
          <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
          <td class="">
            <?php $_from = $this->_tpl_vars['email_templates']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['em']):
?>
              <?php if ($this->_tpl_vars['em']['option_value'] == $this->_tpl_vars['invoices_template']->get('auto_send')): ?><?php echo $this->_tpl_vars['em']['label']; ?>
<?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>
        <tr style="<?php if (! $this->_tpl_vars['invoices_template']->get('auto_send')): ?> display: none;<?php endif; ?>">
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'pattern'), $this);?>
</td>
          <td class="required">&nbsp;</td>
          <td class="">
            <?php $_from = $this->_tpl_vars['patterns']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pt']):
?>
              <?php if ($this->_tpl_vars['pt']['option_value'] == $this->_tpl_vars['invoices_template']->get('pattern')): ?><?php echo $this->_tpl_vars['pt']['label']; ?>
<?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><?php echo smarty_function_help(array('label' => 'recurrent'), $this);?>
</td>
          <td class="required">&nbsp;</td>
          <td class=""><?php if ($this->_tpl_vars['invoices_template']->get('recurrent')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?></td>
        </tr>
        <?php if ($this->_tpl_vars['invoices_template']->get('type') != @PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL || $this->_tpl_vars['invoices_template']->get('status') == 'finished'): ?>
          <?php if ($this->_tpl_vars['invoices_template']->get('recurrent')): ?>
          <?php $this->assign('formula_index', $this->_tpl_vars['invoices_template']->get('periods_start_formula')); ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'periods_start'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td><?php if ($this->_tpl_vars['invoices_template']->get('periods_start') && $this->_tpl_vars['invoices_template']->get('periods_start') != '0000-00-00'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['invoices_template']->get('periods_start'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>
<?php endif; ?><?php if ($this->_tpl_vars['invoices_template']->get('periods_start_formula')): ?> (<?php echo $this->_tpl_vars['formulas'][$this->_tpl_vars['formula_index']]['label']; ?>
)<?php endif; ?></td>
          </tr>
          <?php $this->assign('formula_index', $this->_tpl_vars['invoices_template']->get('periods_end_formula')); ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'periods_end'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td><?php if ($this->_tpl_vars['invoices_template']->get('periods_end') && $this->_tpl_vars['invoices_template']->get('periods_end') != '0000-00-00'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['invoices_template']->get('periods_end'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>
<?php endif; ?><?php if ($this->_tpl_vars['invoices_template']->get('periods_end_formula')): ?> (<?php echo $this->_tpl_vars['formulas'][$this->_tpl_vars['formula_index']]['label']; ?>
)<?php endif; ?></td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'single_period'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('single_period_period'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lp', ob_get_contents());ob_end_clean(); ?>
            <?php $this->assign('spl', ((is_array($_tmp=@$this->_tpl_vars['single_period_lengths'][$this->_tpl_vars['lp']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['single_period_lengths']['day']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['single_period_lengths']['day']))); ?>
            <td><?php echo $this->_tpl_vars['invoices_template']->get('single_period_count'); ?>
 <?php echo $this->_config[0]['vars'][$this->_tpl_vars['lp']]; ?>
 -
              <?php $_from = $this->_tpl_vars['spl']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pl']):
?>
                <?php if ($this->_tpl_vars['pl']['option_value'] == $this->_tpl_vars['invoices_template']->get('single_period_length')): ?>
                  <?php echo $this->_tpl_vars['pl']['label']; ?>

                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            </td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'recurrence'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('recurrence_period'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lp', ob_get_contents());ob_end_clean(); ?>
            <td><?php echo $this->_tpl_vars['invoices_template']->get('recurrence_count'); ?>
 <?php echo $this->_config[0]['vars'][$this->_tpl_vars['lp']]; ?>
</td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'single_period_rows'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td><?php if ($this->_tpl_vars['invoices_template']->get('single_period_rows') == 'one_one'): ?>
                  <?php echo $this->_config[0]['vars']['contracts_one_row_one_quantity']; ?>

                <?php elseif ($this->_tpl_vars['invoices_template']->get('single_period_rows') == 'one_all'): ?>
                  <?php echo $this->_config[0]['vars']['contracts_one_row_all_quantities']; ?>

                <?php elseif ($this->_tpl_vars['invoices_template']->get('single_period_rows') == 'all_one'): ?>
                  <?php echo $this->_config[0]['vars']['contracts_all_rows_one_quantity']; ?>

                <?php endif; ?>
            </td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'first_period_invoice'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td><?php if ($this->_tpl_vars['invoices_template']->get('first_period_invoice') == 'full'): ?>
                  <?php echo $this->_config[0]['vars']['contracts_full_period']; ?>

                <?php elseif ($this->_tpl_vars['invoices_template']->get('first_period_invoice') == 'partial'): ?>
                  <?php echo $this->_config[0]['vars']['contracts_partial_period']; ?>

                <?php elseif ($this->_tpl_vars['invoices_template']->get('first_period_invoice') == 'full_partial'): ?>
                  <?php echo $this->_config[0]['vars']['contracts_partial_full_period']; ?>

                <?php endif; ?>
            </td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'invoices_start'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <?php echo $this->_tpl_vars['invoices_template']->get('issue_start_count'); ?>

              <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('issue_start_period_type'); ?>
_days<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
              <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

              <?php echo $this->_config[0]['vars']['days']; ?>

              <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('issue_start_direction'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
              <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

              <?php ob_start(); ?>period_<?php echo $this->_tpl_vars['invoices_template']->get('issue_start_point'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
              <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

              <?php echo $this->_config[0]['vars']['period_period']; ?>

            </td>
          </tr>
          <?php endif; ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'issue_date'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td class="">
              <?php if ($this->_tpl_vars['invoices_template']->get('recurrent')): ?>
                <?php echo $this->_tpl_vars['invoices_template']->get('issue_date_count'); ?>

                <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('issue_date_period_type'); ?>
_days<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
                <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

                <?php echo $this->_config[0]['vars']['days']; ?>

                <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('issue_date_direction'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
                <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

                <?php ob_start(); ?>period_<?php echo $this->_tpl_vars['invoices_template']->get('issue_date_point'); ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
                <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

                <?php echo $this->_config[0]['vars']['period_period']; ?>

              <?php else: ?>
                <?php echo ((is_array($_tmp=$this->_tpl_vars['invoices_template']->get('issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

                <?php if ($this->_tpl_vars['invoices_template']->get('issue_date_formula')): ?>
                  <?php $this->assign('formula_index', $this->_tpl_vars['invoices_template']->get('issue_date_formula')); ?>
                  (<?php echo $this->_tpl_vars['formulas'][$this->_tpl_vars['formula_index']]['label']; ?>
)
                <?php endif; ?>
              <?php endif; ?>
            </td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'date_of_payment'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td>
              <?php echo $this->_tpl_vars['invoices_template']->get('date_of_payment_count'); ?>

              <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('date_of_payment_period_type'); ?>
_days<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
              <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

              <?php echo $this->_config[0]['vars']['days']; ?>

              <?php $_from = $this->_tpl_vars['pay_after_types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pat']):
?>
                <?php if ($this->_tpl_vars['pat']['option_value'] == $this->_tpl_vars['invoices_template']->get('date_of_payment_point')): ?>
                  <?php echo $this->_tpl_vars['pat']['label']; ?>

                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            </td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'company_data'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td class="">
              <?php if ($this->_tpl_vars['invoices_template']->get('payment_type') == 'bank'): ?>
                <?php $_from = $this->_tpl_vars['bank_accounts']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                  <?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['invoices_template']->get('container_id')): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
              <?php else: ?>
                <?php $_from = $this->_tpl_vars['cashboxes']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['option']):
?>
                  <?php if ($this->_tpl_vars['option']['option_value'] == $this->_tpl_vars['invoices_template']->get('container_id')): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
              <?php endif; ?>
            </td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'employee'), $this);?>
</td>
            <td class="required">&nbsp;</td>
            <td class="">
              <?php echo ((is_array($_tmp=$this->_tpl_vars['invoices_template']->get('employee_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

            </td>
          </tr>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'assign_responsible'), $this);?>
</td>
            <td class="required"><?php echo $this->_config[0]['vars']['required']; ?>
</td>
            <td class="">
                <div id="observer_txt_<?php echo $this->_tpl_vars['invoices_template']->get('id'); ?>
" style="display: inline;">
                <?php $_from = $this->_tpl_vars['users_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['uo']):
?>
                  <?php if ($this->_tpl_vars['uo']['option_value'] == $this->_tpl_vars['invoices_template']->get('observer')): ?><?php echo $this->_tpl_vars['uo']['label']; ?>
<?php endif; ?>
                <?php endforeach; endif; unset($_from); ?>
                </div>
                <?php if ($this->_tpl_vars['action'] == 'viewfinance' && $this->_tpl_vars['contract']->get('status') != 'opened' && $this->_tpl_vars['currentUser']->checkRights('contracts','editfinance')): ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'observer','index' => $this->_tpl_vars['invoices_template']->get('id'),'label' => $this->_config[0]['vars']['contracts_assign_responsible'],'options' => $this->_tpl_vars['users_options'],'required' => 1,'really_required' => 1,'value' => $this->_tpl_vars['invoices_template']->get('observer'),'width' => '200','hidden' => 1)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
                <a href="javascript:void(0);"><img id="observer_switch_<?php echo $this->_tpl_vars['invoices_template']->get('id'); ?>
" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
edit.png" onclick="changeTemplateObserver(this, <?php echo $this->_tpl_vars['invoices_template']->get('contract_id'); ?>
, <?php echo $this->_tpl_vars['invoices_template']->get('id'); ?>
);" class="icon_button" alt="<?php echo $this->_config[0]['vars']['edit']; ?>
" title="<?php echo $this->_config[0]['vars']['edit']; ?>
" /></a>
                <?php endif; ?>
            </td>
          </tr>
          <?php if ($this->_tpl_vars['invoice_type'] < @PH_FINANCE_TYPE_MAX): ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'proforma_invoice'), $this);?>
</td>
            <td class="required">&nbsp;</td>
            <td class="">
              <?php if ($this->_tpl_vars['invoices_template']->get('proforma')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?>
            </td>
          </tr>
          <?php endif; ?>
          <tr>
            <td class="labelbox"><?php echo smarty_function_help(array('label' => 'issue_currency'), $this);?>
</td>
            <td class="required">&nbsp;</td>
            <td class="">
              <?php echo $this->_tpl_vars['invoices_template']->get('issue_currency'); ?>

            </td>
          </tr>
        <?php endif; ?>
      </table>
    </td>
  </tr>
  <tr>
    <td class="t_borderless">
      <table>
      <?php if ($this->_tpl_vars['var']['type']): ?>
        <?php $this->assign('input_type_name', "_gt2_view.html"); ?>
        <?php $this->assign('table', $this->_tpl_vars['var']); ?>
        <tr>
          <td colspan="3" class="t_table">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['input_type_name'], 'smarty_include_vars' => array('var' => $this->_tpl_vars['var'],'standalone' => false,'var_id' => $this->_tpl_vars['var']['id'],'name' => $this->_tpl_vars['var']['name'],'custom_id' => $this->_tpl_vars['var']['custom_id'],'label' => $this->_tpl_vars['var']['label'],'help' => $this->_tpl_vars['var']['help'],'value' => $this->_tpl_vars['var']['value'],'options' => $this->_tpl_vars['var']['options'],'optgroups' => $this->_tpl_vars['var']['optgroups'],'option_value' => $this->_tpl_vars['var']['option_value'],'first_option_label' => $this->_tpl_vars['var']['first_option_label'],'onclick' => $this->_tpl_vars['var']['onclick'],'on_change' => $this->_tpl_vars['var']['on_change'],'check' => $this->_tpl_vars['var']['check'],'scrollable' => $this->_tpl_vars['var']['scrollable'],'calculate' => $this->_tpl_vars['var']['calculate'],'readonly' => $this->_tpl_vars['var']['readonly'],'source' => $this->_tpl_vars['var']['source'],'hidden' => $this->_tpl_vars['var']['hidden'],'hide_label' => 1,'required' => $this->_tpl_vars['var']['required'],'disabled' => $this->_tpl_vars['var']['disabled'],'show_placeholder' => $this->_tpl_vars['var']['show_placeholder'],'text_align' => $this->_tpl_vars['var']['text_align'],'custom_class' => $this->_tpl_vars['var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      <?php endif; ?>
        <tr id="recurrence_legend" style="<?php if (! $this->_tpl_vars['invoices_template']->get('recurrent') || $this->_tpl_vars['table'] && $this->_tpl_vars['table']['vars']['free_text5']['hidden'] != 0 || ! $this->_tpl_vars['table']): ?>display:none<?php endif; ?>">
          <td class="t_borderless" colspan="3">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_finance_recurrence_legend.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
        <tr>
          <td class="t_borderless" colspan="3">&nbsp;</td>
        </tr>
      </table>
    </td>
  </tr>
</table>