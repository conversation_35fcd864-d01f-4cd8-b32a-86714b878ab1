<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:43
         compiled from layout/titlebar.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'layout/titlebar.html', 9, false),)), $this); ?>
<div class="nz-titlebar">
      <div class="nz-navbar">
      <!-- Begin Navigation Bar --><?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'layout/navbar.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
      <!-- End Navigation Bar -->
      </div>

      <!-- Begin Alternative Keyboard Change Menu -->
      <?php if ($this->_tpl_vars['include_keyboard_inputs_toggler'] && $this->_tpl_vars['prefered_keyboard_inputs']): ?>
        <div class="langLink<?php if ($_COOKIE['molang']): ?> <?php echo $_COOKIE['molang']; ?>
<?php endif; ?>"><?php echo ((is_array($_tmp=@$_COOKIE['molang'])) ? $this->_run_mod_handler('default', true, $_tmp, 'OFF') : smarty_modifier_default($_tmp, 'OFF')); ?>
</div>
        <input type="hidden" id="prefered_keyboard_inputs" name="prefered_keyboard_inputs" value="<?php echo $this->_tpl_vars['prefered_keyboard_inputs']; ?>
" />
      <?php endif; ?>
      <!-- End Alternative Keyboard Change Menu -->

      <div class="m_stopwatchbar" id="m_stopwatchbar">
        <!-- Stopwatch Info Bar -->
        <span id="m_stopwatchbar_box">
<?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => 'stopwatchbar.html', 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </span>
        <!-- End Stopwatch Bar -->
      </div>

      <div class="nz-lockedrecords" id="lockedrecords">
        <!-- Begin Lock Info Bar -->
        <!--  Loaded with AJAX -->
        <!-- End Lock Bar -->
      </div>
</div>