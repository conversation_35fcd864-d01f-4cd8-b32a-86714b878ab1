<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:32
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_info_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_info_panel.html', 5, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_info_panel.html', 43, false),array('function', 'cycle', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_info_panel.html', 30, false),array('function', 'popup', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_contracts_info_panel.html', 46, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<table border="0" cellpadding="0" cellspacing="0" class="t_table t_layout_table">
  <tr>
    <td class="t_panel_caption" colspan="6">
      <div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_related_subtypes'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div>
    </td>
  </tr>
  <tr>
    <td class="t_panel_caption" style="width: 10px!important"><div class="t_panel_caption_title"><?php echo $this->_config[0]['vars']['num']; ?>
</div></td>
    <td class="t_panel_caption t_border" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_type'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption t_border" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption t_border" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption t_border" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption t_border" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <?php $_from = $this->_tpl_vars['contract']->get('subelements'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['element']):
        $this->_foreach['i']['iteration']++;
?>
  <?php ob_start(); ?>
    <?php if ($this->_tpl_vars['element']['status'] == 'opened'): ?>
      <?php echo $this->_config[0]['vars']['help_contracts_status_opened']; ?>

    <?php elseif ($this->_tpl_vars['element']['status'] == 'locked'): ?>
      <?php echo $this->_config[0]['vars']['help_contracts_status_locked']; ?>

    <?php elseif ($this->_tpl_vars['element']['status'] == 'closed'): ?>
      <?php echo $this->_config[0]['vars']['help_contracts_status_closed']; ?>

    <?php endif; ?>
    <?php if ($this->_tpl_vars['element']['substatus_name']): ?>
      <br />
      <?php echo $this->_config[0]['vars']['help_contracts_substatus']; ?>
<?php echo $this->_tpl_vars['element']['substatus_name']; ?>

    <?php endif; ?>
  <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('contract_status', ob_get_contents());ob_end_clean(); ?>
  <tr class="<?php if ($this->_tpl_vars['contract']->get('id') == $this->_tpl_vars['element']['id']): ?>strong t_selected_row_for_edit <?php endif; ?><?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
<?php if ($this->_tpl_vars['element']['annulled_by'] || $this->_tpl_vars['element']['subtype_status'] == 'failed'): ?> strike<?php endif; ?> vtop">
    <td class="t_border hright" style="width: 10px!important"><?php echo $this->_foreach['i']['iteration']; ?>
</td>
    <td class="t_border">
    <?php if ($this->_tpl_vars['element']['subtype'] == 'contract'): ?>
      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 (<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_current'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)
    <?php elseif ($this->_tpl_vars['element']['subtype'] == 'original'): ?>
      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 (<?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_first'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
)
    <?php elseif ($this->_tpl_vars['element']['subtype'] == 'annex'): ?>
      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_annex'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

    <?php endif; ?>
    </td>
    <td class="t_border"><a href="<?php echo $_SERVER['SCRIPT_NAME']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=contracts&amp;contracts=view&amp;view=<?php echo $this->_tpl_vars['element']['id']; ?>
"><?php echo ((is_array($_tmp=$this->_tpl_vars['element']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</a></td>
    <td class="t_border"><?php if ($this->_tpl_vars['element']['num']): ?><?php if ($this->_tpl_vars['element']['num'] == 'system'): ?><?php echo $this->_config[0]['vars']['contracts_system_num']; ?>
<?php else: ?><?php echo $this->_tpl_vars['element']['num']; ?>
<?php endif; ?><?php else: ?><i><?php echo ((is_array($_tmp=$this->_config[0]['vars']['contracts_unfinished_contract'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</i><?php endif; ?></td>
    <td class="t_border"><?php echo ((is_array($_tmp=$this->_tpl_vars['element']['status_modified'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>
</td>
    <td class="">
      <?php ob_start(); ?>
        <?php echo smarty_function_popup(array('text' => ((is_array($_tmp=$this->_tpl_vars['contract_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'caption' => $this->_config[0]['vars']['help_contracts_status'],'width' => 250), $this);?>

      <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('popup_and_onclick', ob_get_contents());ob_end_clean(); ?>
      <?php if ($this->_tpl_vars['element']['substatus_name'] && $this->_tpl_vars['element']['icon_name']): ?>
        <img src="<?php echo @PH_CONTRACTS_STATUSES_URL; ?>
<?php echo $this->_tpl_vars['element']['icon_name']; ?>
" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
      <?php else: ?>
        <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
contracts_<?php echo $this->_tpl_vars['element']['status']; ?>
.png" width="16" height="16" border="0" alt="" title="" <?php echo $this->_tpl_vars['popup_and_onclick']; ?>
 />
      <?php endif; ?>
    </td>
  </tr>
  <?php endforeach; endif; unset($_from); ?>
</table>
<?php endif; ?>