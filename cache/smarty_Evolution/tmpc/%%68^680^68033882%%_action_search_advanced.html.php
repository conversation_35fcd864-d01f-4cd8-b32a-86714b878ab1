<?php /* Smarty version 2.6.33, created on 2025-05-21 13:13:35
         compiled from _action_search_advanced.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'counter', '_action_search_advanced.html', 20, false),array('modifier', 'escape', '_action_search_advanced.html', 47, false),)), $this); ?>
<!-- JS arrays for manipulation of the search options -->
<script type="text/javascript">
  search_additional_vars_switch = '<?php echo $this->_tpl_vars['switch_additional']; ?>
';
  advanced_search_obj = <?php echo $this->_tpl_vars['advanced_search_options']; ?>
;
  additional_search_obj = <?php echo $this->_tpl_vars['additional_search_options']; ?>
;
</script>

  <div id="search_advanced_container" class="search_container">
    <input type="hidden" value="" id="filters_action" name="filters_action" <?php if ($this->_tpl_vars['disabled']): ?>disabled="disabled" <?php endif; ?>/>
    <div class="t_caption3_title t_caption3">
      <?php echo $this->_config[0]['vars']['search_filters']; ?>

    </div>
    <div class="search_advanced_filters">
      <table border="0" cellpadding="5" cellspacing="0" id="search_container">
        <tr id="search_headers" style="display: none;">
          <td></td>
        </tr>

        <?php $_from = $this->_tpl_vars['session_filters']['search_fields']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['filter']):
        $this->_foreach['i']['iteration']++;
?>
          <?php echo smarty_function_counter(array('start' => 0,'name' => 'item_counter','print' => false,'assign' => 'current_item'), $this);?>


        <?php endforeach; endif; unset($_from); ?>
        <?php echo smarty_function_counter(array('start' => 0,'name' => 'item_counter','print' => false,'assign' => 'current_item'), $this);?>

        <?php $_from = $this->_tpl_vars['session_filters']['search_fields']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['filter']):
        $this->_foreach['i']['iteration']++;
?>
                    <?php $this->assign('present', 0); ?>
          <?php $_from = $this->_tpl_vars['search_fields']['basic_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt']):
?>
            <?php if ($this->_tpl_vars['opt']['option_value'] == $this->_tpl_vars['filter']): ?><?php $this->assign('present', 1); ?><?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
          <?php if (! $this->_tpl_vars['present']): ?>
            <?php $_from = $this->_tpl_vars['search_fields']['additional_vars']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt']):
?>
              <?php if ($this->_tpl_vars['opt']['option_value'] == $this->_tpl_vars['filter']): ?><?php $this->assign('present', 1); ?><?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
          <?php endif; ?>
        <?php if ($this->_tpl_vars['present']): ?>
          <?php echo smarty_function_counter(array('name' => 'item_counter','print' => false), $this);?>

          <?php if (! preg_match ( '#a__#' , $this->_tpl_vars['filter'] )): ?>
            <?php $this->assign('search_defs', $this->_tpl_vars['search_fields']['basic_vars']); ?>
          <?php else: ?>
            <?php $this->assign('search_defs', $this->_tpl_vars['search_fields']['additional_vars']); ?>
          <?php endif; ?>
        <tr id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
">
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_0" nowrap>
            <?php if ($this->_tpl_vars['view_mode']): ?>
              <?php echo $this->_tpl_vars['current_item']; ?>

            <?php else: ?>
              <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="12" width="12" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row"<?php if (empty ( $this->_tpl_vars['session_filters']['search_fields'] ) || count ( $this->_tpl_vars['session_filters']['search_fields'] ) <= 1): ?> style="visibility: hidden;"<?php endif; ?> onclick="processSearchDef('hide', '<?php echo $this->_tpl_vars['current_item']; ?>
');" />
              <a href="javascript: void(0);" onclick="javascript: processSearchDef('disable', '<?php echo $this->_tpl_vars['current_item']; ?>
');" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"><?php echo $this->_tpl_vars['current_item']; ?>
</a>
            <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_1" style="width: 150px;">
            <?php if ($this->_tpl_vars['additional_search_options'] == 'false' || $this->_tpl_vars['additional_search_options'] == '[]'): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'name' => 'search_fields','custom_class' => 'search_fields','custom_id' => 'search_fields','index' => $this->_tpl_vars['current_item'],'value' => $this->_tpl_vars['filter'],'width' => '150','optgroup_label_source' => 'config','options' => $this->_tpl_vars['search_fields']['basic_vars'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'name' => 'search_fields','custom_class' => 'search_fields','custom_id' => 'search_fields','index' => $this->_tpl_vars['current_item'],'sequences' => 'setSearchDef(this);','value' => $this->_tpl_vars['filter'],'width' => '150','optgroup_label_source' => 'config','optgroups' => $this->_tpl_vars['search_fields'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('name' => 'search_fields_prev','custom_class' => 'search_fields_prev','standalone' => true,'custom_id' => 'search_fields_prev','index' => $this->_tpl_vars['current_item'],'value' => $this->_tpl_vars['filter'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_2" style="width: 150px;">
            <?php $this->assign('selected_compare', $this->_tpl_vars['session_filters']['compare_options'][$this->_tpl_vars['key']]); ?>
            <?php echo smarty_function_counter(array('assign' => 'options_count','start' => 0,'print' => false), $this);?>

            <?php $_from = $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['opt_group'] => $this->_tpl_vars['opt_groups']):
?>
              <?php $_from = $this->_tpl_vars['opt_groups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['idx'] => $this->_tpl_vars['option']):
?>
                <?php echo smarty_function_counter(array('print' => false), $this);?>

                <?php if ($this->_tpl_vars['selected_compare'] == $this->_tpl_vars['option']['option_value']): ?>
                  <?php $this->assign('values_operator_group', $this->_tpl_vars['opt_group']); ?>
                  <?php $this->assign('values_operator', $this->_tpl_vars['idx']); ?>
                  <?php $this->assign('next_input', $this->_tpl_vars['option']['itype']); ?>
                <?php endif; ?>
              <?php endforeach; endif; unset($_from); ?>
            <?php endforeach; endif; unset($_from); ?>
            <?php if ($this->_tpl_vars['options_count'] == 1 || preg_match ( '/\.((search_)?archive|deleted)$/' , $this->_tpl_vars['filter'] ) || preg_match ( '/^(fp\.annulled|(fir|fer)\.active)$/' , $this->_tpl_vars['filter'] )): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'disabled' => $this->_tpl_vars['disabled'],'name' => 'compare_options','custom_class' => 'compare_options','custom_id' => 'compare_options','index' => $this->_tpl_vars['current_item'],'value' => $this->_tpl_vars['selected_compare'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php else: ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'compare_options','custom_class' => 'compare_options','custom_id' => 'compare_options','index' => $this->_tpl_vars['current_item'],'value' => $this->_tpl_vars['selected_compare'],'width' => '150','optgroups' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_3" style="white-space: nowrap!important; width: 205px;">
            <?php if ($this->_tpl_vars['next_input'] == 'autocompleter'): ?>
              <?php $this->assign('restrict', ''); ?>
            <?php elseif ($this->_tpl_vars['next_input'] == 'date'): ?>
              <?php $this->assign('searchable', 1); ?>
              <?php $this->assign('restrict', ''); ?>
            <?php elseif ($this->_tpl_vars['next_input'] == 'number'): ?>
              <?php $this->assign('searchable', 0); ?>
              <?php $this->assign('next_input', 'text'); ?>
              <?php $this->assign('restrict', 'insertOnlyReals'); ?>
            <?php else: ?>
              <?php $this->assign('restrict', ''); ?>
              <?php $this->assign('searchable', 0); ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['filter'] == $this->_tpl_vars['switch_additional'] || preg_match ( '#\.type$#' , $this->_tpl_vars['filter'] )): ?>
              <?php $this->assign('sequences', "setSearchDef(this);"); ?>
            <?php else: ?>
              <?php $this->assign('sequences', ''); ?>
            <?php endif; ?>
            <?php if (isset ( $this->_tpl_vars['session_filters']['values_code'][$this->_tpl_vars['key']] ) || isset ( $this->_tpl_vars['session_filters']['values_autocomplete'][$this->_tpl_vars['key']] )): ?>
              <?php $this->assign('autocomplete_var_type', 'basic'); ?>
            <?php else: ?>
              <?php $this->assign('autocomplete_var_type', 'searchable'); ?>
            <?php endif; ?>
            <?php if ($this->_tpl_vars['next_input']): ?>
              <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_".($this->_tpl_vars['next_input']).".html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'searchable' => $this->_tpl_vars['searchable'],'disabled' => $this->_tpl_vars['disabled'],'show_calendar_icon' => true,'index' => $this->_tpl_vars['current_item'],'name' => 'values','custom_class' => 'values','custom_id' => 'values','width' => '200','hide_calendar_icon' => 1,'restrict' => $this->_tpl_vars['restrict'],'do_not_escape_labels' => true,'value' => $this->_tpl_vars['session_filters']['values'][$this->_tpl_vars['key']],'value_autocomplete' => $this->_tpl_vars['session_filters']['values_autocomplete'][$this->_tpl_vars['key']],'value_code' => $this->_tpl_vars['session_filters']['values_code'][$this->_tpl_vars['key']],'value_name' => $this->_tpl_vars['session_filters']['values_name'][$this->_tpl_vars['key']],'value_date_period' => ((is_array($_tmp=$this->_tpl_vars['session_filters']['date_period'][$this->_tpl_vars['key']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'autocomplete_var_type' => $this->_tpl_vars['autocomplete_var_type'],'autocomplete' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'][$this->_tpl_vars['values_operator_group']][$this->_tpl_vars['values_operator']]['options'],'options' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'][$this->_tpl_vars['values_operator_group']][$this->_tpl_vars['values_operator']]['options'],'optgroups' => $this->_tpl_vars['search_defs'][$this->_tpl_vars['filter']]['compare_options'][$this->_tpl_vars['values_operator_group']][$this->_tpl_vars['values_operator']]['opt_groups'],'show_inactive_options' => true)));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_4" style="width: 30px;">
            <?php if ($this->_foreach['i']['iteration'] == ($this->_foreach['i']['iteration'] == $this->_foreach['i']['total'])): ?>
            &nbsp;
            <?php else: ?>
              <select id="logical_operator_<?php echo $this->_tpl_vars['current_item']; ?>
" name="logical_operator[<?php echo $this->_tpl_vars['current_item']-1; ?>
]"
                      class="selbox logical_operator short" onfocus="highlight(this)" onblur="unhighlight(this)"
                      <?php if ($this->_tpl_vars['disabled']): ?>disabled="disabled"<?php endif; ?>>
              <option value="AND" <?php if ($this->_tpl_vars['session_filters']['logical_operator'][$this->_tpl_vars['key']] == 'AND'): ?>selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['and']; ?>
</option>
              <option value="OR" <?php if ($this->_tpl_vars['session_filters']['logical_operator'][$this->_tpl_vars['key']] == 'OR'): ?>selected="selected"<?php endif; ?>><?php echo $this->_config[0]['vars']['or']; ?>
</option>
              </select>
            <?php endif; ?>
          </td>
          <td id="search_container_<?php echo $this->_tpl_vars['current_item']; ?>
_5">
            <?php if ($this->_tpl_vars['current_item'] == 1 && ! $this->_tpl_vars['view_mode']): ?>
            <div class="t_buttons">
              <div id="search_container_plusButton" onclick="processSearchDef('add');" data-tooltip-content="<?php echo $this->_config[0]['vars']['add_filter']; ?>
"><div class="t_plus"></div></div>
              <div id="search_container_minusButton"<?php if (empty ( $this->_tpl_vars['session_filters']['search_fields'] ) || count ( $this->_tpl_vars['session_filters']['search_fields'] ) <= 1): ?> class="disabled"<?php endif; ?> onclick="processSearchDef('remove');" data-tooltip-content="<?php echo $this->_config[0]['vars']['remove_filter']; ?>
"><div class="t_minus"></div></div>
            </div>
            <?php endif; ?>
          </td>
        </tr>
        <?php endif; ?>
      <?php endforeach; else: ?>
        <tr id="search_container_1" class="nz-advancedSearch-filter">
          <td id="search_container_1_0">
            <?php if ($this->_tpl_vars['view_mode']): ?>
            1
            <?php else: ?>
            <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
/small/delete.png" height="16" width="16" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" class="hide_row" style="visibility: hidden;" onclick="processSearchDef('hide', '1');" />
            <a href="javascript: void(0);" onclick="javascript: processSearchDef('disable', '1');" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['deactivate'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">1</a>
            <?php endif; ?>
          </td>
          <td id="search_container_1_1" style="width: 150px;">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 0,'disabled' => $this->_tpl_vars['disabled'],'name' => 'search_fields','custom_class' => 'search_fields','custom_id' => 'search_fields','index' => 1,'value' => '','options' => $this->_tpl_vars['search_fields']['basic_vars'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_hidden.html", 'smarty_include_vars' => array('standalone' => true,'name' => 'search_fields_prev','custom_class' => 'search_fields_prev','custom_id' => 'search_fields_prev','index' => 1,'value' => '')));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
          <td id="search_container_1_2" style="width: 150px;">
          </td>
          <td id="search_container_1_3" style="white-space: nowrap!important; width: 205px;">
          </td>
          <td id="search_container_1_4" style="width: 30px;">
          </td>
          <td id="search_container_1_5">
            <?php if (! $this->_tpl_vars['view_mode']): ?>
            <div class="t_buttons">
              <div id="search_container_plusButton" onclick="processSearchDef('add');" data-tooltip-content="<?php echo $this->_config[0]['vars']['add_filter']; ?>
"><div class="t_plus"></div></div>
              <div id="search_container_minusButton"<?php if (empty ( $this->_tpl_vars['session_filters']['search_fields'] ) || count ( $this->_tpl_vars['session_filters']['search_fields'] ) <= 1): ?> class="disabled"<?php endif; ?> onclick="processSearchDef('remove');" data-tooltip-content="<?php echo $this->_config[0]['vars']['remove_filter']; ?>
"><div class="t_minus"></div></div>
            </div>
            <?php endif; ?>
          </td>
        </tr>
      <?php endif; unset($_from); ?>
      </table>
    </div>
    <!-- SORT, SAVED FILTERS, RPP -->
    <table border="0" cellpadding="0" cellspacing="0" style="width: 100%">
      <tr>
        <td style="min-width: 33%; vertical-align: top;">
          <div class="t_caption3_title t_caption3">
            <?php echo $this->_config[0]['vars']['sort']; ?>

            <?php if (! $this->_tpl_vars['view_mode']): ?>
            <div class="t_buttons">
              <div id="search_sort_container_plusButton" onclick="addSortCondition('search_sort_container',1)" data-tooltip-content="<?php echo $this->_config[0]['vars']['add_sort']; ?>
"><div class="t_plus"></div></div>
              <div id="search_sort_container_minusButton"<?php if (empty ( $this->_tpl_vars['session_filters']['sort'] ) || count ( $this->_tpl_vars['session_filters']['sort'] ) <= 1): ?> class="disabled"<?php endif; ?> onclick="removeSortCondition('search_sort_container')" data-tooltip-content="<?php echo $this->_config[0]['vars']['remove_sort']; ?>
"><div class="t_minus"></div></div>
          </div>
          <?php endif; ?>
          </div>
          <table border="0" cellpadding="5" cellspacing="0" id="search_sort_container">
            <tr>
              <?php $_from = $this->_tpl_vars['session_filters']['sort']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ii'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ii']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['sort']):
        $this->_foreach['ii']['iteration']++;
?>
              <td style="width: 100px;<?php if ($this->_foreach['ii']['iteration'] != ($this->_foreach['ii']['iteration'] <= 1)): ?> border-left: solid 1px #AAAAAA;<?php endif; ?>">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'sort','custom_id' => 'sort','index' => $this->_foreach['ii']['iteration'],'sequences' => '','value' => $this->_tpl_vars['sort'],'optgroup_label_source' => 'config','optgroups' => $this->_tpl_vars['system_fields']['sort'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
              <?php endforeach; else: ?>
              <td style="width: 100px;">
                <?php ob_start(); ?><?php echo $this->_tpl_vars['alias']; ?>
.added<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('default_sort', ob_get_contents());ob_end_clean(); ?>
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'sort','custom_id' => 'sort','index' => 1,'sequences' => '','value' => $this->_tpl_vars['default_sort'],'optgroup_label_source' => 'config','optgroups' => $this->_tpl_vars['system_fields']['sort'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
              <?php endif; unset($_from); ?>
            </tr>
            <tr>
              <?php $_from = $this->_tpl_vars['session_filters']['order']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['ii'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['ii']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['order']):
        $this->_foreach['ii']['iteration']++;
?>
              <td style="width: 100px;<?php if ($this->_foreach['ii']['iteration'] != ($this->_foreach['ii']['iteration'] <= 1)): ?> border-left: solid 1px #AAAAAA;<?php endif; ?>">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'order','custom_id' => 'order','index' => $this->_foreach['ii']['iteration'],'sequences' => '','value' => $this->_tpl_vars['order'],'options' => $this->_tpl_vars['system_fields']['order'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
              <?php endforeach; else: ?>
              <td style="width: 100px;">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'required' => 1,'disabled' => $this->_tpl_vars['disabled'],'name' => 'order','custom_id' => 'order','index' => 1,'sequences' => '','value' => '','options' => $this->_tpl_vars['system_fields']['order'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
              <?php endif; unset($_from); ?>
            </tr>
          </table>
        </td>
        <?php if (! $this->_tpl_vars['view_mode']): ?>
        <td style="border-left: 1px solid #AAAAAA; min-width: 33%; vertical-align: top;">
          <div class="t_caption3_title t_caption3" style="white-space: nowrap;">
            <?php echo $this->_config[0]['vars']['save_load_filters']; ?>

          </div>
          <table border="0" cellpadding="5" cellspacing="0" id="search_save_container">
            <tr>
              <td nowrap="nowrap"><label for="save_filter_name"><?php echo $this->_config[0]['vars']['save_search']; ?>
:</label></td>
              <td style="width: 200px;">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_combobox.html", 'smarty_include_vars' => array('disabled' => $this->_tpl_vars['disabled'],'standalone' => true,'name' => 'save_filter_name','custom_id' => 'save_filter_name','value' => '','sequences' => '','width' => '200','options' => $this->_tpl_vars['saved_filters'],'label' => $this->_config[0]['vars']['name'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
              <td class="nowrap">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_checkbox.html", 'smarty_include_vars' => array('disabled' => $this->_tpl_vars['disabled'],'standalone' => true,'name' => 'save_as_action','custom_id' => 'save_as_action','option_value' => '1','label' => $this->_config[0]['vars']['save_as_action'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
              <td>
                <button type="button" id="save_filter_button" class="nz-icon-button" title="<?php echo $this->_config[0]['vars']['save']; ?>
">save</button>
              </td>
            </tr>
            <tr>
              <td nowrap="nowrap"><label for="filter_name"><?php echo $this->_config[0]['vars']['load_search']; ?>
:</label></td>
              <td style="width: 200px;">
                <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "input_dropdown.html", 'smarty_include_vars' => array('standalone' => true,'disabled' => $this->_tpl_vars['disabled'],'name' => 'filter_name','custom_id' => 'filter_name','value' => '','sequences' => '','width' => '200','options' => $this->_tpl_vars['saved_filters'],'label' => $this->_config[0]['vars']['filter'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
              </td>
              <td nowrap="nowrap" colspan="2">
                <button type="button" id="load_filter_button" class="nz-icon-button" title="<?php echo $this->_config[0]['vars']['config_load']; ?>
">refresh</button>
                <button type="button" id="delete_filter_button" class="nz-icon-button" title="<?php echo $this->_config[0]['vars']['delete']; ?>
">delete</button>
              </td>
            </tr>
          </table>
        </td>
        <?php endif; ?>
      </tr>
    </table>
  </div>