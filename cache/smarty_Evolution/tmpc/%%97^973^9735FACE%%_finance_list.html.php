<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_list.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_list.html', 5, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_list.html', 10, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_finance_list.html', 56, false),)), $this); ?>
<?php if ($this->_tpl_vars['contract']->get('invoices_templates')): ?>
<?php $this->assign('invoices_templates', $this->_tpl_vars['contract']->get('invoices_templates')); ?>
<br />
<div class="floatr hright">
  <span class="pointer" onclick="toggleTableRowsAll('finance_table', 'expand')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['expand_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span> |
  <span class="pointer" onclick="toggleTableRowsAll('finance_table', 'collapse')"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['collapse_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
</div>
<br />

<table class="t_table t_list" border="0" cellspacing="0" id="finance_table" width="<?php echo ((is_array($_tmp=@$this->_tpl_vars['var']['t_width'])) ? $this->_run_mod_handler('default', true, $_tmp, '100%') : smarty_modifier_default($_tmp, '100%')); ?>
">
  <tr>
    <th class="t_border t_top_border"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['num'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</th>
    <th class="t_border t_top_border"><?php echo $this->_config[0]['vars']['contracts_type']; ?>
</th>
    <th class="t_border t_top_border"><?php echo $this->_config[0]['vars']['contracts_recurrent']; ?>
</th>
    <th class="t_border t_top_border"><?php echo $this->_config[0]['vars']['total']; ?>
</th>
    <th class="t_border t_top_border"><?php echo $this->_config[0]['vars']['contracts_next_issue_date']; ?>
</th>
    <th class="t_border t_top_border"><?php echo $this->_config[0]['vars']['contracts_date_of_payment_all']; ?>
</th>
    <th class="t_top_border">&nbsp;</th>
  </tr>
<?php if ($this->_tpl_vars['model_id']): ?>
  <?php $this->assign('temp_id', $this->_tpl_vars['model_id']); ?>
<?php else: ?>
  <?php $this->assign('temp_id', $this->_tpl_vars['contract']->get('id')); ?>
<?php endif; ?>
<?php $_from = $this->_tpl_vars['invoices_templates']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['jj'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['jj']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['invoices_template']):
        $this->_foreach['jj']['iteration']++;
?>
  <?php $this->assign('invoice_id', $this->_tpl_vars['invoices_template']->get('id')); ?>
  <?php $this->assign('finance_var', $this->_tpl_vars['invoices_template']->get('grouping_table_2')); ?>
  <?php ob_start(); ?><?php echo $this->_tpl_vars['temp_id']; ?>
,<?php echo $this->_tpl_vars['invoice_id']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('finance_change', ob_get_contents());ob_end_clean(); ?>
  <?php $this->assign('layout_cookie_var', "invoices_template_".($this->_tpl_vars['invoice_id'])."_box"); ?>
  <tr id="invoices_template_<?php echo $this->_tpl_vars['invoice_id']; ?>
" class="t_top_border<?php if ($this->_tpl_vars['finance_var']['id'] == $this->_tpl_vars['invoice_id']): ?> t_selected_row_for_edit<?php endif; ?>">
    <td class="pointer nopadding t_border" style="white-space: nowrap; width: 40px;" onclick="toggleTemplates($('invoices_template_<?php echo $this->_tpl_vars['invoice_id']; ?>
'))">
      <table cellspacing="0" cellpadding="0" class="t_table t_borderless">
        <tr>
          <td><div class="switch_<?php if ($_COOKIE[$this->_tpl_vars['layout_cookie_var']] == 'off'): ?>expand<?php else: ?>collapse<?php endif; ?>">&nbsp;</div><div class="hright"><?php echo $this->_foreach['jj']['iteration']; ?>
</div></td>
        </tr>
      </table>
    </td>
    <td class="t_border"><?php if ($this->_tpl_vars['invoices_template']->get('type') == @PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL): ?><strong><?php endif; ?><?php echo $this->_tpl_vars['invoices_template']->get('type_name'); ?>
<?php if ($this->_tpl_vars['invoices_template']->get('proforma')): ?> / <?php echo $this->_config[0]['vars']['contracts_proforma']; ?>
<?php endif; ?><?php if ($this->_tpl_vars['invoices_template']->get('type') == @PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL): ?></strong><?php endif; ?></td>
    <td class="t_border"><?php if ($this->_tpl_vars['invoices_template']->get('recurrent')): ?><?php echo $this->_config[0]['vars']['yes']; ?>
<?php else: ?><?php echo $this->_config[0]['vars']['no']; ?>
<?php endif; ?></td>
    <td class="t_border"><?php echo $this->_tpl_vars['invoices_template']->get('total_with_vat'); ?>
 <?php echo $this->_tpl_vars['invoices_template']->get('currency'); ?>
</td>
    <td class="t_border">
    <?php if ($this->_tpl_vars['invoices_template']->get('type') == @PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL): ?>
      <?php if ($this->_tpl_vars['invoices_template']->get('status') == 'opened'): ?>
        <?php if ($this->_tpl_vars['contract']->get('subtype') == 'contract' && $this->_tpl_vars['contract']->get('status') == 'closed' && $this->_tpl_vars['currentUser']->checkRights('contracts','addinvoice')): ?>
          <img src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
issue_invoice.png" alt="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['issue'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" border="0" onclick="issueFinal(<?php echo $this->_tpl_vars['contract']->get('id'); ?>
);" style="cursor:pointer;" />
        <?php else: ?>
          -
        <?php endif; ?>
      <?php else: ?>
        <?php echo $this->_config[0]['vars']['contracts_no_next_issue_date']; ?>

      <?php endif; ?>
    <?php elseif ($this->_tpl_vars['invoices_template']->get('recurrent')): ?>
      <?php if ($this->_tpl_vars['invoices_template']->get('status') == 'finished'): ?>
        <?php echo $this->_config[0]['vars']['contracts_no_next_issue_date']; ?>

      <?php else: ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['invoices_template']->get('next_issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

      <?php endif; ?>
    <?php else: ?>
      <?php if ($this->_tpl_vars['invoices_template']->get('next_issue_date') && $this->_tpl_vars['invoices_template']->get('next_issue_date') != '0000-00-00'): ?>
        <?php echo ((is_array($_tmp=$this->_tpl_vars['invoices_template']->get('next_issue_date'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_short']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_short'])); ?>

      <?php endif; ?>
      <?php if ($this->_tpl_vars['invoices_template']->get('issue_date_formula')): ?>
        <?php $this->assign('formula_index', $this->_tpl_vars['invoices_template']->get('issue_date_formula')); ?>
        <?php $this->assign('formulas', $this->_tpl_vars['contract']->get('formulas')); ?>
        (<?php echo $this->_tpl_vars['formulas'][$this->_tpl_vars['formula_index']]['label']; ?>
)
      <?php endif; ?>
    <?php endif; ?>
    </td>
    <td class="t_border">
    <?php if ($this->_tpl_vars['invoices_template']->get('recurrent') && $this->_tpl_vars['invoices_template']->get('status') == 'finished' || $this->_tpl_vars['invoices_template']->get('type') == @PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL): ?>
      -
    <?php else: ?>
      <?php echo $this->_tpl_vars['invoices_template']->get('date_of_payment_count'); ?>

      <?php ob_start(); ?><?php echo $this->_tpl_vars['invoices_template']->get('date_of_payment_period_type'); ?>
_days<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('lbl', ob_get_contents());ob_end_clean(); ?>
      <?php echo ((is_array($_tmp=@$this->_config[0]['vars'][$this->_tpl_vars['lbl']])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['lbl']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['lbl'])); ?>

      <?php echo $this->_config[0]['vars']['days']; ?>

      <?php $_from = $this->_tpl_vars['pay_after_types']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['pat']):
?>
        <?php if ($this->_tpl_vars['pat']['option_value'] == $this->_tpl_vars['invoices_template']->get('date_of_payment_point')): ?>
          <?php echo $this->_tpl_vars['pat']['label']; ?>

        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>
    </td>
    <td nowrap="nowrap" style="width: 60px;">
    <?php if ($this->_tpl_vars['invoices_template']->get('type') != @PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL): ?>
      <?php if (! $this->_tpl_vars['hide_edit_delete']): ?>
        <?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/edit.png" border="0" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onclick="'; ?><?php if ($this->_tpl_vars['invoices_template']->get('invoices') && ! $this->_tpl_vars['invoices_template']->get('recurrent') || $this->_tpl_vars['invoices_template']->get('recurrent') && $this->_tpl_vars['invoices_template']->get('status') == 'finished'): ?><?php echo 'alert(i18n[\'messages\'][\'error_no_access_to_action\']);return false;" class="pointer dimmed"'; ?><?php else: ?><?php echo 'editFinance('; ?><?php echo $this->_tpl_vars['finance_change']; ?><?php echo ',this);toggleTemplateRow($(\'invoices_template_'; ?><?php echo $this->_tpl_vars['invoice_id']; ?><?php echo '\'),\'expand\')" class="pointer"'; ?><?php endif; ?><?php echo ' id="img_edit_'; ?><?php echo $this->_tpl_vars['invoices_template']->get('id'); ?><?php echo '" />&nbsp;<input type="image" src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'small/delete.png" alt="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" title="'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['delete'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '" onclick="'; ?><?php if ($this->_tpl_vars['invoices_template']->get('invoices')): ?><?php echo 'alert(i18n[\'messages\'][\'error_no_access_to_action\']);return false;" class="pointer dimmed"'; ?><?php else: ?><?php echo 'return confirmAction(\'delete\', function(el) '; ?>{<?php echo ' delFinance('; ?><?php echo $this->_tpl_vars['finance_change']; ?><?php echo ',el.form); '; ?>}<?php echo ', this);" class="pointer"'; ?><?php endif; ?><?php echo ' />'; ?>

      <?php endif; ?>
    <?php endif; ?>
    </td>
  </tr>
  <tr id="invoices_template_<?php echo $this->_tpl_vars['invoice_id']; ?>
_box"<?php if ($_COOKIE[$this->_tpl_vars['layout_cookie_var']] == 'off'): ?> style="display: none"<?php endif; ?> class="invoices_template_box t_top_border">
    <td colspan="9" id="cell_<?php echo $this->_tpl_vars['invoice_id']; ?>
" class="t_grouping_table_container">
      <table cellpadding="0" cellspacing="0" width="100%" border="0">
    <?php if ($this->_tpl_vars['hide_edit_delete']): ?>
      <tr>
        <td colspan="3" class="t_table">
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => ($this->_tpl_vars['templatesDir'])."_finance_view.html", 'smarty_include_vars' => array()));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
        </td>
      </tr>
    <?php endif; ?>
        <?php $this->assign('view_type_name', "_gt2_view.html"); ?>
        <?php $this->assign('table', $this->_tpl_vars['finance_var']); ?>
        <tr>
          <td colspan="3" class="t_table">
            <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => $this->_tpl_vars['view_type_name'], 'smarty_include_vars' => array('var' => $this->_tpl_vars['finance_var'],'standalone' => false,'var_id' => $this->_tpl_vars['finance_var']['id'],'name' => $this->_tpl_vars['finance_var']['name'],'custom_id' => $this->_tpl_vars['varfinance_']['custom_id'],'label' => $this->_tpl_vars['finance_var']['label'],'help' => $this->_tpl_vars['finance_var']['help'],'value' => $this->_tpl_vars['finance_var']['value'],'options' => $this->_tpl_vars['finance_var']['options'],'optgroups' => $this->_tpl_vars['finance_var']['optgroups'],'option_value' => $this->_tpl_vars['finance_var']['option_value'],'first_option_label' => $this->_tpl_vars['finance_var']['first_option_label'],'onclick' => $this->_tpl_vars['finance_var']['onclick'],'on_change' => $this->_tpl_vars['finance_var']['on_change'],'check' => $this->_tpl_vars['finance_var']['check'],'scrollable' => $this->_tpl_vars['finance_var']['scrollable'],'calculate' => $this->_tpl_vars['finance_var']['calculate'],'readonly' => $this->_tpl_vars['finance_var']['readonly'],'source' => $this->_tpl_vars['finance_var']['source'],'hidden' => $this->_tpl_vars['finance_var']['hidden'],'required' => $this->_tpl_vars['finance_var']['required'],'disabled' => $this->_tpl_vars['finance_var']['disabled'],'view_mode' => $this->_tpl_vars['finance_var']['view_mode'],'hide_label' => 1,'thumb_width' => $this->_tpl_vars['finance_var']['thumb_width'],'thumb_height' => $this->_tpl_vars['finance_var']['thumb_height'],'show_placeholder' => $this->_tpl_vars['finance_var']['show_placeholder'],'text_align' => $this->_tpl_vars['finance_var']['text_align'],'custom_class' => $this->_tpl_vars['finance_var']['custom_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
          </td>
        </tr>
      </table>
    </td>
  </tr>
<?php endforeach; endif; unset($_from); ?>
</table>
<script type="text/javascript">
    onReady().then(() => {set_opened_template();});
</script>
<?php endif; ?>