<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:46
         compiled from input_autocompleter.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_autocompleter.html', 40, false),array('modifier', 'replace', 'input_autocompleter.html', 118, false),array('modifier', 'escape', 'input_autocompleter.html', 236, false),array('modifier', 'strip_tags', 'input_autocompleter.html', 237, false),array('function', 'help', 'input_autocompleter.html', 73, false),array('function', 'array', 'input_autocompleter.html', 119, false),array('function', 'math', 'input_autocompleter.html', 165, false),array('function', 'uniqid', 'input_autocompleter.html', 223, false),array('function', 'json', 'input_autocompleter.html', 295, false),)), $this); ?>

<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>
<?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['height'] && ! preg_match ( '#%$#' , $this->_tpl_vars['height'] )): ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo 'px'; ?><?php elseif ($this->_tpl_vars['height']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['height']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('height', ob_get_contents());ob_end_clean(); ?>

<?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'searchable' && ! ((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
<?php if (( preg_match ( '/combobox/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['buttons'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) || ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['combobox'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) && ! preg_match ( '/combobox/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['buttons_hide'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) && ! preg_match ( '/combobox/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete_buttons_hide'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) )): ?>
    <?php $this->assign('include_combobox_button', 1); ?>
<?php endif; ?>
<?php endif; ?>

<?php if (! ((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
<tr<?php if (((is_array($_tmp=@$this->_tpl_vars['hidden'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> style="display: none"<?php endif; ?> class="nz-form-input">
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
        <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
  </td>

    <td<?php if (((is_array($_tmp=@$this->_tpl_vars['required'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td>
<?php endif; ?>
<span class="nz-form-input-wrapper<?php if (((is_array($_tmp=@$this->_tpl_vars['include_combobox_button'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> nz-ac-has-combobox<?php endif; ?>">
        <?php if (! ((is_array($_tmp=@$this->_tpl_vars['value_autocomplete'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ( ((is_array($_tmp=@$this->_tpl_vars['value_code'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ((is_array($_tmp=@$this->_tpl_vars['value_name'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) )): ?>
      <?php ob_start(); ?><?php if (((is_array($_tmp=@$this->_tpl_vars['value_code'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['value_code']; ?>
] <?php endif; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['value_name'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['value_name']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('value_autocomplete', ob_get_contents());ob_end_clean(); ?>
    <?php endif; ?>

        <?php $this->assign('var_name', ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name']))); ?>

        <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'basic' && $this->_tpl_vars['basic_vars_additional_settings'][$this->_tpl_vars['var_name']]['autocomplete']): ?>
      <?php $_from = $this->_tpl_vars['basic_vars_additional_settings'][$this->_tpl_vars['var_name']]; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['setting_key'] => $this->_tpl_vars['setting_value']):
?>
        <?php if (in_array ( $this->_tpl_vars['setting_key'] , array ( 'autocomplete' , 'readonly' , 'hidden' , 'width' , 'show_placeholder' , 'custom_class' , 'text_align' ) ) && $this->_tpl_vars['setting_value'] !== '' && $this->_tpl_vars['setting_value'] !== null): ?>
          <?php $this->assign($this->_tpl_vars['setting_key'], $this->_tpl_vars['setting_value']); ?>
        <?php endif; ?>
      <?php endforeach; endif; unset($_from); ?>
    <?php endif; ?>

    <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'basic' || ( ((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'searchable' && ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['search_by_id'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) )): ?>
      <input type="hidden"
             name="<?php echo $this->_tpl_vars['name']; ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>"
             id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
             value="<?php echo ((is_array($_tmp=@$this->_tpl_vars['value'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')); ?>
" />

      <?php ob_start(); ?><?php echo $this->_tpl_vars['name']; ?>
_autocomplete<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_name', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_autocomplete<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_id', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php if (((is_array($_tmp=@$this->_tpl_vars['value_autocomplete'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['value_autocomplete']; ?>
<?php elseif (((is_array($_tmp=@$this->_tpl_vars['value'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['value']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_value', ob_get_contents());ob_end_clean(); ?>
      <?php $this->assign('value_id', $this->_tpl_vars['value']); ?>

      <?php if (! ((is_array($_tmp=@$this->_tpl_vars['autocomplete'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ((is_array($_tmp=@$this->_tpl_vars['autocomplete_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
        <?php if (preg_match ( '#^([^_]*)_(.*)#' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete_type'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')) , $this->_tpl_vars['act_matches'] )): ?>
          <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['act_matches'][1]; ?>
&<?php echo $this->_tpl_vars['controller_param']; ?>
=<?php echo $this->_tpl_vars['act_matches'][2]; ?>
&<?php echo $this->_tpl_vars['act_matches'][2]; ?>
=ajax_select<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('autocomplete_url', ob_get_contents());ob_end_clean(); ?>
        <?php else: ?>
          <?php ob_start(); ?><?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=<?php echo $this->_tpl_vars['autocomplete_type']; ?>
&<?php echo $this->_tpl_vars['autocomplete_type']; ?>
=ajax_select<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('autocomplete_url', ob_get_contents());ob_end_clean(); ?>
        <?php endif; ?>
        <?php if (! ((is_array($_tmp=@$this->_tpl_vars['view_mode'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php $this->assign('view_mode', 'link'); ?><?php endif; ?>
        <?php ob_start(); ?><?php if ($this->_tpl_vars['view_mode'] == 'link'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['autocomplete_url'])) ? $this->_run_mod_handler('replace', true, $_tmp, 'ajax_select', 'view&view=') : smarty_modifier_replace($_tmp, 'ajax_select', 'view&view=')); ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('view_mode_url', ob_get_contents());ob_end_clean(); ?>
        <?php echo smarty_function_array(array('assign' => 'autocomplete','type' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'url' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete_url'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'min_chars' => ((is_array($_tmp=@$this->_tpl_vars['min_chars'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'buttons' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete_buttons'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'buttons_hide' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete_buttons_hide'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'execute_after' => ((is_array($_tmp=@$this->_tpl_vars['execute_after'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'filters' => ((is_array($_tmp=@$this->_tpl_vars['filters_array'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'addquick_type' => ((is_array($_tmp=@$this->_tpl_vars['addquick_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'stop_customer_details' => ((is_array($_tmp=@$this->_tpl_vars['stop_customer_details'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'suggestions' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete_suggestions'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'fill_options' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete_fill_options'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'view_mode' => ((is_array($_tmp=@$this->_tpl_vars['view_mode'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)),'view_mode_url' => ((is_array($_tmp=@$this->_tpl_vars['view_mode_url'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))), $this);?>

      <?php else: ?>
        <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'searchable'): ?>
          <?php echo smarty_function_array(array('assign' => 'autocomplete','fill_options' => ''), $this);?>

        <?php elseif (((is_array($_tmp=@$this->_tpl_vars['filters_array'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
          <?php echo smarty_function_array(array('assign' => 'autocomplete','filters' => $this->_tpl_vars['filters_array']), $this);?>

        <?php endif; ?>
      <?php endif; ?>
            <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'basic'): ?>
                <?php echo smarty_function_array(array('assign' => 'autocomplete','var_type' => $this->_tpl_vars['autocomplete_var_type']), $this);?>

      <?php endif; ?>
    <?php else: ?>
      <?php ob_start(); ?><?php echo $this->_tpl_vars['name']; ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_name', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if (((is_array($_tmp=@$this->_tpl_vars['index'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_id', ob_get_contents());ob_end_clean(); ?>
      <?php ob_start(); ?><?php if (((is_array($_tmp=@$this->_tpl_vars['value_autocomplete'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo $this->_tpl_vars['value_autocomplete']; ?>
<?php elseif ($this->_tpl_vars['value']): ?><?php echo $this->_tpl_vars['value']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('visible_value', ob_get_contents());ob_end_clean(); ?>
    <?php endif; ?>

    <?php $this->assign('buttons_count', 0); ?>
    <?php $this->assign('include_clear_button', 0); ?>
    <?php $this->assign('include_add_button', 0); ?>
    <?php $this->assign('include_search_button', 0); ?>
    <?php $this->assign('include_refresh_button', 0); ?>
    <?php $this->assign('include_report_button', 0); ?>
    <?php $this->assign('include_edit_button', 0); ?>
    <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'searchable' && ! $this->_tpl_vars['readonly']): ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'filter' || ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'search' || ( preg_match ( '/clear/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['buttons'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) || $this->_tpl_vars['autocomplete']['clear'] ) && ! preg_match ( '/clear/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/clear/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php $this->assign('include_clear_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if (( ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'filter' && ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'search' ) && ( preg_match ( '/search/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['buttons'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) || ! $this->_tpl_vars['autocomplete']['buttons'] ) && ! preg_match ( '/search/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/search/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php $this->assign('include_search_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if (( preg_match ( '/refresh/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['buttons'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) || ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['refresh'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) && ! preg_match ( '/refresh/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['buttons_hide'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) ) && ! preg_match ( '/refresh/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete_buttons_hide'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) )): ?>
        <?php $this->assign('include_refresh_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete']['report'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ! preg_match ( '/report/' , ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['buttons_hide'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) )): ?>
        <?php $this->assign('include_report_button', 1); ?>
        <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

      <?php endif; ?>
      <?php if (( ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'filter' && ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'search' ) && ( ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'customers' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<contactpersons>' , $this->_tpl_vars['autocomplete']['filters'] ) ) || $this->_tpl_vars['autocomplete']['type'] == 'projects' || $this->_tpl_vars['autocomplete']['type'] == 'nomenclatures' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<customer_trademark>' , $this->_tpl_vars['autocomplete']['filters'] ) ) ) && ( preg_match ( '/add/' , $this->_tpl_vars['autocomplete']['buttons'] ) || $this->_tpl_vars['autocomplete']['add'] ) && ! preg_match ( '/add/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/add/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['autocomplete']['type'],'add')): ?>
          <?php $this->assign('include_add_button', 1); ?>
        <?php endif; ?>
        <?php if (((is_array($_tmp=@$this->_tpl_vars['include_add_button'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'basic'): ?>
          <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

        <?php endif; ?>
      <?php endif; ?>
      <?php if (( ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'filter' && ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'search' ) && ( ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'customers' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<contactpersons>' , $this->_tpl_vars['autocomplete']['filters'] ) ) || $this->_tpl_vars['autocomplete']['type'] == 'nomenclatures' && ! ( ! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<customer_trademark>' , $this->_tpl_vars['autocomplete']['filters'] ) ) ) && ( preg_match ( '/edit/' , $this->_tpl_vars['autocomplete']['buttons'] ) || $this->_tpl_vars['autocomplete']['edit'] ) && ! preg_match ( '/edit/' , $this->_tpl_vars['autocomplete']['buttons_hide'] ) && ! preg_match ( '/edit/' , $this->_tpl_vars['autocomplete_buttons_hide'] )): ?>
        <?php if ($this->_tpl_vars['currentUser']->checkRights($this->_tpl_vars['autocomplete']['type'],'edit')): ?>
          <?php $this->assign('include_edit_button', 1); ?>
        <?php endif; ?>
        <?php if (((is_array($_tmp=@$this->_tpl_vars['include_edit_button'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'basic'): ?>
          <?php echo smarty_function_math(array('equation' => "x + 1",'x' => $this->_tpl_vars['buttons_count'],'assign' => 'buttons_count'), $this);?>

        <?php endif; ?>
      <?php endif; ?>
    <?php endif; ?>

    <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete']['button_menu'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ((is_array($_tmp=@$this->_tpl_vars['buttons_count'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) > 0 && ( ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'filter' && ((is_array($_tmp=@$this->_tpl_vars['available_action']['action'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) != 'search' )): ?>
      <?php $this->assign('button_menu', 1); ?>
      <?php $this->assign('buttons_count', 1); ?>
      <?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete_var_type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'basic' && ((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ! ( $this->_tpl_vars['width'] && preg_match ( '#^(\d+%|)$#' , $this->_tpl_vars['width'] ) ) && empty ( $this->_tpl_vars['basic_vars_additional_settings'][$this->_tpl_vars['var_name']]['width'] )): ?>
        <?php $this->assign('width', 222); ?>
      <?php endif; ?>
    <?php else: ?>
      <?php $this->assign('button_menu', 0); ?>
    <?php endif; ?>

    <?php ob_start(); ?><?php echo ''; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['standalone'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?><?php echo ''; ?><?php if (preg_match ( '#^(\d+%|)$#' , ((is_array($_tmp=@$this->_tpl_vars['width'])) ? $this->_run_mod_handler('default', true, $_tmp, '') : smarty_modifier_default($_tmp, '')) )): ?><?php echo '100%'; ?><?php else: ?><?php echo ''; ?><?php echo smarty_function_math(array('equation' => "x - (y*z)",'x' => $this->_tpl_vars['width'],'y' => $this->_tpl_vars['buttons_count'],'z' => 22), $this);?><?php echo 'px'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('width', ob_get_contents());ob_end_clean(); ?>

        <?php echo smarty_function_uniqid(array('assign' => 'uniqID','more_entropy' => true), $this);?>

        <?php $this->assign('uniqID', ((is_array($_tmp=$this->_tpl_vars['uniqID'])) ? $this->_run_mod_handler('replace', true, $_tmp, '.', '') : smarty_modifier_replace($_tmp, '.', ''))); ?>

    <input
       type="text"
       class="txtbox autocompletebox<?php if (! ((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && ! ((is_array($_tmp=@$this->_tpl_vars['hidden'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> autocomplete_<?php echo ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['type'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)); ?>
<?php endif; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> readonly<?php if (((is_array($_tmp=@$this->_tpl_vars['autocomplete']['view_mode'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) == 'link' && ((is_array($_tmp=@$this->_tpl_vars['autocomplete']['view_mode_url'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> hidden<?php endif; ?><?php endif; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['include_combobox_button'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> combobox<?php endif; ?><?php if (((is_array($_tmp=@$this->_tpl_vars['custom_class'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> <?php echo $this->_tpl_vars['custom_class']; ?>
<?php endif; ?>"
       name="<?php echo $this->_tpl_vars['visible_name']; ?>
"
       id="<?php echo $this->_tpl_vars['visible_id']; ?>
"
       value="<?php echo ((is_array($_tmp=$this->_tpl_vars['visible_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
       title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
       style="<?php if ($this->_tpl_vars['hidden']): ?>display: none;<?php elseif (( $this->_tpl_vars['width'] )): ?>width: <?php echo $this->_tpl_vars['width']; ?>
;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
;<?php endif; ?>"
       <?php if (((is_array($_tmp=@$this->_tpl_vars['show_placeholder'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
         placeholder="<?php if ($this->_tpl_vars['show_placeholder'] === 'label'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php elseif ($this->_tpl_vars['show_placeholder'] === 'help'): ?><?php echo ((is_array($_tmp=$this->_tpl_vars['help'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_tpl_vars['show_placeholder'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>"
       <?php endif; ?>
       <?php if (((is_array($_tmp=@$this->_tpl_vars['onkeydown'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?>
         onkeydown="<?php if ($this->_tpl_vars['onkeydown']): ?><?php echo $this->_tpl_vars['onkeydown']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeydown'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeydown']; ?>
;<?php endif; ?>"
       <?php endif; ?>
       <?php if (((is_array($_tmp=@$this->_tpl_vars['restrict'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
         onkeypress="return changeKey(this, event, <?php echo $this->_tpl_vars['restrict']; ?>
);"
       <?php elseif ($this->_tpl_vars['onkeypress'] || ! empty ( $this->_tpl_vars['js_methods']['onkeypress'] )): ?>
         onkeypress="<?php if ($this->_tpl_vars['onkeypress']): ?><?php echo $this->_tpl_vars['onkeypress']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeypress'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeypress']; ?>
;<?php endif; ?>"
       <?php endif; ?>
       <?php if (((is_array($_tmp=@$this->_tpl_vars['onkeyup'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) || ! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?>
         onkeyup="<?php if ($this->_tpl_vars['onkeyup']): ?><?php echo $this->_tpl_vars['onkeyup']; ?>
;<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onkeyup'] )): ?><?php echo $this->_tpl_vars['js_methods']['onkeyup']; ?>
;<?php endif; ?>"
       <?php endif; ?>
       onfocus="<?php if (! empty ( $this->_tpl_vars['js_methods']['onfocus'] )): ?><?php echo $this->_tpl_vars['js_methods']['onfocus']; ?>
;<?php endif; ?>"
       onblur="<?php if (! empty ( $this->_tpl_vars['js_methods']['onblur'] )): ?><?php echo $this->_tpl_vars['js_methods']['onblur']; ?>
;<?php endif; ?><?php if (! $this->_tpl_vars['readonly']): ?>cancelAutocompleter(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);<?php endif; ?>"
       onclick="<?php if ($this->_tpl_vars['include_combobox_button']): ?>toggleAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);<?php endif; ?><?php if (! empty ( $this->_tpl_vars['js_methods']['onclick'] )): ?><?php echo $this->_tpl_vars['js_methods']['onclick']; ?>
;<?php endif; ?>"
       oncontextmenu="return false;"
       ondrop="return false;"
       <?php $_from = ((is_array($_tmp=@$this->_tpl_vars['js_methods'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['method'] => $this->_tpl_vars['func']):
?>
         <?php if ($this->_tpl_vars['func'] && ((is_array($_tmp=@$this->_tpl_vars['method'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null)) && $this->_tpl_vars['method'] != 'onkeydown' && $this->_tpl_vars['method'] != 'onkeypress' && $this->_tpl_vars['method'] != 'onkeyup' && $this->_tpl_vars['method'] != 'onfocus' && $this->_tpl_vars['method'] != 'onblur' && $this->_tpl_vars['method'] != 'onclick'): ?>
           <?php echo $this->_tpl_vars['method']; ?>
="<?php echo $this->_tpl_vars['func']; ?>
"
         <?php endif; ?>
       <?php endforeach; endif; unset($_from); ?>
       <?php if (((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> readonly="readonly"<?php endif; ?>
       <?php if (((is_array($_tmp=@$this->_tpl_vars['disabled'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?> disabled="disabled"<?php endif; ?>
       uniqid="<?php echo $this->_tpl_vars['uniqID']; ?>
"/>

    <?php if (! ((is_array($_tmp=@$this->_tpl_vars['readonly'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
      <?php if (! ((is_array($_tmp=@$this->_tpl_vars['exclude_oldvalues'])) ? $this->_run_mod_handler('default', true, $_tmp, null) : smarty_modifier_default($_tmp, null))): ?>
        <input type="hidden"
               name="<?php echo $this->_tpl_vars['name']; ?>
_oldvalue<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
               id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_oldvalue<?php if ($this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>"
               value="<?php echo ((is_array($_tmp=$this->_tpl_vars['visible_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
               disabled="disabled" />
      <?php endif; ?>

      <div id="suggestions_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="autocompletebox" style="display: none;"></div>

            
      <script type="text/javascript">
        <?php echo smarty_function_array(array('assign' => 'autocomplete','uniqid' => $this->_tpl_vars['uniqID']), $this);?>

        params_<?php echo $this->_tpl_vars['uniqID']; ?>
 = <?php echo smarty_function_json(array('encode' => ((is_array($_tmp=@$this->_tpl_vars['autocomplete'])) ? $this->_run_mod_handler('default', true, $_tmp, false) : smarty_modifier_default($_tmp, false))), $this);?>
;
        initAutocompleter(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);
      </script>
    <?php elseif ($this->_tpl_vars['autocomplete']['view_mode'] == 'link' && $this->_tpl_vars['autocomplete']['view_mode_url']): ?>
      <?php ob_start(); ?><?php echo $this->_tpl_vars['uniqID']; ?>
 id_var-<?php if ($this->_tpl_vars['autocomplete_var_type'] == 'basic' || ( $this->_tpl_vars['autocomplete_var_type'] == 'searchable' && $this->_tpl_vars['autocomplete']['search_by_id'] )): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php else: ?><?php echo $this->_tpl_vars['autocomplete']['id_var']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('ac_class', ob_get_contents());ob_end_clean(); ?>
      <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_view_autocompleter.html", 'smarty_include_vars' => array('value' => ((is_array($_tmp=@$this->_tpl_vars['value_name'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['visible_value']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['visible_value'])),'value_id' => $this->_tpl_vars['value_id'],'view_mode_url' => $this->_tpl_vars['autocomplete']['view_mode_url'],'ac_class' => $this->_tpl_vars['ac_class'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    <?php endif; ?>

    <?php if ($this->_tpl_vars['autocomplete_var_type'] != 'searchable' && ! $this->_tpl_vars['readonly']): ?>
        <?php ob_start(); ?><?php if (! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<customer_trademark>' , $this->_tpl_vars['autocomplete']['filters'] )): ?>trademarks<?php elseif (! empty ( $this->_tpl_vars['autocomplete']['filters'] ) && array_key_exists ( '<contactpersons>' , $this->_tpl_vars['autocomplete']['filters'] )): ?>contactpersons<?php else: ?><?php echo $this->_tpl_vars['autocomplete']['type']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('autocomplete_type', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_clear_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('clear_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_search_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('search_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_add_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('add_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_refresh_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('refresh_param', ob_get_contents());ob_end_clean(); ?>
        <?php ob_start(); ?>autocomplete_edit_<?php echo $this->_tpl_vars['autocomplete_type']; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('edit_param', ob_get_contents());ob_end_clean(); ?>

        <?php if ($this->_tpl_vars['include_combobox_button']): ?>
          <a href="javascript:void(0);" class="nz-ac-combobox-trigger"
             onclick="toggleAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
              <i class="material-icons combobox_button">expand_more</i>
          </a>
        <?php endif; ?>
      <?php if ($this->_tpl_vars['button_menu']): ?>
      <div  id="button_menu_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="nz-actions-wrapper nz-ac-menu">
        <ul class="nz-actions-list">
          <li class="nz-actions-list-item nz-actions-list-item-has_options nz-openable" title="">
            <a href="javascript:void(0);"><i class="material-icons nz-glyph">more_horiz</i></a>
            <ul class="nz-actions-list-dropdown">
              <?php if ($this->_tpl_vars['include_clear_button']): ?><li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="clearAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
                  <i class="material-icons nz-glyph nz-md-backspace">backspace</i>
                  <span class="nz-actions-list-label"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['clear_param']]; ?>
</span>
                </a>
              </li><?php endif; ?>
              <?php if ($this->_tpl_vars['include_search_button']): ?><li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="filterAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
                  <i class="material-icons nz-glyph">search</i>
                  <span class="nz-actions-list-label"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['search_param']]; ?>
</span>
                </a>
              </li><?php endif; ?>
              <?php if ($this->_tpl_vars['include_report_button']): ?><li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="reportAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
                  <i class="material-icons nz-input-icon">find_in_page</i>
                  <span class="nz-actions-list-label"><?php echo $this->_config[0]['vars']['autocomplete_search_report']; ?>
</span>
                </a>
              </li><?php endif; ?>
              <?php if ($this->_tpl_vars['include_refresh_button']): ?><li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="refreshAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
                  <i class="material-icons nz-glyph">refresh</i>
                  <span class="nz-actions-list-label"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['refresh_param']]; ?>
</span>
                </a>
              </li><?php endif; ?>
              <?php if ($this->_tpl_vars['include_add_button']): ?><li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="addAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
                  <i class="material-icons nz-glyph">playlist_add</i>
                  <span class="nz-actions-list-label"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['add_param']]; ?>
</span>
                </a>
              </li><?php endif; ?>
              <?php if ($this->_tpl_vars['include_edit_button']): ?><li class="nz-actions-list-item">
                <a href="javascript:void(0);" onclick="editAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
                  <i class="material-icons nz-glyph">edit_note</i>
                  <span class="nz-actions-list-label"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['edit_param']]; ?>
</span>
                </a>
              </li><?php endif; ?>
            </ul>
          </li>
        </ul>
      </div>
      <?php else: ?>
        <?php if ($this->_tpl_vars['include_clear_button']): ?>
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#clear_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['autocomplete_clear_items'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
             onclick="clearAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
              <i class="material-icons nz-input-icon nz-md-backspace">backspace</i>
          </a>
          <div id="clear_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-center"><?php echo $this->_config[0]['vars']['autocomplete_clear_items']; ?>
 <?php echo $this->_config[0]['vars'][$this->_tpl_vars['clear_param']]; ?>
</div>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_search_button']): ?>
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#search_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['autocomplete_search_items'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
             onclick="filterAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
              <i class="material-icons nz-input-icon">search</i>
          </a>
          <div id="search_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-center"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['search_param']]; ?>
</div>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_report_button']): ?>
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#report_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['autocomplete_search_items'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
             onclick="reportAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
              <i class="material-icons nz-input-icon">find_in_page</i>
          </a>
          <div id="report_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-center"><?php echo $this->_config[0]['vars']['autocomplete_search_report']; ?>
</div>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_refresh_button']): ?>
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#refresh_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['autocomplete_refresh_items'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
             onclick="refreshAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
              <i class="material-icons nz-input-icon">refresh</i>
          </a>
          <div id="refresh_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-center"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['refresh_param']]; ?>
</div>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_add_button']): ?>
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#add_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['add'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
             onclick="addAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
              <i class="material-icons nz-input-icon">playlist_add</i>
          </a>
          <div id="add_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-center"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['add_param']]; ?>
</div>
        <?php endif; ?>
        <?php if ($this->_tpl_vars['include_edit_button']): ?>
          <a href="javascript:void(0);" class="nz-input-controls nz-tooltip-trigger nz-tooltip-autoinit"
             data-tooltip-element="#edit_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
"
             data-tooltip-position="panel: bottom center at: top center"
             data-tooltip-title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['edit'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"
             onclick="editAutocompleteItems(params_<?php echo $this->_tpl_vars['uniqID']; ?>
);">
              <i class="material-icons nz-input-icon">edit_note</i>
          </a>
          <div id="edit_button_tooltip_<?php echo $this->_tpl_vars['uniqID']; ?>
" class="nz-tooltip-content nz-tooltip-notch__bottom-center"><?php echo $this->_config[0]['vars'][$this->_tpl_vars['edit_param']]; ?>
</div>
        <?php endif; ?>
      <?php endif; ?>
    <?php endif; ?>

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
    <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => $this->_tpl_vars['custom_id'],'name' => $this->_tpl_vars['name'],'back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
    </span>
<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>