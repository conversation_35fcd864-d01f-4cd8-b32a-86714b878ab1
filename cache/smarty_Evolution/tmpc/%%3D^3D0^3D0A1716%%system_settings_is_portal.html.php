<?php /* Smarty version 2.6.33, created on 2025-05-21 15:58:03
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/system_settings_is_portal.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('function', 'uniqid', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/system_settings_is_portal.html', 2, false),array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/system_settings_is_portal.html', 6, false),)), $this); ?>
<?php if (! $this->_tpl_vars['currentUser']->get('is_portal')): ?>
  <?php ob_start(); ?>_<?php echo smarty_function_uniqid(array(), $this);?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_portal_suffix', ob_get_contents());ob_end_clean(); ?>
  <tr<?php if (! $this->_tpl_vars['viewable']): ?> style="display: none;"<?php endif; ?>>
    <td class="labelbox" nowrap="nowrap">
    <?php if ($this->_tpl_vars['action'] == 'view'): ?>
      <?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:
    <?php else: ?>
      <a name="error_is_portal"><label for="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"<?php if ($this->_tpl_vars['messages']->getErrors('is_portal')): ?> class="error"<?php endif; ?>><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
:</label></a>
    <?php endif; ?>
    </td>
    <td class="unrequired">&nbsp;</td>
    <td nowrap="nowrap">
    <?php if ($this->_tpl_vars['action'] == 'view'): ?>
      <?php if ($this->_tpl_vars['object']->get('is_portal')): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?>
    <?php else: ?>
      <?php ob_start(); ?><?php echo ''; ?><?php if (isset ( $_POST['is_portal'] )): ?><?php echo ''; ?><?php echo $_POST['is_portal']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['object']->isDefined('is_portal') && $this->_tpl_vars['object']->get('is_portal') == 1): ?><?php echo '1'; ?><?php else: ?><?php echo '0'; ?><?php endif; ?><?php echo ''; ?>
<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('is_portal', ob_get_contents());ob_end_clean(); ?>
      <input type="radio" name="is_portal" id="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="1" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)" <?php if (! $this->_tpl_vars['object']->checkPermissions('is_portal')): ?> disabled="disabled"<?php endif; ?><?php if ($this->_tpl_vars['is_portal']): ?> checked="checked"<?php endif; ?> /><label for="is_portal1<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
      <input type="radio" name="is_portal" id="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
" value="0" title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" onfocus="highlight(this)" onblur="unhighlight(this)"<?php if (! $this->_tpl_vars['object']->checkPermissions('is_portal')): ?> disabled="disabled"<?php endif; ?><?php if (! $this->_tpl_vars['is_portal']): ?> checked="checked"<?php endif; ?> /><label for="is_portal2<?php echo $this->_tpl_vars['is_portal_suffix']; ?>
"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['is_not_portal'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</label>
    <?php endif; ?>
    </td>
  </tr>
<?php endif; ?>