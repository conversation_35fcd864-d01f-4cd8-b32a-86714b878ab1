<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:46
         compiled from _back_label.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', '_back_label.html', 9, false),array('modifier', 'escape', '_back_label.html', 13, false),)), $this); ?>
<?php if ($this->_tpl_vars['back_label']): ?>
<?php ob_start(); ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
<?php if (( $this->_tpl_vars['custom_id'] || $this->_tpl_vars['name'] ) && $this->_tpl_vars['index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('for', ob_get_contents());ob_end_clean(); ?>
<?php if (! $this->_tpl_vars['for']): ?>
 <?php echo $this->_tpl_vars['back_label']; ?>

<?php else: ?>
  <label for="<?php echo $this->_tpl_vars['for']; ?>
" class="back_label"<?php if ($this->_tpl_vars['back_label_style']): ?> style="<?php echo ((is_array($_tmp=$this->_tpl_vars['back_label_style'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php endif; ?>><?php echo $this->_tpl_vars['back_label']; ?>
</label>
<?php endif; ?>
<?php endif; ?>