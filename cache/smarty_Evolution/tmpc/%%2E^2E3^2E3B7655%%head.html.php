<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:39
         compiled from layout/head.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', 'layout/head.html', 1, false),array('modifier', 'capitalize', 'layout/head.html', 53, false),array('function', 'fetch', 'layout/head.html', 9, false),)), $this); ?>
<title>:. <?php echo ((is_array($_tmp=$this->_config[0]['vars']['sitename'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
 .:. <?php if ($this->_tpl_vars['title']): ?><?php echo $this->_tpl_vars['title']; ?>
<?php elseif ($this->_tpl_vars['app_location'] == 'frontend'): ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['menu_home_frontend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['menu_home_backend'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?> .:</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta http-equiv="X-UA-Compatible" content="IE=edge; IE=9; IE=8; IE=7">
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo ((is_array($_tmp=$this->_config[0]['vars']['charset'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" />
<meta http-equiv="content-language" content="<?php echo $_SESSION['lang']; ?>
" />
<link rel="alternate" type="application/rss+xml" title="RSS" href="rss" />

<style>
  <?php echo smarty_function_fetch(array('file' => ($this->_tpl_vars['theme']->stylesDir)."special/inline.css"), $this);?>

</style>

<script>
  <?php echo smarty_function_fetch(array('file' => ($this->_tpl_vars['theme']->scriptsDir)."special/inline.js"), $this);?>

</script>

<?php $_from = $this->_tpl_vars['theme']->getStyles(); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['style']):
?>
<link href="<?php echo $this->_tpl_vars['theme']->stylesUrl; ?>
<?php echo $this->_tpl_vars['style']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
" type="text/css" rel="stylesheet" />
<?php endforeach; endif; unset($_from); ?>
<?php if (! empty ( $this->_tpl_vars['custom_styles'] )): ?>
  <style type="text/css">
    <?php $_from = $this->_tpl_vars['custom_styles']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['custom_selector'] => $this->_tpl_vars['custom_style']):
?>
      <?php echo $this->_tpl_vars['custom_selector']; ?>
 { <?php echo $this->_tpl_vars['custom_style']; ?>
 }
    <?php endforeach; endif; unset($_from); ?>
  </style>
<?php endif; ?>
<!-- DASHLET's Specific styles -->
<?php if (! empty ( $this->_tpl_vars['dashlets_css'] )): ?>
<?php $_from = $this->_tpl_vars['dashlets_css']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['dashlet']):
?>
<?php $_from = $this->_tpl_vars['dashlet']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['css']):
?>
<link href="<?php echo $this->_tpl_vars['css']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
" type="text/css" rel="stylesheet" />
<?php endforeach; endif; unset($_from); ?>
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>
<link rel="shortcut icon" href="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
favicon.ico?v=2" type="image/x-icon" />
<link rel="icon" type="image/png" href="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
icon.png" sizes="32x32">
<!-- General Scripts -->
<script type="text/javascript" src="<?php echo @PH_JAVASCRIPT_URL; ?>
scriptaculous/lib/prototype.js"></script>
<script type="text/javascript" src="<?php echo @PH_JAVASCRIPT_URL; ?>
scriptaculous/src/scriptaculous.js"></script>
<script type="text/javascript" src="<?php echo @PH_JAVASCRIPT_URL; ?>
scriptaculous/src/cookie.js"></script>
<?php if ($this->_tpl_vars['common_scripts']): ?>
<!-- Common Scripts -->
<?php $_from = $this->_tpl_vars['common_scripts']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['script']):
?>
<?php if ($this->_tpl_vars['script']['type'] == 'external'): ?>
<script type="text/javascript" src="<?php echo $this->_tpl_vars['script']['src']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
<?php elseif ($this->_tpl_vars['script']['type'] == 'inline'): ?>
<script type="text/javascript">
  <?php echo $this->_tpl_vars['script']['content']; ?>

</script>
<?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['module_scripts']): ?>
<!-- Module "<?php echo ((is_array($_tmp=$this->_tpl_vars['module'])) ? $this->_run_mod_handler('capitalize', true, $_tmp) : smarty_modifier_capitalize($_tmp)); ?>
" Specific Scripts -->
<?php $_from = $this->_tpl_vars['module_scripts']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['script']):
?>
<?php if ($this->_tpl_vars['script']['type'] == 'external'): ?>
<script type="text/javascript" defer src="<?php echo $this->_tpl_vars['script']['src']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
<?php elseif ($this->_tpl_vars['script']['type'] == 'inline'): ?>
<script type="text/javascript">
  <?php echo $this->_tpl_vars['script']['content']; ?>

</script>
<?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['theme']->getScripts()): ?>
<!-- Theme "<?php echo ((is_array($_tmp=$this->_tpl_vars['theme']->get('name'))) ? $this->_run_mod_handler('capitalize', true, $_tmp) : smarty_modifier_capitalize($_tmp)); ?>
" Specific Scripts -->
<?php $_from = $this->_tpl_vars['theme']->getScripts(); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['script']):
?>
<script type="text/javascript" defer src="<?php echo $this->_tpl_vars['theme']->scriptsUrl; ?>
<?php echo $this->_tpl_vars['script']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>
<?php if (! empty ( $this->_tpl_vars['dashlets_js'] )): ?>
<!-- DASHLET's Specific Scripts -->
  <?php $_from = $this->_tpl_vars['dashlets_js']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['scripts']):
?>
    <?php $_from = $this->_tpl_vars['scripts']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['script']):
?>
    <script async type="text/javascript" defer src="<?php echo $this->_tpl_vars['script']; ?>
?<?php echo $this->_tpl_vars['system_options']['build']; ?>
"></script>
    <?php endforeach; endif; unset($_from); ?>
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>
<?php if ($this->_tpl_vars['custom_libs']): ?>
<!--Custom Libs -->
<?php $_from = $this->_tpl_vars['custom_libs']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['custom_lib']):
?>
  <?php if (! empty ( $this->_tpl_vars['custom_lib']['css'] )): ?>
    <?php $_from = $this->_tpl_vars['custom_lib']['css']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['css']):
?>
    <link rel="stylesheet" type="text/css" href="<?php echo $this->_tpl_vars['css']; ?>
<?php if (! preg_match ( '#\/(ext|zapatec|keyboard)\/#' , $this->_tpl_vars['css'] )): ?>?<?php echo $this->_tpl_vars['system_options']['build']; ?>
<?php endif; ?>" />
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
  <?php if (! empty ( $this->_tpl_vars['custom_lib']['js'] )): ?>
    <?php $_from = $this->_tpl_vars['custom_lib']['js']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['script']):
?>
    <script type="text/javascript" src="<?php echo $this->_tpl_vars['script']; ?>
<?php if (! preg_match ( '#\/(ext|zapatec|keyboard)\/#' , $this->_tpl_vars['script'] )): ?>?<?php echo $this->_tpl_vars['system_options']['build']; ?>
<?php endif; ?>"></script>
    <?php endforeach; endif; unset($_from); ?>
  <?php endif; ?>
<?php endforeach; endif; unset($_from); ?>
<?php endif; ?>

<?php $_from = $this->_tpl_vars['css_file_paths']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['css_file_path']):
?>
<link rel="stylesheet" type="text/css" href="<?php echo $this->_tpl_vars['css_file_path']; ?>
<?php if (! preg_match ( '#\/(ext|zapatec|keyboard)\/#' , $this->_tpl_vars['script'] )): ?>?<?php echo $this->_tpl_vars['system_options']['build']; ?>
<?php endif; ?>" />
<?php endforeach; endif; unset($_from); ?>