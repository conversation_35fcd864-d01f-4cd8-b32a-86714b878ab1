<?php /* Smarty version 2.6.33, created on 2025-05-21 16:46:06
         compiled from /var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info.html', 3, false),array('modifier', 'mb_truncate', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info.html', 5, false),array('modifier', 'default', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info.html', 5, false),array('modifier', 'date_format', '/var/www/Nzoom-Hella/_libs/modules/contracts/templates/_info.html', 8, false),)), $this); ?>
<?php echo ''; ?><?php $this->assign('layout', $this->_tpl_vars['contract']->getLayoutsDetails('num')); ?><?php echo '<strong><u>'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</u></strong> '; ?><?php if ($this->_tpl_vars['contract']->get('num')): ?><?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('num') == 'system'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['contracts_system_num']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['contract']->get('num'); ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo '<i>'; ?><?php echo $this->_config[0]['vars']['contracts_unfinished_contract']; ?><?php echo '</i>'; ?><?php endif; ?><?php echo '<br />'; ?><?php $this->assign('layout', $this->_tpl_vars['contract']->getLayoutsDetails('name')); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('name'))) ? $this->_run_mod_handler('mb_truncate', true, $_tmp) : smarty_modifier_mb_truncate($_tmp)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?><?php echo '<br />'; ?><?php $this->assign('layout', $this->_tpl_vars['contract']->getLayoutsDetails('type')); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('type_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['added'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('added'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('added_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['status_modified'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('status_modified'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('status_modified_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '<br />'; ?><?php if ($this->_tpl_vars['contract']->isDeleted()): ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['deleted'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong> '; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['contract']->get('deleted'))) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('deleted_by_name')): ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['by'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ' '; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['contract']->get('deleted_by_name'))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ''; ?><?php endif; ?><?php echo '<br />'; ?><?php endif; ?><?php echo ''; ?><?php $this->assign('layout', $this->_tpl_vars['contract']->getLayoutsDetails('status')); ?><?php echo '<strong>'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['layout']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong>&nbsp;'; ?><?php if ($this->_tpl_vars['contract']->get('status') == 'opened'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_contracts_status_opened']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['contract']->get('status') == 'locked'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_contracts_status_locked']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['contract']->get('status') == 'closed'): ?><?php echo ''; ?><?php echo $this->_config[0]['vars']['help_contracts_status_closed']; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['contract']->get('substatus_name')): ?><?php echo '<br />'; ?><?php echo $this->_config[0]['vars']['help_contracts_substatus']; ?><?php echo ''; ?><?php echo $this->_tpl_vars['contract']->get('substatus_name'); ?><?php echo ''; ?><?php endif; ?><?php echo '<br /><strong>'; ?><?php echo ((is_array($_tmp=$this->_config[0]['vars']['translations'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo ':</strong><span class="translations">'; ?><?php $_from = $this->_tpl_vars['contract']->get('translations'); if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }if (count($_from)):
    foreach ($_from as $this->_tpl_vars['trans']):
?><?php echo '<img src="'; ?><?php echo $this->_tpl_vars['theme']->imagesUrl; ?><?php echo 'flags/'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '.png" alt="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" title="'; ?><?php echo $this->_tpl_vars['trans']; ?><?php echo '" border="0" align="absmiddle"'; ?><?php if ($this->_tpl_vars['trans'] == $this->_tpl_vars['contract']->get('model_lang')): ?><?php echo ' class="selected"'; ?><?php endif; ?><?php echo ' />'; ?><?php endforeach; endif; unset($_from); ?><?php echo '</span><br />'; ?>
