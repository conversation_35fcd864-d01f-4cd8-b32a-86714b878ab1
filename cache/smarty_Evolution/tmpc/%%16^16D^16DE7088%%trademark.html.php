<?php /* Smarty version 2.6.33, created on 2025-05-21 13:02:55
         compiled from /var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/trademark.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '/var/www/Nzoom-Hella/_libs/themes/Evolution/templates/grid/cols/basic/trademark.html', 6, false),)), $this); ?>
<?php echo '
<script id="grid-col-trademark" type="text/x-template">
    ${if(properties.trademark_name && properties.trademark_name!=\'null\')}
    <a class="nz-grid-cell-link nz-link-nomenclature"
        href="'; ?>
<?php echo $_SERVER['PHP_SELF']; ?>
?<?php echo $this->_tpl_vars['module_param']; ?>
=nomenclatures&amp;nomenclatures=view&amp;view=<?php echo '${properties.trademark}"'; ?>

        title="<?php echo ((is_array($_tmp=$this->_config[0]['vars']['view'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
: <?php echo '${properties.trademark_name}">${properties.trademark_name}</a>
    ${/if}
</script>
'; ?>
