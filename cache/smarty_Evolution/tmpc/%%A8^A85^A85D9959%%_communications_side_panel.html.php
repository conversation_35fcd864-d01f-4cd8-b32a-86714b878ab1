<?php /* Smarty version 2.6.33, created on 2025-05-27 12:09:22
         compiled from _communications_side_panel.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'escape', '_communications_side_panel.html', 5, false),array('modifier', 'default', '_communications_side_panel.html', 18, false),array('modifier', 'date_format', '_communications_side_panel.html', 51, false),array('function', 'cycle', '_communications_side_panel.html', 9, false),array('function', 'popup', '_communications_side_panel.html', 38, false),)), $this); ?>
<?php if (! $this->_tpl_vars['hide_side_panel']): ?>
<input type="hidden" id="<?php echo $this->_tpl_vars['side_panel']; ?>
_total" name="<?php echo $this->_tpl_vars['side_panel']; ?>
_total" class="total" value="<?php echo $this->_tpl_vars['total']; ?>
" />
<table border="0" cellpadding="0" cellspacing="0" class="t_layout_table">
  <tr>
    <td class="t_panel_caption" nowrap="nowrap" style="width: 160px"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['from'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
/<?php echo ((is_array($_tmp=$this->_config[0]['vars']['to'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
/<?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
    <td class="t_panel_caption" nowrap="nowrap"><div class="t_panel_caption_title"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['communications_communication_text'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</div></td>
  </tr>
  <?php $_from = $this->_tpl_vars['communications']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['i'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['i']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['communication']):
        $this->_foreach['i']['iteration']++;
?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
 vtop"  onclick="expandCommunication($(this).select('td.content')[0]);">
      <td class="t_border<?php if ($this->_tpl_vars['communication']['expand']): ?> pointer communicationsdatefield<?php endif; ?>">
        <?php if (( $this->_tpl_vars['communication']['attachments'] )): ?>
          <span class="floatl" style="padding: 3px 3px 3px 0;">
            <img border="0" src="<?php echo $this->_tpl_vars['theme']->imagesUrl; ?>
attachments.png" width="16" height="16" alt="" style="vertical-align: middle;"
            onmouseover="showFiles(this, '<?php if ($this->_tpl_vars['current_module']): ?><?php echo $this->_tpl_vars['current_module']; ?>
<?php else: ?><?php echo $this->_tpl_vars['module']; ?>
<?php endif; ?>', '<?php if ($this->_tpl_vars['current_controller']): ?><?php echo $this->_tpl_vars['current_controller']; ?>
<?php else: ?><?php echo $this->_tpl_vars['controller']; ?>
<?php endif; ?>', <?php echo $this->_tpl_vars['model_id']; ?>
, '<?php echo $this->_tpl_vars['communication']['attachments']; ?>
'<?php if ($this->_tpl_vars['archive']): ?>, 1<?php endif; ?>)"
            onmouseout="mclosetime()" />
          </span>
        <?php endif; ?>
        <span class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['from'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>: <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['from'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?>

        <?php if ($this->_tpl_vars['communication']['to']): ?>
        <br />
        <span class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['to'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>:
        <?php $_from = $this->_tpl_vars['communication']['to']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['c'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['c']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['to_user']):
        $this->_foreach['c']['iteration']++;
?>
          <?php if ($this->_tpl_vars['to_user']['mail_status'] == 'sent' && $this->_tpl_vars['to_user']['receive_status'] == 'received'): ?>
            <?php $this->assign('name_color', '000000'); ?>
            <?php $this->assign('name_message', 'email_sent_received_comment_reason'); ?>
          <?php elseif ($this->_tpl_vars['to_user']['mail_status'] == 'received'): ?>
            <?php $this->assign('name_color', '006600'); ?>
            <?php $this->assign('name_message', 'email_received_comment_reason'); ?>
          <?php else: ?>
            <?php $this->assign('name_color', 'FF0000'); ?>
            <?php if ($this->_tpl_vars['to_user']['receive_status'] == 'not_received'): ?>
              <?php $this->assign('name_message', 'email_sent_not_received_comment_reason'); ?>
            <?php elseif ($this->_tpl_vars['to_user']['mail_status'] != 'sent'): ?>
              <?php ob_start(); ?>email_not_sent_<?php echo $this->_tpl_vars['to_user']['mail_status']; ?>
_reason<?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('name_message', ob_get_contents());ob_end_clean(); ?>
            <?php endif; ?>
          <?php endif; ?>
          <?php echo '<span style="color: #'; ?><?php echo $this->_tpl_vars['name_color']; ?><?php echo '; cursor: pointer;" '; ?><?php echo smarty_function_popup(array('caption' => ((is_array($_tmp=$this->_config[0]['vars']['mail_status'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'text' => ((is_array($_tmp=$this->_config[0]['vars'][$this->_tpl_vars['name_message']])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)),'width' => 250), $this);?><?php echo '>'; ?><?php if ($this->_tpl_vars['communication']['type'] == 'comment'): ?><?php echo ''; ?><?php if ($this->_tpl_vars['to_user']['name']): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['to_user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?><?php echo ''; ?><?php else: ?><?php echo '&#60;'; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['to_user']['email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?><?php echo '&#62;'; ?><?php endif; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php if ($this->_tpl_vars['to_user']['name']): ?><?php echo ''; ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['to_user']['name'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, "&nbsp;") : smarty_modifier_default($_tmp, "&nbsp;")); ?><?php echo ' '; ?><?php endif; ?><?php echo ''; ?><?php if ($this->_tpl_vars['to_user']['email']): ?><?php echo '&#60;'; ?><?php echo ((is_array($_tmp=$this->_tpl_vars['to_user']['email'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?><?php echo '&#62;'; ?><?php endif; ?><?php echo ''; ?><?php endif; ?><?php echo '</span>'; ?><?php if (! ($this->_foreach['c']['iteration'] == $this->_foreach['c']['total'])): ?><?php echo ', '; ?><?php endif; ?><?php echo ''; ?>

        <?php endforeach; else: ?>
          &nbsp;
        <?php endif; unset($_from); ?>
        <?php endif; ?>
        <br />
        <span class="strong"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['date'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>: <?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['communication']['date'])) ? $this->_run_mod_handler('date_format', true, $_tmp, $this->_config[0]['vars']['date_mid']) : smarty_modifier_date_format($_tmp, $this->_config[0]['vars']['date_mid'])))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>

      </td>
          <td class="<?php echo $this->_tpl_vars['communications_sort']['content']['isSorted']; ?>
 content" id="communication_<?php echo $this->_tpl_vars['communication']['id']; ?>
">
        <?php if (( $this->_tpl_vars['communication']['subject'] )): ?><strong><?php echo ((is_array($_tmp=$this->_tpl_vars['communication']['subject'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</strong><br /><?php endif; ?>
        <div id="communication_full_<?php echo $this->_foreach['i']['iteration']; ?>
" class="<?php echo $this->_tpl_vars['communication']['type']; ?>
_parent communication_parent">
          <div class="<?php echo $this->_tpl_vars['communication']['type']; ?>
 communication"><?php echo $this->_tpl_vars['communication']['content']; ?>
</div>
        </div>
        </td>
    </tr>
  <?php endforeach; else: ?>
    <tr class="<?php echo smarty_function_cycle(array('values' => 't_odd,t_even'), $this);?>
">
      <td class="error" colspan="2"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['no_items_found'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</td>
    </tr>
  <?php endif; unset($_from); ?>
</table>
<?php endif; ?>