#Forbid Directory Listing
Options -Indexes

#Settings for PHP7 (should be the same as those for PHP5)
<IfModule mod_php7.c>
    php_flag display_errors on
    ### VERY IMPORTANT!!!!!
    ### The value 32767 stands for E_ALL (PHP constant).
    ### In future versions of PHP it might be changed.
    ### Here we can't use PHP constants, so make sure to update it if necessary.
    php_value error_reporting 32767
    php_flag magic_quotes_gpc off
    php_value max_execution_time 180
    php_value post_max_size 256M
    php_value upload_max_filesize 256M
    php_value max_file_uploads 300
    php_value max_input_vars 100000
    php_value memory_limit 1024M

    ### VERY IMPORTANT!!!!!
    ### ALLOWS REGEX for large strings!!!
    php_value pcre.backtrack_limit 100000000
</IfModule>

#Settings for PHP5 (should be the same as those for PHP7)
<IfModule mod_php5.c>
    php_flag display_errors on
    ### VERY IMPORTANT!!!!!
    ### The value 32767 stands for E_ALL (PHP constant).
    ### In future versions of PHP it might be changed.
    ### Here we can't use PHP constants, so make sure to update it if necessary.
    php_value error_reporting 32767
    php_flag magic_quotes_gpc off
    php_value max_execution_time 180
    php_value post_max_size 256M
    php_value upload_max_filesize 256M
    php_value max_file_uploads 300
    php_value max_input_vars 100000
    php_value memory_limit 1024M
    
    ### VERY IMPORTANT!!!!!
    ### ALLOWS REGEX for large strings!!!
    php_value pcre.backtrack_limit 100000000
</IfModule>

# Workaround for missing Authorization header under CGI/FastCGI Apache
# http://php.net/manual/en/features.http-auth.php#114877
SetEnvIf Authorization .+ HTTP_AUTHORIZATION=$0

#Deny access to all configuration files
##############
# Apache 2.4 #
##############
<IfModule mod_authz_core.c>
    <Files ~ "\.(ini|conf|htconfig|htaccess|htpass)$">
        # Deny direct access to this folder
        Require all denied
    </Files>
</IfModule>
##############
# Apache 2.2 #
##############
<IfModule !mod_authz_core.c>
    <Files ~ "\.(ini|conf|htconfig|htaccess|htpass)$">
        # Deny direct access to this folder
        Deny From All
    </Files>
</IfModule>

<IfModule mod_rewrite.c>
    RewriteEngine On

    #Default icon of nZoom
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^favicon.ico$ /_libs/themes/Default/images/favicon.ico

    #Rule for test actions with soap module
    RewriteRule ^soap/([^/]+)/([^/]+)/?$  index.php?launch=soap&plugin=$1&soap=$2 [QSA]

    #Rule for soap handler
    RewriteRule ^soap/([^/]+)/?$  index.php?launch=soap&plugin=$1&soap=handle

    #Rule for callback module
	RewriteCond %{REQUEST_URI}::$1 ^(/.+)/(.*)::\2$
	RewriteRule ^(.*) - [E=BASE:%1]
	RewriteRule ^callback(s)?/([^/]*)(/?.*)$ %{ENV:BASE}/index.php?launch=callbacks&callbacks=call&hash=$2&uri=$3 [QSA]	
    #RewriteRule ^callback/([^/]+)/?$  index.php?launch=callbacks [QSA]
    #RewriteRule ^callback(/?.*)$ index.php?launch=callbacks [QSA]

    #Rule for rss
    RewriteRule ^rss$  index.php?launch=rss

    #Rule for unsubscribe
    RewriteRule ^unsubscribe index.php?launch=emails&controller=targetlists&targetlists=unsubscribe [QSA]

    #File manager module of nzoom launching kcfinder
    RewriteCond %{HTTP_REFERER} launch=filemanager
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^((js|css)/index.php)$ /_libs/inc/ext/kcfinder/$1 [L]

    RewriteCond %{HTTP_REFERER} launch=filemanager
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^((themes/(default|dark)/(css|js)|js_localize).php)$ /_libs/inc/ext/kcfinder/$1 [L]

    RewriteCond %{HTTP_REFERER} launch=filemanager
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ((browse|upload).php) index.php?launch=filemanager&filemanager=$2 [QSA,L]

    RewriteCond %{HTTP_REFERER} css.php [OR]
    RewriteCond %{HTTP_REFERER} launch=filemanager
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(themes/(default|dark)/img/(.*/)?.*.(gif|png)) /_libs/inc/ext/kcfinder/$1 [L]
    #End of kcfinder rules

    # Anti-hack measures
    RewriteCond %{QUERY_STRING} cmd [OR]
    RewriteCond %{QUERY_STRING} perl [OR]
    RewriteCond %{QUERY_STRING} proc/self/environ [OR]
    # Block out any script trying to set a mosConfig value through the URL
    RewriteCond %{QUERY_STRING} mosConfig_[a-zA-Z_]{1,21}(=|\%3D) [OR]
    # Block out any script trying to base64_encode crap to send via URL
    RewriteCond %{QUERY_STRING} base64_encode.*(.*) [OR]
    # Block out any script that includes a <script> tag in URL
    RewriteCond %{QUERY_STRING} (<|%3C)script(>|%3E) [NC,OR]
    # Block out any script trying to set a PHP GLOBALS variable via URL
    RewriteCond %{QUERY_STRING} GLOBALS(=|[|\%[0-9A-Z]{0,2}) [OR]
    # Block out any script trying to modify a _REQUEST variable via URL
    RewriteCond %{QUERY_STRING} _REQUEST(=|[|\%[0-9A-Z]{0,2})
    # Send all blocked request to homepage with 403 Forbidden error!
    RewriteRule ^(.*)$ index.php [F,L]

</IfModule>
