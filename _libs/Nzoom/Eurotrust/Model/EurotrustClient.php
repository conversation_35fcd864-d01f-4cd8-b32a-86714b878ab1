<?php

namespace Nzoom\Eurotrust\Model;


use phpseclib3\Crypt\Rijndael;
use stdClass;
use ZipArchive;

class EurotrustClient
{
    const SIGNED_NAME = '_SIGNED';
    const OUT_ENC_KEY_FILENAME = '_OUT.enc.key';
    const OUT_ENC_IV_FILENAME = '_OUT.enc.iv';
    const OUT_ENC_FILENAME = '_OUT.enc';
    const OUT_ENC_FILENAME_NAME = '_OUT.enc.filename';
    const OUT_SIGNED_NAME = '_OUT_SIGNED.pdf';
    //const OUT_SIGNED_NAME = '_OUT_SIGNED.tmp';

    const ENDPOINTS = [
        'transactionStatus' => '/document/status',
        'threadStatus' => '/document/thread/status',
        'auth' => '/document/auth/online',
        'documentSign' => '/document/doc/online',
        'documentGroupSign' => '/document/group/online',
        'download' => '/document/download',
        'downloadGroup' => '/document/group/download',
        'withdraw' => '/document/withdraw'
    ];

    public static $fileNameForbiddenSymbols = "<>:\"\/|?*'!#$%&+=";
    public static $fileNameAllowedExtensions = ['pdf', 'xml', 'cad'];
    public static $maxGroupFilesCount = 20;

    private ClientConfig $config;

    private LoggingInterface $logger;

    public function __construct(ClientConfig $config)
    {
        $this->config = $config;
    }

    /**
     * @throws \Exception
     */
    public function sendDocument(EurotrustFile $file, EurotrustSendFileData $data):EurotrustResponse
    {
        $dataJson = json_encode($data);
        $authorizationHeader = $this->defineAuthorizationHeader($dataJson);

        $postData = array(
            'data' => $dataJson,
            'document' => new \CURLFile($file->getPath(), $file->getMime(), $file->getName()),
        );

        return $this->callEurotrust('documentSign', $authorizationHeader, $postData);
    }

    /**
     * @throws \Exception
     */
    public function sendGroupDocuments(Array $files, EurotrustSendGroupFilesData $data):EurotrustResponse
    {
        $dataJson = json_encode($data);
        $authorizationHeader = $this->defineAuthorizationHeader($dataJson);

        $postData = array('data' => $dataJson);
        foreach ($files as $idx => $file) {
            $postData["documents[{$idx}]"] = new \CURLFile($file->getPath(), $file->getMime(), $file->getName());
        }

        return $this->callEurotrust('documentGroupSign', $authorizationHeader, $postData);
    }

    /**
     * @param EurotrustDownloadFileData $data
     * @return EurotrustResponse
     * @throws \Exception
     */
    public function downloadDocument(EurotrustDownloadFileData $data):EurotrustResponse
    {
        $dataJson = json_encode($data);
        $authorizationHeader = $this->defineAuthorizationHeader($dataJson);

        return $this->callEurotrust('download', $authorizationHeader, $dataJson);
    }

    /**
     * @param EurotrustDownloadGroupFilesData $data
     * @return EurotrustResponse
     * @throws \Exception
     */
    public function downloadGroupDocuments(EurotrustDownloadGroupFilesData $data):EurotrustResponse
    {
        $dataJson = json_encode($data);
        $authorizationHeader = $this->defineAuthorizationHeader($dataJson);

        return $this->callEurotrust('downloadGroup', $authorizationHeader, $dataJson);
    }

    /**
     * @param $id string|int The id of the transaction from Eurotrust
     * @return stdClass The status of the transaction.
     */
    /*
    public function status(string $id): object
    {
        $body = new stdClass();
        $body->transactionID = $id;
        $body->vendorNumber = $this->config['vendorNumber'];
        $jsonEncodedData = json_encode($body);
        $url = $this->baseLink . Eurotrust::ENDPOINTS['transactionStatus'];

        $dataToHex = iconv(mb_detect_encoding($jsonEncodedData, mb_detect_order(), true), "UTF-8", $jsonEncodedData);
        $VendorApiKeySha256 = pack('H*', hash('sha256', $this->config['vendorKey']));
        $authorizationHeader = hash_hmac('sha256', $dataToHex, $VendorApiKeySha256);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-type: application/json',
            'Authorization: ' . $authorizationHeader
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonEncodedData);
        $data = new stdClass();
        $data->response = json_decode(curl_exec($ch));
        $data->httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $response = new stdClass();
        switch ($data->response->status){
            case Eurotrust::PENDING:
            default:
                $response->status = 'pending';
                break;
            case Eurotrust::SIGNED:
                switch ($data->response->isProcessing){
                    default:
                    case 1:
                        $response->status = 'pending';
                        break 2;
                    case 0:
                        $response->status = 'confirmed';
                        break 2;
                }
            case Eurotrust::REJECTED:
                $response->status = 'rejected';
                break;
            case Eurotrust::EXPIRED:
                $response->status = 'expired';
                break;
            case Eurotrust::FAILED:
                $response->status = 'error';
        }
        return $response;
    }
    */

    private function getKey(ZipArchive $zip, string $privateKey)
    {
        $transactionId = $this->getTransactionId($zip);
        $res = openssl_get_privatekey($privateKey);
        $outEncFile = $zip->getStream($transactionId . self::OUT_ENC_KEY_FILENAME);

        //$fp = fopen($outEncFile, "r");
        $outerKey = fread($outEncFile, 8192);
        fclose($outEncFile);
        openssl_private_decrypt(
            base64_decode($outerKey),
            $newsource,
            $res
        );
        return $newsource;
    }


    /**
     * @param $ciphertext string The encrypted text that is about to be decrypted.
     * @param $key string Key
     * @param $iv string
     * @return false|string
     */
    private function decrypt(string $ciphertext, string $key, string $iv): string
    {
        $ciphertext = base64_decode($ciphertext);

        $rijndael = new Rijndael('cbc');
        $rijndael->setKey($key);
        $rijndael->setBlockLength(256);
        $rijndael->setIV($iv);
        $rijndael->disablePadding();

        $plaintext = $rijndael->decrypt($ciphertext);

        return base64_decode(rtrim($plaintext, ""));
    }

    private function getTransactionId($zipFile):string {
        $suffix = self::OUT_ENC_FILENAME_NAME;
        $transactionId = '';
        for ($idx = 0; $zFile = $zipFile->statIndex($idx); $idx++) {
            if (preg_match('#(.*)' . $suffix . '$#', $zFile['name'], $m)) {
                $transactionId = $m[1];
                break;
            }
        }
        return $transactionId;
    }

    public function getFilename(ZipArchive $zipFile):string {
        $transactionId = $this->getTransactionId($zipFile);
        $fp = $zipFile->getStream($transactionId . self::OUT_ENC_FILENAME_NAME);
        if(!$fp){
            throw new \Exception("Failed to open ZIP!");
        }

        $filename = '';
        while (!feof($fp)) {
            $filename .= fread($fp, 2);
        }
        fclose($fp);

        return $filename;
    }

    /**
     * @param $zipFile ZipArchive The encrypted zip file as downloaded from Eurotrust
     * @param $privKey string The private key of the communications created when the document was sent to Eurotrust
     * @param $outputPath string  The output file of decrypted file
     * @return ZipArchive The stream of the ZIP file after decryption
     * @throws \Exception
     */
    public function decryptZipFile(ZipArchive $zipFile, string $privKey, string $outputPath):ZipArchive
    {
        $transactionId = $this->getTransactionId($zipFile);
        $input_file_handle = $zipFile->getStream($transactionId . self::OUT_ENC_FILENAME);
        $output_file_handle = fopen($outputPath, 'w+');
        if(!$input_file_handle){
            throw new \Exception("Failed to get input file from ZIP!");
        }
        if(!$output_file_handle){
            throw new \Exception("Could not open output file!");
        }

        $key = $this->getKey($zipFile, $privKey);
        $fp = $zipFile->getStream( $transactionId . self::OUT_ENC_IV_FILENAME);
        $iv = fread($fp, 8192);
        fclose($fp);

        while (!feof($input_file_handle)) {
            //4096 bytes plaintext become 7296 bytes of encrypted base64 text
            $buffer = fread($input_file_handle, 7296);
            $decrypted_string = $this->decrypt($buffer, $key, $iv);
            //$decrypted_string = $this->decrypt($buffer, $zipFile, $privKey, $transaction);
            fwrite($output_file_handle, $decrypted_string);
        }

        fclose($input_file_handle);
        $zipFile->close();
        $decrZip = new ZipArchive();
        $decrZip->open($outputPath);

        return $decrZip;
    }

    /**
     * @param $zipFile ZipArchive The encrypted zip file as downloaded from Eurotrust
     * @param $privKey string The private key of the communications created when the document was sent to Eurotrust
     * @param $outputPath string  The output file of decrypted file
     * @return ZipArchive The stream of the ZIP file after decryption
     * @throws \Exception
     */
    public function decryptContents(string $encryptedContents, string $privKey, string $outputPath):ZipArchive
    {
        $encZipFilePath = tempnam($path, 'encZip');
        file_put_contents($encZipFilePath, $encryptedContents);
        $encArchive = new \ZipArchive();
        $encArchive->open($encZipFilePath);


        $transactionId = $this->getTransactionId($encArchive);
        $input_file_handle = $encArchive->getStream($transactionId . self::OUT_ENC_FILENAME);
        $output_file_handle = fopen($outputPath, 'w+');
        if(!$input_file_handle){
            throw new \Exception("Failed to get input file from ZIP!");
        }
        if(!$output_file_handle){
            throw new \Exception("Could not open output file!");
        }

        $key = $this->getKey($encArchive, $privKey);
        $fp = $encArchive->getStream( $transactionId . self::OUT_ENC_IV_FILENAME);
        $iv = fread($fp, 8192);
        fclose($fp);

        while (!feof($input_file_handle)) {
            //4096 bytes plaintext become 7296 bytes of encrypted base64 text
            $buffer = fread($input_file_handle, 7296);
            $decrypted_string = $this->decrypt($buffer, $key, $iv);
            //$decrypted_string = $this->decrypt($buffer, $zipFile, $privKey, $transaction);
            fwrite($output_file_handle, $decrypted_string);
        }

        fclose($input_file_handle);
        $encArchive->close();
        $decrZip = new ZipArchive();
        $decrZip->open($outputPath);

        return $decrZip;
    }

    public function extractFile(ZipArchive $zip, string $filepath)
    {
        $transactionId = $this->getTransactionId($zip);
        $file = $zip->getStream($transactionId . self::OUT_SIGNED_NAME);
        $tmpFile = fopen($filepath, 'w+');
        stream_copy_to_stream($file, $tmpFile);
        fclose($tmpFile);
        $zip->close();
        return true;
    }

    /*
    public function withdrawDocument($id)
    {
        $body = new stdClass();
        $body->threadID = $id;
        $body->vendorNumber = $this->config['vendorNumber'];
        $jsonEncodedData = json_encode($body);
        $url = $this->baseLink . Eurotrust::ENDPOINTS['withdraw'];

        $dataToHex = iconv(mb_detect_encoding($jsonEncodedData, mb_detect_order(), true), "UTF-8", $jsonEncodedData);
        $VendorApiKeySha256 = pack('H*', hash('sha256', $this->config['vendorKey']));
        $authorizationHeader = hash_hmac('sha256', $dataToHex, $VendorApiKeySha256);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-type: application/json',
            'Authorization: ' . $authorizationHeader
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonEncodedData);
        $data = new stdClass();
        $data->response = json_decode(curl_exec($ch));
        $data->httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    }
    */

    public function defineAuthorizationHeader(string $jsonData):string
    {
        $dataToHex = iconv(mb_detect_encoding($jsonData, mb_detect_order(), true), "UTF-8", $jsonData);
        $vendorApiKeySha256 = pack('H*', hash('sha256', $this->config->getVendorKey()));

        return hash_hmac('sha256', $dataToHex, $vendorApiKeySha256);
    }

    public function getLogger():?LoggingInterface
    {
        return $this->logger??null;
    }

    public function setLogger(LoggingInterface $logger):void
    {
        $this->logger = $logger;
    }

    /**
     * @return string
     */
    public function getRequest(): string
    {
        return $this->request;
    }

    /**
     * @return string
     */
    public function getResponse(): string
    {
        return $this->response;
    }

    /**
     * @return array
     */
    public function getCurlInfo(): array
    {
        return $this->curlInfo;
    }

    /**
     * @return string
     */
    public function getHttpCode(): string
    {
        return $this->httpCode;
    }

    public static function validateFilename($fileName): bool
    {

        return !preg_match('#[' . preg_quote(self::$fileNameForbiddenSymbols) . ']+#', $fileName)
            && preg_match('#\.(' . implode('|', self::$fileNameAllowedExtensions) . ')$#', $fileName);
    }

    /**
     * @param string $endpoint
     * @return false|resource
     */
    public function getCurlResource(string $endpoint)
    {
        if (!array_key_exists($endpoint, self::ENDPOINTS)) {
            throw new \Exception('Unssported Eurotrust endpoint!');
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->config->getBaseUrl() . self::ENDPOINTS[$endpoint]);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        //IMPORTANT: CURLOPT_HEADER should be set in order to get response headers
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLINFO_HEADER_OUT, true);

        return $ch;
    }

    /**
     * @param $ch
     * @param array|string $data
     * @return EurotrustResponse
     * @throws \Exception
     */
    public function execute($ch, $data): EurotrustResponse
    {
        $responseRaw = curl_exec($ch);
        $curlInfo = curl_getinfo($ch);
        $curlError = curl_error($ch);
        curl_close($ch);

        $headerSize = $curlInfo['header_size']??0;
        $response = $responseRaw;
        if ($headerSize> 0) {
            $curlInfo['response_header'] = substr($responseRaw, 0, $headerSize);
            $response = substr($responseRaw, $headerSize);
        }

        if ($this->config->shouldLogRequest() && $logger = $this->getLogger()) {
            $logData = is_array($data) ? json_encode($data) : $data;
            $logger->log($logData, $response, $curlInfo);
        }

        $httpCode = $curlInfo['http_code'];
        if ($httpCode > 399) {
            throw new EurotrustException($response, $httpCode);
        }
        if ($curlError) {
            throw new EurotrustException($curlError, $httpCode);
        }

        return new EurotrustResponse($response, $curlInfo['response_header']??'', $httpCode);
    }

    /**
     * @param string $url
     * @param string $authorizationHeader
     * @param array|string $postData
     * @return array|EurotrustResponse
     * @throws \Exception
     */
    public function callEurotrust(string $endpoint, string $authorizationHeader, $postData)
    {
        $ch = $this->getCurlResource($endpoint);

        $hasFiles = false;
        if (is_array($postData)) {
            foreach ($postData as $pd) {
                if (is_a($pd, \CURLFile::class)) {
                    $hasFiles = true;
                    break;
                }
            }
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-type: ' . ($hasFiles ? 'multipart/form-data': 'application/json'),
            'Authorization: ' . $authorizationHeader
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);

        return $this->execute($ch, $postData);
    }
}
