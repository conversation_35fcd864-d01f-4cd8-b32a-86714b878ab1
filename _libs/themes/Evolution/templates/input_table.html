<tr{if $hidden_all} style="display: none;"{/if}><td colspan="3">{if trim($var.label)}<div class="t_caption2_title">{$var.label}</div>{/if}
{if trim($var.javascript)}
<script type="text/javascript">
    {$var.javascript}
</script>
{/if}
    <div class="nz-view-wrapper">
<table{if $hidden_all} style="display: none;"{/if} class="t_grouping_table{if $var.borderless} t_borderless{/if}{if $var.custom_class} {$var.custom_class}{/if}"{if $var.t_width} width="{$var.t_width}"{/if} id="var_table_{$var.table}">
  <tr>
    {foreach key='key' from=$var.labels item='label'}
      {capture assign='info'}{if $var.help[$key]}{$var.help[$key]}{else}{$var.labels[$key]}{/if}{/capture}
      <th{if $var.hidden[$key]} style="display: none;"{/if}>
        <div{if $var.width[$key]} style="width: {$var.width[$key]}px;"{/if}>
          <a name="error_{$var.names[$key]}"></a><label for="{$var.names[$key]}"{if $messages->getErrors($var.names[$key])} class="error"{/if}>{help label_content=$label text_content=$info}{if $var.required[$key]} {#required#}{/if}</label>
        </div>
      </th>
    {/foreach}
  </tr>
  <tr>
    {foreach key='key' from=$var.names item='name'}
    {if $var.types[$key] eq 'formula'}
      {assign var=formula_value value=$var.formula[$key].value}
    {else}
      {assign var=formula_value value=$var.index[$key].formula}
    {/if}
    <td{if $var.hidden[$key]} style="display: none;"{/if}>
      {include file=input_`$var.types[$key]`.html
        standalone=true
        name=$name
        value=$var.values[$key]
        value_id=$var.$name.value_id
        var_id=$var.ids[$key]
        label=$var.labels[$key]
        help=$var.help[$key]
        description=$var.descriptions[$key]
        back_label=$var.back_labels[$key]
        back_label_style=$var[$name].back_label_style
        readonly=$var.readonly[$key]
        calculate=$var.calculate[$key]
        hidden=$var.hidden[$key]
        autocomplete=$var.autocomplete[$name]
        origin='table'
        height=$var.height[$key]
        options=$var[$name].options
        optgroups=$var[$name].optgroups
        on_change=$var[$name].on_change
        onchange=$var[$name].onchange
        sequences=$var[$name].sequences
        really_required=$var.required[$key]
        required=$var.required[$key]
        view_mode=$var[$name].view_mode
        thumb_width=$var[$name].thumb_width
        thumb_height=$var[$name].thumb_height
        width=$var.width[$key]
        formulas=$formulas
        formula_value=$formula_value
        source=$var.formula[$key].source
        indexes=$indexes
        date_value=$var.index[$key].date
        options_align=$var[$name].options_align
        js_methods=$var.js_methods[$key]
        restrict=$var.js_filters[$key]
        deleteid=$var.deleteids[$key]
        show_placeholder=$var[$name].show_placeholder
        text_align=$var[$name].text_align
        custom_class=$var[$name].custom_class
      }
    </td>
    {/foreach}
  </tr>
</table>
    </div>
</td></tr>
