{counter name='stat_items_sequence1' assign='stat_items_sequence'}
{*if !$pagination}
  {#pagination_displayed#|escape}:
  <strong>
    <script type="text/javascript">
    document.write('<span id="totalItemsFound_{$stat_items_sequence}">');
    document.write(document.getElementsByName('items[]').length);
    document.write('</span>');
    </script>
  </strong> /
  {#count_selected_items#|escape}:
  <span id="selectedItemsCount_{$stat_items_sequence}">
    {if $selected_items.ids}
      {$selected_items.ids|@count}
    {else}
      0
    {/if}
  </span><br /><br />
{/if*}
<form name="{$module}" action="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}" method="post" enctype="multipart/form-data">
  {if $exclude|default:"" != 'all'}
    <div class="nz-grid-multi-actions">
      <div class="nz-visible">
      <input type="hidden" name="session_param" value="{$session_param}" />
      <input type="hidden" name="after_action" value="" />
      {capture assign='additional_str'}{if $module ne $controller}, '{$controller}'{else}, ''{/if}, {$stat_items_sequence}{if !empty($skip_session_ids)}, '', 1{/if}{/capture}
      {capture assign='module_check'}{if $controller && ($module ne $controller)}{$module}_{$controller}{else}{$module}{/if}{/capture}
      {if empty($exclude) || !preg_match('#activate#', $exclude)}
        {if $currentUser->checkRights($module_check, 'activate')}
        <button type="submit" name="activateButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="activate"
            >{#activate#|escape}</button>
        {/if}
      {/if}

      {if empty($exclude) || !preg_match('#deactivate#', $exclude)}
        {if $currentUser->checkRights($module_check, 'deactivate')}
          <button type="submit" name="deactivateButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="deactivate"
            >{#deactivate#|escape}</button>
        {/if}
      {/if}

      {if empty($exclude) || !preg_match('#delete#', $exclude)}
        {if $currentUser->checkRights($module_check, 'delete')}
          <button type="submit" name="deleteButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="delete"
          >{#delete#|escape}</button>
        {/if}
      {/if}

      {if empty($exclude) || !preg_match('#restore#', $exclude)}
        {if $currentUser->checkRights($module_check, 'restore')}
          <button type="submit" name="restoreButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="restore"
          >{#restore#|escape}</button>
        {/if}
      {/if}

      {if (empty($exclude) || !preg_match('#purge#', $exclude)) && preg_match('#purge#', $include)}
        {if $currentUser->checkRights($module_check, 'purge')}
          <button type="submit" name="purgeButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="purge"
          >{#purge#|escape}</button>
        {/if}
      {/if}

      {if empty($exclude) || !preg_match('#multiedit#', $exclude)}
        {if $currentUser->checkRights($module_check, 'multiedit')}
          <button type="submit" name="multieditButton" class="nz-button--multi nz-button-multi-confirm submit_button" data-action="multiedit" data-mode="submit"
          >{#multiedit#|escape}</button>
        {/if}
      {/if}

      {if preg_match('#tags#', $include) && (!empty($tags_options) || !empty($tags_optgroups)) && $currentUser->checkRights($module_check, 'tags_view') && $currentUser->checkRights($module_check, 'tags_edit')}
        <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                data-popout-template="#multitags-popout"
                data-popout-position="panel: top center at: bottom center"
                data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
        >{#tags#|escape}&nbsp;<i class="material-icons">arrow_drop_down</i></button>
        <script type="text/x-template" id="multitags-popout">
          <aside class="nz-multitags-panel nz-popout-panel nz-pointer-top-center">
            <div class="nz-popout-surface nz-surface nz-elevation--z6">
              <div class="nz-popout-body">
                <div class="nz-block">
                  {include file="`$theme->templatesDir`input_dropdown.html"
                           standalone=true
                           name='tagsSelect'
                           id='tagsSelect'
                           custom_class='notsmall'
                           label=#tags#
                           first_option_label=#select#
                           options=$tags_options
                           optgroups=$tags_optgroups
                  }
                </div>
                <div>
                  {capture assign='tags_onclick'}if ($('tagsSelect').value != '') {ldelim} return confirmation($('tagsSelect'), '{$module}', 'multitag'{$additional_str}) {rdelim} else {ldelim}alert('{#alert_tags#|escape:'quotes'|escape}'); $('tagsSelect').focus(); return false;{rdelim}{/capture}
                  <button type="submit"
                          name="multitag"
                          data-action="multitag"
                          class="nz-button nz-form-button multitags-submit-button"
                  ><i class="material-icons">add_circle_outline</i> {#add#}</button>
                  <button type="submit"
                          name="multiremovetag"
                          data-action="multiremovetag"
                          class="nz-button nz-form-button multitags-submit-button"
                  ><i class="material-icons">remove_circle_outline</i> {#remove#}</button>
                </div>
              </div>
            </div>
          </aside>
      </script>
      {/if}

      {if preg_match('#multistatus#', $include) && !empty($statuses)}
        {if $currentUser->checkRights($module_check, 'multistatus')}
        <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                data-popout-template="#multistatus-popout"
                data-popout-position="panel: top center at: bottom center"
                data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
                >{#setstatus#|escape}&nbsp;<i class="material-icons">arrow_drop_down</i></button>
          <script type="text/x-template" id="multistatus-popout">
            <aside class="nz-multistatus-panel nz-popout-panel nz-pointer-top-center">
              <div class="nz-popout-surface nz-surface nz-elevation--z6">
                  <div class="nz-popout-body">
                    <div class="nz-block">
                      <table>
                        <tr>
                          <td class="required">&nbsp;</td>
                          <td class="labelbox"><select name="multistatusSelect" id="multistatusSelect" class="selbox"
                                                       onchange="toggleUndefined(this); showHideMultistatusCommentField(this);" title="{#setstatus#|escape}">
                                <option class="undefined" value="">[{#select#|escape}]</option>
                                {foreach from=$statuses item='status'}
                                  <option value="{$status.id}" class="{$status.requires_comment}">{$status.name|escape}</option>
                                {/foreach}
                              </select></td>
                        </tr>
                      </table>
                    </div>
                    <div class="nz-block">
                      <table id="multistatus_available_comment_table" style="display: none;">
                        <tr>
                          <td class="required" id="multistatus_required_comment" rowspan="2" style="visibility: hidden;">{#required#}<input type="hidden" name="multistatus_requires_comment" id="multistatus_requires_comment" value="0" /></td>
                          <td class="labelbox"><label for="multistatus_comment">{help label='comment'}</label></td>
                        </tr>
                        <tr>
                          <td>
                            <textarea class="areabox" name="multistatus_comment" id="multistatus_comment" onfocus="highlight(this)" onblur="unhighlight(this)" title="{#comment#|escape}"></textarea>
                            {if $include_portal_users_option && !$currentUser->get('is_portal')}
                              <br />
                              {capture assign="is_portal_suffix"}_{uniqid}{/capture}
                              <input type="radio" name="is_portal" id="is_portal1{$is_portal_suffix}" value="1" title="{#is_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if !isset($default_portal_comment) || $default_portal_comment} checked="checked"{/if} /><label for="is_portal1{$is_portal_suffix}">{#is_portal#|escape}</label>
                              <input type="radio" name="is_portal" id="is_portal2{$is_portal_suffix}" value="0" title="{#is_not_portal#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)"{if isset($default_portal_comment) && !$default_portal_comment} checked="checked"{/if} /><label for="is_portal2{$is_portal_suffix}">{#is_not_portal#|escape}</label>
                            {/if}
                          </td>
                        </tr>
                      </table>
                    </div>
                    <div>
                      {capture assign="button_label"}{$module}_setstatus{/capture}
                      <button type="submit" name="multistatusButton" data-action="multistatus" class="nz-form-button nz-button-primary">{$smarty.config.$button_label|default:#setstatus#|escape}</button>
                    </div>
                  </div>
              </div>
            </aside>
          </script>
        {/if}
      {/if}

      {if preg_match('#multiprint#', $include) && !empty($patterns_grouped)}
        {if $currentUser->checkRights($module_check, 'multiprint')}
          {assign var='one_pattern' value=''}
          {if $patterns_grouped|@count eq 1}
            {foreach from=$patterns_grouped item='patterns' key='pgk' name='pgi'}
              {if $patterns|@count eq 1}
                {assign var='one_pattern' value=$patterns[0].id}
              {/if}
            {/foreach}
          {/if}
          {if $one_pattern}
            <button type="submit" name="multiprintButton" class="nz-button--multi nz-button-multi-confirm" data-action="multiprint"
            >{#print#|escape}</button>
            <input type="hidden" name="pattern" id="pattern" value="{$one_pattern}" />
          {else}

            <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                    data-popout-template="#multiprint-popout"
                    data-popout-position="panel: top center at: bottom center"
                    data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
            >{#print#|escape}&nbsp;<i class="material-icons">arrow_drop_down</i></button>
            <script type="text/x-template" id="multiprint-popout">
              <aside class="nz-multiprint-panel nz-popout-panel nz-pointer-top-center">
                <div class="nz-popout-surface nz-surface nz-elevation--z6">
                  <div class="nz-popout-body">
                    <div class="nz-block">
                        <select name="pattern" id="pattern" class="undefined selbox" onchange="toggleUndefined(this);" title="{#attachments_pattern#|escape}">
                          <option class="undefined" value="">[{#select#|escape}]</option>
                          {foreach from=$patterns_grouped item='patterns' key='pgk' name='pgi'}
                            {if $pgk}<optgroup label="{$pgk|escape}">{/if}
                            {foreach from=$patterns item='pattern'}
                              <option value="{$pattern.id}">{$pattern.name|escape}</option>
                            {/foreach}
                            {if $pgk}</optgroup>{/if}
                          {/foreach}
                        </select>
                    </div>
                    <div>
                      <button type="submit" name="multiprintButton" id="multiprintButton"  data-action="multiprint" data-mode="download" class="nz-form-button nz-button-primary" value="multiprint"
                              >{#print#|escape}</button>
                    </div>
                  </div>
                </div>
              </aside>
            </script>
          {/if}
        {/if}
      {/if}

      {if $module_check eq 'finance_expenses_reasons' && preg_match('#multiaddinvoice#', $include) && $expense_proformas}
        {capture assign='module_type_check'}{$module_check}{$smarty.const.PH_FINANCE_TYPE_EXPENSES_INVOICE}{/capture}
        {if $currentUser->checkRights($module_type_check, 'add')}
          <button type="submit" name="multiaddinvoiceButton" id="multiaddinvoiceButton" class="nz-button--multi" value="multiaddinvoice" onclick="addMergedExpensesInvoice(this); return false;">{#finance_expenses_reasons_addinvoice#|escape}</button>
        {/if}
      {/if}

      {if $module_check eq 'contracts' && preg_match('#change_templates_observer#', $include) && $currentUser->checkRights('contracts', 'editfinance')}
        <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                data-popout-template="#changeTemplateObserver-popout"
                data-popout-position="panel: top center at: bottom center"
                data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
        >{#contracts_change_templates_observer#|escape}&nbsp;<i class="material-icons">arrow_drop_down</i></button>

        <script type="text/x-template" id="changeTemplateObserver-popout">
          <aside class="nz-multichangeObserver-panel nz-popout-panel nz-pointer-top-center">
            <div class="nz-popout-surface nz-surface nz-elevation--z6">
              <div class="nz-popout-body">
                <div class="nz-block">
                  {include file="input_dropdown.html"
                  name='templates_observer'
                  standalone=true
                  options=$users_options
                  label=#contracts_invoices_templates_observer#
                  }
                </div>
                <div>
                  {capture assign="button_label"}{$module}_change_templates_observer{/capture}
                  <button type="submit" name="multiChangeObserverButton" data-action="changeTemplatesObserver" class="nz-form-button nz-button-primary">{$smarty.config.$button_label}</button>
                </div>
              </div>
            </div>
          </aside>
        </script>
      {/if}

      {*if preg_match('#events#', $include)}
        <button type="submit" name="eventsButton" class="nz-button--multi" onclick="return confirmation(this, 'events', 'multiadd')">{#events#|escape}</button>
        <select name="type" id="eventType" class="nz-button--multi" onfocus="highlight(this)" onblur="unhighlight(this)">
        {foreach from=$eventTypes item='event_type'}
          <option value="{$event_type->get('id')}">{$event_type->get('name')}</option>
        {/foreach}
        </select>&nbsp;
      {/if*}

      {if $locking_records && $module eq 'users' && (empty($exclude) || !preg_match('#unlock#', $exclude))}
        {if $currentUser->checkRights($module_check, 'unlock')}
        <button type="submit" name="unlockButton" class="nz-button--multi" onclick="return confirmation(this, '{$module}', 'unlock'{$additional_str})">{#unlock#|escape}</button>
        {/if}
      {/if}

      {if $module eq 'layouts' && $currentUser->checkRights($module_type_check, 'edit')}
        {if preg_match('#multiassignpermissions#', $include)}
          <button type="submit" name="multiAssignPermissionsButton" id="multiAssignPermissionsButton" class="nz-button--multi" value="multiassignpermissions" onclick="launchMultiAssignPermissions(this); return false;">{#layouts_multi_assign_permissions#|escape}</button>
        {/if}
        {if $allow_layouts_order}
          <button type="submit" name="saveOrderButton" id="saveOrderButton" class="nz-button--multi" value="saveorder" onclick="return layoutsOrderSave() && confirmation(this, '{$module}', 'saveorder'{$additional_str}, 1);">{#layouts_saveorder#|escape}</button>
        {/if}
      {/if}

      {if preg_match('#export#', $include)}
        {if $currentUser->checkRights($module_check, 'export')}
          <button type="button" class="nz-button--multi nz-popout-trigger nz-popout-autoinit"
                  data-popout-template="#multiexport-popout"
                  data-popout-position="panel: top center at: bottom center"
                  data-popout-el-parent=".nz-grid-multi-actions .nz-visible"
          >{#export#|escape}&nbsp;<i class="material-icons">arrow_drop_down</i></button>
          <script type="text/x-template" id="multiexport-popout">
            <aside class="nz-multiexport-panel nz-popout-panel nz-pointer-top-center">
              <div class="nz-popout-surface nz-surface nz-elevation--z6">
                <div class="nz-popout-body">
                  <div class="nz-block">
                    <table>
                    {foreach from=$exportAction.options item='exportVar'}
                      {include file="input_`$exportVar.type`.html"
                        name=$exportVar.name
                        id=$exportVar.custom_id|default:null
                        label=$exportVar.label
                        help=$exportVar.help
                        onchange=$exportVar.onchange
                        value=$exportVar.value
                        required=$exportVar.required|default:0
                        hidden=$exportVar.hidden|default:false
                        options=$exportVar.options|default:null}
                    {/foreach}
                    </table>
                  </div>
                  <div>
                    {capture assign="button_label"}{$module}_export{/capture}
                    <button type="submit"
                            name="multiexportButton"
                            data-action="export"
                            data-mode="download"
                            class="nz-form-button nz-button-primary">{$smarty.config.$button_label|default:#export#|escape}</button>
                  </div>
                </div>
              </div>
            </aside>
          </script>
        {/if}
      {/if}

      {*if (!$exclude || !preg_match('#addtonextaction#', $exclude)) && !$skip_session_ids }
        <br /><br />
        <button type="button" name="delIdButton" class="nz-button--multi" onclick="removeIds(this.form,'{$module}','{$controller}');set_checkboxes(this.form, false);">{#del_selected#|escape}</button>
      {/if*}
      </div>
      <div class="nz-extended">
        <i class="nz-icon-button nz-extended-button">more_horiz</i>
        <div class="nz-extended-list">
        </div>
      </div>
    </div>
  {/if}
</form>

<script>
  {literal}
  nz_ready().then(function() {
    // Compatibility mode
    if (document.querySelector('#nz-grid')) {
      return;
    }
    document.body.addEventListener('click', (e) => {
      const targetEl = e.target.closest('.nz-popout-panel button.nz-form-button[name^="multi"][data-action]')
      if (!targetEl) {
        return;
      }
      const action = targetEl.getAttribute('data-action');
      const controller =  env.module_name === env.controller_name ? '' : env.controller_name;

      if (action === 'changeTemplatesObserver') {
        changeTemplatesObserver(document.querySelector('.nz-button--multi'), document.querySelector('#templates_observer'));
        e.preventDefault();
      } else if (['multitag', 'multiremovetag'].includes(action)) {
        e.preventDefault();
        const tagSelectEl = document.querySelector('#tagsSelect');
        if (tagSelectEl.value === '') {
          return;
        }
        return confirmation(tagSelectEl, env.module_name, action, controller, 1);
      } else if (action === 'multistatus') {
        e.preventDefault();
        confirmation(document.querySelector('.nz-button--multi'), env.module_name, action, controller);
      } else {
        e.preventDefault();
        confirmation(targetEl, env.module_name, action, controller, 1);
      }
    });
    const multiActionsBarEL = document.querySelector('.nz-grid-multi-actions');
    if (!multiActionsBarEL) {
      return;
    }
    multiActionsBarEL.classList.add('nz--active');
    multiActionsBarEL.querySelectorAll('.nz-button-multi-confirm').forEach((button) => {
      const controller =  env.module_name === env.controller_name ? '' : env.controller_name;
      button.addEventListener('click', function(e) {
        const actionInpEl = document.querySelector('#multiaction-action');
        const action = this.getAttribute('data-action');
        e.preventDefault();
        confirmation(this, env.module_name, action, controller)
      });
    });
  });
  {/literal}
</script>
