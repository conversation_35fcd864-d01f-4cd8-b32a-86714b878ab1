#lightbox {
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 100000;
    text-align: center;
    line-height: 0;
}
#lightbox #lightboxContents > img, #lightbox #lightboxContents > div.lb_content > img { width: auto; height: auto; max-width: 100%; display: block; }

#dialogHeader {
    font: 1em Verdana, Helvetica, sans-serif;
    margin: 0 auto;
    line-height: 1.25em;
    overflow: hidden;
    padding: 0.5em 0.75em;
    /*background: #ccc url(../images/rounded-white.png) repeat-x 0px -60px;*/
}
#dialogHeader #dialogIcon { float: left; height: 16px; width: auto; margin: 0 0 -5px; }
#dialogHeader #dialogCaption { position: absolute; height: 16px; overflow: hidden; left: 8px; right: 22px; float: left; text-align: left; font-weight: bold; }
#dialogHeader #dialogCaption.hasIcon { padding-left: 2em; }
#dialogHeader #dialogClose {
    margin: 0 -0.5em 0 0;
    float: right;
    /*padding-bottom: 16px;*/
    outline: none;
    /*background: url(../images/close_window.png) 100% 0% no-repeat;*/
}
#dialogHeader #dialogClose::before {
    font-family: "Material Icons";
    content: 'cancel';
    vertical-align: middle;
    font-size: 1.5rem;

    color: var(--primary-color);
}

#outerDialogContainer {
    position: relative;
    background-color: var(--background-color);
    min-width: 50px;
    width: 500px;
    min-height: 50px;
    margin: 0 auto;
    overflow: hidden;
    border-radius: var(--border-radius);

    box-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
                0px 6px 10px 0px rgba(0, 0, 0, 0.14),
                0px 1px 18px 0px rgba(0,0,0,.12);
}
#outerDialogContainer .focus-dummy { width: 1px; height: 0; display: block; overflow: hidden; outline: 0; }
#dialogContainer { padding: 10px; text-align: left; line-height: normal; }
#lightboxContents { height: 100%; overflow: auto; }
#lightboxContents form:last-child { margin-bottom: 0 }
#lightboxContents > div.lb_content { overflow: auto; }

#loadingContainer { position: absolute; top: 0%; left: 0%; height: 100%; width: 100%; text-align: center; line-height: 0; background-color: #ffffff; }
#loadingLink { position: absolute; height: 100%; left: 0; right: 0; background: transparent url(../images/loading1.gif) 50% 50% no-repeat; }
#hoverNav { position: absolute; top: 28px; left: 0; bottom: 0; width: 100%; z-index: 10; }
#hoverNav a { outline: none; }

#prevLink, #nextLink { top: 0; bottom: 0; position: absolute; width: 10%; height: 100%; background-image: url(data:image/gif;base64,AAAA); /* Trick IE into showing hover */ display: block; overflow: hidden; }
#prevLink { left: 0; float: left; right: 90%; min-width: 43px; }
#nextLink { right: 0; float: right; left: 90%; min-width: 43px; }
#prevLink:hover .arrow, #prevLink:visited:hover .arrow { background: rgba(0,0,0,0.4) none 50% 50%/16px auto no-repeat; }
#nextLink:hover .arrow, #nextLink:visited:hover .arrow { background: rgba(0,0,0,0.4) none 50% 50%/16px auto no-repeat; }

#prevLink .arrow, #nextLink .arrow { width: 100%; height: 64px; margin-top: -32px; position: absolute; vertical-align: top; top: 50%; line-height: 1.4em; color: #fff; font-size: 42px; text-align: center; }
#prevLink .arrow { right: 0px; }
#nextLink .arrow { left: 0px; }

#imageDataContainer { font: 10px Verdana, Helvetica, sans-serif; background-color: #fff; margin: 0 auto; line-height: 1.4em; overflow: auto; min-width: 50px; width: 100%; display: none; }

#imageData{ padding: 0 10px; color: #666; }
#imageData #imageDetails { width: 100%; float: left; text-align: left; padding-bottom: 1.0em; }
#imageData #caption { font-weight: bold; }
#imageData #numberDisplay { display: block; clear: left; }
#imageData #bottomNavClose { width: 66px; float: right; padding-bottom: 2.4em; outline: none; background: url(../images/close_window.png) 100% 0% no-repeat; }

#overlay { position: fixed; top: 0; left: 0; z-index: 10001; width: 100%; height: 100%; background-color: #000; }
