
class NzDialog extends NzAbstractOpenable {
    static _dialogStack=[];
    element;
    contentEl;
    scrimEl;
    modal = false;
    contentLoader;
    shouldRecycleView = true;
    template;
    onLoad;
    onOpen;
    onClose;
    clickOutsideClose;
    outsideClickHandlerBound;
    dialogAttributes
    position;
    title;
    content;
    closeOnOutsideClick;
    keyBindingBound;

    constructor(options) {
        super('dialog');
        this.element = options.element ? options.element : null;
        this.template = options.template ? options.template : null;
        this.onLoad = options.onLoad ? options.onLoad : null;
        this.onOpen = options.onOpen ? options.onOpen : null;
        this.onClose = options.onClose ? options.onClose : null;
        this.title = options.title ? options.title : null;
        this.content = options.content ? options.content : null;
        this.width = options.width ? options.width : 400;
        this.height = options.height ? options.height : null
        this.closeOnOutsideClick = options.closeOnOutsideClick ? options.closeOnOutsideClick : true;

        this.dialogAttributes = options.dialogAttributes ? options.dialogAttributes : null;

        this.disableCloseButton = options.disableCloseButton ? options.disableCloseButton : false;
        this.modal = options.modal ? options.modal : null;
        this.clickOutsideClose = !this.modal;

        this.position = options.position ? options.position : {
            panel: 'center middle',
            at: 'center middle',
        };

        if (this.template) {
            this.element = Nz.template2Element(this.template)[0];
        } else if (!this.element) {
            this.element = this.createDialog();
        }
        this.contentEl = this.element.querySelector('.nz-dialog-content');

        if (this.modal) {
            this.scrimEl = this.createScrim();
        }

        if (this.dialogAttributes) {
            for (const [key, value] of Object.entries(this.dialogAttributes)) {
                // class attribute should be added as a list not replaced
                if (key === 'class') {
                    this.element.classList.add(...value.split(' '));
                    continue;
                }
                // other attributes
                this.element.setAttribute(key, value);
            }
        }

        this.setTitle(this.title);

        if (options.content) {
            this.setContent(options.content);
        }

        if (options.buttons) {
            options.buttons.forEach(button => {
                const callback = typeof button.action === 'string' ? window[button.action] : button.action;
                this.addButton(button.label, callback, button.attributes);
            });
        }
        this.applySize();

        if (options.endpoint) {
            this.setContentLoader(new NzContentLoader(options.endpoint, this.contentEl));
        }

        this.outsideClickHandlerBound = this.outsideClickHandler.bind(this);
        this.keyBindingBound = this.keyBinding.bind(this);
    }

    setWidth(width) {
        this.width = width;
        this.applySize();
    }

    setHeight(height) {
        this.height = height;
        this.applySize();
    }

    applySize() {
        if (this.width) {
            this.element.style.width = this.width + 'px';
        } else {
            this.element.style.width = null;
        }

        if (this.height) {
            this.element.style.height = this.height + 'px';
        } else {
            this.element.style.height = null;
        }
    }

    setPositon(position) {
        this.position = position;
        this.applyPosition();
    }

    applyPosition() {
        const rect = this.element.getBoundingClientRect();
        this.element.style.top = `calc(50% - ${rect.height}px / 2 )`;
        this.element.style.left = `calc(50% - ${rect.width}px / 2 )`;
    }

    setZPosition(zPosition) {
        const zIndex = 10200+2*zPosition;
        this.element.style.zIndex = zIndex+1;
        if (this.scrimEl) {
            this.scrimEl.style.zIndex = zIndex;
        }
    }

    addButton(label, callback, attributes) {
        const buttonEl = document.createElement('button');
        buttonEl.classList.add('nz-button');

        let iconEl = '';
        if (attributes && attributes.icon) {
            iconEl = `<i class="material-icons">${attributes.icon}</i> `;
            attributes.icon = null;
        }

        buttonEl.innerHTML = iconEl+label;
        if (callback) {
            buttonEl.addEventListener('click', callback.bind(this));
        }
        if (attributes) {
            for (const [key, value] of Object.entries(attributes)) {
                if (value === null) {
                    continue;
                }
                // class attribute should be added as a list not replaced
                if (key === 'class') {
                    buttonEl.classList.add(...value.split(' '));
                    continue;
                }
                // other attributes
                buttonEl.setAttribute(key, value);
            }
        }
        this.element.querySelector('.nz-dialog-buttons').appendChild(buttonEl);

        return buttonEl;
    }

    createDialog() {
        const dialogEl = document.createElement('div');
        dialogEl.classList.add('nz-dialog');

        const dialogSurface = document.createElement('div');
        dialogSurface.classList.add('nz-dialog-surface', 'nz-surface', 'nz-elevation--z10');
        dialogEl.appendChild(dialogSurface);

        const headEl = document.createElement('div');
        headEl.classList.add('nz-dialog-head');
        dialogSurface.appendChild(headEl);

        const titleEl = document.createElement('div');
        titleEl.classList.add('nz-dialog-title');
        headEl.appendChild(titleEl);

        if (!this.disableCloseButton) {
            const closeEl = document.createElement('i');
            closeEl.classList.add('nz-dialog-close');
            closeEl.classList.add('nz-dialog--close');
            closeEl.classList.add('material-icons');
            closeEl.innerHTML = 'close'
            headEl.appendChild(closeEl);
        }

        const bodyEl = document.createElement('div');
        bodyEl.classList.add('nz-dialog-body');
        dialogSurface.appendChild(bodyEl);

        const contentEl = document.createElement('div');
        contentEl.classList.add('nz-dialog-content');
        bodyEl.appendChild(contentEl);

        const buttonsEl = document.createElement('div');
        buttonsEl.classList.add('nz-dialog-buttons');
        bodyEl.appendChild(buttonsEl);
        return dialogEl;
    }

    createScrim() {
        const scrimEl = document.createElement('div');
        scrimEl.classList.add('nz-dialog-scrim');
        return scrimEl;
    }

    attach(element) {
        if(!element) {
            element = document.body;
        }

        if(typeof element === 'string') {
            element = document.querySelector(element);
        }

        element.appendChild(this.element);
        this.element.classList.add('nz-dialog--upgrade');

        if (this.scrimEl) {
            element.appendChild(this.scrimEl);
        }

        this.element.addEventListener('click', (e) => {
            const close = e.target.closest('.nz-dialog--close');
            const scrim = e.target.closest('.nz-dialog-scrim');

            if ((close && close.closest('.nz-dialog') === this.element)
                    || (scrim && scrim === this.scrimEl)) {
                this.close(e.target);
            }
        });
    }

    detach() {
        this.element.parentElement.removeChild(this.element);

        if (this.scrimEl) {
            this.scrimEl.parentElement.removeChild(this.scrimEl);
        }
    }

    setTitle(title) {
        const headEl = this.element.querySelector('.nz-dialog-head');
        if (title === null) {
            headEl.classList.add('nz--hidden');
            return;
        }

        const titleEl = this.element.querySelector('.nz-dialog-title');
        if (titleEl) {
            titleEl.innerHTML = title;
            headEl.classList.remove('nz--hidden');
        }
    }
    setContent(content) {
        const contentEl = this.element.querySelector('.nz-dialog-content');
        if (contentEl) {
            contentEl.innerHTML = content;
        }
    }

    outsideClickHandler(e) {
        if (this.state !== 'opened') { return; }
        const foundDialog = e.target.closest('.nz-dialog');
        if(foundDialog === this.element) { return; }

        if (foundDialog && foundDialog === this.element) {
            return;
        }
        if (this.closeOnOutsideClick) {
            e.preventDefault();
            this.close(e.target);
        }
    }

    onOpenFn() {
        this.setZPosition(this.getOpenedLength());
        this.applyPosition();

        this.element.classList.add('nz--opening');
        if(this.scrimEl) {
            this.scrimEl.classList.add('nz--opening');
        }
        setTimeout(() => {
            this.element.classList.add('nz--opened');
            this.element.classList.remove('nz--opening');

            if(this.scrimEl) {
                this.scrimEl.classList.add('nz--opened');
                this.scrimEl.classList.remove('nz--opening');
            }
            this.applyPosition();
            if (this.clickOutsideClose) {
                document.body.addEventListener('click', this.outsideClickHandlerBound);
            }
        }, 1);

        const firstBut = this.element.querySelector('button,a');
        if (firstBut) {
            firstBut.focus();
        }

        if (typeof this.onOpen === 'function') {
            this.onOpen.apply(this, [this.element]);
        }

        document.addEventListener('keyup', this.keyBindingBound);
    }

    onCloseFn() {
        this.element.classList.add('nz--closing');
        if (this.scrimEl) {
            this.scrimEl.classList.add('nz--closing');
        }
        setTimeout(() => {
            this.element.classList.remove('nz--opened');
            this.element.classList.remove('nz--closing');

            if (this.scrimEl) {
                this.scrimEl.classList.remove('nz--opened');
                this.scrimEl.classList.remove('nz--closing');
            }
        }, 220);

        if (this.clickOutsideClose) {
            document.body.removeEventListener('click', this.outsideClickHandlerBound);
        }

        if (typeof this.onClose === 'function') {
            this.onClose.apply(this, [this.element]);
        }
        document.removeEventListener('keyup', this.keyBindingBound);
    }
    keyBinding(e) {
        if(e.isComposing || e.keyCode === 229 || this.getTopOpened() !== this) {
            return;
        }

        if (e.key === 'Escape' && !this.disableCloseButton) {
            this.close();
        }
    }

    static closeAll() {
        super.closeAll('dialog');
    }
}
