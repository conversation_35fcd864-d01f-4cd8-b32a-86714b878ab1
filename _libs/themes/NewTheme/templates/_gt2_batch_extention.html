<tr class="batch_data">
  <td colspan="{$cols_count+1}">
  {if empty($val.available_in)}
  <span class="red">{#error_no_quantities_for_selected_warehouses#|escape}</span>
  {else}
    {assign var=article_id value=$val.article_id}
    {json encode=$val.vars_settings assign=vars_settings_json}
    <input type="hidden" id="article_{$row_index}_vars_settings" value="{$vars_settings_json|escape}" />
    {if !$model->get('detailed') && !$existing_batches_processed}
      {if !$val.vars_settings.custom.hidden}
        {assign var=buttons_column value=custom}
      {elseif $val.has_expire && !$val.vars_settings.expire.hidden}
        {assign var=buttons_column value=expire}
      {elseif ($val.has_serial || !empty($existing_serials.$article_id)) && !$val.vars_settings.serial.hidden}
        {assign var=buttons_column value=serial}
      {elseif $val.has_delivery_price && !$val.vars_settings.delivery_price.hidden}
        {assign var=buttons_column value=delivery_price}
      {else}
        {assign var=buttons_column value=quantity}
      {/if}
    {/if}
    <table {if !$model->get('detailed')}id="articles_{$idx}_batch"{/if} class="t_grouping_table">
      <tr>
        <th>{if !$model->get('detailed')}{#num#}{/if}</th>
        <th{if count($warehouses) eq 1} style="display:none;"{/if}>{#finance_warehouses_documents_warehouse#}</th>
        {if $val.has_batch_code || !empty($existing_batches.$article_id) || $existing_batches_processed}
          <th>{#finance_warehouses_documents_batch#}</th>
        {/if}
        <th>
          {assign var='var_label' value=$val.vars_settings.available_quantity.label|default:$table.vars.quantity.label|escape}
          {capture assign='var_help'}{if $val.vars_settings.available_quantity.help}{$val.vars_settings.available_quantity.help|escape}{else}{$var_label}{/if}{/capture}
          {help label_content=$var_label text_content=$var_help label_sufix=''}
          {if $buttons_column eq 'quantity'}
            <div class="t_buttons">
              <div id="articles_{$idx}_batch_plusButton" onclick="addField('articles_{$idx}_batch', '', '', 1)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
              <div id="articles_{$idx}_batch_minusButton" {if !empty($val.batch_data) && $val.batch_data|@count lt 2}class="disabled"{/if} onclick="calcWhQuantity($$('#' + $(articles_{$idx}_batch).rows[$(articles_{$idx}_batch).rows.length-1].id + ' .quantity')[0], {$idx}, 'remove');removeField('articles_{$idx}_batch');recalcAvailableQuantity({$idx})" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
            </div>
          {/if}
        </th>
        {if $val.has_delivery_price}
          <th{if $val.vars_settings.delivery_price.hidden} style="display:none"{/if}>
            {assign var='var_label' value=$val.vars_settings.delivery_price.label|default:#finance_documents_types_gt2_last_delivery_price#|escape}
            {capture assign='var_help'}{if $val.vars_settings.delivery_price.help}{$val.vars_settings.delivery_price.help|escape}{else}{$var_label}{/if}{/capture}
            {help label_content=$var_label text_content=$var_help label_sufix=''}
            {if $buttons_column eq 'delivery_price'}
              <div class="t_buttons">
                <div id="articles_{$idx}_batch_plusButton" onclick="addField('articles_{$idx}_batch', '', '', 1)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                <div id="articles_{$idx}_batch_minusButton" {if !empty($val.batch_data) && $val.batch_data|@count lt 2}class="disabled"{/if} onclick="calcWhQuantity($$('#' + $(articles_{$idx}_batch).rows[$(articles_{$idx}_batch).rows.length-1].id + ' .quantity')[0], {$idx}, 'remove');removeField('articles_{$idx}_batch');recalcAvailableQuantity({$idx})" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
              </div>
            {/if}
          </th>
        {/if}
        {if $val.has_serial || !empty($existing_serials.$article_id)}
          <th{if $val.vars_settings.serial.hidden} style="display:none"{/if}>
            {assign var='var_label' value=$val.vars_settings.serial.label|default:#finance_warehouses_documents_serial#|escape}
            {capture assign='var_help'}{if $val.vars_settings.serial.help}{$val.vars_settings.serial.help|escape}{else}{$var_label}{/if}{/capture}
            {help label_content=$var_label text_content=$var_help label_sufix=''} {#required#}
            {if $buttons_column eq 'serial'}
              <div class="t_buttons">
                <div id="articles_{$idx}_batch_plusButton" onclick="addField('articles_{$idx}_batch', '', '', 1)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                <div id="articles_{$idx}_batch_minusButton" {if !empty($val.batch_data) && $val.batch_data|@count lt 2}class="disabled"{/if} onclick="calcWhQuantity($$('#' + $(articles_{$idx}_batch).rows[$(articles_{$idx}_batch).rows.length-1].id + ' .quantity')[0], {$idx}, 'remove');removeField('articles_{$idx}_batch');recalcAvailableQuantity({$idx})" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
              </div>
            {/if}
          </th>
        {/if}
        {if $val.has_expire}
          <th{if $val.vars_settings.expire.hidden} style="display:none"{/if}>
            {assign var='var_label' value=$val.vars_settings.expire.label|default:#finance_warehouses_documents_expire#|escape}
            {capture assign='var_help'}{if $val.vars_settings.expire.help}{$val.vars_settings.expire.help|escape}{else}{$var_label}{/if}{/capture}
            {help label_content=$var_label text_content=$var_help label_sufix=''} {#required#}
            {if $buttons_column eq 'expire'}
              <div class="t_buttons">
                <div id="articles_{$idx}_batch_plusButton" onclick="addField('articles_{$idx}_batch', '', '', 1)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
                <div id="articles_{$idx}_batch_minusButton" {if !empty($val.batch_data) && $val.batch_data|@count lt 2}class="disabled"{/if} onclick="calcWhQuantity($$('#' + $(articles_{$idx}_batch).rows[$(articles_{$idx}_batch).rows.length-1].id + ' .quantity')[0], {$idx}, 'remove');removeField('articles_{$idx}_batch');recalcAvailableQuantity({$idx})" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
              </div>
            {/if}
          </th>
        {/if}
        <th{if $val.vars_settings.custom.hidden} style="display:none"{/if}>
          {assign var='var_label' value=$val.vars_settings.custom.label|default:#finance_warehouses_documents_custom#|escape}
          {capture assign='var_help'}{if $val.vars_settings.custom.help}{$val.vars_settings.custom.help|escape}{else}{$var_label}{/if}{/capture}
          {help label_content=$var_label text_content=$var_help label_sufix=''}
          {if $buttons_column eq 'custom'}
            <div class="t_buttons">
              <div id="articles_{$idx}_batch_plusButton" onclick="addField('articles_{$idx}_batch', '', '', 1)" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
              <div id="articles_{$idx}_batch_minusButton" {if !empty($val.batch_data) && $val.batch_data|@count lt 2}class="disabled"{/if} onclick="calcWhQuantity($$('#' + $(articles_{$idx}_batch).rows[$(articles_{$idx}_batch).rows.length-1].id + ' .quantity')[0], {$idx}, 'remove');removeField('articles_{$idx}_batch');recalcAvailableQuantity({$idx})" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
            </div>
          {/if}
        </th>
      </tr>
      {foreach from=$val.batch_data item=bo name=r}
      {assign var=batch_id value=$bo.batch}
      {assign var=batch_code value=$bo.batch_code}
      <tr id="articles_{$idx}_batch_{if $model->get('detailed')}static_{/if}{$smarty.foreach.r.iteration}">
        <td style="white-space: nowrap;">
          {if $model->get('detailed')}
            {capture assign=field_name}article_{$row_index}_active{/capture}
            {capture assign=field_onclick}reverseHandoverRowManip(this, 'articles_{$idx}_batch_static_{$smarty.foreach.r.iteration}');{/capture}
            {include file=input_checkbox.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              value=$bo.active
              option_value=1
              standalone=true
              onclick=$field_onclick
            }
          {else}
            <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" {if $val.batch_data|@count lt 2} style="visibility: hidden;"{/if} onclick="return confirmAction('delete_row', function(el) {ldelim} calcWhQuantity($('article_{$row_index}_quantity_{$smarty.foreach.r.iteration}'), {$idx}, 'remove');hideField('articles_{$idx}_batch','{$smarty.foreach.r.iteration}');calcWhQuantity($('article_{$row_index}_quantity_{$smarty.foreach.r.iteration}'), {$idx}, 'enable');recalcAvailableQuantity({$idx}); {rdelim}, this);" />&nbsp;<span>{$smarty.foreach.r.iteration}</span>
          {/if}
        </td>
        <td style="{if count($warehouses) eq 1}display: none;{/if} min-width: 200px;">
        {if count($warehouses) eq 1 || count($val.available_in) eq 1 || $model->get('detailed')}
          {foreach from=$warehouses item=wh}
            {if in_array($wh->get('id'), $val.available_in) && $bo.warehouse eq $wh->get('id') || $warehouses|@count eq 1}
              {$wh->get('name')}
              <input type="hidden" name="article_{$row_index}_wh[{$smarty.foreach.r.iteration-1}]" id="article_{$row_index}_wh_{$smarty.foreach.r.iteration}" value="{$wh->get('id')}" onchange="calcWhQuantity(this, {$idx});recalcAvailableQuantity({$idx})"{if $model->get('detailed') && !$bo.active} disabled="disabled"{/if} class="{if $model->get('detailed') && !$bo.active}input_inactive{/if}{if count($warehouses) eq 1 || count($val.available_in) eq 1} copy_values{/if}" />
            {/if}
          {/foreach}
          {if $model->get('detailed')}
            <input type="hidden" name="article_{$row_index}_batch[{$smarty.foreach.r.iteration-1}]" id="article_{$row_index}_batch_{$smarty.foreach.r.iteration}" value="{$bo.batch}" {if $model->get('detailed') && !$bo.active} disabled="disabled" class="input_inactive"{/if} />
          {/if}
        {else}
          <select class="selbox{if !$bo.warehouse} undefined{/if}" name="article_{$row_index}_wh[{$smarty.foreach.r.iteration-1}]" id="article_{$row_index}_wh_{$smarty.foreach.r.iteration}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this); calcWhQuantity(this, {$idx});recalcAvailableQuantity({$idx})" title="{#finance_warehouses_documents_warehouse#|escape}">
            <option value="" class="undefined">[{#please_select#|escape}]</option>
            {foreach from=$warehouses item=wh}
              {if $table.handover_direction eq 'incoming' || in_array($wh->get('id'), $val.available_in)}
                <option value="{$wh->get('id')}"{if $bo.warehouse eq $wh->get('id')} selected="selected"{/if}>{$wh->get('name')}</option>
              {/if}
            {/foreach}
          </select>
        {/if}
        {capture assign=field_name}article_{$row_index}_wh_old{/capture}
        {if $model->get('detailed') && !$bo.active}
          {assign var=field_disabled value=true}
          {assign var=field_class value='input_inactive'}
        {else}
          {assign var=field_disabled value=false}
          {assign var=field_class value='dont_copy_values'}
        {/if}
        {include file=input_hidden.html
          value=''
          name=$field_name
          index=$smarty.foreach.r.iteration
          standalone=1
          disabled=$field_disabled
          custom_class=$field_class
        }
        </td>
        {if $val.has_batch_code && empty($existing_batches.$article_id) && !$existing_batches_processed}
        <td style="min-width:200px;">
          {capture assign=field_name}article_{$row_index}_batch{/capture}
          {if $model->get('detailed')}
            {$bo.batch_code}
          {else}
            {include file=input_text.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$bo.batch_code
              disabled=$field_disabled
              custom_class=$field_class
              label=#finance_warehouses_documents_batch#
              help=#finance_warehouses_documents_batch#
            }
            {capture assign=field_name}article_{$row_index}_batch_isCustom{/capture}
              {include file=input_hidden.html
                name=$field_name
                index=$smarty.foreach.r.iteration
                standalone=1
                value=1
                disabled=$field_disabled
                custom_class=copy_values
              }
          {/if}
        </td>
        {elseif !empty($existing_batches.$article_id) || $existing_batches_processed}
        <td style="min-width:200px;">
          {capture assign=field_name}article_{$row_index}_batch{/capture}
          {if $val.has_batch_code && ($model->get('type') eq PH_FINANCE_TYPE_INSPECTION || $model->get('handover_direction') eq 'incoming' && $model->modelName eq 'Finance_Expenses_Reason')}
            {assign var=existing_batches_options value=$existing_batches.$article_id}
            {if $batch_id && empty($existing_batches.$article_id.$batch_id)}
              {* VERY SPECIAL BEHAVIOUR FOR COMBOBOX CUSTOM ITEM *}
              {assign var=skip_ps value=true}
              {array assign=new_item label=$batch_code option_value=$batch_id class_name=undefined}
              {array_prepend array=existing_batches_options key=$batch_id new_item=$new_item}
            {else}
              {assign var=skip_ps value=false}
            {/if}
            {include file=input_combobox.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$bo.batch
              disabled=$field_disabled
              readonly=$existing_batches_processed
              custom_class=$field_class
              options=$existing_batches_options
              onchange=getExistingBatchData(this)
              skip_please_select=$skip_ps
              label=#finance_warehouses_documents_batch#
              help=#finance_warehouses_documents_batch#
            }
            {if $skip_ps}
              {capture assign=field_name}article_{$row_index}_batch_isCustom{/capture}
              {include file=input_hidden.html
                name=$field_name
                index=$smarty.foreach.r.iteration
                standalone=1
                value=1
                disabled=$field_disabled
                custom_class=$field_class
              }
            {/if}
          {elseif !$existing_batches_processed}
            {include file=input_dropdown.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$bo.batch
              disabled=$field_disabled
              custom_class=$field_class
              options=$existing_batches.$article_id
              onchange=getExistingBatchData(this)
              label=#finance_warehouses_documents_batch#
              help=#finance_warehouses_documents_batch#
            }
          {else}
            {$bo.batch_code}
            {include file=input_hidden.html
                name=$field_name
                index=$smarty.foreach.r.iteration
                standalone=1
                value=$bo.batch
                disabled=$field_disabled
                custom_class=$field_class
              }
          {/if}
          {if $bo.batch_data_json_object}
            {json encode=$bo.batch_data_json_object assign=batch_data_json_object}
            {capture assign=field_name}article_{$row_index}_batch_data_json_object{/capture}
            {include file=input_hidden.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$batch_data_json_object
            }
          {/if}
        </td>
        {/if}
        <td style="white-space: nowrap;{if $val.vars_settings.available_quantity.text_align}text-align: {$val.vars_settings.available_quantity.text_align};{/if}{if $val.vars_settings.available_quantity.width} width: {$val.vars_settings.available_quantity.width}{if preg_match('#^\d+$#', $val.vars_settings.available_quantity.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_quantity{/capture}
          {capture assign=f_onkeyup}calcWhQuantity(this, {$idx}){/capture}
          {capture assign=q_field_class}{$field_class} quantity{/capture}
          {if $val.vars_settings.available_quantity.width gt 0}
            {assign var=f_width value=$val.vars_settings.available_quantity.width/2}
          {else}
            {assign var=f_width value=60}
          {/if}
          {include file=input_text.html
            name=$field_name
            index=$smarty.foreach.r.iteration
            standalone=1
            onkeyup=$f_onkeyup
            restrict=insertOnlyFloats
            value=$bo.quantity
            disabled=$field_disabled
            custom_class=$q_field_class
            width=$f_width
            text_align=right
            label=$val.vars_settings.available_quantity.label|default:$table.vars.quantity.label
            help=$val.vars_settings.available_quantity.help|default:$table.vars.quantity.help
            show_placeholder=$val.vars_settings.available_quantity.show_placeholder|default:$table.vars.quantity.show_placeholder
          }
          {capture assign=available_key}{$bo.serial}_{$bo.custom}{/capture}
          {foreach from=$bo.batch_data_json_object.available_quantity item=aq key=sc}
            <span id="{$sc|escape}" {if $sc ne $available_key || $val.vars_settings.available_quantity.hidden}style="display:none"{/if}{if $sc eq $available_key} class="active_available"{/if}>/ {$aq}</span>
          {foreachelse}
            {*if !$existing_batches_processed*}
              <span> / {$bo.available_quantity}</span>
            {*/if*}
          {/foreach}
          {capture assign=field_name}article_{$row_index}_quantity_old{/capture}
          {include file=input_hidden.html
            value=$bo.quantity
            name=$field_name
            index=$smarty.foreach.r.iteration
            standalone=1
            disabled=$field_disabled
            custom_class=$field_class
          }
        </td>
        {if $existing_batches.$article_id.$batch_id && !$existing_batches.$article_id.$batch_id.just_added || $val.vars_settings.delivery_price.readonly}
          {assign var=freadonly value=true}
        {else}
          {assign var=freadonly value=false}
        {/if}
        {if $val.has_delivery_price}
        <td style="white-space: nowrap;{if $val.vars_settings.delivery_price.text_align}text-align: {$val.vars_settings.delivery_price.text_align};{/if}{if $val.vars_settings.delivery_price.hidden} display: none;{/if}{if $val.vars_settings.delivery_price.width} width: {$val.vars_settings.delivery_price.width}{if preg_match('#^\d+$#', $val.vars_settings.delivery_price.width)}px{/if}{/if}">
        {capture assign=field_name}article_{$row_index}_delivery_price{/capture}
        {if $val.vars_settings.delivery_price.width gt 0}
          {assign var=f_width value=$val.vars_settings.delivery_price.width/2}
        {else}
          {assign var=f_width value=60}
        {/if}
        {if $val.has_delivery_price eq 2}
          {include file=input_text.html
              value=$bo.delivery_price
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              disabled=$field_disabled
              custom_class=$field_class
              hidden=$freadonly
              restrict=insertOnlyFloats
              text_align=right
              onkeyup=gt2calc(this)
              width=$f_width
              label=$val.vars_settings.delivery_price.label|default:#finance_documents_types_gt2_last_delivery_price#
              help=$val.vars_settings.delivery_price.help|default:#finance_documents_types_gt2_last_delivery_price#
              show_placeholder=$val.vars_settings.delivery_price.show_placeholder
            }
            {if $freadonly}<div id="{$field_name}_{$smarty.foreach.r.iteration}_formatted" style="text-align: right">{$bo.delivery_price|string_format:$price_prec} {$bo.currency}</div>{/if}
            {capture assign=field_name}article_{$row_index}_currency{/capture}
            {include file=input_dropdown.html
              value=$bo.currency
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              disabled=$field_disabled
              custom_class=$field_class
              hidden=$freadonly
              options=$currencies_available
              required=1
              width=$f_width
              label=$table.plain_vars.currency.label|default:#finance_warehouses_documents_currency#
              help=$table.plain_vars.currency.help|default:#finance_warehouses_documents_currency#
              show_placeholder=$table.plain_vars.currency.show_placeholder
            }
        {else}
          {$bo.delivery_price|string_format:$price_prec} {$bo.currency}
            {include file=input_hidden.html
              value=$bo.delivery_price
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              disabled=$field_disabled
              custom_class=$field_class
            }
            {capture assign=field_name}article_{$row_index}_currency{/capture}
            {include file=input_hidden.html
              value=$bo.currency
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              disabled=$field_disabled
              custom_class=$field_class
            }
          {/if}
        </td>
        {/if}
        {if $val.has_serial}
        <td style="{if $val.vars_settings.serial.text_align}text-align: {$val.vars_settings.serial.text_align};{/if}{if $val.vars_settings.serial.hidden}display: none;{/if}{if $val.vars_settings.serial.width} width: {$val.vars_settings.serial.width}{if preg_match('#^\d+$#', $val.vars_settings.serial.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_serial{/capture}
          {if empty($existing_serials.$article_id.$batch_id)}
              {if $model->get('detailed') || $val.vars_settings.serial.readonly || $existing_batches_processed}
                {$bo.serial}
                {include file=input_hidden.html
                  name=$field_name
                  index=$smarty.foreach.r.iteration
                  standalone=1
                  value=$bo.serial
                  disabled=$field_disabled
                  custom_class=$field_class
                }
              {else}
                {include file=input_text.html
                  name=$field_name
                  index=$smarty.foreach.r.iteration
                  standalone=1
                  value=$bo.serial
                  disabled=$field_disabled
                  custom_class=$field_class
                  label=$val.vars_settings.serial.label|default:#finance_warehouses_documents_serial#
                  help=$val.vars_settings.serial.help|default:#finance_warehouses_documents_serial#
                  show_placeholder=$val.vars_settings.serial.show_placeholder
                }
              {/if}
          {else}
            {capture assign=s_change}showSerialCustomQuantity(this);recalcAvailableQuantity({$idx}){/capture}
            {include file=input_dropdown.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$bo.serial
              disabled=$field_disabled
              readonly=$val.vars_settings.serial.readonly
              custom_class=$field_class
              options=$existing_serials.$article_id.$batch_id
              onchange=$s_change
              required=1
              label=$val.vars_settings.serial.label|default:#finance_warehouses_documents_serial#
              help=$val.vars_settings.serial.help|default:#finance_warehouses_documents_serial#
              show_placeholder=$val.vars_settings.serial.show_placeholder
            }
          {/if}
        </td>
        {else}
        {/if}
        {if $val.has_expire}
        {if $existing_batches.$article_id.$batch_id && !$existing_batches.$article_id.$batch_id.just_added}
          {assign var=freadonly value=true}
        {else}
          {assign var=freadonly value=false}
        {/if}
        <td style="{if $val.vars_settings.expire.text_align}text-align: {$val.vars_settings.expire.text_align};{/if}{if $val.vars_settings.expire.hidden}display: none;{/if}{if $val.vars_settings.expire.width} width: {$val.vars_settings.expire.width}{if preg_match('#^\d+$#', $val.vars_settings.expire.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_expire{/capture}
          {if $model->get('detailed') || $val.vars_settings.expire.readonly || $existing_batches_processed}
            {$bo.expire|date_format:#date_short#}
            {include file=input_hidden.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$bo.expire
              disabled=$field_disabled
              custom_class=$field_class
            }
          {else}
            {include file=input_date.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$bo.expire
              disabled=$field_disabled
              custom_class=$field_class
              readonly=$freadonly
              label=$val.vars_settings.expire.label|default:#finance_warehouses_documents_expire#
              help=$val.vars_settings.expire.help|default:#finance_warehouses_documents_expire#
              show_placeholder=$val.vars_settings.expire.show_placeholder
            }
          {/if}
        </td>
        {/if}
        <td style="{if $val.vars_settings.custom.text_align}text-align: {$val.vars_settings.custom.text_align};{/if}{if $val.vars_settings.custom.hidden}display: none;{/if}{if $val.vars_settings.custom.width} width: {$val.vars_settings.custom.width}{if preg_match('#^\d+$#', $val.vars_settings.custom.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_custom{/capture}
          {if empty($existing_custom.$article_id.$batch_id)}
              {if $model->get('detailed') || $val.vars_settings.custom.readonly || $existing_batches_processed}
                {$bo.custom}
                {include file=input_hidden.html
                  name=$field_name
                  index=$smarty.foreach.r.iteration
                  standalone=1
                  value=$bo.custom
                  disabled=$field_disabled
                  custom_class=$field_class
                }
              {else}
                {include file=input_text.html
                  name=$field_name
                  index=$smarty.foreach.r.iteration
                  standalone=1
                  value=$bo.custom
                  disabled=$field_disabled
                  custom_class=$field_class
                  label=$val.vars_settings.custom.label|default:#finance_warehouses_documents_custom#
                  help=$val.vars_settings.custom.help|default:#finance_warehouses_documents_custom#
                  show_placeholder=$val.vars_settings.custom.show_placeholder
                }
              {/if}
          {else}
            {capture assign=s_change}showSerialCustomQuantity(this);recalcAvailableQuantity({$idx}){/capture}
            {include file=input_dropdown.html
              name=$field_name
              index=$smarty.foreach.r.iteration
              standalone=1
              value=$bo.custom
              disabled=$field_disabled
              readonly=$val.vars_settings.custom.readonly
              custom_class=$field_class
              options=$existing_custom.$article_id.$batch_id
              onchange=$s_change
              required=1
              label=$val.vars_settings.custom.label|default:#finance_warehouses_documents_custom#
              help=$val.vars_settings.custom.help|default:#finance_warehouses_documents_custom#
              show_placeholder=$val.vars_settings.custom.show_placeholder
            }
          {/if}
          <script type="text/javascript">if ($('article_{$row_index}_wh_{$smarty.foreach.r.iteration}')) {ldelim} $('article_{$row_index}_wh_{$smarty.foreach.r.iteration}').onchange(); {rdelim}</script>
        </td>
      </tr>
      {foreachelse}
      <tr id="articles_{$idx}_batch_{$custom_iterator|default:1}">
        <td style="white-space: nowrap;">
          <img src="{$theme->imagesUrl}small/delete.png" height="12" width="12" alt="{#delete#|escape}" title="{#delete#|escape}" class="hide_row" style="visibility: hidden;" onclick="return confirmAction('delete_row', function(el) {ldelim} calcWhQuantity($('article_{$row_index}_quantity_{$custom_iterator|default:1}'), {$idx}, 'remove');hideField('articles_{$idx}_batch','{$custom_iterator|default:1}');calcWhQuantity($('article_{$row_index}_quantity_{$custom_iterator|default:1}'), {$idx}, 'enable');recalcAvailableQuantity({$idx}); {rdelim}, this);" />&nbsp;<span>1</span>
        </td>
        <td style="{if count($warehouses) eq 1}display: none;{/if} min-width: 200px;">
        {if count($warehouses) eq 1 || count($val.available_in) eq 1}
          {foreach from=$warehouses item=wh}
          {$wh->get('name')}
          <input type="hidden" name="article_{$row_index}_wh[{if $custom_iterator}{$custom_iterator-1}{else}0{/if}]" id="article_{$row_index}_wh_{$custom_iterator|default:1}" value="{$wh->get('id')}" class="copy_values">
          {/foreach}
        {else}
          <select class="selbox undefined" name="article_{$row_index}_wh[{if $custom_iterator}{$custom_iterator-1}{else}0{/if}]" id="article_{$row_index}_wh_{$custom_iterator|default:1}" onfocus="highlight(this);" onblur="unhighlight(this);" onchange="toggleUndefined(this); calcWhQuantity(this, {$idx});recalcAvailableQuantity({$idx})" title="{#finance_warehouses_documents_warehouse#|escape}">
            <option value="" class="undefined">[{#please_select#|escape}]</option>
            {foreach from=$warehouses item=wh}
              {if $table.handover_direction eq 'incoming' || in_array($wh->get('id'), $val.available_in)}
                <option value="{$wh->get('id')}">{$wh->get('name')}</option>
              {/if}
            {/foreach}
          </select>
        {/if}
        {capture assign=field_name}article_{$row_index}_wh_old{/capture}
        {include file=input_hidden.html
          value=''
          name=$field_name
          index=$custom_iterator|default:1
          standalone=1
          custom_class=dont_copy_values
        }
        </td>
        {assign var=article_id value=$val.article_id}
        {if $val.has_batch_code && empty($existing_batches.$article_id)}
        <td style="min-width: 200px;">
          {capture assign=field_name}article_{$row_index}_batch{/capture}
          {include file=input_text.html
            name=$field_name
            index=$custom_iterator|default:1
            standalone=1
            value=''
            label=#finance_warehouses_documents_batch#
            help=#finance_warehouses_documents_batch#
          }
          {capture assign=field_name}article_{$row_index}_batch_isCustom{/capture}
          {include file=input_hidden.html
            name=$field_name
            index=1
            standalone=1
            value=1
            disabled=$field_disabled
            custom_class=copy_values
          }
        </td>
        {elseif !empty($existing_batches.$article_id)}
        <td style="min-width: 200px;">
          {capture assign=field_name}article_{$row_index}_batch{/capture}
          {if $val.has_batch_code && ($model->get('type') eq PH_FINANCE_TYPE_INSPECTION || $model->get('handover_direction') eq 'incoming' && $model->modelName eq 'Finance_Expenses_Reason')}
            {include file=input_combobox.html
              name=$field_name
              index=$custom_iterator|default:1
              standalone=1
              options=$existing_batches.$article_id
              onchange=getExistingBatchData(this)
              label=#finance_warehouses_documents_batch#
              help=#finance_warehouses_documents_batch#
            }
          {else}
            {include file=input_dropdown.html
              name=$field_name
              index=$custom_iterator|default:1
              standalone=1
              options=$existing_batches.$article_id
              onchange=getExistingBatchData(this)
              label=#finance_warehouses_documents_batch#
              help=#finance_warehouses_documents_batch#
            }
          {/if}
        </td>
        {/if}
        <td style="{if $val.vars_settings.available_quantity.text_align}text-align: {$val.vars_settings.available_quantity.text_align};{/if}{if $val.vars_settings.available_quantity.width} width: {$val.vars_settings.available_quantity.width}{if preg_match('#^\d+$#', $val.vars_settings.available_quantity.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_quantity{/capture}
          {capture assign=f_onkeyup}calcWhQuantity(this, {$idx}){/capture}
          {include file=input_text.html
            name=$field_name
            index=$custom_iterator|default:1
            standalone=1
            onkeyup=$f_onkeyup
            restrict=insertOnlyFloats
            value=''
            text_align=right
            custom_class=quantity
            label=$val.vars_settings.available_quantity.label|default:$table.vars.quantity.label
            help=$val.vars_settings.available_quantity.help|default:$table.vars.quantity.help
            show_placeholder=$val.vars_settings.available_quantity.show_placeholder|default:$table.vars.quantity.show_placeholder
          }
          {capture assign=field_name}article_{$row_index}_quantity_old{/capture}
          {include file=input_hidden.html
            value=''
            name=$field_name
            index=$custom_iterator|default:1
            standalone=1
            custom_class=dont_copy_values
          }
        </td>
        {if $val.has_delivery_price}
        <td style="{if $val.vars_settings.delivery_price.text_align}text-align: {$val.vars_settings.delivery_price.text_align};{/if}{if $val.vars_settings.delivery_price.hidden}display: none;{/if} white-space: nowrap;{if $val.vars_settings.delivery_price.width} width: {$val.vars_settings.delivery_price.width}{if preg_match('#^\d+$#', $val.vars_settings.delivery_price.width)}px{/if}{/if}">
        {capture assign=field_name}article_{$row_index}_delivery_price{/capture}
        {if $val.vars_settings.delivery_price.width gt '0'}
          {assign var=f_width value=$val.vars_settings.delivery_price.width/2}
        {else}
          {assign var=f_width value=60}
        {/if}
        {include file=input_text.html
            value=''
            name=$field_name
            index=$custom_iterator|default:1
            standalone=1
            restrict=insertOnlyFloats
            text_align=right
            width=$f_width
            readonly=$val.vars_settings.delivery_price.readonly
            label=$val.vars_settings.delivery_price.label|default:#finance_documents_types_gt2_last_delivery_price#
            help=$val.vars_settings.delivery_price.help|default:#finance_documents_types_gt2_last_delivery_price#
            show_placeholder=$val.vars_settings.delivery_price.show_placeholder
          }
          {capture assign=field_name}article_{$row_index}_currency{/capture}
            {include file=input_dropdown.html
              value=$main_currency
              name=$field_name
              index=1
              standalone=1
              options=$currencies_available
              width=$f_width
              required=1
              readonly=$val.vars_settings.delivery_price.readonly
              label=$table.plain_vars.currency.label|default:#finance_warehouses_documents_currency#
              help=$table.plain_vars.currency.help|default:#finance_warehouses_documents_currency#
              show_placeholder=$table.plain_vars.currency.show_placeholder
            }
        </td>
        {/if}
        {if $val.has_serial}
        <td style="{if $val.vars_settings.serial.text_align}text-align: {$val.vars_settings.serial.text_align};{/if}{if $val.vars_settings.serial.hidden}display: none;{/if}{if $val.vars_settings.serial.width} width: {$val.vars_settings.serial.width}{if preg_match('#^\d+$#', $val.vars_settings.serial.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_serial{/capture}
          {include file=input_text.html
            name=$field_name
            index=$custom_iterator|default:1
            standalone=1
            value=''
            readonly=$val.vars_settings.serial.readonly
            label=$val.vars_settings.serial.label|default:#finance_warehouses_documents_serial#
            help=$val.vars_settings.serial.label|default:#finance_warehouses_documents_serial#
            show_placeholder=$val.vars_settings.serial.show_placeholder
          }
        </td>
        {/if}
        {if $val.has_expire}
        <td style="{if $val.vars_settings.expire.text_align}text-align: {$val.vars_settings.expire.text_align};{/if}{if $val.vars_settings.expire.hidden}display: none;{/if}{if $val.vars_settings.expire.width} width: {$val.vars_settings.expire.width}{if preg_match('#^\d+$#', $val.vars_settings.expire.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_expire{/capture}
          {include file=input_date.html
            name=$field_name
            index=$custom_iterator|default:1
            standalone=1
            value=''
            readonly=$val.vars_settings.expire.readonly
            label=$val.vars_settings.expire.label|default:#finance_warehouses_documents_expire#
            help=$val.vars_settings.expire.help|default:#finance_warehouses_documents_expire#
            show_placeholder=$val.vars_settings.expire.show_placeholder
          }
        </td>
        {/if}
        <td style="{if $val.vars_settings.custom.text_align}text-align: {$val.vars_settings.custom.text_align};{/if}{if $val.vars_settings.custom.hidden}display: none;{/if}{if $val.vars_settings.custom.width} width: {$val.vars_settings.custom.width}{if preg_match('#^\d+$#', $val.vars_settings.custom.width)}px{/if}{/if}">
          {capture assign=field_name}article_{$row_index}_custom{/capture}
          {include file=input_text.html
            name=$field_name
            index=$custom_iterator|default:1
            standalone=1
            value=''
            readonly=$val.vars_settings.custom.readonly
            label=$val.vars_settings.custom.label|default:#finance_warehouses_documents_custom#
            help=$val.vars_settings.custom.help|default:#finance_warehouses_documents_custom#
            show_placeholder=$val.vars_settings.custom.show_placeholder
          }
        </td>
      </tr>
      {/foreach}
    </table>
  {/if}
  </td>
</tr>
