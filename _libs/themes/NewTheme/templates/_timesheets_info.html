<table cellpadding="0" cellspacing="0" border="0" class="attachments t_grouping_table t_table hleft" style="border: 1px solid #999999; z-index: 10000; width: 300px; margin: 0px;">
  <tr>
    <th class="nowrap">
      <a title="{#expand_all#|escape}" href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$smarty.request.real_module}{if $smarty.request.real_controller ne $smarty.request.real_module}&amp;controller={$smarty.request.real_controller}{/if}&amp;{$smarty.request.real_controller}=timesheets&amp;timesheets={$smarty.request.model_id}{if $smarty.request.archive}&amp;archive=1{/if}"><span class="t_panel_caption_title">{#tasks_timesheets_last_records_info#|escape}</span></a>
    </th>
  </tr>

{foreach from=$timesheets name='ti' item='timesheet'}
  {if $smarty.foreach.ti.first}{capture assign='cycle_name'}last_records_{$timesheet->modelName}_{$timesheet->get('task_id')}{/capture}{/if}
  <tr class="{cycle name=$cycle_name values='t_odd,t_even'}">
    <td class="t_border t_bottom_border" style="color: #000000;">
      {$timesheet->get('user_id_name')|escape} ({$timesheet->get('startperiod')|date_format:#date_short#}{if $timesheet->get('startperiod')|date_format:#date_iso_short# ne $timesheet->get('endperiod')|date_format:#date_iso_short#}-{$timesheet->get('endperiod')|date_format:#date_short#}{/if}, {$timesheet->get('duration')} {if $timesheet->get('duration') eq 1}{#minute#}{else}{#minutes#}{/if})<br />
      {if $timesheet->get('subject')}<span class="strong">{$timesheet->get('subject')}</span><br />{/if}
      {$timesheet->get('content')|strip_tags|mb_truncate:130|nl2br|url2href}
      {*
      <strong>{#tasks_timesheets_startperiod#}:</strong> {if $timesheet->get('period_type') eq 'dates'}{$timesheet->get('startperiod')|date_format:"%d.%m.%Y %H:%M"}{else}{$timesheet->get('startperiod')|date_format:"%d.%m.%Y"}{/if}<br />
      <strong>{#tasks_timesheets_endperiod#}:</strong> {if $timesheet->get('period_type') eq 'dates'}{$timesheet->get('endperiod')|date_format:"%d.%m.%Y %H:%M"}{else}{$timesheet->get('endperiod')|date_format:"%d.%m.%Y"}{/if}<br />
      *}
    </td>
  </tr>
{foreachelse}
  <tr>
    <td class="t_border t_bottom_border" style="color: #FF0000;">
      {if $no_permissions}{#timesheets_no_permissions#|escape}{else}{#error_no_items_found#|escape}{/if}
    </td>
  </tr>
{/foreach}
</table>
