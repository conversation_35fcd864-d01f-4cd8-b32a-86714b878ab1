/*Header Menu styles for old menus where we have*/
.m_header_menu {
    color: #B2B2B2;
    height: 30px;
    background: #FFFFFF;
    padding-top: 5px;
    padding-bottom: 1px;
    vertical-align: bottom;
    background: left bottom repeat-x #D3D3D3;
    border-bottom: 1px solid #222433;
}

div.m_header_menu {
    height: 0 !important;
    padding: 0;
}

.m_header_menu ul {
    margin: 0;
    padding: 5px;
    z-index: 40;
}

.m_header_menu li {
    display: inline;
    margin: 0;
    padding: 0;
}

.m_header_menu li span {
    float: left;
    background: url('../images/menu_tab_left.gif') no-repeat left top;
    background-position: 0% 0px;
    margin: 0;
    padding: 0 0 0 4px;
    text-decoration: none;
}

.m_header_menu li span a {
    float: left;
    display: block;
    background: url('../images/menu_tab_right.gif') no-repeat right top;
    padding: 5px 7px 4px 2px;
    color: #FF6600;
    cursor: pointer;
}

/* Commented Backslash Hack hides rule from IE5-Mac \*/
.m_header_menu li span a {
    /*float:none;*/
}

/* End IE5-Mac hack */
.m_header_menu li span:hover, .m_header_menu li span.selected {
    background-position: 0% -42px;
}

.m_header_menu li span a:hover, .m_header_menu li span.selected a {
    color: #000;
    background-position: 100% -42px;
}

.m_header_menu li span.selected a {
    font-weight: bold;
    color: #000;
    background-position: 100% -42px;
}

.m_header_menu li a:hover img, .m_header_menu li span.selected img {
    /*filter: alpha(opacity=100);*/
}

.m_header_menu li a img {
    float: left;
    margin: 0 2px 0 0;
}

.m_header_menu li.has_sub span a {
    padding-right: 20px;
    background-image: url('../images/menu_tab_right_has_sub.gif');
    background-position: 100% 0px;
}

/*************************************************************************************/
/* styles from themes/layout/basic.css we will need for our theme */
.zpMenuContainer * {
    -moz-box-sizing: content-box;
}

/* Top menu, horizontal */
.zpMenu-horizontal-mode .zpMenu-level-1 {
    float: left;
}

.zpMenuContainer, .zpMenu {
    float: left;
    /* IE fix */
    position: relative;
    color: #222433;
}

/* What to do if the LI has NO icon
by default show NO icons
-you css can override this
*/
.zpMenu-level-1 {
    background: none;
}

.zpMenu-item {
    cursor: pointer;
}

/* Hide expansion indicators */
/* These are indicators for items that have sub-menus, and are hidden cause they generate a
   blank space in front of the content of those items */
.zpMenu .zpMenu-item .minus, .zpMenuContainer .zpMenuContainer .zpMenu-item .minus, .zpMenu .zpMenu-item .plus, .zpMenuContainer .zpMenuContainer .zpMenu-item .plus {
    display: none;
}

/* Helper classes to hide the menu onload */
/* To hide the menu before it is loaded */
ul.zpHideOnLoad {
    display: none;
}

/* Sub-menu */
.zpMenuContainer .zpMenuContainer {
    position: absolute;
}

/* Must include this CSS for Animation and Special Effects.  Fixes IE problems */
.zpMenuContainer .zpMenuContainer .zpMenu {
    /*filter: alpha(opacity = 100);*/
}

/*  THEME STYLES TO MAKE THE NZOOM's MENU LOOKS LIKE THE OLDER MENU */
.m_header_menu .zpMenuNzoom {
    padding-left: 10px;
    background: url('../images/menu_bg_border.jpg') repeat-x left bottom;
}

.zpMenu-table {
    height: 25px;
    padding-right: 20px;
    padding-bottom: 1px;
}

.zpMenu-label {
    white-space: nowrap !important;
    text-align: left;
    padding-left: 5px;
    color: #222433;
}

.zpMenu-table .icon {
    padding: 0px;
}

.zpMenuNoIcon .zpMenuContainer .zpMenuContainer .zpMenu-table .icon {
    display: none;
}

.zpMenu-label a {
    color: #222433 !important;
}

.zpMenu-item-selected .zpMenu-label a, .zpMenu-item-expanded .zpMenu-label a {
}

.zpMenu-item.strike .zpMenu-label a, .zpMenu-item.strike .zpMenu-label a {
    text-decoration: line-through;
}

/* Fix for no tabbed buttons (the upper right print button) */
.zpMenuNzoom .zpMenu-level-1.no_tab {
    background-image: none !important;
}

.zpMenuNzoom .zpMenu-level-1.no_tab div {
    background-image: none !important;
}

.zpMenuNzoom .zpMenu-level-1.no_tab table {
    background-image: none !important;
    padding: 0;
}

/* FIRST LEVEL MENU ITEMS */
.zpMenu-level-1 {
    float: left;
    padding-left: 4px;
    margin-left: 0px;
    background: left 0px no-repeat;
}

.zpMenu-level-1.zpMenu-item-expanded, .zpMenu-level-1.zpMenu-item-selected, .zpMenu-level-1.menu-path {
    background: left -42px no-repeat;
}

zpmenu-level-1.zpMenu-item-selected, .zpMenu-level-1.menu-path {
    font-weight: bold;
}

.zpMenu-level-1 .zpMenu-table {
    background: right 0px no-repeat;
}

.zpMenu-level-1.zpMenu-item-selected .zpMenu-table, .zpMenu-level-1.menu-path .zpMenu-table {
    background: right -42px no-repeat;
}

.zpMenu-level-1.zpMenu-item-collapsed .zpMenu-table {
    background: right 0px no-repeat;
}

.zpMenu-level-1.zpMenu-item-expanded .zpMenu-table, .zpMenu-level-1.zpMenu-item-collapsed.menu-path .zpMenu-table {
    background: right -42px no-repeat;
}

/* OTHER LEVELS MENU ITEMS */
.zpMenuContainer .zpMenuContainer .zpMenu {
    border: 1px solid #B5B5B5;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item {
    min-width: 170px;
    background-color: #F3F3F3 !important;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item-expanded, .zpMenuContainer .zpMenuContainer .zpMenu-item-selected, .zpMenuContainer .zpMenuContainer .menu-path {
    background-color: #E3E3E3 !important;
}

.zpMenuContainer .zpMenuContainer .menu-path {
    font-weight: bold;
}

.zpMenuContainer .zpMenuContainer .zpMenu-table {
    background: none;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item-even, .zpMenuContainer .zpMenuContainer .zpMenu-item-odd {
    border-bottom: 1px solid #C5C5C5;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item-single, .zpMenuContainer .zpMenuContainer .zpMenu-item-last {
    border-bottom: 0px;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item-collapsed, .zpMenuContainer .zpMenuContainer .zpMenu-item-expanded {
    background: url("../images/arrow_right1.png") no-repeat right center;
}

.zpMenuContainer .zpMenuContainer .zpMenu-table .icon {
    background-color: #D0D8E3;
    padding-left: 5px !important;
    width: 20px !important;
}

.zpMenuContainer .zpMenuContainer .zpMenu-item-hr {
    background-color: #C5C5C5 !important;
    height: 1px !important;
}

/* styles to make a menu option look like a button */
.no_tab.button {
    padding: 1px 7px;
    height: inherit;
    background-color: #F5F5F5 !important;
    color: #333333 !important;
}

.no_tab.button:hover {
    background-color: #5371AF !important;
    color: #FFFFFF !important;
}

.no_tab.button .zpMenu-table {
    height: inherit !important;
    padding: 0px !important;
}

.no_tab.button .zpMenu-table .zpMenu-label {
    color: inherit;
    background: transparent url("../images/arrow_down.png") no-repeat right center;
    padding-right: 14px !important;
}

.no_tab.button .zpMenu-table td {
    padding: 0px !important;
}
