{** ALLOWED PARAMETERS:
 * standalone           - defines whether only the HTML element should be inserted or in a row of table
 * name                 - the form name of the variable (in latin characters), for the group tables the name is array [$index] is added
 * var_id               - id of the variable (as in the _fields_meta DB table)
 * custom_id            - each variable contains custom_id which defines the variable uniquely in the DOM
 * index                - index is the number of row in the group tables (starting with 1)
 * eq_indexes           - the index used in the name is the same as the index used in the array
 * group_num            - used only for group tables, configurators and one row tables
 * label                - label (translated in the language of the interface)
 * help                 - help text shown in the help baloon with overlib (translated in the language of the interface)
 * value                - the actual value of the variable
 * required             - flag that defines whether the variables is required (should be validated) or not
 * readonly             - flag that defines whether the variables should be readonly (not editable) or not
 * hidden               - if the variable is defined as hidden it is not displayed at all hiding it with style="display: none"
 * disabled             - if the variable is defined as disabled
 * width                - the width of the variable defines the width of the HTML element. In the standalone mode the width is defined as 100% of the cell width
 * calculate            - defines whether the HTML element should have calculate formula or not:
 *                        0 - no calculation formula
 *                        1 - calculation formula WITH button for calculation
 *                        2 - calculation formula WITHOUT button for calculation (if the width is 0 the input is not displayed at all)
 * options              - list of options (used only for checkboxes, dropdowns, radio buttons)
 * optgroups            - list of optgroups and their options (overwrites options)(used only for checkboxes, dropdowns, radio buttons)
 * option_value         - the value of the single option (used only for single checkbox)
 * first_option_label   - the label of the first option of a dropdown (used only for dropdowns)
 * origin               - defines the origin of the variable - group, config, table (typically it is not required)
 * format               - defines the format of the element (used only for the date and datetime fields)
 * disallow_date_before - does not allow input of dates before specified date (used only for the date and datetime fields)
 * disallow_date_after  - does not allow input of dates after specified date (used only for the date and datetime fields)
 * hide_calendar_icon   - does not allow showing of calendar icon (used only for the date and datetime fields)
 * onclick              - function defined for the onclick event(used only for buttons, checkboxes and radio buttons)
 * on_change            - function defined for the onchange event(used only for linked dropdowns)
 * js_methods           - defines a JavaScript functions for some keyboard and mouse events
 * restrict             - defines a JavaScript restriction of the input characters.
 *                        For example restrict insertOnlyDigits will allow only digits to be inserted in the text field
 * back_label           - text for the back label
 * back_label_style     - styles (inline CSS) for the back label tag
 *}
{* Define indexes *}
{if $index}{strip}
  {capture assign='index_array'}
    {if $eq_indexes}
      {$index}
    {elseif $empty_indexes}
    {elseif $name_index}
      {$name_index}
    {else}
      {$index-1}
    {/if}
  {/capture}
{/strip}{/if}
{* Define width *}
{capture assign='width'}{strip}
  {if $standalone}
    {if preg_match('#^(\d+%|)$#', $width)}
      80%!important
    {else}
      {math equation="x - y - z" x=$width y=20 z=4}px
    {/if}
  {/if}
{/strip}{/capture}
{* Define height *}
{capture assign='height'}{if $height && !preg_match('#%$#', $height)}{$height}px{elseif $height}{$height}{/if}{/capture}

{if !$standalone}
<tr{if $hidden || ($calculate > 1 && $width === "0")} style="display: none"{/if}>
  {* Label Cell *}
  <td class="labelbox">
    {* Anchor for error reference *}
    <a name="error_{$custom_id|default:$name}"></a>
    {* Label of the variable *}
    <label for="{$custom_id|default:$name}" style="white-space: nowrap;"{if $messages->getErrors($name)} class="error"{/if}>{help label_content=$label text_content=$help}</label>
  </td>

  {* Required Cell *}
  <td{if $required} class="required">{#required#}{else} class="unrequired">&nbsp;{/if}</td>

  {* Element Cell *}
  <td nowrap="nowrap">
{/if}
    {* Element *}
  {if !$formula_only}
    <input type="hidden"{if $custom_class} class="{$custom_class}"{/if} id="change_formula_{$name}{if $index}_{$index}{/if}" name="change_formula_{$name}{if $index}[{$index_array}]{/if}" value="{$name}{if $formula_value && (!$value || $value eq '0000-00-00')}_formula{/if}{if $index}_{$index}{/if}" />
    {if $source eq 'text' || $source eq 'index' || empty($source)}
    <input
      type="text"
      name="{$name}{if $index}[{$index_array}]{/if}"
      id="{$custom_id|default:$name}{if $index}_{$index}{/if}"
      class="txtbox{if $readonly} readonly{/if}{if $custom_class} {$custom_class}{/if}"
      style="{if ($hidden || ($formula_value && !$value))}display: none;{/if}{if $width}width: {$width};{/if}{if $text_align} text-align:{$text_align};{/if}{if $height}height: {$height};{/if}"
      value="{$value|escape}"
      title="{$label|strip_tags:false|escape}"
      {if $restrict}
        onkeypress="return changeKey(this, event, {$restrict});"
      {elseif $onkeypress || !empty($js_methods.onkeypress)}
        onkeypress="{if $onkeypress}{$onkeypress};{/if}{if !empty($js_methods.onkeypress)}{$js_methods.onkeypress};{/if}"
      {/if}
      onfocus="highlight(this);{if !empty($js_methods.onfocus)}{$js_methods.onfocus};{/if}"
      onblur="unhighlight(this);{if !empty($js_methods.onblur)}{$js_methods.onblur};{/if}"
      {foreach from=$js_methods key='method' item='func'}
        {if $func
        && $method
        && $method ne 'onkeypress'
        && $method ne 'onfocus'
        && $method ne 'onblur'}
          {$method}="{$func}"
        {/if}
      {/foreach}
      {if $readonly} readonly="readonly"{/if}
      {if $disabled} disabled="disabled"{/if} />
    {elseif $source eq 'date'}
    <input
      type="text"
      name="{$name}_formatted{if $index}[{$index_array}]{/if}"
      id="{$custom_id|default:$name}_formatted{if $index}_{$index}{/if}"
      class="txtbox datebox{if $readonly} readonly{/if}{if $custom_class} {$custom_class}{/if}"
      style="{if ($hidden || ($formula_value && (!$value || $value eq '0000-00-00')))}display: none;{/if}{if $width}width: {$width};{/if}{if $height}height: {$height};{/if}"
      autocomplete="off"
      {if $value && !preg_match('/0000-00-00/', $value)}
        value="{$value|date_format:#date_short#|default:'  .  .    '}"
      {else}
        value="  .  .    "
      {/if}
      title="{$label|strip_tags:false|escape}"
      onfocus="highlight(this);
               datetimePositionMouseCursor(this);
               {if !$readonly}
               calendarInit(this.id,
                            this.id{if !$index} + '_trigger'{/if},
                            false,
                            '{$format|default:#date_short#}',
                            '%Y-%m-%d',
                            {if $disallow_date_before}disallowDateBefore{elseif $disallow_date_after}disallowDateAfter{else}false{/if});
               {/if}"
      onblur="unhighlight(this); validateDate(this, -1); formatDate(this);{$custom_onblur}"
      {if $readonly} 
        readonly="readonly"
      {else}
        onkeydown="return isAllowedDateKey(event)"
        onkeyup="return changeKey(this, event, filterDate);"
      {/if}
      {if $disabled} disabled="disabled"{/if} />
    <input
      type="hidden"
      name="{$name}{if $index}[{$index_array}]{/if}"
      id="{$custom_id|default:$name}{if $index}_{$index}{/if}"
      class="datebox{if $custom_class} {$custom_class}{/if}"
      {if $value && !preg_match('#0000-00-00#', $value)}
        value="{$value|escape}"
      {else}
        value=""
      {/if}
      />
    {/if}
  {/if}
    <select 
      name="{$name}_formula{if $readonly}_readonly{/if}{if $index}[{$index_array}]{/if}"
      id="{$custom_id|default:$name}_formula{if $readonly}_readonly{/if}{if $index}_{$index}{/if}"
      class="selbox{if $custom_class} {$custom_class}{/if}{if !$formula_value} undefined{/if}"
      style="{if !$formula_only && (($hidden_formula) || !$formula_value || ($value && $value ne '0000-00-00'))}display: none;{/if}{if $width}width: {$width};{/if}{if $height}height: {$height};{/if}"
      title="{$label|strip_tags:false|escape}"
      onfocus="highlight(this);"
      onblur="unhighlight(this);"
      onchange="toggleUndefined(this);{if $onchange_formula} {$onchange_formula}{/if}"
      {if $disabled || $readonly} disabled="disabled"{/if}
    >
      <option value="" class="undefined"{if $formula_value === ""} selected="selected"{/if}>[{#please_select#|escape}]</option>
      {foreach from=$formulas item='option' key='idx'}
        {if $option.type == $source || ($option.type == 'text' && ($source == 'index' || !$source))}
          <option value="{$option.option_value|escape}"{if $option.option_value === $formula_value} selected="selected"{/if}>{$option.label|default:'&nbsp;'}</option>
        {/if}
      {/foreach}
    </select>
    {if $readonly}
      <input type="hidden"{if $custom_class} class="{$custom_class}"{/if} name="{$name}_formula{if $index}[{$index_array}]{/if}" id="{$custom_id|default:$name}_formula{if $index}_{$index}{/if}" value="{$formula_value}" />
    {/if}
    {if !$formula_only}
      <a href="javascript:void(0);"><img id="{$name}_switchFormula{if $index}_{$index}{/if}" src="{$theme->imagesUrl}formula.png" onclick="setFormulaValue('{$name}{if $index}_{$index}{/if}', 'Formula')" class="icon_button formula_value" alt="" title="{#formula#}" {if $formula_value && (!$value || preg_match('#0000-00-00#', $value)) || $readonly || $disabled || $hidden}style="display:none"{/if}/></a>
      <a href="javascript:void(0);"><img id="{$name}_switchValue{if $index}_{$index}{/if}" src="{$theme->imagesUrl}edit.png"  onclick="setFormulaValue('{$name}{if $index}_{$index}{/if}', 'Value')" class="icon_button formula_value" alt="" title="{#value#}" {if $value && !preg_match('#0000-00-00#', $value) || (!$value || preg_match('#0000-00-00#', $value)) && !$formula_value || $readonly || $disabled || $hidden}style="display:none"{/if}/></a>
    {/if}
    {if $include_index}
      {include file=`$theme->templatesDir`input_index.html
               name=`$name`_index
               standalone=true
               value=$index_value
               options=$indexes
               readonly=$readonly
               hidden=$hidden
               really_required=$var.required
               required=$required
               disabled=$disabled
               date_value=$index_date
               formula_value=$index_formula
               width=300
               custom_class=$custom_class
      }
    {/if}

    {* Back label *}
    {if !$back_label && $var.back_label}
      {assign var='back_label' value=$var.back_label}
    {/if}
    {if !$back_label_style && $var.back_label_style}
      {assign var='back_label_style' value=$var.back_label_style}
    {/if}
    {include file="_back_label.html"
      custom_id=$custom_id
      name=$name
      back_label=$back_label
      back_label_style=$back_label_style}
{if !$standalone}
  </td>
</tr>
{/if}
