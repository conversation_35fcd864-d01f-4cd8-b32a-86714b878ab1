  {if empty($table)}
    {assign var='table' value=$model->get('grouping_table_2')}
  {/if}

  {if trim($table.label) ne '' && !$hide_label}
    <div class="t_caption2_title" style="float: left">{$table.label}</div>
    <div class="clear"></div>
  {/if}
<input type="hidden" name="calc_price" id="calc_price" value="{$table.calculated_price}" />
<table class="t_grouping_table grouping_table2 gt2view" style="{if $table.width}width:{$table.width}{if preg_match('#^\d+$#', $table.width)}px{/if};{/if}"{if $table.bb} id="var_gt2_{$table.id}"{/if}>
<tr>
  {if $transform}
    <th width="15"><input type="checkbox" name="checkall_{$table.type}_{$table.new_type}" title="{#check_to_include_all#|escape}" checked="checked" onclick="toggleCheckboxes(this,'items_{$table.type}_{$table.new_type}', this.checked)" /></th>
  {/if}
  <th width="20" style="text-align:right">{#num#|escape}</th>
  {foreach from=$table.vars item='var' key='key'}
    {capture assign='info'}{if $var.help}{$var.help}{else}{$var.label}{/if}{/capture}
    <th style="{if $var.hidden}display: none;{/if}{if $var.width}width: {$var.width}px;{/if}">
      {if !$var.hidden && trim($var.label) ne ''}
        {help label_content=$var.label text_content=$info label_sufix=''}{if $var.required}{#required#}{/if}
      {/if}
    </th>
  {/foreach}
</tr>
{counter assign='cols_count' print=false name='colcount' start=0}
{foreach from=$table.values item='val' key='row' name='i'}
<tr>
  {if $transform}
    <td width="15"><input onclick="" type="checkbox" name="items_{$table.type}_{$table.new_type}[]" value="{$row}" title="{#check_to_include#|escape}" checked="checked" /></td>
  {/if}
  <td style="text-align:right" nowrap="nowrap">{$smarty.foreach.i.iteration}</td>
  {foreach from=$table.vars item='var' key='key' name='j'}
      {if $smarty.foreach.i.first && (!$var.hidden || $var.hidden === '0' || $var.hidden === 0)}
        {counter assign='cols_count' print=false name='colcount'}
      {/if}
      {if $smarty.foreach.i.first && !empty($var.agregate)}
        {counter assign='agregates_count' print=false name='agregatescount'}
      {/if}
      <td class="{$var.name}" style="{if $var.hidden}display: none;{elseif $var.text_align} text-align:{$var.text_align}{/if}" title="{$var.label}">
        {capture assign=formula_key}{$key}_formula{/capture}
        {strip}
          {if $var.type eq 'formula' && $val.$key eq 0 && $val.$formula_key}
            {foreach from=$formulas item=formula}
              {if $formula.option_value eq $val.$formula_key}{$formula.label}{/if}
            {/foreach}
          {elseif $var.type eq 'index'}
            {capture assign=date_key}{$key}_date{/capture}
            {foreach from=$indexes item=index}
              {if $index.option_value eq $val.$key}
                {$index.label}<br />
                {if (!$val.$date_key || $val.$date_key eq '0000-00-00') && $val.$formula_key}
                  {foreach from=$formulas item=formula}
                    {if $val.$formula_key eq $formula.option_value}{$formula.label}{/if}
                  {/foreach}
                {else}
                  {$val.$date_key|date_format:#date_short#}
                {/if}
              {/if}
            {/foreach}
          {elseif in_array($var.type, array('dropdown', 'radio'))}
            {include file="_view_dropdown_radio.html"
              var=$var
              value=$val.$key
            }
          {elseif $var.type eq 'date' && $val.$key ne 0}
            {$val.$key|date_format:#date_short#|default:'&nbsp;'}
          {elseif $var.type eq 'datetime' && $val.$key ne 0}
            {$val.$key|date_format:#date_mid#|default:'&nbsp;'}
          {elseif $var.type eq 'time' && $val.$key ne 0}
            {$val.$key|date_format:#time_short#|default:'&nbsp;'}
          {elseif $var.type eq 'file_upload'}
            {assign var='file_info' value=$val.$key}
            {if !empty($file_info) && is_object($file_info) && !$file_info->get('deleted_by')}
              {if !$file_info->get('not_exist')}
                {if $var.view_mode eq 'thumbnail' && $file_info->isImage()}
                  {getimagesize assign='image_dimensions' image_path=$file_info->get('path')}
                  <img src="{$smarty.server.SCRIPT_NAME}?{$module_param}=files&amp;files=viewfile&amp;viewfile={$file_info->get('id')|encrypt:'_viewfile_'|escape:'url'}{if $file_info->get('archived_by')}&amp;archive=1{/if}{if $var.thumb_width}&amp;maxwidth={$var.thumb_width}{/if}{if $var.thumb_height}&amp;maxheight={$var.thumb_height}{/if}" onclick="showFullLBImage('{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=viewfile&amp;viewfile={$var.model_id}&amp;file={$file_info->get('id')}{if $file_info->get('archived_by')}&amp;archive=1{/if}', '{$var.label}', '{$image_dimensions.width}', '{$image_dimensions.height}')" alt="" />
                {else}
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=viewfile&amp;viewfile={$var.model_id}&amp;file={$file_info->get('id')}{if $file_info->get('archived_by')}&amp;archive=1{/if}" target="_blank"><img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" title="{#open#|escape}" /></a>
                  <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}{$controller_string}&amp;{$action_param}=getfile&amp;getfile={$var.model_id}&amp;file={$file_info->get('id')}"><img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" title="{#download#|escape}" /></a>
                {/if}
              {else}
                <img src="{$theme->imagesUrl}{$file_info->getIconName()}.png" width="16" height="16" border="0" alt="{#open#|escape}" class="pointer dimmed" />
                <img src="{$theme->imagesUrl}download.png" width="16" height="16" border="0" alt="{#download#|escape}" class="pointer dimmed" />
              {/if}
            {else}
              &nbsp;
            {/if}
          {elseif $var.type eq 'autocompleter'}
            {include file="_view_autocompleter.html"
                     value=$val.$key
                     value_id=$val[$var.autocomplete.id_var]
                     view_mode_url=$var.autocomplete.view_mode_url
            }
          {else}
            {$val.$key|default:'&nbsp;'|nl2br}
          {/if}
          {if ($val.$key || $val.$key === '0' || $val.$formula_key) && $var.back_label}&nbsp;{$var.back_label}{/if}
        {/strip}
      </td>
  {/foreach}
</tr>
{* extension for handovers' rows where we have batch articles *}
{if $val.has_batch}{include file="_gt2_batch_view.html" idx=$smarty.foreach.i.iteration}{/if}
{/foreach}
<tr style="display:none"><td></td></tr>
{if !$table.hide_agregates}
  {if $agregates_count > 0}
    <tr class="gt2_agregates">
      {if $transform}<td>&nbsp;</td>{/if}
      <td>&nbsp;</td>
    {foreach name='j' key='key' from=$table.vars item='var'}
      <td style="white-space:nowrap!important;{if $var.hidden}display: none;{elseif $var.text_align} text-align:{$var.text_align}{/if}">{strip}
        {if $var.agregate}
            {capture assign='ag_name'}{$var.name}_agregate{/capture}
            {capture assign='ag_class}{if $var.agregate}agregate{/if}{/capture}
            {include file=`$theme->templatesDir`input_text.html
                     hidden=1
                     custom_class='agregate'
                     name=$ag_name
                     value=$var.agregate
                     standalone=true
                     width=$var.width
            }
            {capture assign='ag_text'}gt2_tagregates_{$var.agregate}{/capture}
            {$smarty.config.$ag_text}: <span class="{$var.name}_ag_content"></span>
        {else}
        &nbsp;
        {/if}
      {/strip}</td>
    {/foreach}
    </tr>
  {/if}
{/if}
{if $table.totals_texts_colspan}
{assign var='totals_texts_colspan' value=$table.totals_texts_colspan}
{else}
{assign var='totals_texts_colspan' value=3}
{/if}
{assign var='totals_colspan' value=$cols_count-$totals_texts_colspan}
{if $transform}{assign var='totals_colspan' value=$totals_colspan+1}{/if}

{assign var='var' value=$table.plain_vars.total_without_discount}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_without_discount|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total_discount_percentage}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_discount_percentage|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total_discount_value}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_discount_value|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total_surplus_percentage}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_surplus_percentage|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total_surplus_value}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_surplus_value|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total_vat_rate}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">
  {if $table.plain_vars.total_no_vat_reason_text && !$table.plain_vars.total_no_vat_reason_text.hidden}
    {$table.plain_vars.total_no_vat_reason_text.label}:
    {include file="_view_autocompleter.html"
             value=$table.plain_values.total_no_vat_reason_text
             value_id=$table.plain_values.total_no_vat_reason
             view_mode_url=$table.plain_vars.total_no_vat_reason_text.autocomplete.view_mode_url
    }{if $table.plain_values.total_no_vat_reason_text && $table.plain_vars.total_no_vat_reason_text.back_label} {$table.plain_vars.total_no_vat_reason_text.back_label}{/if}
  {else}
    &nbsp;
  {/if}
  </td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_vat_rate} %{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total_vat}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">
    {if $table.show_totals_inwords}{$var.label} ({#in_words#}): {$table.plain_values.total_vat|inwords:$table.plain_values.currency:$lang}{/if}
  </td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_vat|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.total_with_vat}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">
    {if $table.show_totals_inwords}{$var.label} ({#in_words#}): {$table.plain_values.total_with_vat|inwords:$table.plain_values.currency:$lang}{/if}
  </td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.total_with_vat|default:0}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}

{assign var='var' value=$table.plain_vars.currency}
{if $var}
<tr{if $table.hide_totals || $var.hidden} style="display:none"{/if}>
  <td {if $totals_colspan} colspan="{$totals_colspan}"{/if} style="border:none">&nbsp;</td>
  <td{if $totals_texts_colspan} colspan="{$totals_texts_colspan}"{/if} style="text-align:right">{$var.label}</td>
  <td style="{if $var.text_align}text-align:{$var.text_align}{/if}">
    {$table.plain_values.currency}{if $var.back_label} {$var.back_label}{/if}
  </td>
</tr>
{/if}
</table>
{if !$hide_script}
<script type="text/javascript">
    if (typeof(gt2calc) == 'function') {ldelim}
        gt2calc('agregates'{if $table.bb}, 'var_gt2_{$table.id}'{/if});
    {rdelim}
</script>
{/if}
