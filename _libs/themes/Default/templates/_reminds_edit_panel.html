              <div id="reminds_errors"></div>
              <table cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                  <td nowrap="nowrap" class="t_caption">
                    <div class="t_caption_title" id="remind_edit_panel_label">{if $reminder_event_id}{#reminds_edit_remind#}{else}{#reminds_add_remind#}{/if}</div>
                  </td>
                </tr>
                <tr>
                  <td class="nopadding">
                    <form name="remind_add" action="" method="post" onsubmit="saveRemind(this,'{$current_model->get('id')}'); return false;">
                      <input type="hidden" name="model_id" id="model_id" value="{$current_model->get('id')}" />
                      <input type="hidden" name="model" id="model" value="{$current_model->modelName}" />
                      <input type="hidden" name="reminder_event_id" value="{$reminder_event_id}" />
                      <table cellpadding="0" cellspacing="0" border="0">
                        <tr>
                          <td class="labelbox"><label for="reminder_type">{#reminds_type#}:</label></td>
                          <td class="required">{#required#}</td>
                          <td nowrap="nowrap">
                            <select class="selbox" name="reminder_type" id="reminder_type" title="{#reminds_type#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">
                              {foreach from=$available_reminder_types item='reminderType' key='value'}
                                <option value="{$value}"{if $value eq $remind_type} selected="selected"{/if}>{$reminderType}</option>
                              {/foreach}
                            </select>
                          </td>
                        </tr>
                        {include file="input_datetime.html"
                          standalone=false
                          required=1
                          name='reminder_date'
                          label=#reminds_remind#
                          help=#reminds_remind#
                          show_calendar_icon=1
                          width=200px
                          value=$remind_date}
                        <tr>
                          <td class="labelbox"><a name="error_custom_message"><label for="custom_message"{if $messages->getErrors('custom_message')} class="error"{/if}>{#reminds_custom_message#}:</label></a></td>
                          <td>&nbsp;</td>
                          <td class="databox doubled">
                            <textarea class="areabox" name="custom_message" id="custom_message" title="{#reminds_custom_message#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$remind_custom_message|escape}</textarea>
                          </td>
                        </tr>
                        <tr>
                          <td class="labelbox"><a name="error_remind_to_others"><label for="remind_to_others"{if $messages->getErrors('remind_to_others')} class="error"{/if}>{#reminds_remind_to_others#}:</label></a></td>
                          <td>&nbsp;</td>
                          <td nowrap="nowrap">
                            <input type="checkbox" onblur="unhighlight(this)" onfocus="highlight(this)" title="{#reminds_remind_to_others#}" value="1" id="remind_to_others" name="remind_to_others" onclick="showRemindToOthers(this)"{if $remind_to_others} checked="checked"{/if} />
                          </td>
                        </tr>
                        {capture assign='hide_remind_panel'}{if $remind_to_others}0{else}1{/if}{/capture}
                        {include file          = 'input_checkbox_group.html'
                                 name          = 'assign_remind'
                                 label         = $smarty.config.reminds_assigned
                                 help          = $smarty.config.reminds_assigned
                                 required      = 1
                                 hidden        = $hide_remind_panel
                                 options       = $assign_users
                                 value         = $remind_assigned_users
                                 scrollable    = true}
                        <tr>
                          <td colspan="3">
                            <button type="submit" class="button" name="addRemind" id="addRemind">{if $reminder_event_id}{#edit#}{else}{#add#}{/if}</button><button type="button" name="cancel" class="button" onclick="confirmAction('cancel', function() {ldelim} clearRemindPanel(); {rdelim}, this)">{#cancel#|escape}</button>
                          </td>
                        </tr>
                      </table>
                    </form>
                  </td>
                </tr>
              </table>