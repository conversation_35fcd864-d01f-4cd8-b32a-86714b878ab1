<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}
{include file=`$theme->templatesDir`_submenu_actions_box.html}

<form name="{$origin}s" action="{$submitLink}" method="post">
<input type="hidden" name="id" id="id" value="{$model->get('id')}" />
<input type="hidden" name="model_lang" id="model_lang" value="{$model->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="vtop nopadding">
            <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
              <tr>
                <td class="labelbox">{help label='name'}</td>
                <td class="required">{#required#}</td>
                <td nowrap="nowrap">
                  {if $origin eq 'role'}
                    {mb_truncate_overlib text=$model->get('name')|escape|default:"&nbsp;"}
                  {else}
                    {$model->get('firstname')|escape} {$model->get('lastname')|escape}
                  {/if}
                </td>
              </tr>
              {if $origin eq 'role'}
              <tr>
                <td class="labelbox">{help label='description'}</td>
                <td>&nbsp;</td>
                <td nowrap="nowrap">
                  {$model->get('description')|mb_wordwrap|url2href}
                </td>
              </tr>
              {/if}
            </table>
          </td>
          <td class="index_class">
            <div id="cashboxes_and_bank_accounts_index_holder" class="cashboxes_and_bank_accounts_index_holder">
              <a name="cashboxes_and_bank_accounts_index"></a>
              {assign var='label_truncate_length' value=30}
              {if $cashboxes}
                <a style="display: block;" href="#cashboxes_index" class="strong">{capture assign=cbTitle}{$origin}s_cashboxes{/capture}{$smarty.config.$cbTitle|escape}</a>
                {foreach from=$models_cashboxes item=model_type}
                  {capture assign=model_type_name}finance_{$model_type}{/capture}
                  <a style="display: block;" href="#cashboxes_{$model_type}_index">{$smarty.config.$model_type_name|escape|indent:3:"&nbsp;"}</a>
                {/foreach}
                {if $cashboxes|@count gt 10}{assign var='label_truncate_length' value=20}{/if}
              {/if}
              {if $bank_accounts}
                <a style="display: block;" href="#bank_accounts_index" class="strong">{capture assign=cbTitle}{$origin}s_bank_accounts{/capture}{$smarty.config.$cbTitle|escape}</a>
                {foreach from=$models_bank_accounts item=model_type}
                  {capture assign=model_type_name}finance_{$model_type}{/capture}
                  <a style="display: block;" href="#bank_accounts_{$model_type}_index">{$smarty.config.$model_type_name|escape|indent:3:"&nbsp;"}</a>
                {/foreach}
                {if $bank_accounts|@count gt 10}{assign var='label_truncate_length' value=20}{/if}
              {/if}
            </div>
          </td>
        </tr>
        {if $cashboxes}
        <tr>
          <td colspan="2" class="nopadding">
            <table id="cashboxes" style="width: 100%;" border="0" cellpadding="0" cellspacing="0">
              <tr>
                <td id="cashoxes_title" class="t_caption3 t_caption3_title pointer" onclick="toggleAllSubsections(this)" style="width:150px;">
                  <div class="switch_{if $smarty.cookies.cashboxes_box eq 'off'}expand{else}collapse{/if}"></div>
                  <a name="cashboxes_index"></a>{capture assign=cbTitle}{$origin}s_cashboxes{/capture}{$smarty.config.$cbTitle|escape}
                </td>
                {foreach from=$cashboxes item=cashbox}
                  <td class="t_caption3 t_caption3_title">
                    <input type="checkbox" onclick="toggleCheckboxesByClass('cashbox_{$cashbox.option_value}', this.checked);" />
                  </td>
                {/foreach}
                <td class="t_caption3 t_caption3_title pointer" style="width: 16px;" align="right">
                  <div>
                    <a href="#cashboxes_and_bank_accounts_index" class="index_arrow_anchor"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                  </div>
                </td>
              </tr>
              {foreach from=$models_cashboxes item=model_type}
              {capture assign=model_type_name}finance_{$model_type}{/capture}
              {capture assign='model_type_cookie'}cashboxes_{$model_type}_box{/capture}
              <tr{if $smarty.cookies.cashboxes_box eq 'off'} style="display: none;"{/if}>
                <td class="t_caption2 strong pointer" style="padding-left: 20px;" onclick="toggleViewLayouts(this)" id="cashboxes_{$model_type}_switch">
                  <div class="switch_{if $smarty.cookies.$model_type_cookie eq 'off'}expand{else}collapse{/if}"></div>
                  <a name="cashboxes_{$model_type}_index"></a>{$smarty.config.$model_type_name|escape}
                </td>
                {foreach from=$cashboxes item=cashbox}
                  <td class="t_caption2">
                    <input type="checkbox" class="cashbox_{$cashbox.option_value}" onclick="toggleCheckboxesByClass('{$model_type}_{$cashbox.option_value}', this.checked);" />
                  </td>
                {/foreach}
                <td class="t_caption2 strong pointer" style="width: 16px;" align="right">
                  <div>
                    <a href="#cashboxes_and_bank_accounts_index" class="index_arrow_anchor"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                  </div>
                </td>
              </tr>
              <tr{if $smarty.cookies.$model_type_cookie eq 'off' || $smarty.cookies.cashboxes_box eq 'off'} style="display: none;"{/if} class="cashboxes_{$model_type}">
                <th style="width: 100px!important;">&nbsp;</th>
                {foreach from=$cashboxes item=cashbox}
                  <th nowrap="nowrap" style="min-width: 80px;" class="{if !$cashbox.active_option} inactive_option" title="{#inactive_option#}">* {else}">{/if}{mb_truncate_overlib text=$cashbox.label|escape length=$label_truncate_length middle=1 break_words=1}</th>
                {/foreach}
                <th style="width: 16px;">
                  &nbsp;
                </th>
              </tr>
              {foreach from=$actions_cashboxes item=action_name}
              {if !($model_type eq 'transfer' and $action_name eq 'balance' or $model_type eq 'TR' and $action_name neq 'view')}
              <tr {if $smarty.cookies.$model_type_cookie eq 'off' || $smarty.cookies.cashboxes_box eq 'off'} style="display: none;"{/if} class="cashboxes_{$model_type}">
              {capture assign=action_label}action_{$action_name}{/capture}
                <td class="strong" style="width: 100px!important;">{$smarty.config.$action_label|escape}</td>
              {foreach from=$cashboxes item=cashbox}
                <td>
                  <input class="{$origin}_cashbox {$model_type}_{$cashbox.option_value} cashbox_{$cashbox.option_value}" type="checkbox" value="{$cashbox.option_value}" name="{$origin}_cashboxes_{$model_type}_{$action_name}[]" id="{$origin}_cashboxes_{$model_type}_{$action_name}_{$cashbox.option_value}"{if @in_array($cashbox.option_value, $model_cashboxes[$model_type][$action_name])} checked="checked"{/if} />
                </td>
              {/foreach}
              <td style="width: 16px;">
                  &nbsp;
                </td>
              </tr>
              {/if}
              {/foreach}
              {/foreach}
            </table>
          </td>
        </tr>
        {/if}
        {if $bank_accounts}
        <tr>
          <td colspan="2" class="nopadding">
            <table id="bank_accounts" style="width: 100%;" border="0" cellpadding="0" cellspacing="0">
              <tr>
                <td id="bank_accounts_title" class="t_caption3 t_caption3_title pointer" onclick="toggleAllSubsections(this)" style="width:150px;">
                  <div class="switch_{if $smarty.cookies.bank_accounts_box eq 'off'}expand{else}collapse{/if}"></div>
                  <a name="bank_accounts_index"></a>{capture assign=cbTitle}{$origin}s_bank_accounts{/capture}{$smarty.config.$cbTitle|escape}
                </td>
                {foreach from=$bank_accounts item=bank_account}
                  <td class="t_caption3 t_caption3_title">
                    <input type="checkbox" onclick="toggleCheckboxesByClass('bank_{$bank_account.option_value}', this.checked);" />
                  </td>
                {/foreach}
                <td class="t_caption3 t_caption3_title pointer" style="width: 16px;" align="right">
                  <div>
                    <a href="#cashboxes_and_bank_accounts_index" class="index_arrow_anchor"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                  </div>
                </td>
              </tr>
              {foreach from=$models_bank_accounts item=model_type}
              {capture assign=model_type_name}finance_{$model_type}{/capture}
              {capture assign='model_type_cookie'}bank_accounts_{$model_type}_box{/capture}
              <tr{if $smarty.cookies.bank_accounts_box eq 'off'} style="display: none;"{/if}>
                <td class="t_caption2 strong pointer" style="padding-left: 20px;" onclick="toggleViewLayouts(this)" id="bank_accounts_{$model_type}_switch">
                  <div class="switch_{if $smarty.cookies.$model_type_cookie eq 'off'}expand{else}collapse{/if}"></div>
                  <a name="bank_accounts_{$model_type}_index"></a>{$smarty.config.$model_type_name|escape}
                </td>
                {foreach from=$bank_accounts item=bank_account}
                  <td class="t_caption2">
                    <input type="checkbox" class="bank_{$bank_account.option_value}" onclick="toggleCheckboxesByClass('{$model_type}_{$bank_account.option_value}', this.checked);" />
                  </td>
                {/foreach}
                <td class="t_caption2 strong pointer" style="width: 16px;" align="right">
                  <div>
                    <a href="#cashboxes_and_bank_accounts_index" class="index_arrow_anchor"><img src="{$theme->imagesUrl}arrow_top.png" border="0" title="{#back_to_index#|escape}" alt="{#back_to_index#|escape}" /></a>
                  </div>
                </td>
              </tr>
              <tr class="bank_accounts_{$model_type}"{if $smarty.cookies.$model_type_cookie eq 'off' || $smarty.cookies.bank_accounts_box eq 'off'} style="display: none;"{/if}>
                <th style="width: 100px!important;">&nbsp;</th>
                {foreach from=$bank_accounts item=bank_account}
                  <th nowrap="nowrap" style="min-width: 80px;" class="{if !$bank_account.active_option} inactive_option" title="{#inactive_option#}">* {else}">{/if}{mb_truncate_overlib text=$bank_account.label|escape length=$label_truncate_length middle=1 break_words=1}</th>
                {/foreach}
                <th style="width: 16px;">
                  &nbsp;
                </th>
              </tr>
              {foreach from=$actions_bank_accounts item=action_name}
              {if !($model_type eq 'transfer' and $action_name eq 'balance' or $model_type eq 'TR' and $action_name neq 'view')}
              <tr class="bank_accounts_{$model_type}"{if $smarty.cookies.$model_type_cookie eq 'off' || $smarty.cookies.bank_accounts_box eq 'off'} style="display: none;"{/if}>{capture assign=action_label}action_{$action_name}{/capture}
                <td class="strong" style="width: 100px!important;">{$smarty.config.$action_label|escape}</td>
              {foreach from=$bank_accounts item=bank_account}
                <td>
                  <input class="{$origin}_bank_account bank_{$bank_account.option_value} {$model_type}_{$bank_account.option_value}" type="checkbox" value="{$bank_account.option_value}" name="{$origin}_bank_accounts_{$model_type}_{$action_name}[]" id="{$origin}_bank_accounts_{$model_type}_{$action_name}_{$bank_account.option_value}"{if @in_array($bank_account.option_value, $model_bank_accounts[$model_type][$action_name])} checked="checked"{/if} />
                </td>
              {/foreach}
                <td style="width: 16px;">
                  &nbsp;
                </td>
              </tr>
              {/if}
              {/foreach}
              {/foreach}
            </table>
          </td>
        </tr>
        {/if}
        <tr>
          <td colspan="2" style="padding: 15px;" class="t_top_border">
            <button type="submit" name="saveButton1" class="button">{#save#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
