{include file=`$templatesDir`_assignments_configurator_panel.html config_templates=$config_templates}
<script type="text/javascript" src="{$scriptsUrl}assignments.js?{$system_options.build}"></script>
<form method="post" action="{$submitLink}">
  <input type="hidden" name="a_type" value="{$type}" />
  <input type="hidden" id="model_name" value="{$model_name}" />
  <input type="hidden" id="model_type" value="{$model_type}" />
  {include file=`$templatesDir`_assignments_group.html type=$type users=$data.users}
  <table border="0" cellpadding="3" cellspacing="3" width="100%" class="t_table">
    <tr>
      {if $messages->getErrors()}
        <td>
          {include file=`$theme->templatesDir`message.html display="error" items=$messages->getErrors()}
        </td>
      {else}
        {include file=`$templatesDir`_assign.html borderless=true}
      {/if}
    </tr>
    {if !$messages->getErrors()}
    <tr>
      <td>
        <button type="submit" class="button" name="assignmentsGo" id="assignmentsGo" title="{#assign#}">{#assign#}</button>
      </td>
    </tr>
    {/if}
  </table>
</form>
