<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=tags&amp;tags={$action}&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="tags" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=tags" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#tags_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.model.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.model.link}">{#tags_model#|escape}</div></td>
          <td class="t_caption t_border {$sort.model_types.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.model_types.link}">{#tags_model_types#|escape}</div></td>
          <td class="t_caption t_border {$sort.color.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.color.link}">{#tags_color#|escape}</div></td>
          <td class="t_caption t_border {$sort.place.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.place.link}">{#tags_place#|escape}</div></td>
          <td class="t_caption t_border {$sort.section.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.section.link}">{#tags_section#|escape}</div></td>
          <td class="t_caption t_border {$sort.description.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.description.link}">{#tags_description#|escape}</div></td>
          <td class="t_caption t_border {$sort.added.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.added.link}">{#date#|escape}</div></td>
          <td class="t_caption">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$tags item='tag'}
      {capture assign="color"}{strip}
        tags_color_{$tag->get('color')}
      {/strip}{/capture}
      {capture assign="model"}{strip}
          tags_model_{$tag->get('model')|escape}
      {/strip}{/capture}
      {strip}
      {capture assign='info'}
        <strong>{#tags_name#|escape}:</strong> {$tag->get('name')|escape}<br />
        <strong>{#tags_model#|escape}:</strong> {$smarty.config.$model|escape}<br />
        <strong>{#tags_color#|escape}:</strong> {$smarty.config.$color|escape}<br />
        <strong>{#tags_place#|escape}:</strong> {$tag->get('place')|escape}<br />
        <strong>{#tags_description#|escape}:</strong> {$tag->get('description')|mb_truncate|escape}<br />
        <strong>{#added#|escape}:</strong> {$tag->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$tag->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$tag->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$tag->get('modified_by_name')|escape}<br />
        {if $tag->isDeleted()}<strong>{#deleted#|escape}:</strong> {$tag->get('deleted')|date_format:#date_mid#|escape}{if $tag->get('deleted_by_name')} {#by#|escape} {$tag->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$tag->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $tag->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
        <tr class="{cycle values='t_odd,t_even'}{if !$tag->get('active')} t_inactive{/if}{if $tag->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});"
                   type="checkbox"
                   name='items[]'
                   value="{$tag->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($tag->get('id'), $selected_items.ids) ||
                       (@$selected_items.select_all eq 1 && @!in_array($tag->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=view&amp;view={$tag->get('id')}">{$tag->get('name')|escape|default:"&nbsp;"}</a></td>
          <td class="t_border {$sort.model.isSorted}">{$smarty.config.$model|escape}</td>
          <td class="t_border {$sort.model_types.isSorted}">
          {foreach name='mt' from=$tag->get('current_model_types') item='model_type'}
            {$smarty.foreach.mt.iteration}. {$model_type.label|escape}<br />
          {foreachelse}
            &nbsp;
          {/foreach}
          </td>
          <td class="t_border {$tag->get('color')|escape}_pushpin {$sort.color.isSorted}">{$smarty.config.$color}</td>
          <td class="t_border hright {$sort.place.isSorted}">{$tag->get('place')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.section.isSorted}">{$tag->get('section_name')|escape|default:"&nbsp;"}</td>
          <td class="t_border {$sort.description.isSorted}">{$tag->get('description')|escape|nl2br|url2href|default:"&nbsp;"}</td>
          <td class="t_border {$sort.added.isSorted}">{$tag->get('added')|date_format:#date_short#|escape}</td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$tag}
          </td>
        </tr>
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="11">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="11"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>
    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
