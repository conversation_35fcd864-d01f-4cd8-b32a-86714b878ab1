<?php

class Nomenclatures_Counters_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Nomenclatures_Counter';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Nomenclatures_Counters';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit'
   );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit', 'translate'
    );

    /**
     * generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'search':
            $this->_search();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclatures_counter = Nomenclatures_Counters::buildModel($this->registry);
            if ($nomenclatures_counter->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_counters_add_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_counters_add_failed'), '', -1);
            }
        } else {
            //create empty counter model
            $nomenclatures_counter = Nomenclatures_Counters::buildModel($this->registry);
        }

        if (!empty($nomenclatures_counter)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('nomenclatures_counter', $nomenclatures_counter->sanitize());
        }

        return true;
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclatures_counter = Nomenclatures_Counters::buildModel($this->registry);
            if ($nomenclatures_counter->save()) {
                //show message 'message_nomenclatures_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_counters_edit_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_counters_edit_failed'), '', -1);

                //register the model, with all the posted details
                $this->registry->set('nomenclatures_counter', $nomenclatures_counter);
            }

        } elseif ($id > 0) {

            // the model from the DB
            $filters = array('where' => array('nc.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclatures_counter = Nomenclatures_Counters::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclatures_counter);
        }

        if (!empty($nomenclatures_counter)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclatures_counter')) {
                $this->registry->set('nomenclatures_counter', $nomenclatures_counter->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_counter'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * View model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('nc.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $nomenclatures_counter = Nomenclatures_Counters::searchOne($this->registry, $filters);

        //check access and ownership of the model
        $this->checkAccessOwnership($nomenclatures_counter);

        if (!empty($nomenclatures_counter)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclatures_counter')) {
                $this->registry->set('nomenclatures_counter', $nomenclatures_counter->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_counter'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclatures_counter = Nomenclatures_Counters::buildModel($this->registry);

            if ($nomenclatures_counter->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_counters_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_counters_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('nc.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclatures_counter = Nomenclatures_Counters::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclatures_counter);
        }

        if (!empty($nomenclatures_counter)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('nomenclatures_counter', $nomenclatures_counter->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_counter'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Nomenclatures_Counters::changeStatus($this->registry, $ids, $status);
        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete items
        $result = Nomenclatures_Counters::delete($this->registry, $ids);

        if ($result < 0) {
            //there was nothing to delete
            $this->registry['messages']->setError( $this->i18n('error_nothing_to_delete'));
        } elseif ($result > 0) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));
            if ($result > 1) {
                $this->registry['messages']->setWarning( $this->i18n('warning_not_all_items_deleted'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Nomenclatures_Counters::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Nomenclatures_Counters::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }
}

?>
