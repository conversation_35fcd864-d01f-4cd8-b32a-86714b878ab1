nomenclatures_counters = Numeratoare de nomenclaturi
nomenclatures_counters_name = Nume
nomenclatures_counters_formula = Formulă
nomenclatures_counters_description = Descriere
nomenclatures_counters_next_number = Număr următor
nomenclatures_counters_count_nomenclatures = Număr nomenclaturi
nomenclatures_counters_types_used = Folosit în tipuri
nomenclatures_counters_status = Status
nomenclatures_counters_status_active = Activ
nomenclatures_counters_status_inactive = Inactiv
nomenclatures_counters_added_by = Adăugat de
nomenclatures_counters_modified_by = Modificat de
nomenclatures_counters_added = Adăugat la
nomenclatures_counters_modified = Modificat la

nomenclatures_counters_add = Adăugare numerator nomenclaturi
nomenclatures_counters_edit = Editare numerator nomenclaturi
nomenclatures_counters_view = Vizualizare numerator nomenclaturi
nomenclatures_counters_translate = Traducere numerator nomenclaturi

message_nomenclatures_counters_add_success = Datele pentru numeratorul au fost adăugate cu succes!
message_nomenclatures_counters_edit_success = Datele pentru numeratorul au fost editate cu succes!
message_nomenclatures_counters_translate_success = Numeratorul a fost tradus cu succes!

error_nomenclatures_counters_edit_failed = Datele pentru numeratorul nu au fost editate cu succes:
error_nomenclatures_counters_add_failed = Datele pentru numeratorul nu au fost adăugate:
error_nomenclatures_counters_translate_failed = Numeratorul nu a fost tradus cu succes:

error_no_such_nomenclature_counter = Nu puteți vizualiza această înregistrare!
error_no_counter_name_specified = Nu ați introdus nume!
error_no_counter_formula_specified = Nu ați introdus formulă!
error_invalid_next_number = Vă rugăm să introduceți număr următor pentru numeratorul, compus numai din cifre, cu valoare mai mare de 0!
error_no_types_used = nu este folosit în nici un tip de anunț

nomenclatures_counters_formula_delimiter = Separator
nomenclatures_counters_empty_delimiter = fără separator
nomenclatures_counters_formula_leading_zeroes = Număr de zerouri de frunte
nomenclatures_counters_formula_date_format = Format
nomenclatures_counters_formula_date_delimiter = cu separator

nomenclatures_counters_formula_date_format_year = aaaa
nomenclatures_counters_formula_date_format_year_short = aa
nomenclatures_counters_formula_date_format_month = ll
nomenclatures_counters_formula_date_format_day = zz

nomenclatures_counters_formula_date_format1 = aaaa
nomenclatures_counters_formula_date_format2 = ll/aaaa
nomenclatures_counters_formula_date_format3 = ll/aa
nomenclatures_counters_formula_date_format4 = aaaa/ll
nomenclatures_counters_formula_date_format5 = aa/ll
nomenclatures_counters_formula_date_format6 = zz/ll/aaaa
nomenclatures_counters_formula_date_format7 = zz/ll/aa
nomenclatures_counters_formula_date_format8 = ll/zz/aaaa
nomenclatures_counters_formula_date_format9 = ll/zz/aa
nomenclatures_counters_formula_date_format10 = aaaa/zz/ll
nomenclatures_counters_formula_date_format11 = aa/zz/ll
nomenclatures_counters_formula_date_format12 = aaaa/ll/zz
nomenclatures_counters_formula_date_format13 = aa/ll/zz
nomenclatures_counters_formula_date_format14 = aa
nomenclatures_counters_formula_date_format15 = aaa/ll

nomenclatures_counters_formula_legend = Legendă pentru comletarea formulei numeratorului
nomenclatures_counters_formula_prefix = Prefix
nomenclatures_counters_formula_num = Număr anunț
nomenclatures_counters_formula_code_suffix = Cod sufix
nomenclatures_counters_formula_user_code = Cod utilizator
nomenclatures_counters_formula_added = Data nomenclatură

nomenclatures_counters_formula_prefix_descr = se completează direct cu 2-3 litere, de exemplu pentru articol ART.
nomenclatures_counters_formula_num_descr = se completează numărul de ordine al nomenclatură.
nomenclatures_counters_formula_code_suffix_descr = se completează sufix setat automat codul pentru tipul de nomenclatură.
nomenclatures_counters_formula_user_code_descr = se completează codul utilizatorului, care a creat nomenclatură.
nomenclatures_counters_formula_added_descr = data adăugării nomenclatură.

nomenclatures_counters_formula_note = <strong>NOTĂ:</strong> La formula numaratorului se pot adăuga <strong>numai 5 elemente</strong>

help_nomenclatures_counters_next_number = Număr următor. Folosiți acest câmp pentru a defini numărul din care să înceapă numeratorul.
