<?php

class Nomenclatures_Measures_List_Viewer extends Viewer {
    public $template = 'measures_list.html';

    public function prepare() {

        $nomenclatures_measures = $this->registry['nomenclatures_measures'];

        $num_new = 0;
        foreach ($nomenclatures_measures as $measure) {
            if ($measure->get('id') === false || $measure->get('id') === '') {
                $num_new++;
            }
        }
        // add an empty row in the end
        if (!$num_new) {
            $num_new++;
            $measure = new Nomenclatures_Measure($this->registry, array('_origin' => 'artificial'));
            $nomenclatures_measures[] = $measure->sanitize();
        }
        $this->data['num_new'] = $num_new;

        $this->data['nomenclatures_measures'] = $nomenclatures_measures;

        //set model langs for the models
        $model_langs = $this->registry['config']->getParamAsArray('i18n', 'model_langs');
        $default_lang = $this->registry['config']->getParam('i18n', 'default_lang');
        $default_lang_pos = array_search($default_lang, $model_langs);
        if ($default_lang_pos > 0) {
            unset($model_langs[$default_lang_pos]);
            array_unshift($model_langs, $default_lang);
        }
        $this->data['model_langs'] = $model_langs;

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('nomenclatures_measures');
        $this->data['title'] = $title;
    }
}

?>
