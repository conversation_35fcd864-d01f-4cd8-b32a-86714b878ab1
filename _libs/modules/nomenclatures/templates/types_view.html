<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_types_basic_settings#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_name'}</td>
          <td class="required">{#required#}</td>
          <td>
            {mb_truncate_overlib text=$nomenclatures_type->get('name')|escape|default:"&nbsp;"}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_name_plural'}</td>
          <td class="required">{#required#}</td>
          <td>
            {$nomenclatures_type->get('name_plural')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_subtype'}</td>
          <td class="required">{#required#}</td>
          <td>
            {if $nomenclatures_type->get('subtype') eq 'commodity'}
              {#nomenclatures_types_subtype_commodity#}
            {elseif $nomenclatures_type->get('subtype') eq 'service'}
              {#nomenclatures_types_subtype_service#}
            {elseif $nomenclatures_type->get('subtype') eq 'other' || !$nomenclatures_type->get('subtype')}
              {#nomenclatures_types_subtype_other#}
            {/if}
          </td>
        </tr>
        {if $nomenclatures_type->get('subtype') eq 'commodity'}
        <tr>
          <td class="labelbox">{help label='batch_options'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $nomenclatures_type->get('default_has_batch')}
              {#nomenclatures_has_batch#}
              {if $nomenclatures_type->get('default_has_serial')}/ {#nomenclatures_has_serial#}{/if}
              {if $nomenclatures_type->get('default_has_expire')}/ {#nomenclatures_has_expire#}{/if}
              {if $nomenclatures_type->get('default_has_batch_code')}/ {#nomenclatures_has_batch_code#}{/if}
            {else}
              {#no#}
            {/if}
          </td>
        </tr>
        {/if}
        <tr>
          <td class="labelbox">{help label='type_section'}</td>
          <td class="required">{#required#}</td>
          <td>
            {$nomenclatures_type->get('section_name')|escape}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_description'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {$nomenclatures_type->get('description')|mb_wordwrap|url2href}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_icon'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $nomenclatures_type->get('icon_name')}
              <img src="{$smarty.const.PH_NOMENCLATURES_TYPES_URL}{$nomenclatures_type->get('icon_name')}" alt="" />
            {/if}
          </td>
        </tr>
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_types_counter_settings#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_counter'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $nomenclatures_type->get('counter')}
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=counters&amp;counters=view&amp;view={$nomenclatures_type->get('counter')}" target="_blank">{$counter_name|escape}</a> - {$counter_formula|escape}
            {/if}
          </td>
        </tr>
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_types_additional_settings_of_fields#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_validate_unique'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            {if $nomenclatures_type->get('validate_unique_current_year')}
              <span style="position: absolute; left: 450px;">{#nomenclatures_types_validate_unique_current_year#|escape}</span>
            {/if}
            {foreach from=$nomenclatures_type->get('validate_unique') item='field' name='f'}
              {$field|escape|default:'&nbsp;'}{if !$smarty.foreach.f.last}<br />{/if}
            {/foreach}
          </td>
        </tr>
        {if $layouts_search_url}
        <tr>
          <td class="labelbox">{help label_content=#menu_layouts#}</td>
          <td class="unrequired">&nbsp;</td>
          <td>
            <a href="{$layouts_search_url}">{#help_menu_layouts#|escape}</a>
          </td>
        </tr>
        {/if}
        <tr>
          <td class="t_section_title" colspan="3"><div class="t_section_title t_caption3_title">{#nomenclatures_default_settings#|escape}</div></td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_auto_code'}</td>
          <td class="required">{#required#}</td>
          <td>
            {0|str_repeat:$nomenclatures_type->get('auto_code_leading_zeros')-1}1{$nomenclatures_type->get('auto_code_suffix')}
          </td>
        </tr>
        <tr>
          <td class="labelbox">{help label='types_group'}</td>
          <td class="unrequired">&nbsp;</td>
          <td>{$default_group}</td>
        </tr>
{include file=`$templatesDir`_categories.html from_type=1}
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$nomenclatures_type}
</div>