<?php

class Customers_Contactpersons_Add_Viewer extends Viewer {
    public $template = '_contact_persons_add.html';

    public function prepare() {
        $this->model = $this->registry['contact_person'];
        $this->data['contact_person'] = $this->model;

        //prepare salutations
        $this->data['salutations'] = Dropdown::getSalutations(array($this->registry));

        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array(
            'where' => array('u.is_portal = 0', 'u.active = 1'),
            'model_lang' => $this->model->get('model_lang'),
            'sanitize' => true
        );
        $this->data['assign_users'] = Users::search($this->registry, $filters);

        require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
        $filters_branches = array(
            'where' => array('c.parent_customer = ' . $this->model->get('parent_customer_id'),
                             'c.subtype = \'branch\'',
                             'c.active = 1'),
            'sort' => array('c.is_main DESC', 'ci18n.name ASC', 'c.id DESC'),
            'model_lang' => $this->model->get('model_lang'),
            'sanitize' => true
        );
        $this->data['parent_branches'] = Customers_Branches::search($this->registry, $filters_branches);
        if (!$this->model->isDefined('parent_branch') && count($this->data['parent_branches']) == 1) {
            $first_branch = reset($this->data['parent_branches']);
            $this->model->set('parent_branch', $first_branch->get('id'), true);
        }

        //preparing the array with branches and the contact person for them
        $branch_main_contacts = array();
        foreach ($this->data['parent_branches'] as $parent_branch) {
            $branch_main_contacts[$parent_branch->get('id')] = $parent_branch->get('contact_person_id');
        }
        $this->data['branch_main_contacts'] = json_encode($branch_main_contacts);

        // session param = container id
        $this->data['customers_contact_persons_session_param'] = 'contact_persons_ajax_customers_' . $this->model->get('parent_customer_id') . '_';

        if ($this->theme->isModern()) {
            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }

        $this->setFrameset($this->templatesDir . $this->template);

        if ($this->registry['action'] == 'ajax_add') {
            echo 'var result = ' . json_encode(array('data' => $this->fetch(), 'title' => $this->i18n('customers_contact_persons_add_contact'))) . ';';
            exit;
        }
    }
}

?>
