<h1>{$title|escape}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="vtop">
      <div id="form_container" class="main_panel_container">

        {include file=`$theme->templatesDir`actions_box.html}
        {include file=`$theme->templatesDir`translate_box.html}
        {include file=`$theme->templatesDir`_submenu_actions_box.html}

        <form name="customers" action="{$submitLink}" method="post">
        <input type="hidden" name="id" id="id" value="{$customer->get('id')}" />
        <input type="hidden" name="is_company" id="is_company" value="{$customer->get('is_company')}" />
        <input type="hidden" name="model_lang" id="model_lang" value="{$customer->get('model_lang')|default:$lang}" />
        <table border="0" cellpadding="0" cellspacing="0" class="t_table">
          <tr>
            <td>
              {include file=$view_template layouts_vars=$customer->get('vars')}
            </td>
          </tr>
        </table>
        {include file=`$theme->templatesDir`help_box.html}
        {include file=`$theme->templatesDir`system_settings_box.html object=$customer}
        </form>
      </div>
    </td>
    {if isset($side_panels)}
    <td class="side_panel_container">
      {include file=`$theme->templatesDir`_side_panel_options.html}
      {include file=`$theme->templatesDir`side_panels_box.html}
    </td>
    {/if}
  </tr>
</table>
<br />
<br />
{if isset($side_panels) && in_array('related_records', $side_panels) && $related_records_modules}
<table border="0" cellpadding="0" cellspacing="0" class="subpanel_container">
  <tr>
    <td>
      {if $smarty.cookies.customers_selected_related_tab && in_array($smarty.cookies.customers_selected_related_tab, $related_records_modules)}
        {assign var='rel_type' value=$smarty.cookies.customers_selected_related_tab}
      {else}
        {assign var='rel_type' value=$related_records_modules.0}
      {/if}
      <input type="hidden" id="rel_type" name="rel_type" value="{$rel_type}" />
      <a name="related_subpanel_customer{$customer->get('id')}"></a>
      {include file=`$theme->templatesDir`related_records_actions_box.html}
      <div class="m_header_m_menu scroll_box_container">
        {foreach from=$related_records_modules item=module key=model}
          <div id="{$session_params.$module}" class="rel_tab{if $rel_type eq $module} loaded{else}" style="display: none;{/if}">
            {if $rel_type eq $module}
              <script type="text/javascript">
                ajaxUpdater({ldelim}
                  link: '{$related.$module}',
                  target: '{$session_params.$module}',
                  execute_after: function() {ldelim} removeClass($('related_records_action_tabs'), 'hidden'); {rdelim} 
                {rdelim});
              </script>
            {/if}
          </div>
        {/foreach}
      </div>
    </td>
  </tr>
</table>
{/if}