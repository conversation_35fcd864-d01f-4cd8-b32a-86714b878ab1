<?php

class Projects_Attachments_Viewer extends Viewer {
    public $template = 'attachments.html';

    public function prepare() {
        $this->model = $this->registry['project'];
        $this->data['project'] = $this->model;

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        if ($this->registry['added_files']) {
            $this->data['added_files'] = $this->registry['added_files'];
        }
        if ($this->registry['modified_files']) {
            $this->data['modified_files'] = $this->registry['modified_files'];
        }

        if ($this->registry['erred_added_files']) {
            $this->data['erred_added_files'] = $this->registry['erred_added_files'];
        }
        if ($this->registry['erred_modified_files']) {
            $this->data['erred_modified_files'] = $this->registry['erred_modified_files'];
        }
        $this->data['session'] = $this->registry['session'];

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('projects_attachments'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
