<?php

require_once('roles.model.php');

/**
 * Roles model class
 */
Class Roles extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Role';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#r.active#', $sort)) {
                $sort = 'ORDER BY r.active desc, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;

            }
        } else {
            $sort = 'ORDER BY r.active desc, r.id DESC';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT (r.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_ROLES . ' AS r' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_ROLES_I18N . ' AS ri18n' . "\n" .
                       '  ON (r.id=ri18n.parent_id AND ri18n.lang="' . $model_lang . '")' . "\n";

        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to role to fetch added by info
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (r.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to role to fetch modified by info
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (r.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }

        ///where clause
        $sql['where'] = $where . "\n";

        //group clause
        $sql['group'] = 'GROUP BY r.id' . "\n";

        //order by clause
        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#r.active#', $sort)) {
                $sort = 'ORDER BY r.active desc, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;
            }
        } else {
            $sort = 'ORDER BY r.active desc, r.id DESC';
        }

        //select clause
        $sql['select'] = 'SELECT r.*, ri18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name, ' . "\n" .
                         '  CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_ROLES . ' AS r' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_ROLES_I18N . ' AS ri18n' . "\n" .
                       '  ON (r.id=ri18n.parent_id AND ri18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to role to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (r.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                        //relate to role to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (r.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                        //relate to role to fetch deleted by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                       '  ON (r.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")';

        ///where clause
        $sql['where'] = $where . "\n";

        //group clause
        $sql['group_by'] = 'GROUP BY r.id' . "\n";

        //order by clause
        $sql['order'] = $sort  . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        foreach ($models as $key => $model) {
            $models[$key]->set('menu_settings', unserialize($model->get('menu_settings')), true);
        }

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(r.id) AS total';
                $sql['limit'] = '';
                $sql['group_by'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, $filters = array()) {

        $where[] = 'WHERE (';
        $current_user_id = $registry['currentUser']->get('id');
        $rights = $registry['currentUser']->get('rights');
        $module = 'roles';
        $action = $registry['action'];
        $current_right = $registry['module']==$module && isset($rights[$module][$action]) ?
                        $rights[$module][$action] : '';

        //additional 'where' for hiding not allowed models
        if ($current_user_id && $current_right && $current_right != 'all' && empty($filters['force'])) {
            if ($current_right == 'mine') {
                $where[] = "r.added_by=$current_user_id AND ";
            } elseif ($current_right == 'group') {
                $user_groups = $registry['currentUser']->get('groups');
                $user_departments = $registry['currentUser']->get('departments');
                $where[] = "(r.added_by=$current_user_id" .
                            (count($user_groups)?' OR r.`group` IN ('.implode(',', $user_groups).')':'') .") AND ";
            } elseif ($current_right == 'none' || $current_right == '') {
                $where[] = '0 AND ';
            }
        }

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                    /*if (preg_match('#ci18n(\d)?.name#', $var)) {
                        $var = preg_replace('#name#', 'lastname', $var);
                        $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                    } elseif (preg_match('#ui18n(\d)?.firstname#', $var)) {
                        $var = preg_replace('#firstname#', 'lastname', $var);
                        $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                    }*/
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#r.deleted#', $where)) {
            $where .= ' AND r.deleted = 0';
        }

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionParam = 'list_') {
        $sessionParam = strtolower($sessionParam . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     *
     * @param object $registry - the main registry
     * @return object - model object
     */
    public static function buildModel(&$registry) {
        $allowedRequestParams = array('id', 'model_lang', 'name', 'description',
                                      'document_types_assignments',
                                      'task_types_assignments',
                                      'contract_types_assignments',
                                      'finance_incomes_reason_types_assignments',
                                      'finance_expenses_reason_types_assignments',
                                      'finance_warehouses_document_types_assignments');
        $model = self::buildFromRequest($registry, self::$modelName, $allowedRequestParams);

        return $model;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //exclude the readonly models
        $where[] = 'id!=' . PH_ROLES_ADMIN;

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']       = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']     = sprintf("modified=now()");
        $set['modified_by']  = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_ROLES . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //exclude the readonly models
        $where = 'id!=' . PH_ROLES_ADMIN;

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_ROLES, 'id', $where);

        if (!$deleted) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //exclude the readonly models
        $where = 'id!=' . PH_ROLES_ADMIN;

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_ROLES, 'id', $where);

        if (!$restored) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models. Deletion is real.
     * ATTENTION: deletion has no restore
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function purge(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //exclude the readonly models
        $where = 'id!=' . PH_ROLES_ADMIN;

        //multiple purge is part of the transaction
        $purged = self::purgeMultiple($registry, $ids, DB_TABLE_ROLES, 'id', $where);

        if (!$purged) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Merges permissions of various roles
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the roles
     * @return array - array of merged permissions
     */
    public static function mergePermissions(&$registry, $ids) {
        $db = $registry['db'];

        if (!$ids) {
            return false;
        }

        $merged = array();
        foreach ($ids as $id) {
            $query = 'SELECT rd.id, rp.permission' . "\n" .
                     'FROM ' . DB_TABLE_ROLES_DEFINITIONS . ' AS rd' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_ROLES_PERMISSIONS . ' AS rp' . "\n" .
                     '  ON (rd.id=rp.definition_id AND rp.parent_id=' . $id . ')' . "\n" .
                     'ORDER BY rd.module, rd.position' . "\n";
            $records = $registry['db']->GetAssoc($query);

            if (empty($merged)) {
                $merged = $records;
            } else {
                foreach ($records as $definition_id => $permission) {
                    $merged_permission = constant('PH_PERMISSION_' . strtoupper($merged[$definition_id]));
                    $record_permission = constant('PH_PERMISSION_' . strtoupper($permission));
                    if ($merged_permission < $record_permission) {
                        //the permission is in favour of allowance
                        $merged[$definition_id] = $permission;
                    }
                }
            }
        }

        return $merged;
    }

}

?>
