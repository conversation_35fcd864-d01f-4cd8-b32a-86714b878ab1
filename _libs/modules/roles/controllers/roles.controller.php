<?php

class Roles_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Role';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Roles';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'menu', 'clone',
        'view', 'edit', 'assignments', 'companiesandoffices', 'cachboxesandbankaccounts'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit', 'assignments', 'companiesandoffices', 'cachboxesandbankaccounts'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the upper right menu
     */
    public $actionDefinitionsUpRight = array();

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add',
        'view', 'edit', 'translate',
        'assignments', 'companiesandoffices', 'cachboxesandbankaccounts', 'menu'
    );

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'clone':
            $this->_clone();
            break;
        case 'companiesandoffices':
            $this->_companiesAndOffices();
            break;
        case 'cachboxesandbankaccounts':
            $this->_cachboxesAndBankAccounts();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'search':
            $this->_search();
            break;
        case 'assignments':
            $this->_assignments();
            break;
        case 'menu':
            $this->_menu();
            break;
        case 'ajax_delete_menu_icon':
            $this->_deleteIcon();
            break;
        case 'ajax_display_roles_sections':
            $this->_displayRolesSections();
            break;
        case 'ajax_get_menu_icon':
            $this->_getIcon();
            break;
        case 'preview':
            $this->_preview();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $role = Roles::buildModel($this->registry);
            $role->getPermissions('request');

            if ($role->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_roles_add_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_roles_add_failed'),'',-1);
            }
        } else {
            //create empty roles model
            $role = Roles::buildModel($this->registry);
            $role->getPermissions('request');
        }

        if (!empty($role)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('role', $role->sanitize());
        }

        return true;
    }

    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $role = Roles::buildModel($this->registry);
            $role->getPermissions('request', array(), true);

            if ($role->save()) {
                //show message 'message_roles_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_roles_edit_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_roles_edit_failed'),'',-1);
                //register the model, with all the posted details
                $this->registry->set('role', $role);
            }

        } elseif ($id) {
            // the model from the DB
            $filters = array ('where' => array('r.id = ' . $id),
                              'model_lang' => $request->get('model_lang'));
            $role = Roles::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($role);

            if (!empty($role)) {
                $role->getPermissions();
            }
        }

        if (!empty($role)) {
            //check if the role is readonly (only the Admin role is read only)
            if ($role->isReadOnly()) {
                //the role is readonly
                $this->registry['messages']->setError($this->i18n('error_role_is_readonly'));
                $this->registry['messages']->insertInSession($this->registry);

                //nothing to edit here, redirect to the listing
                $this->redirect($this->module, 'list');
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('role')) {
                $this->registry->set('role',  $role->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Assignments of a role
     */
    private function _assignments() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $role = Roles::buildModel($this->registry);

            if ($role->saveAssignmentPermissions('Document') &&
                $role->saveAssignmentPermissions('Task') &&
                $role->saveAssignmentPermissions('Contract') &&
                $role->saveAssignmentPermissions('Finance_Incomes_Reason') &&
                $role->saveAssignmentPermissions('Finance_Expenses_Reason') &&
                $role->saveAssignmentPermissions('Finance_Warehouses_Document')) {
                //show message 'message_roles_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_roles_assignments_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_roles_assignments_failed'), '', -1);
                //register the model, with all the posted details
                $this->registry->set('role', $role);
            }

        } elseif ($id) {
            // the model from the DB
            $filters = array ('where' => array('r.id = ' . $id),
                              'model_lang' => $request->get('model_lang'));
            $role = Roles::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($role);

            if (!empty($role)) {
                $role->getAssignmentPermissions('Document');
                $role->getAssignmentPermissions('Task');
                $role->getAssignmentPermissions('Contract');
                $role->getAssignmentPermissions('Finance_Incomes_Reason');
                $role->getAssignmentPermissions('Finance_Expenses_Reason');
                $role->getAssignmentPermissions('Finance_Warehouses_Document');
            }
        }

        if (!empty($role)) {
            //check if the role is readonly
            if ($role->isReadOnly()) {
                //the role is readonly
                $this->registry['messages']->setError($this->i18n('error_role_is_readonly'));
                $this->registry['messages']->insertInSession($this->registry);
                //nothing to edit here, redirect to the listing
                $this->redirect($this->module, 'list');
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('role')) {
                $this->registry->set('role', $role->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $role = Roles::buildModel($this->registry);

            if ($role->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_roles_translate_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_roles_translate_failed'), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array ('where' => array('r.id = ' . $id),
                              'model_lang' => $request->get('model_lang'));
            $role = Roles::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($role);
        }

        if (!empty($role)) {
            //check if the role is readonly
            if ($role->isReadOnly()) {
                //the role is readonly
                $this->registry['messages']->setError($this->i18n('error_role_is_readonly'));
                $this->registry['messages']->insertInSession($this->registry);

                //nothing to edit here, redirect to the listing
                $this->redirect($this->module, 'list');
            }

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('role', $role->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * View model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array ('where' => array('r.id = ' . $id),
                              'model_lang' => $request->get('model_lang'));
        $role = Roles::searchOne($this->registry, $filters);

        if (!empty($role)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($role);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('role')) {
                $this->registry->set('role', $role->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Clone model
     */
    private function _clone() {
        //get the request
        $request = &$this->registry['request'];

        //get the requested model
        $id      = $request->get($this->action);
        $filters = array ('where'      => array('r.id = ' . $id),
                          'model_lang' => $request->get('model_lang'));
        $role    = Roles::searchOne($this->registry, $filters);

        //load the permissions of the role
        $role->getPermissions();

        if (!empty($role)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($role);

            //get assignments permissions
            $role->getAssignmentPermissions('Document');
            $role->getAssignmentPermissions('Task');
            $role->getAssignmentPermissions('Contract');
            $role->getAssignmentPermissions('Finance_Incomes_Reason');
            $role->getAssignmentPermissions('Finance_Expenses_Reason');
            $role->getAssignmentPermissions('Finance_Warehouses_Document');

            //get companies
            $role->getCompanies();

            //get offices
            $role->getOffices();

            //get cashboxes
            $role->getCashboxes();
            foreach ($role->get('role_cashboxes') as $cashbox_model_name => $cashbox_model_value) {
                foreach ($cashbox_model_value as $cashbox_action_name => $cashboxes) {
                    $role->set('role_cashboxes_' . $cashbox_model_name . '_' . $cashbox_action_name, $cashboxes, true);
                }
            }
            unset($role->properties['role_cashboxes']);

            //get bank accounts
            $role->getBankAccounts();
            foreach ($role->get('role_bank_accounts') as $bank_account_model_name => $bank_account_model_value) {
                foreach ($bank_account_model_value as $bank_account_action_name => $bank_accounts) {
                    $role->set('role_bank_accounts_' . $bank_account_model_name . '_' . $bank_account_action_name, $bank_accounts, true);
                }
            }
            unset($role->properties['role_bank_accounts']);

            // keep original id, unset id and set new name
            $role->set('original_id', $role->get('id'), true);
            $role->set('id', null, true);
            $role->set('name', $request->get('new_name'), true);

            //clone the role
            if ($role->cloneModel()) {
                //redirect to cloned model
                $this->registry['messages']->setMessage($this->i18n('message_roles_clone_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'edit', 'edit=' . $role->get('id'));
            } else {
                //redirect to original model
                $this->registry['messages']->setError($this->i18n('error_clone_role'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $id);
            }
        } else {
            //show error 'no such role'
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Edit access to companies and offices
     */
    private function _companiesAndOffices() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $role = Roles::buildModel($this->registry);
            $role->set('role_companies', $request->get('role_companies'), true);
            $role->set('role_offices', $request->get('role_offices'), true);

            if ($role->saveCompaniesAndOffices()) {
                //show message 'message_roles_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_roles_edit_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_roles_edit_failed'),'',-1);
                //register the model, with all the posted details
                $this->registry->set('role', $role);
            }

        } elseif ($id) {
            // the model from the DB
            $filters = array ('where' => array('r.id = ' . $id),
                              'model_lang' => $request->get('model_lang'));
            $role = Roles::searchOne($this->registry, $filters);
            if ($role) {
                $role->getCompanies();
                $role->getOffices();
            }

            //check access and ownership of the model
            $this->checkAccessOwnership($role);
        }

        if (!empty($role)) {

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('role')) {
                $this->registry->set('role',  $role->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Edit access to cashboxes and bank accounts
     */
    private function _cachboxesAndBankAccounts() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $role = Roles::buildModel($this->registry);
            $properties = $request->getAll();
            foreach ($properties as $p_name => $property) {
                if (strpos($p_name, 'role_') === 0) {
                    $role->set($p_name, $property, true);
                }
            }

            if ($role->saveCachboxesAndBankAccounts()) {
                //show message 'message_roles_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_roles_edit_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_roles_edit_failed'),'',-1);
                //register the model, with all the posted details
                $this->registry->set('role', $role);
            }

        } elseif ($id) {
            // the model from the DB
            $filters = array ('where' => array('r.id = ' . $id),
                              'model_lang' => $request->get('model_lang'));
            $role = Roles::searchOne($this->registry, $filters);
            if ($role) {
                $role->getCashboxes();
                $role->getBankAccounts();
            }

            //check access and ownership of the model
            $this->checkAccessOwnership($role);
        }

        if (!empty($role)) {

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('role')) {
                $this->registry->set('role',  $role->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        $this->getModel();
        $actions = parent::getActions($action_defs);

        //set clone action
        if ($this->model && $this->model->get('id') && isset($actions['clone'])) {
            $actions['clone']['options'] = array('label' => $this->i18n('clone'));
            $actions['clone']['ajax_no'] = 1;
            $actions['clone']['template'] = PH_MODULES_DIR . 'roles/templates/_action_clone.html';
            $actions['clone']['model_id'] = $this->model->get('id');
        }
        if ((!$this->model || !$this->model->get('id')) && isset($actions['menu'])) {
            unset($actions['menu']);
        }
        if ($this->model && $this->model->get('id') && isset($actions['edit']) && $this->registry['currentUser'] &&
        ($this->registry['currentUser']->get('role') == PH_ROLES_ADMIN || $this->registry['currentUser']->get('real_role') == PH_ROLES_ADMIN)) {
            $actions['preview'] = array_merge($actions['edit'],
                array('action' => 'preview', 'name' => 'preview', 'selected' => '',
                      'url' => $_SERVER['PHP_SELF'] . '?' . $this->registry['module_param'] . '=roles&amp;roles=preview&amp;preview=' .
                               $actions['edit']['model_id'] . '&amp;model_lang=' . $actions['edit']['model_lang']));
            $theme = $this->registry['theme'];
            if ($this->registry['currentUser']->get('role') == $this->model->get('id')) {
                $actions['preview']['label'] = $this->i18n('signout') . ': ' . $this->i18n('preview');
                $actions['preview']['img'] = 'laquo_big';
                if ($theme) {
                    $actions['preview']['icon'] = $theme->getIconForAction('preview_exit');
                }
                $actions['preview']['url'] .= '&amp;exit=1';
            } else {
                $actions['preview']['label'] = $this->i18n('preview');
                $actions['preview']['img'] = 'raquo_big';
                if ($theme) {
                    $actions['preview']['icon'] = $theme->getIconForAction('preview');
                }
            }
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        // check the current action and sets the alternative actions for view, edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } elseif ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit nor view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } elseif (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && ! empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('permissions');
                }
            }
        }

        if ($this->model) {
            if (isset($actions['search'])) {
                unset ($actions['search']);
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        $result = false;

        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        $readonly_status = $this->_checkReadOnly($ids);
        if ($readonly_status != 'error') {
            //activate/deactivate
            $result = Roles::changeStatus($this->registry, $ids, $status);
        }

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);

            //attempt to modify readonly model
            if ($readonly_status == 'warning') {
                $text = $this->i18n('warning_cannot_modify_readonly');
                $this->registry['messages']->setWarning($text);
            }
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);

            //attempt to modify readonly model
            if ($readonly_status == 'error') {
                $text = $this->i18n('error_cannot_modify_readonly');
                $this->registry['messages']->setError($text);
            }
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        $result = false;

        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        $readonly_status = $this->_checkReadOnly($ids);
        if ($readonly_status != 'error') {
            //delete roles
            $result = Roles::delete($this->registry, $ids);
        }

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));

            //attempt to modify readonly model
            if ($readonly_status == 'warning') {
                $text = $this->i18n('warning_cannot_modify_readonly');
                $this->registry['messages']->setWarning($text);
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);

            //attempt to modify readonly model
            if ($readonly_status == 'error') {
                $text = $this->i18n('error_cannot_modify_readonly');
                $this->registry['messages']->setError($text);
            }
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        $result = false;

        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        $readonly_status = $this->_checkReadOnly($ids);
        if ($readonly_status != 'error') {
            //restore items
            $result = Roles::restore($this->registry, $ids);
        }

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));

            //attempt to modify readonly model
            if ($readonly_status == 'warning') {
                $text = $this->i18n('warning_cannot_modify_readonly');
                $this->registry['messages']->setWarning($text);
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);

            //attempt to modify readonly role
            if ($readonly_status == 'error') {
                $text = $this->i18n('error_cannot_modify_readonly');
                $this->registry['messages']->setError($text);
            }
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;
        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        $result = false;

        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        $readonly_status = $this->_checkReadOnly($ids);
        if ($readonly_status != 'error') {
            //purge items
            $result = Roles::purge($this->registry, $ids);
        }

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));

            //attempt to modify readonly model
            if ($readonly_status == 'warning') {
                $text = $this->i18n('warning_cannot_modify_readonly');
                $this->registry['messages']->setWarning($text);
            }
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);

            //attempt to modify readonly model
            if ($readonly_status == 'error') {
                $text = $this->i18n('error_cannot_modify_readonly');
                $this->registry['messages']->setError($text);
            }
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }


    /**
     * Checks if ids contain readonly models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _checkReadOnly(&$ids) {
        $result = false;

        if (!is_array($ids) && is_numeric($ids)) {
            $ids = array($ids);
        }

        //check for attempt to modify readonly role
        if (count($ids) == 1 && in_array(PH_ROLES_ADMIN, $ids)) {
            $result = 'error';
        } elseif (count($ids) > 1 && in_array(PH_ROLES_ADMIN, $ids)) {
            $result = 'warning';
        }

        return $result;
    }

    private function _menu() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $role = Roles::buildModel($this->registry);

            if ($role->saveCustomMenu()) {
                //show message 'message_roles_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_roles_menu_success'),'',-1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_roles_menu_failed'),'',-1);
                //register the model, with all the posted details
                $this->registry->set('role', $role);
            }

        } elseif ($id) {
            // the model from the DB
            $filters = array ('where' => array('r.id = ' . $id),
                              'model_lang' => $request->get('model_lang'));
            $role = Roles::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($role);

        }

        if (!empty($role)) {

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('role')) {
                $this->registry->set('role',  $role->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_role'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    public function _deleteIcon() {

        $icon = $this->registry['request']->get($this->action);
        if (!$icon) {
            exit('0');
        }

        $icon = preg_replace('#\?(.*)$#', '', $icon);

        $file_path = PH_ROLES_MENUS_DIR . $icon;
        if (unlink($file_path)) {
            exit('1');
        }
        exit('0');
    }

    public function _getIcon() {

        $icon = Transliterate::convert($this->registry['request']->get($this->action));
        if (!$icon) {
            exit($this->registry['theme']->imagesUrl . 'menu/empty.png?' . uniqid());
        }

        $file_path = '';
        $files = FilesLib::readDir(PH_ROLES_MENUS_DIR);
        foreach ($files as $file_name) {
            if (preg_match('#^' . preg_quote($icon) . '\.[^\.]*#', $file_name)) {
                $file_path = PH_ROLES_MENUS_DIR . $file_name;
                $file_name = PH_ROLES_MENUS_URL . $file_name;
                break;
            } else {
            }
        }
        if ($file_path && file_exists($file_path)) {
            exit($file_name . '?' . uniqid());
        }

        $icon = preg_replace('#role_\d+_#', '', $icon);
        if (preg_match('#_sections_\d+$#', $icon)) {
            //gets section icon
            $module = preg_replace('#_sections_\d+$#', '', $icon);
            $matches = explode('_', $module, 2);
            if (count($matches) == 2 && $matches[0] == 'finance') {
                $module = $matches[0];
            }
            if ($module == 'finance') {
                $factory = PH_MODULES_DIR . $module . '/models/' . $module . '.documents_sections.factory.php';
            } else {
                $factory = PH_MODULES_DIR . $module . '/models/' . $module . '.sections.factory.php';
            }
            if (file_exists($factory)) {
                require_once($factory);
                if ($module == 'finance') {
                    $factory_class = ucfirst($module) . '_Documents_Sections';
                } else {
                    $factory_class = ucfirst($module) . '_Sections';
                }
                $alias = $factory_class::getAlias($module, preg_replace('#^' . $module . '_#', '', strtolower($factory_class)));
                $filters = array('sanitize' => true,
                                 'where' => array($alias . '.id = ' . preg_replace('#.*_(\d+)$#', '$1', $icon)));
                $model = $factory_class::searchOne($this->registry, $filters);
                $dir = 'PH_' . strtoupper($factory_class) . '_DIR';
                $url = 'PH_' . strtoupper($factory_class) . '_URL';
                if ($model && $model->get('icon_name') && file_exists(constant($dir) . $model->get('icon_name'))) {
                    exit(constant($url) . $model->get('icon_name') . '?' . uniqid());
                }
            }
        } elseif (preg_match('#_types_\d+$#', $icon)) {
            //gets type icon
            $module = preg_replace('#_types_\d+$#', '', $icon);
            $matches = explode('_', $module, 2);
            if (count($matches) == 2 && $matches[0] == 'finance') {
                $module = $matches[0];
            }
            if ($module == 'finance') {
                $factory = PH_MODULES_DIR . $module . '/models/' . $module . '.documents_types.factory.php';
            } else {
                $factory = PH_MODULES_DIR . $module . '/models/' . $module . '.types.factory.php';
            }
            if (file_exists($factory)) {
                require_once($factory);
                if ($module == 'finance') {
                    $factory_class = ucfirst($module) . '_Documents_Types';
                } else {
                    $factory_class = ucfirst($module) . '_Types';
                }
                $alias = $factory_class::getAlias($module, preg_replace('#^' . $module . '_#', '', strtolower($factory_class)));
                $filters = array('sanitize' => true,
                                 'where' => array($alias . '.id = ' . preg_replace('#.*_(\d+)$#', '$1', $icon)));
                $model = $factory_class::searchOne($this->registry, $filters);
                $dir = 'PH_' . strtoupper($factory_class) . '_DIR';
                $url = 'PH_' . strtoupper($factory_class) . '_URL';
                //customers icon column is branch_icon_file, for the other modules - icon_name
                $icon = $model->get('icon_name') ?:$model->get('branch_icon_file');
                if ($model && $icon && file_exists(constant($dir) . $icon)) {
                    exit(constant($url) . $icon . '?' . uniqid());
                }
            }
        }
        if (file_exists($this->registry['theme']->imagesDir . 'menu/' . $icon . '.png')) {
            exit($this->registry['theme']->imagesUrl . 'menu/' . $icon . '.png?' . uniqid());
        }

        exit($this->registry['theme']->imagesUrl . 'menu/empty.png?' . uniqid());
    }

    /**
     * Get permissions from given role and section like documents, projects
     * then return rendered template
     *
     * @param object $model - model object
     * @param bool $redirect - defines whether to redirect after check or not
     * @return string - rendered template
     */
    public function _displayRolesSections() {
        // Get the request from the registry (this is not the AJAX request)
        // It`s always GET
        $request = $this->registry['request'];
        // Get name of the current section from the request
        $current_section = $request->get('section');

        // If there`s id for this role
        if (intval($request->get('role_id')) > 0) {
            // Build the WHERE statement for the query
            $filters = array ('where' => array('r.id = ' . $request->get('role_id')),
                              'model_lang' => $request->get('model_lang'));
            // Build the role model from the database
            $role = Roles::searchOne($this->registry, $filters);
            // Load the model permissions from the database
            $role->getPermissions();
        } else {
            // This will happen when we are adding a new role
            // Build the role model
            $role = Roles::buildModel($this->registry);
            // Get the permissions from the request
            $role->getPermissions('request');
        }

        // Sanitize the model
        $role->sanitize();
        // Get the role`s sections and their elements
        $roles_groups = $role->rolesGroups;
        // Prepare the array used for the link elements
        $link_elements_properties = array();

        // Get all permission and then unset the ones that are not from the current section
        $current_section_permissions = $role->get('permissions');

        // For each section permissions
        foreach ($current_section_permissions as $key => $permission_content) {
            // If this section group is not among the ones from the current section groups
            if (!in_array($key, $roles_groups[$current_section])) {
                // Unset this section grop
                unset($current_section_permissions[$key]);
            } else {
                // Load the data for this section group
                // Get the first key of this section (i.e. get the group`s label)
                $first_label = array_keys($permission_content);
                $first_label = array_shift($first_label);
                // Getting the texts for the anchors used in the permissions index
                $link_elements_properties[$key]['module_label'] = $permission_content[$first_label][-1]['module_label'];
                // If there are any other elements than the module (i.e. if there are any types)
                if (count($permission_content) > 1) {
                    // Get the keys of the types for this module
                    $section_groups = array_keys($current_section_permissions[$key]);
                    // Remove the first key (the key of the module)
                    array_shift($section_groups);
                    // Array for the types and their label
                    $module_types_labels = array();
                    // Array for the first type element
                    $first_element = array();

                    // For each type
                    foreach ($section_groups as $section_group_key) {
                        // Get only the first element of this type
                        //   (because there are many sub elements for this type but they all have the same values for model_type and subtitle_type_label)
                        $first_element = current($current_section_permissions[$key][$section_group_key]);
                        if (!empty($first_element['subtitle_type_label'])) {
                            $module_types_labels[$key . '_' . $first_element['model_type']] = $first_element['subtitle_type_label'];
                        }
                    }

                    // Sort the types by label
                    asort($module_types_labels);
                    // Set the types for this module into the link elements
                    $link_elements_properties[$key]['types_labels'] = $module_types_labels;
                }
            }
        }

        // Load the viewer data for the permissions
        $permissions_viewer = new Viewer($this->registry);
        $permissions_viewer->setFrameset(PH_MODULES_DIR . 'roles/templates/_permissions_section.html');
        $permissions_viewer->model = $role;
        $permissions_viewer->data['current_section_permissions'] = $current_section_permissions;
        $permissions_viewer->data['section_name']                = $current_section;
        $permissions_viewer->data['mode']                        = $request->get('mode');

        // Load the viewer data for the permissions index
        $permissions_index_viewer = new Viewer($this->registry);
        $permissions_index_viewer->setFrameset(PH_MODULES_DIR . 'roles/templates/_permissions_index.html');
        $permissions_index_viewer->data['section_name']  = $current_section;
        $permissions_index_viewer->data['link_elements'] = $link_elements_properties;

        // Prepare the result
        $result = array(
            'viewer'        => $permissions_viewer->fetch(),
            'link_elements' => $permissions_index_viewer->fetch()
        );

        // Return the result
        print json_encode($result);

        // Just exit
        exit;
    }

    /**
     * Enter or exit preview mode of another role. Action is available only to users with administrator role
     */
    private function _preview() {
        $id = $this->registry['request']->get($this->action);
        if ($id && $this->registry['currentUser'] &&
        ($this->registry['currentUser']->get('role') == PH_ROLES_ADMIN || $this->registry['currentUser']->get('real_role') == PH_ROLES_ADMIN)) {
            if ($this->registry['request']->get('exit')) {
                // exit preview
                if ($this->registry['currentUser']->get('real_role') && $this->registry['currentUser']->get('role') == $id) {
                    $this->registry['currentUser']->set('role', $this->registry['currentUser']->get('real_role'), true);
                    $this->registry['currentUser']->unsetProperty('real_role', true);
                    $this->registry['session']->remove('preview_role');
                }
            } else {
                // enter preview
                if ($role = Roles::searchOne($this->registry, array('where' => array('r.id=\'' . $id . '\''), 'sanitize' => true, 'force' => true))) {
                    if ($this->registry['currentUser']->get('role') == PH_ROLES_ADMIN) {
                        $this->registry['currentUser']->set('real_role', $this->registry['currentUser']->get('role'), true);
                    }
                    $this->registry['currentUser']->set('role', $id, true);
                    $this->registry['session']->set('preview_role', $id, '', true);
                }
            }
        }
        if (isset($_SERVER['HTTP_REFERER'])) {
            header('Location: ' . $_SERVER['HTTP_REFERER']);
        } else {
            $this->redirect('index');
        }
    }
}

?>
