<?php

class Translations_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Translation';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Translations';

    /**
     * Action definitions for this controller
     * Import action is stopped as it should be performed in developlemnt environment only.
     */
    public $actionDefinitions = array('export'/* , 'import' */);

    /**
     * Action name one of the add, edit, delete, list, etc.
     */
    public $defaultAction = 'export';

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            /* case 'import':
                $this->_import();
                break; */
            case 'export':
            default:
                $this->setAction('export');
                $this->_export();
                break;
        }
    }

    /**
     * Export translations
     *
     * @return boolean
     */
    protected function _export() {

        $translation = Translations::buildModel($this->registry);

        if ($this->registry['request']->isPost()) {
            // perform the export
            $translation->export();
        }

        //register the model (sanitized)
        //so that it could be used further by the viewer
        $this->registry->set('translation', $translation->sanitize());

        return true;
    }

    /**
     * Import translations
     *
     * @return boolean
     */
    protected function _import() {

        $translation = Translations::buildModel($this->registry);

        if ($this->registry['request']->isPost()) {
            // perform the import
            if ($translation->import()) {
                // display success message
                $this->registry['messages']->setMessage($this->i18n('message_translations_import_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = 1;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_translations_import_failed'), '', -1);
            }
        }

        //register the model (sanitized)
        //so that it could be used further by the viewer
        $this->registry->set('translation', $translation->sanitize());

        return true;
    }
}

?>
