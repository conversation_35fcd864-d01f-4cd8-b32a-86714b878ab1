<?php

require_once PH_MODULES_DIR . 'soap/models/soap.handler.php';

class Custom_Soap_Handler extends Soap_Handler {

    /**
     * Diagnostics data - array of arrays containing
     *   code, message text and trace information
     * List of codes for registration of damages
     * 0: Successfully added damage
     * 1: Incorrect carCode!
     * 2: Insurance contract not found!
     * 3: Invalid damage data
     * 4: Invalid phone
     * 5: Invalid mobile phone
     * 6: Invalid email
     * 7: Invalid engine capacity
     * 8: Invalid car seats
     * 9: Invalid car doors
     * 10: Invalid year of production
     * 100: General error adding damage
     *
     * List of codes for examination reservations
     * 1000: Successful operation
     * 1001: Invalid UCN (report of invalid UCN or HIN to conceal the error to prevent SQL injection)
     * 1002: Invalid HIN (deprecated)
     * 1003: Non-existing HIN
     * 1004: No schedule document found or the schedule has not intervals in it
     * 1005: Invalid start hour of registration
     * 1006: Invalid end hour of registration
     * 1007: Invalid date of registration
     * 1008: Start/end hour do not match schedule interval
     * 1009: The interval is already reserved by another user
     * 1010: The interval is already reserved by the user (no remove flag sent)
     * 1011: The remove flag is sent but the reservation is made by another user
     * 1012: <PERSON>rror adding the reservation document
     * 1013: Error editing the reservation document
     * 1014: No reservation document found (although the ID should be correct)
     * 1015: Error saving the schedule document (lock/unlock the intervals)
     * 1016: Invalid Email address
     * 1017: Invalid reservation (the date is past)
     * 1018: Invalid reservation (limit exceeded)
     * 1019: Invalid reservation (deadline expired)
     * 1100: General error
     */
     private $diag;

    /**
     * @var $_scheduleID - stores the id of the schedule document (type 14)
     */
     private $_scheduleID;

    /**
     * @var $_reservationID - stores the id of the reservation document (type 15)
     */
     private $_reservationID;

    /**
     * @var $_reservationCorrections - the number of corrections made for the HIN of the logged user in reservation document (type 15)
     *                    IMPORTANT: this value is used to check against the corrections limit
     */
    private $_reservationCorrections;

    /**
     * @var $_reservationDate - the date of the reservation of the current user as set in the reservation document (type 15)
     *                        IMPORTANT: this value is used to check against the corrections window (in days)
     */
    private $_reservationDate;

    /**
     * @var $_reservationStartHour - the start hour of the reservation of the current user as set in the reservation document (type 15)
     */
    private $_reservationStartHour;

    /**
     * @var $_reservationEndHour - the end hour of the reservation of the current user as set in the reservation document (type 15)
     */
    private $_reservationEndHour;

    /**
     * @var $_correctionsLimit - the max number of corrections as per schedule document (type 14)
     */
    private $_correctionsLimit;

    /**
     * @var $_correctionsWindowDays - duration in days of the wind schedule document (type 14)
     */
    private $_correctionsWindowDays;

    /**
     * @var $_correctionsWindowsDate - the date from which all of the corrections are expired
     */
    private $_correctionsWindowsDate;

    public static $encryptionKey = 'AON_car_insurance!';

    /**
     * Function to set diagnostic data
     *
     * @param int $code          - codes are listed above (in the $diag property help section)
     * @param array $text_params - list of params used to replace the placeholders %s in the i18n texts
     * @param mixed $trace       - debug information
     * @return string $text      - the message text
     */
    private function _setDiagnostics($code, $text_params = array(), $trace = null) {
        $label = $this->i18n('message_' . $code);
        if (empty($label)) {
            $label = $code;
            $code = 'unknown';
        }
        $text         = vsprintf($label, $text_params);
        $this->diag[] = array('code'  => $code,
                              'text'  => $text,
                              'trace' => (!empty($trace) ? var_export($trace, true) : ''));

        return $text;
    }

    /**
     * Get policy data by car number
     * The data is stored in car insurance contracts (ID: 4), car nomenclatures (ID: 5)
     *
     * @param object $params - function parameters
     * @return array         - the result and the diagnostic data
     */
    public function nzGetPolicyData($params) {
        // validate
        if (!$params->carCode) {
            $this->_setDiagnostics(1);
        }

        // we have errors occurred so exit and return the errors
        if (!empty($this->diag)) {
            return array('nzExaminationResults' => '', 'nzResult' => false, 'nzDiag' => $this->diag);
        }

        // get the examination results
        //Todo: define variables names/IDs as settings
        $query = 'SELECT DISTINCT(n.id) as carID,' . "\n" .
                 '       n.code as carCode, ni.name as carName, ' . "\n" .
                 '       nc1.value as carType, nc2.value as carRamaNum, nc3.value as carCapacity, nc4.value as carSeats, ' . "\n" .
                 '       nc5.value as carDoors, nc6.value as carColor, nc7.value as carPaint, nc8.value as carMadeYear, nc9.value as carFuelType,' . "\n" .
                 '       IF(co.id IS NOT NULL, cocstm2.value, cocstm4.value) as policyType,' . "\n" .
                 '       IF(co.id IS NOT NULL, co.id, co2.id) as policyID,' . "\n" .
                 '       IF(co.id IS NOT NULL, co.custom_num, co2.custom_num) as policyNum,' . "\n" .
                 '       IF(co.id IS NOT NULL, co.date_start, co2.date_start) as policyFrom, ' . "\n" .
                 '       IF(co.id IS NOT NULL, co.date_validity, co2.date_validity) as policyTo, ' . "\n" .
                 '       IF(co.id IS NOT NULL, c.id, c2.id) as ownerID, ' . "\n" .
                 '       IF(co.id IS NOT NULL, TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)), TRIM(CONCAT(ci18n2.name, " ", ci18n2.lastname))) as ownerName, ' . "\n" .
                 '       IF(co.id IS NOT NULL, IF(c.is_company, c.eik, c.ucn), IF(c2.is_company, c2.eik, c2.ucn)) as ownerEGNEIK, ' . "\n" .
                 '       IF(co.id IS NOT NULL, ci18n.city, ci18n2.city) as ownerCity, ' . "\n" .
                 '       IF(co.id IS NOT NULL, IF(c.is_company, IF(ci18n.registration_address!="", ci18n.registration_address, ci18n.address), IF(ci18n.address_by_personal_id!="", ci18n.address_by_personal_id, ci18n.address)),' . "\n" .
                 '                             IF(c2.is_company, IF(ci18n2.registration_address!="", ci18n2.registration_address, ci18n2.address), IF(ci18n2.address_by_personal_id!="", ci18n2.address_by_personal_id, ci18n2.address))) as ownerAddress ' . "\n" .
                 'FROM nom n ' . "\n" .
                 'JOIN nom_i18n ni' . "\n" .
                 '  ON n.active AND ' . "\n" .
                 '     n.deleted_by=0 AND' . "\n" .
                 '     n.type=5 AND' . "\n" . //nomenclature type 5
                 '     TRIM(n.code)="' . trim(General::slashesEscape($params->carCode)) . '"' . "\n" .
                 '     AND n.id=ni.parent_id' . "\n" .
                 //Individual insurance (contract 4)
                 'LEFT JOIN contracts_cstm cocstm1' . "\n" .
                 '  ON cocstm1.var_id=401 AND cocstm1.value=n.id' . "\n" . //401: car_id (contract 4)
                 'LEFT JOIN contracts co' . "\n" .
                 '  ON cocstm1.model_id=co.id AND co.subtype="contract" AND co.deleted_by=0 AND co.annulled_by=0 AND co.active=1 AND co.date_validity > CURDATE()' . "\n" .
                 'LEFT JOIN contracts_cstm cocstm2' . "\n" .
                 '  ON cocstm2.var_id=405 AND cocstm2.value=18 AND cocstm2.model_id=co.id' . "\n" . //405: insurance_type_con4 (contract 4) - insurance type should be kasko (option_value 18)
                 //Group insurance (contract 6)
                 'LEFT JOIN contracts_cstm cocstm3' . "\n" .
                 '  ON cocstm3.var_id=610 AND cocstm3.value=n.id' . "\n" . //610: car_id (contract 6)
                 'LEFT JOIN contracts co2' . "\n" .
                 '  ON cocstm3.model_id=co2.id AND co2.subtype="contract" AND co2.deleted_by=0 AND co2.annulled_by=0 AND co2.active=1 AND co2.date_validity > CURDATE()' . "\n" .
                 'LEFT JOIN contracts_cstm cocstm4' . "\n" .
                 '  ON cocstm4.var_id=602 AND cocstm4.value=18 AND cocstm4.model_id=co2.id' . "\n" . //602: insurance_type_con6 (contract 6) - insurance type should be kasko (option_value 18)
                 //car data
                 'LEFT JOIN nom_cstm nc1' . "\n" .
                 '  ON nc1.var_id=50505 AND nc1.model_id=n.id' . "\n" . //50505: car_type (nomenclature 5)
                 'LEFT JOIN nom_cstm nc2' . "\n" .
                 '  ON nc2.var_id=50501 AND nc2.model_id=n.id' . "\n" . //50501: car_rama_num (nomenclature 5)
                 'LEFT JOIN nom_cstm nc3' . "\n" .
                 '  ON nc3.var_id=50502 AND nc3.model_id=n.id' . "\n" . //50502: car_capacity (nomenclature 5)
                 'LEFT JOIN nom_cstm nc4' . "\n" .
                 '  ON nc4.var_id=50511 AND nc4.model_id=n.id' . "\n" . //50511: car_seats (nomenclature 5)
                 'LEFT JOIN nom_cstm nc5' . "\n" .
                 '  ON nc5.var_id=50512 AND nc5.model_id=n.id' . "\n" . //50512: car_doors (nomenclature 5)
                 'LEFT JOIN nom_cstm nc6' . "\n" .
                 '  ON nc6.var_id=50506 AND nc6.model_id=n.id' . "\n" . //50506: car_color (nomenclature 5)
                 'LEFT JOIN nom_cstm nc7' . "\n" .
                 '  ON nc7.var_id=50514 AND nc7.model_id=n.id' . "\n" . //50514: car_paint (nomenclature 5)
                 'LEFT JOIN nom_cstm nc8' . "\n" .
                 '  ON nc8.var_id=50503 AND nc8.model_id=n.id' . "\n" . //50503: car_made_year (nomenclature 5)
                 'LEFT JOIN nom_cstm nc9' . "\n" .
                 '  ON nc9.var_id=50504 AND nc9.model_id=n.id' . "\n" . //50504: car_fuel_type (nomenclature 5)
                 //customer data
                 'LEFT JOIN customers c' . "\n" .
                 '  ON co.customer=c.id' . "\n" .
                 'LEFT JOIN customers_i18n ci18n' . "\n" .
                 '  ON c.id=ci18n.parent_id AND ci18n.lang="' . $this->registry['lang'] . '"' . "\n" .
                 'LEFT JOIN customers c2' . "\n" .
                 '  ON co2.customer=c2.id' . "\n" .
                 'LEFT JOIN customers_i18n ci18n2' . "\n" .
                 '  ON c2.id=ci18n2.parent_id AND ci18n2.lang="' . $this->registry['lang'] . '"' . "\n" .
                 'HAVING policyType=18'; //the insurance policy should always be KASKO (18)
        $data = $this->registry['db']->GetRow($query);
        if (!empty($data)) {
            // obfuscate all IDs
            foreach($data as $item => $value) {
                if (preg_match('#.*ID$#', $item)) {
                    $data[$item] = General::encrypt($value, self::$encryptionKey, 'xtea');
                }
            }
            $result = true;
            $this->_setDiagnostics(0);
        } else {
            $result = false;
            $this->_setDiagnostics(2, $params->carCode);
        }

        // Return the result and the diagnostic data
        return array('nzPolicyData' => $data, 'nzResult' => $result, 'nzDiag' => $this->diag);
    }

    /**
     * Add document type Damage Registration (10)
     *
     * @param object $params - function parameters
     * @return array         - the result and the diagnostic data
     */
    public function nzRegisterDamage($params) {
        // validate
        $nzResult = true;
        if (!$params->carID || !$params->carCode || !$params->policyID || !$params->policyNum || !$params->ownerName || !$params->ownerID) {
            //these values come with the first part of the soap (nzGetPolicyData)
            $this->_setDiagnostics(3);
            $nzResult = false;
        }
        if (!empty($params->notifierPhone) && !preg_match('#^\+?[0-9]+$#', $params->notifierPhone)) {
            //validate phone
            $this->_setDiagnostics(4);
            $nzResult = false;
        }
        if (!empty($params->notifierMobile) && !preg_match('#^\+?[0-9\-\(\)\ ]+$#', $params->notifierMobile)) {
            //validate mobile
            $this->_setDiagnostics(5);
            $nzResult = false;
        }
        if ($params->notifierEmail && !Validator::validEmail($params->notifierEmail)) {
            //validate email
            $this->_setDiagnostics(6);
            $nzResult = false;
        }
        if ($params->carCapacity && !preg_match('#^\d+$#', $params->carCapacity)) {
            //validate car engine capacity
            $this->_setDiagnostics(7);
            $nzResult = false;
        }
        if ($params->carSeats && !preg_match('#^\d+$#', $params->carSeats)) {
            //validate car seats
            $this->_setDiagnostics(8);
            $nzResult = false;
        }
        if ($params->carDoors && !preg_match('#^\d+$#', $params->carDoors)) {
            //validate car doors
            $this->_setDiagnostics(9);
            $nzResult = false;
        }
        if ($params->carMadeYear &&
            //YYYY, dd.mm.YYY or dd/mm/YYYY
            (!preg_match('#^(\d{2}[\.|\/]\d{2}[\.|\/]\d{4}|\d{4})$#', $params->carMadeYear) ||
            strlen($params->carMadeYear) == 10 &&
                (!checkdate(substr($params->carMadeYear, 3, 2), substr($params->carMadeYear, 0, 2), substr($params->carMadeYear, 6, 4)) ||
                 General::strftime('%Y-%m-%d', strtotime($params->carMadeYear)) > date('Y-m-d')) ||
            strlen($params->carMadeYear) == 4 && $params->carMadeYear > date('Y'))) {
            //validate car production year
            $this->_setDiagnostics(10);
            $nzResult = false;
        }
        if (!$nzResult) {
            return array('documentNumber' => '', 'nzResult' => false, 'nzDiag' => $this->diag);
        }

        // the id of car, policy and owner are encrypted to avoid intrusions
        $params->carID = General::decrypt($params->carID, self::$encryptionKey, 'xtea');
        $params->policyID = General::decrypt($params->policyID, self::$encryptionKey, 'xtea');
        $params->ownerID = General::decrypt($params->ownerID, self::$encryptionKey, 'xtea');

        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';

        //ToDo: define type as setting
        $type_id = 10;
        $filters = array('where' => array('dt.id = ' . $type_id,
                                          'dt.active = 1',
                                          'dt.inheritance = 0'),
                         'sanitize' => true);
        $type = Documents_Types::searchOne($this->registry, $filters);

        //create empty document model
        $document = new Document($this->registry);

        $old_document = new Document($this->registry, array('type' => $type_id));
        $this->registry->set('get_old_vars', true, true);
        $old_document->getVars();
        $old_document->sanitize();


        //set the variables in the request as POST
        $mapping = array(
            'notifierName'                 => 'notifier_name',
            'notifierID'                   => 'notifier_id',
            'notifierCity'                 => 'notifier_city',
            'notifierAddress'              => 'notifier_address',
            'notifierAddressNum'           => 'notifier_address__num',
            'notifierAddressBlock'         => 'notifier_address__block',
            'notifierAddressEntry'         => 'notifier_address__entry',
            'notifierAddressApp'           => 'notifier_address__app',
            'notifierPhone'                => 'notifier_phone',
            'notifierMobile'               => 'notifier_mobile',
            'notifierEmail'                => 'notifier_email',
            'notifierFunction'             => 'notifier_function',
            'carID'                        => 'car_id',
            'carCode'                      => 'car_code',
            'carType'                      => 'car_type',
            'carName'                      => 'car_name',
            'carRamaNum'                   => 'car_rama_num',
            'carCapacity'                  => 'car_capacity',
            'carFuelType'                  => 'car_fuel_type',
            'carSeats'                     => 'car_seats',
            'carDoors'                     => 'car_doors',
            'carColor'                     => 'car_color',
            'carPaint'                     => 'car_paint',
            'carMadeYear'                  => 'car_made_year',
            'ownerID'                      => 'owner_id',
            'ownerName'                    => 'owner_name',
            'ownerEGNEIK'                  => 'owner_egn_eik',
            'ownerCity'                    => 'owner_city',
            'ownerAddress'                 => 'owner_address',
            'damagesResults'               => 'damages_results',
            'insuranceEventDate'           => 'insurance_event_date',
            'insuranceEventCircumstance'   => 'insurance_event_circumstance',
            'insuranceEventAutoplace'      => 'insurance_event_autoplace',
            'insuranceInsurerName'         => 'insurance_insurer_name',
            'insuranceInsurerID'           => 'insurance_insurer_id',
            'insuranceIdentifiedDisabilities' => 'insurance_identified_disabilities',
            'policyID'                     => 'policy_id',
            'policyNum'                    => 'policy_num',
            'policyFrom'                   => 'policy_from',
            'policyTo'                     => 'policy_to',
        );

        //push params into request
        foreach($mapping as $param => $var) {
            switch($param) {
                case 'insuranceEventDate':
                    $value = ($params->$param) ? General::strftime('%Y-%m-%d %T', $params->$param) : '';
                    break;
                case 'policyFrom':
                case 'policyTo':
                    $value = ($params->$param) ? General::strftime('%Y-%m-%d', $params->$param) : '';
                    break;
                default:
                    $value = $params->$param;
            }
            $this->registry['request']->set($var, $value, 'all', true);
        }
        //manage uploaded files
        if (!empty($params->uploadedFiles)) {
            $files_types = array();
            $delete_files_types = array();
            $uploadedFiles = $params->uploadedFiles;
            if (is_object($params->uploadedFiles) && isset($params->uploadedFiles->fileName)) {
                //just one file uploaded
                $uploadedFiles = array($params->uploadedFiles);
            }

            foreach($uploadedFiles as $uploadedFile) {
                //the dropdown in the grouping table
                $files_types[] = $uploadedFile->fileDescription;
                $delete_files_types[] = '';

                //the file itself
                $file_path = '';
                if ($uploadedFile->fileContent) {
                    $file_path = tempnam(ini_get('upload_tmp_dir'), 'docx');
                    $fh = fopen($file_path, 'w+');
                    fwrite($fh, gzinflate(base64_decode($uploadedFile->fileContent)));
                    fclose($fh);
                }
                $_FILES['files_file']['name'][]     = $uploadedFile->fileName;
                $_FILES['files_file']['type'][]     = $uploadedFile->fileType;
                $_FILES['files_file']['tmp_name'][] = $file_path;
                $_FILES['files_file']['error'][]    = $uploadedFile->fileError;
                $_FILES['files_file']['size'][]     = $uploadedFile->fileSize;
            }
            //Todo: define variables as settings
            $this->registry['request']->set('files_type', $files_types, 'all', true);
            $this->registry['request']->set('delete_files_type', $delete_files_types, 'all', true);
        }

        //set default values from the document type
        $document->set('type', $type_id, true);
        $document->set('type_name', $type->get('name'), true);
        $document->set('generate_system_task', $type->get('generate_system_task'), true);
        $document->set('direction', $type->get('direction'), true);
        $document->set('name', $type->get('default_name'), true);
        $document->set('department', $type->getDefaultDepartment(), true);
        $document->set('group', $type->getDefaultGroup(), true);
        $document->set('media', $type->get('default_media'), true);
        $document->set('media_name', $type->get('default_media_name'), true);
        //set customer as the car owner
        $document->set('customer', $params->ownerID, true);

        //get post vars
        $this->registry->set('get_old_vars', false, true);
        $document->set('plain_vars', null, true);
        $this->registry->set('edit_all', true, true);
        $document->getVars();

        if ($document->save()) {
            $filters = array('where' => array('d.id = ' . $document->get('id')),
                             'model_lang' => $document->get('model_lang'),
                             'skip_assignments' => true,
                             'skip_permissions_check' => true);
            $new_document = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_document->getVars();

            //IMPORTANT: the email notifications are probably OFF for this installation, but this email SHOULD BE SEND!
            $do_not_send = $this->registry['config']->getParam('emails', 'do_not_send');
            //turn the notifications on the send the email
            $this->registry['config']->setInt('emails', 'do_not_send', 0, 0);
            $new_document->defaultAssign();
            //restore the setting
            $this->registry['config']->setInt('emails', 'do_not_send', $do_not_send, 0);

            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            $audit_parent = Documents_History::saveData($this->registry,
                                                        array('model' => $document,
                                                              'action_type' => 'add',
                                                              'new_model' => $new_document,
                                                              'old_model' => $old_document));
            $result = true;
            $this->_setDiagnostics(0, $document->get('full_num'));
        } else {
            $this->_setDiagnostics(100);
            $errors = $this->registry['messages']->getErrors();
            foreach($errors as $err_field => $err_text) {
                $this->_setDiagnostics($err_text, null, $err_field);
            }
        }

        // Return the result and the diagnostic data
        return array('documentNumber' => $document->get('full_num'), 'nzResult' => $result, 'nzDiag' => $this->diag);
    }

    /**
     * Login for medical exams page by UCN and HIN
     * The data is stored in nomenclature type  HIN (id: 8)
     *
     * @param object $params - function parameters
     * @return array         - the result and the diagnostic data
     */
    public function nzLoginMedicalExam($params) {
        //remove whitespaces
        $UCN   = isset($params->UCN) ? trim($params->UCN) : '';
        $HIN   = isset($params->HIN) ? trim($params->HIN) : '';
        $email = isset($params->Email) ? trim($params->Email) : '';

        // validate
        if (!$UCN || !is_numeric($UCN)) {
            $this->_setDiagnostics(1001);
        }
        /*
        if (!preg_match('#^\d+$#', $HIN)) {
            $this->_setDiagnostics(1002);
        }
        */
        if ($email && !Validator::validEmail($email)) {
            $this->_setDiagnostics(1016);
        }

        // we have errors occurred so exit and return the errors
        if (!empty($this->diag)) {
            return array('nzInsuredPersonName' => '',
                        'nzHospital' => '',
                        'nzHospitalAddress' => '',
                        'nzContactName' => '',
                        'nzContactPhone' => '',
                        'nzContactEmail' => '',
                        'nzResult' => false,
                        'nzDiag' => $this->diag);
        }

        // get HIN
        $hin_id = $this->_getHIN($UCN, $HIN);
        if (!$hin_id) {
            //invalid UCN/HIN provided
            $this->_setDiagnostics(1001);
            return array('nzInsuredPersonName' => '',
                        'nzHospital' => '',
                        'nzHospitalAddress' => '',
                        'nzContactName' => '',
                        'nzContactPhone' => '',
                        'nzContactEmail' => '',
                        'nzResult' => false,
                        'nzDiag' => $this->diag);
        }

        // get details about the medical examination
        $data = $this->_getMedicalExamData($hin_id);
        if (empty($data)) {
            //set empty values,
            //there is no opened active, not deleted schedule document
            $data = array(
                'insured_person_name' => '',
                'hospital'            => '',
                'hospital_address'    => '',
            );
            $this->_setDiagnostics(1004);
            $nzResult = false;
        } else {
            //the result is true
            $this->_setDiagnostics(1000);
            $nzResult = true;
        }

        // Return the result and the diagnostic data
        return array('nzInsuredPersonName' => $data['insured_person_name'],
                     'nzHospital' => $data['hospital'],
                     'nzHospitalAddress' => $data['hospital_address'],
                     'nzContactName' => $data['contact_name'],
                     'nzContactPhone' => $data['contact_phone'],
                     'nzContactEmail' => $data['contact_email'],
                     'nzResult' => $nzResult,
                     'nzDiag' => $this->diag);
    }

    /**
     * Get schedule by UCN/HIN from document type 14/15
     *
     * @param object $params - function parameters
     * @return array         - the result and the diagnostic data
     */
    public function nzGetMedicalExamSchedule($params) {
        //remove whitespaces
        $UCN = trim($params->UCN);
        $HIN = trim($params->HIN);

        // validate
        if (!$UCN || !is_numeric($UCN)) {
            $this->_setDiagnostics(1001);
        }
        /*
        if (!preg_match('#^\d+$#', $HIN)) {
            $this->_setDiagnostics(1002);
        }
        */

        // we have errors occurred so exit and return the errors
        if (!empty($this->diag)) {
            return array('nzMedicalExamSchedule' => '', 'nzResult' => false, 'nzDiag' => $this->diag);
        }

        // get HIN
        $hin_id = $this->_getHIN($UCN, $HIN);
        if (!$hin_id) {
            //invalid UCN/HIN provided
            $this->_setDiagnostics(1001);
            return array('nzMedicalExamSchedule' => '', 'nzResult' => false, 'nzDiag' => $this->diag);
        }

        // get details about the medical examination
        $schedule = $this->_getMedicalExamSchedule($hin_id);
        if (empty($schedule)) {
            //the result is true, there is no schedule yet
            $this->_setDiagnostics(1004);
            return array('nzMedicalExamSchedule' => '', 'nzResult' => false, 'nzDiag' => $this->diag);
        } else {
            $nzMedicalExamSchedule = array();
            foreach($schedule as $date => $intervals) {
                $o = new stdClass();
                $o->date = $date;
                $o->intervals = array();
                foreach($intervals as $interval) {
                    $i = new stdClass();
                    $i->hourStart = $interval['start_hour'];
                    $i->hourEnd = $interval['end_hour'];
                    $i->status = $interval['status'];
                    $i->restrictions = $interval['restrictions'];
                    $o->intervals[] = $i;
                }
                $nzMedicalExamSchedule[] = $o;
            }

        }

        //the result is true, there is no schedule yet
        $this->_setDiagnostics(1000);

        // Return the result and the diagnostic data
        return array(
            'nzReservationDate' => $this->_reservationDate,
            'nzReservationStartHour' => $this->_reservationStartHour,
            'nzReservationEndHour' => $this->_reservationEndHour,
            'nzReservationCorrections' => $this->_reservationCorrections,
            'nzCorrectionsLimit' => $this->_correctionsLimit,
            'nzCorrectionsWindowDays' => $this->_correctionsWindowDays,
            'nzCorrectionsWindowDate' => $this->_correctionsWindowDate,
            'nzMedicalExamSchedule' => $nzMedicalExamSchedule,
            'nzResult' => true, 'nzDiag' => $this->diag);
    }

    /**
     * Make a reservation in document type 15
     *
     * @param object $params - function parameters
     * @return array         - the result and the diagnostic data
     */
    public function nzReserveMedicalExam($params) {
        //remove whitespaces
        $UCN   = trim($params->UCN);
        $HIN   = trim($params->HIN);
        $email = trim($params->Email);

        // validate
        if (!$UCN || !is_numeric($UCN)) {
            $this->_setDiagnostics(1001);
        }
        /*
        if (!preg_match('#^\d+$#', $HIN)) {
            $this->_setDiagnostics(1002);
        }
        */
        if ($email && !Validator::validEmail($email)) {
            $this->_setDiagnostics(1016);
        }

        // we have errors occurred so exit and return the errors
        if (!empty($this->diag)) {
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        }

        // get HIN
        $hin_id = $this->_getHIN($UCN, $HIN);
        if (!$hin_id) {
            //invalid UCN/HIN provided
            $this->_setDiagnostics(1001);
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        }

        //validate hour and date
        if (!preg_match('#^(\d{2})\:(\d{2})$#', trim($params->hourStart), $matches)) {
            $this->_setDiagnostics(1005);
        } else {
            $h = (int) preg_replace('#^0*(\d*)#', '\1', $matches[1]);
            $m = (int) preg_replace('#^0*(\d*)#', '\1', $matches[2]);
            if ($h >23 || $m > 59) {
                $this->_setDiagnostics(1005);
            }
        }
        if (!preg_match('#^(\d{2})\:(\d{2})$#', trim($params->hourEnd), $matches)) {
            $this->_setDiagnostics(1006);
        } else {
            $h = (int) preg_replace('#^0*(\d*)#', '\1', $matches[1]);
            $m = (int) preg_replace('#^0*(\d*)#', '\1', $matches[2]);
            if ($h >23 || $m > 59) {
                $this->_setDiagnostics(1006);
            }
        }
        if (!preg_match('#^(\d{4})-(\d{2})-(\d{2})$#', trim($params->date), $matches) ||
            !checkdate($matches[2], $matches[3], $matches[1])) {
            $this->_setDiagnostics(1007);
        }
        // we have errors occurred so exit and return the errors
        if (!empty($this->diag)) {
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        }

        //check if the date and hours are an valid intervals
        $schedule = $this->_getMedicalExamSchedule($hin_id);
        if (empty($schedule)) {
            //the result is true, there is no schedule yet
            $this->_setDiagnostics(1004);
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        } elseif (!isset($schedule[$params->date][$params->hourStart]) ||
                   $schedule[$params->date][$params->hourStart]['end_hour'] != $params->hourEnd){
            //the date and start/end hours are not correct
            $this->_setDiagnostics(1008);
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        } elseif ($schedule[$params->date][$params->hourStart]['status'] == 'reserved' && !$params->remove) {
            //the interval already reserved by another user
            $this->_setDiagnostics(1009);
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        } elseif ($schedule[$params->date][$params->hourStart]['status'] == 'mine' && !$params->remove) {
            //the interval already reserved by the user but no remove flag sent
            $this->_setDiagnostics(1010);
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        } elseif ($schedule[$params->date][$params->hourStart]['status'] != 'mine' && $params->remove) {
            //remove flag sent but the interval is not 'mine'
            $this->_setDiagnostics(1011);
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        } elseif ($schedule[$params->date][$params->hourStart]['restrictions']) {
            $restrictions = $schedule[$params->date][$params->hourStart]['restrictions'];
            if (strpos($restrictions, 'past') !== false) {
                // the date is past
                $this->_setDiagnostics(1017);
            }
            if (strpos($restrictions, 'exceeded') !== false) {
                // the corrections limit has been exceeded
                $this->_setDiagnostics(1018);
            }
            if (strpos($restrictions, 'expired') !== false) {
                // the corrections window has expired
                $this->_setDiagnostics(1019);
            }
            return array('nzResult' => false, 'nzDiag' => $this->diag);
        }

        //get details about the HIN (name, id, etc.)
        $hin_data = $this->_getMedicalExamData($hin_id);

        //get the schedule document
        $filters = array('where' => array('d.id = ' . $this->_scheduleID),
                         'sanitize' => true,
                         'skip_permissions_check' => true);
        $schedule_doc = Documents::searchOne($this->registry, $filters);
        $schedule_doc->getVars();
        $schedule_vars = $schedule_doc->get('vars');
        $schedule_assoc_vars = $schedule_doc->getAssocVars();

        //start transaction
        $this->registry['db']->StartTrans();

        //array that contains the schedule row numbers to be locked
        //num => count of reservations per this row
        $lock_rows = array();

        //get the email from nomenclature
        $hin_email = $this->registry['db']->GetOne('SELECT value FROM  ' . DB_TABLE_NOMENCLATURES_CSTM . ' WHERE var_id=902 AND model_id=' . $hin_id);

        if ($email && $email != $hin_email) {
            //store the email in the HIN (nomenclature)
            $old_nom = Nomenclatures::searchOne($this->registry, array('where' => array('n.id = ' . $hin_id)));
            $this->registry->set('get_old_vars', true, true);
            $old_nom->getVars();

            //ToDo define var id as setting
            $query = 'UPDATE ' . DB_TABLE_NOMENCLATURES_CSTM . ' SET value="'. General::slashesEscape($email) . '" WHERE var_id=902 AND model_id=' . $hin_id;
            $this->registry['db']->Execute($query);

            $new_nom = Nomenclatures::searchOne($this->registry, array('where' => array('n.id = ' . $hin_id)));
            $new_nom->getVars();
            Nomenclatures_History::saveData($this->registry, array('model' => $new_nom, 'action_type' => 'edit', 'new_model' => $new_nom, 'old_model' => $old_nom));
        } elseif (!$email) {
            $email = $hin_email;
        }

        if (!$this->_reservationID) {
            //create reservation document
            //ToDo: define type as setting
            $type_id = 15;
            $filters = array('where' => array('dt.id = ' . $type_id,
                             'dt.active = 1',
                             'dt.inheritance = 0'),
                             'sanitize' => true);
            $type = Documents_Types::searchOne($this->registry, $filters);

            //create empty document model
            $document = new Document($this->registry);

            $old_document = new Document($this->registry, array('type' => $type_id));
            $this->registry->set('get_old_vars', true, true);
            $old_document->getVars();
            $old_document->sanitize();

            //set the variables in the request as POST
            $inherit_from_schedule = array(
                'hospital_id', 'hospital_name', 'hospital_address',
                'policy_id', 'policy_name', 'policy_from', 'policy_to',
                'policy_insurer_id', 'policy_insurer_name',
                'aon_employee_id', 'aon_employee_name', 'aon_employee_gsm', 'aon_employee_email',
                'plat_corr_days'
            );
            foreach($inherit_from_schedule as $var) {
                $this->registry['request']->set($var, $schedule_assoc_vars[$var]['value'], 'all', true);
            }

            //set the variables in the grouping table (the reservation itself)
            $this->registry['request']->set('health_insurance_number', array($HIN), 'all', true);
            $this->registry['request']->set('insured_person_egn', array($UCN), 'all', true);
            $this->registry['request']->set('insured_person_id', array($hin_id), 'all', true);
            $this->registry['request']->set('insured_person_name', array($hin_data['insured_person_name']), 'all', true);
            $this->registry['request']->set('reserved_date', array($params->date), 'all', true);
            $this->registry['request']->set('reserved_hour', array($params->hourStart), 'all', true);
            $this->registry['request']->set('reserved_hour_to', array($params->hourEnd), 'all', true);
            //it is the first save of the document, insert the limit as the field contains the remaining corrections
            // IMPORTANT: insured_person_corr_nums is just for information of how many corrections has left
            //            the real stats of hin's corrections number is in the second grouping table
            $this->registry['request']->set('insured_person_corr_nums', array($this->_correctionsLimit), 'all', true);
            $this->registry['request']->set('insured_person_email', array($email), 'all', true);

            //store the stats of the corrections
            // IMPORTANT: 'stats_corrections' stores the number of corrections made (deletions are corrections too!),
            //            not the remaining corrections left
            //            When adding reservation for the first the correction number is 0!
            $this->registry['request']->set('stats_hin_id', array($hin_id), 'all', true);
            $this->registry['request']->set('stats_corrections', array(0), 'all', true);

            //define the lock rows
            $lock_rows[$schedule[$params->date][$params->hourStart]['num']] = 1;

            //set default values from the document type
            $document->set('type', $type_id, true);
            $document->set('type_name', $type->get('name'), true);
            $document->set('generate_system_task', $type->get('generate_system_task'), true);
            $document->set('direction', $type->get('direction'), true);
            $document->set('name', $type->get('default_name'), true);
            $document->set('department', $type->getDefaultDepartment(), true);
            $document->set('group', $type->getDefaultGroup(), true);
            $document->set('media', $type->get('default_media'), true);
            $document->set('media_name', $type->get('default_media_name'), true);
            //set customer as in the schedule
            $document->set('customer', $schedule_doc->get('customer'), true);
            $document->set('branch', $schedule_doc->get('branch'), true);
            $document->set('contact_person', $schedule_doc->get('contact_person'), true);
            //set current date
            $document->set('date', General::strftime('%Y-%m-%d'), true);

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $document->set('plain_vars', null, true);
            $this->registry->set('edit_all', true, true);
            $document->getVars();

            if ($document->save()) {
                //store the relatives
                $query = 'INSERT INTO documents_relatives (parent_id, link_to, link_to_model_name, origin) VALUES ' . "\n".
                         '('.$document->get('id').', '.$schedule_doc->get('id').', "Document", "inherited")';
                $this->registry['db']->Execute($query);

                $filters = array('where' => array('d.id = ' . $document->get('id')),
                    'model_lang' => $document->get('model_lang'),
                    'skip_assignments' => true,
                    'skip_permissions_check' => true);
                $new_document = Documents::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_document->getVars();

                //default assignments
                $new_document->defaultAssign();

                $audit_parent = Documents_History::saveData($this->registry,
                    array('model' => $document,
                          'action_type' => 'add',
                          'new_model' => $new_document,
                          'old_model' => $old_document));
                $nzResult = true;
            } else {
                $this->_setDiagnostics(1012);
                $errors = $this->registry['messages']->getErrors();
                foreach($errors as $err_field => $err_text) {
                    $this->_setDiagnostics($err_text, null, $err_field);
                }
                $nzResult = false;
            }
        } else {
            //the reservation already exists
            //just edit the group table
            $filters = array('where' => array('d.id = ' . $this->_reservationID),
                             'skip_permissions_check' => true);
            $document = Documents::searchOne($this->registry, $filters);
            if (!$document) {
                //reservation doc not found
                $this->registry['db']->FailTrans();
                $this->registry['db']->CompleteTrans();
                $this->_setDiagnostics(1014, $this->_reservationID);
                return array('nzResult' => false, 'nzDiag' => $this->diag);
            }
            //get variables
            $this->registry->set('get_old_vars', true, true);
            $document->getVars();
            $vars = $document->get('vars');
            $assoc_vars = $document->getAssocVars();

            $old_document = clone $document;

            //now try to change the variables
            //get the rows in the table that contain the HIN
            $rows_to_delete = array_keys($assoc_vars['insured_person_id']['value'], $hin_id);
            //IMPORTANT: there should be only one reservation per HIN
            if (count($rows_to_delete) == 1 && !$params->remove) {
                //update the row (in fact get the index of the ro to be updated)
                $idx = $rows_to_delete[0];
            } else {
                //delete rows for this HIN
                foreach ($rows_to_delete as $idx) {
                    foreach($vars as $vidx => $var) {
                        switch($var['name']) {
                        case 'health_insurance_number':  unset($vars[$vidx]['value'][$idx]);break;
                        case 'insured_person_egn':       unset($vars[$vidx]['value'][$idx]);break;
                        case 'insured_person_id':        unset($vars[$vidx]['value'][$idx]);break;
                        case 'insured_person_name':      unset($vars[$vidx]['value'][$idx]);break;
                        case 'reserved_date':            unset($vars[$vidx]['value'][$idx]);break;
                        case 'reserved_hour':            unset($vars[$vidx]['value'][$idx]);break;
                        case 'reserved_hour_to':         unset($vars[$vidx]['value'][$idx]);break;
                        case 'insured_person_corr_nums': unset($vars[$vidx]['value'][$idx]);break;
                        case 'insured_person_email':     $email = ($email) ? $email : $vars[$vidx]['value'][$idx];
                                                         unset($vars[$vidx]['value'][$idx]);break;
                        }
                    }
                }
                //add new row
                $idx = max(array_keys($assoc_vars['health_insurance_number']['value']))+1;
            }

            //increment the number of the corrections made
            //IMPORTANT: do increment ONLY if there is a CHANGE in the reservation (date or time)
            //           or if the reservation is deleted!
            //           DO not increment when adding reservation for the first time!
            if ($this->_reservationDate ||
                (!$this->_reservationDate && $this->_reservationCorrections> 0) &&
                ($params->date != $this->_reservationDate ||
                $params->hourStart != $this->_reservationStartHour || $params->remove)) {
                $this->_reservationCorrections++;
            }

            if (!$params->remove) {
                //update the row data
                foreach($vars as $vidx => $var) {
                    switch($var['name']) {
                        case 'health_insurance_number':  $vars[$vidx]['value'][$idx] = $HIN;break;
                        case 'insured_person_egn':       $vars[$vidx]['value'][$idx] = $UCN;break;
                        case 'insured_person_id':        $vars[$vidx]['value'][$idx] = $hin_id;break;
                        case 'insured_person_name':      $vars[$vidx]['value'][$idx] = $hin_data['insured_person_name'];break;
                        case 'reserved_date':            $vars[$vidx]['value'][$idx] = $params->date;break;
                        case 'reserved_hour':            $vars[$vidx]['value'][$idx] = $params->hourStart;break;
                        case 'reserved_hour_to':         $vars[$vidx]['value'][$idx] = $params->hourEnd;break;
                        // IMPORTANT: store the number of remaining corrections
                        case 'insured_person_corr_nums': $vars[$vidx]['value'][$idx] = $this->_correctionsLimit - $this->_reservationCorrections;break;
                        case 'insured_person_email':     $vars[$vidx]['value'][$idx] = $email;break;
                    }
                }
            }

            $rows_stats = array_keys($assoc_vars['stats_hin_id']['value'], $hin_id);
            //IMPORTANT: there should be only one stats row per HIN
            if (count($rows_stats) == 1) {
                //update the stat row (in fact get the index of the ro to be updated)
                $idx = $rows_stats[0];
            } else {
                if (count($rows_stats) > 1) {
                    //delete stats rows for this HIN (should not go inside this if)
                    foreach ($rows_stats as $idx) {
                        foreach ($vars as $vidx => $var) {
                            switch ($var['name']) {
                                case 'stats_hin_id':      unset($vars[$vidx]['value'][$idx]); break;
                                case 'stats_corrections': unset($vars[$vidx]['value'][$idx]); break;
                            }
                        }
                    }
                }
                //add new row
                $idx = max(array_keys($assoc_vars['stats_hin_id']['value']))+1;
            }
            //update the stats row data
            foreach($vars as $vidx => $var) {
                switch($var['name']) {
                    case 'stats_hin_id':      $vars[$vidx]['value'][$idx] = $hin_id;break;
                    case 'stats_corrections': $vars[$vidx]['value'][$idx] = intval($this->_reservationCorrections);break;
                }
            }
            $document->set('vars', $vars, true);
            $this->registry->set('edit_all', true, true);

            //define the lock rows
            //get the indexes of the key variables: reserved_date, reserved_hour
            $reserved_date = '';
            $reserved_hour = '';
            foreach($vars as $vidx => $var) {
                if ($var['name'] == 'reserved_date') {
                    $reserved_date = $vars[$vidx]['value'];
                } elseif ($var['name'] == 'reserved_hour') {
                    $reserved_hour = $vars[$vidx]['value'];
                }
            }
            foreach($reserved_date as $n => $val) {
                $num = $schedule[$val][$reserved_hour[$n]]['num'];
                if (array_key_exists($num, $lock_rows)) {
                    $lock_rows[$num]++;
                } else {
                    $lock_rows[$num] = 1;
                }
            }

            if ($document->save()) {
                $filters = array('where' => array('d.id = ' . $document->get('id')),
                    'model_lang' => $document->get('model_lang'),
                    'skip_assignments' => true,
                    'skip_permissions_check' => true);
                $new_document = Documents::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_document->getVars();

                $audit_parent = Documents_History::saveData($this->registry,
                    array('model' => $document,
                        'action_type' => 'add',
                        'new_model' => $new_document,
                        'old_model' => $old_document));
                $nzResult = true;
            } else {
                $this->_setDiagnostics(1013);
                $errors = $this->registry['messages']->getErrors();
                foreach($errors as $err_field => $err_text) {
                    $this->_setDiagnostics($err_text, null, $err_field);
                }
                $nzResult = false;
            }
        }

        //UPDATE THE LOCK OF THE SCHEDULE
        foreach ($schedule_vars as $vidx => $var) {
            if ($var['name'] == 'plan_locked') {
                foreach($var['value'] as $num => $val) {
                    $schedule_vars[$vidx]['value'][$num] = $lock_rows[$num];
                }
            }
        }
        $schedule_doc->set('vars', $schedule_vars, true);
        $this->registry->set('edit_all', true, true);

        $schedule_doc->unsanitize();
        if ($schedule_doc->save()) {
            //do not save history
            $nzResult = true;
        } else {
            $this->_setDiagnostics(1015);
            $errors = $this->registry['messages']->getErrors();
            foreach($errors as $err_field => $err_text) {
                $this->_setDiagnostics($err_text, null, $err_field);
            }
            $nzResult = false;
        }

        //end transaction
        if ($nzResult && $this->registry['db']->HasFailedTrans()) {
            //the result is false, something went wrong within the transaction
            $this->_setDiagnostics(1100, array(), $this->registry['db']->ErrorMsg());
        } elseif ($nzResult) {
            if ($email && !$params->remove) {
                //now send email to the user
                //get email template
                // ToDo: set email template id as setting
                $filters = array('where' => array('e.id = 1001'), 'sanitize' => true);
                $mail = Emails::searchOne($this->registry, $filters);
                if (!empty($mail)) {
                    //IMPORTANT: the email notifications are probably OFF for this installation, but this email SHOULD BE SEND!
                    $do_not_send = $this->registry['config']->getParam('emails', 'do_not_send');
                    //turn the notifications on the send the email
                    $this->registry['config']->setInt('emails', 'do_not_send', 0, 0);

                    //set additional placeholders
                    $document->set('additionalEmailVars', array(
                        'hin_name' => $hin_data['insured_person_name'],
                        'reservation_date' => General::strftime('%d.%m.%Y (%A)', $params->date),
                        'reservation_start_hour' => $params->hourStart,
                        'reservation_corrections' => $this->_correctionsLimit - $this->_reservationCorrections,
                        'reservation_window_days' => $this->_correctionsWindowDays,
                    ), true);

                    $document->set('body', $mail->get('body'), true);
                    $document->set('email_subject', $mail->get('subject'), true);
                    $document->set('email_template', $mail->get('id'), true);
                    $document->set('add_signature', true, true);
                    $document->set('customer_email', $hin_data['insured_person_name'] . ' <'. $email . '>', true);

                    if ($document->sanitized) {
                        $document->unsanitize();
                        $sa = true;
                    }
                    $result = $document->sendAsMail();
                    if ($sa) {
                        $document->sanitize();
                    }

                    //restore the setting
                    $this->registry['config']->setInt('emails', 'do_not_send', $do_not_send, 0);
                }


            }

            //the result is OK
            $this->_setDiagnostics(1000);
        }

        $this->registry['db']->CompleteTrans();


        // Return the result and the diagnostic data
        return array('nzResult' => $nzResult, 'nzDiag' => $this->diag);
    }

    /**
     * Get nomenclature of type 8 (Health Insurance Number)
     *
     * @param string $UCN - EGN, Unique citizenship number
     * @param string $HIN - HIN, health insurance number
     * @return int $hin_id - the id of the nomenclature storing the HIN
     */
    private function _getHIN($UCN, $HIN) {
        // get nom hin id by UCN and HIN
        //Todo: define variables names/IDs as settings
        $query = 'SELECT n.id' . "\n" .
            'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
            //3016: policy_id (document type 14)
            'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1'  . "\n" .
            ' ON d.type=14 AND d.active=1 AND d.deleted_by=0 AND d.status="opened" AND
                 dc1.model_id=d.id AND dc1.var_id=3016 AND dc1.num=1 AND dc1.lang=""' . "\n" .
            //909: policy_id (nom type 8)
            'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc3' . "\n" .
            '  ON nc3.var_id=909 AND nc3.num=1 AND nc3.lang="" AND dc1.value=nc3.value' . "\n" .
            'JOIN ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
            '  ON n.id=nc3.model_id AND n.active AND n.deleted_by=0 AND n.type=8' . "\n" .
            //917: EGN (nom type 8)
            'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc1' . "\n" .
            '  ON n.id=nc1.model_id AND nc1.var_id=917 AND TRIM(nc1.value)="' . General::slashesEscape($UCN) . '"' . "\n" .
            '     AND nc1.num=1 AND nc1.lang=""' . "\n" .
            //901: health_insurance_number (nom type 8)
            'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc2' . "\n" .
            '  ON n.id=nc2.model_id AND nc2.var_id=901 AND TRIM(nc2.value)="' . General::slashesEscape($HIN) . '"' . "\n" .
            '     AND nc2.num=1 AND nc2.lang=""';

        return $this->registry['db']->GetOne($query);
    }

    /**
     * Get medical exam schedule from the grouping table in document Schedule (type 14)
     *
     * @param string $hin_id - the nomenclature id (type 8)
     * @return array $schedule - array consisting of schedule intervals
     */
    private function _getMedicalExamSchedule($hin_id) {
        // get data from schedule document
        //Todo: define variables names/IDs as settings
        $query = 'SELECT DISTINCT(d.id) as schedule_id,' . "\n" .
                 '       dc2.value as date_from,' . "\n" .
                 '       dc3.value as date_to,' . "\n" .
                 '       dc4.value as start_hour,' . "\n" .
                 '       dc5.value as end_hour,' . "\n" .
                 '       dc6.value as `interval`,' . "\n" .
                 '       dc7.value as capacity,' . "\n" .
                 '       dc8.value as `lock`,' . "\n" .
                 '       dc8.num' . "\n" .
                 'FROM ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
                 //909: policy_id (nom type 8)
                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc3' . "\n" .
                 '  ON n.id=nc3.model_id AND nc3.var_id=909 AND ' . "\n" .
                 '     n.active AND ' . "\n" .
                 '     n.deleted_by=0 AND n.id=' . $hin_id . "\n" .
                 //3016: policy_id (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                 '  ON dc1.var_id=3016 AND dc1.value=nc3.value' . "\n" .
                 //documents (type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                 '  ON dc1.model_id=d.id AND d.active=1 AND d.deleted_by=0 AND d.type=14 AND d.status="opened"' . "\n" .
                 //3009: plan_date_from (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc2' . "\n" .
                 '  ON dc2.var_id=3009 AND dc1.model_id=dc2.model_id' . "\n" .
                 //3010: plan_date_from (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc3' . "\n" .
                 '  ON dc3.var_id=3010 AND dc1.model_id=dc3.model_id AND dc2.num=dc3.num' . "\n" .
                 //3011: plan_start_hour (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc4' . "\n" .
                 '  ON dc4.var_id=3011 AND dc1.model_id=dc4.model_id AND dc2.num=dc4.num' . "\n" .
                 //3012: plan_end_hour (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc5' . "\n" .
                 '  ON dc5.var_id=3012 AND dc1.model_id=dc5.model_id AND dc2.num=dc5.num' . "\n" .
                 //3013: plan_interval (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc6' . "\n" .
                 '  ON dc6.var_id=3013 AND dc1.model_id=dc6.model_id AND dc2.num=dc6.num' . "\n" .
                 //3014: plan_capacity (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc7' . "\n" .
                 '  ON dc7.var_id=3014 AND dc1.model_id=dc7.model_id AND dc2.num=dc7.num' . "\n" .
                 //3015: plan_lock (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc8' . "\n" .
                 '  ON dc8.var_id=3015 AND dc1.model_id=dc8.model_id AND dc2.num=dc8.num' . "\n" .
                 //3023: insured_person_corr_limit (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc9' . "\n" .
                 '  ON dc9.var_id=3023 AND dc9.model_id=d.id' . "\n" .
                 //3024: plat_corr_days (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc10' . "\n" .
                 '  ON dc10.var_id=3024 AND dc10.model_id=d.id' . "\n" .
                 //sort by start date and start hour
                 'ORDER BY dc2.value, dc4.value';
        $schedule_rows = $this->registry['db']->GetAll($query);

        $reservations = array();
        $this->_reservationID = '';
        if (!empty($schedule_rows)) {
            //schedule id
            $this->_scheduleID = '';
            if (!empty($schedule_rows)) {
                $this->_scheduleID = $schedule_rows[0]['schedule_id'];
            }

            //get settings corrections limit and corrections window (in days)
            $query = 'SELECT dc1.value as corr_limit,' . "\n" .
                     '       dc2.value as corr_days' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                     //3023: insured_person_corr_limit (doc type 14)
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                     '  ON d.id=' . $this->_scheduleID . ' AND dc1.var_id=3023 AND dc1.model_id=d.id' . "\n" .
                      //3024: plat_corr_days (doc type 14)
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc2' . "\n" .
                     '  ON dc2.var_id=3024 AND dc2.model_id=d.id';
            $settings = $this->registry['db']->GetRow($query);
            //default values are hard-coded
            $this->_correctionsLimit = 2;
            if (!empty($settings['corr_limit'])) {
                $this->_correctionsLimit = $settings['corr_limit'];
            }
            $this->_correctionsWindowDays = 5;
            if (!empty($settings['corr_days'])) {
                $this->_correctionsWindowDays = $settings['corr_days'];
            }

            //get data from reservation document
            //IMPORTANT: there should be only one active, not deleted reservation document
            $query = 'SELECT d.id' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' as dr' . "\n" .
                     //documents (type 15)
                     'JOIN ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                     '  ON dr.link_to=' . $this->_scheduleID . ' AND ' . "\n" .
                     '     dr.link_to_model_name="Document" AND ' . "\n" .
                     '     dr.parent_model_name="Document" AND ' . "\n" .
                     '     dr.parent_id=d.id AND ' . "\n" .
                     '     d.active=1 AND d.deleted_by=0 AND d.type=15';
            $this->_reservationID = $this->registry['db']->GetOne($query);
            if ($this->_reservationID) {
                $query = 'SELECT dc1.value as hin_id,' . "\n" .
                         '       dc2.value as date,' . "\n" .
                         '       dc3.value as start_hour,' . "\n" .
                         '       dc4.value as end_hour,' . "\n" .
                         '       dc6.value as corrections' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                         //3109: insured_person_id (doc type 15)
                         //3113: reserved_date (doc type 15)
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc2' . "\n" .
                         '  ON dc1.model_id=' . $this->_reservationID . ' AND dc1.var_id=3109 AND ' . "\n" .
                         '     dc2.var_id=3113 AND dc2.model_id=dc1.model_id AND dc1.num=dc2.num' . "\n" .
                         //3114: reserved_hour (doc type 15)
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc3' . "\n" .
                         '  ON dc3.var_id=3114 AND dc3.model_id=dc1.model_id AND dc1.num=dc3.num' . "\n" .
                         //3117: reserved_hour_to (doc type 15)
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc4' . "\n" .
                         '  ON dc4.var_id=3117 AND dc4.model_id=dc1.model_id AND dc1.num=dc4.num' . "\n" .
                         //3126: stats_hin_id (doc type 15)
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc5' . "\n" .
                         '  ON dc5.var_id=3126 AND dc5.value=dc1.value AND dc5.model_id=dc1.model_id' . "\n" .
                         //3127: stats_corrections (doc type 15)
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc6' . "\n" .
                         '  ON dc6.var_id=3127 AND dc6.model_id=dc5.model_id AND dc6.num=dc5.num';
                         //no need to sort the rows
                $reservation_rows = $this->registry['db']->GetAll($query);
                foreach ($reservation_rows as $row) {
                    $reservations[$row['date'] . ' ' . $row['start_hour']][] = $row['hin_id'];
                    if ($row['hin_id'] == $hin_id) {
                        // the field contains the remaining corrections
                        $this->_reservationDate = $row['date'];
                        $this->_reservationStartHour = $row['start_hour'];
                        $this->_reservationEndHour = $row['end_hour'];
                    }
                }

                $query = 'SELECT dc2.value as corrections' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                         //3126: stats_hin_id (doc type 15)
                         //3127: stats_corrections (doc type 15)
                         'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc2' . "\n" .
                         '  ON dc1.model_id=' . $this->_reservationID . ' AND dc1.var_id=3126 AND ' . "\n" .
                         '     dc2.var_id=3127 AND dc2.model_id=dc1.model_id AND dc1.num=dc2.num AND dc1.value=' . $hin_id;
                $this->_reservationCorrections = intval($this->registry['db']->GetOne($query));
            }
        }

        /**
         * Restrictions could be
         * past (the date is past - not available),
         * expired (the corrections window has expired - not available),
         * exceeded (the corrections limit has been exceeded - not available)
         */
        $default_restrictions = array();
        if ($this->_reservationDate) {
            if ($this->_reservationCorrections >= $this->_correctionsLimit) {
                $default_restrictions[] = 'exceeded';
            }
            // check for expired corrections window period
            // check for exceeded corrections limit
            $this->_correctionsWindowDate = new DateTime($this->_reservationDate);
            $this->_correctionsWindowDate->sub(new DateInterval('P' . $this->_correctionsWindowDays . 'D'));
            $this->_correctionsWindowDate = $this->_correctionsWindowDate->format('Y-m-d');
            if (General::strftime('%Y-%m-%d') >= $this->_correctionsWindowDate) {
                $default_restrictions[] = 'expired';
            }
        }

        $schedule = array();
        foreach($schedule_rows as $row) {
            $date = $row['date_from'];
            do {
                $start_hour = $row['start_hour'];
                if (Calendars_Calendar::isWorkingDay($this->registry, $date)) {
                    do {
                        //add interval minutes to start hour
                        $d = new DateTime('1970-01-01 ' . $start_hour . ':00');
                        $d->add(new DateInterval('PT' . $row['interval'] . 'M'));
                        $end_hour = $d->format('H:i');
                        unset($d);

                        if ($this->_reservationDate && $this->_reservationStartHour &&
                            $this->_reservationDate == $date && $this->_reservationStartHour == $start_hour
                        ) {
                            $this->_reservationEndHour = $end_hour;
                        }

                        /**
                         * Status can be:
                         *   available,
                         *   reserved (reserved by someone else),
                         *   mine (reserved by current user),
                         *   past (the date is past -not available),
                         *   expired (the corrections window has expired - not available),
                         *   exceeded (the corrections limit has been exceeded - not available)
                         */
                        $status = 'available';
                        if (isset($reservations[$date . ' ' . $start_hour])) {
                            if (in_array($hin_id, $reservations[$date . ' ' . $start_hour])) {
                                $status = 'mine';
                            } elseif (count($reservations[$date . ' ' . $start_hour]) >= $row['capacity']) {
                                $status = 'reserved';
                            }
                        }
                        //restrictions
                        $restrictions = $default_restrictions;
                        if ($date < General::strftime('%Y-%m-%d')) {
                            // the past dates should not be accessible
                            $restrictions[] = 'past';
                        }


                        $schedule[$date][$start_hour] = array(
                            'start_hour' => $start_hour,
                            'end_hour' => $end_hour,
                            'status' => $status,
                            'restrictions' => implode(' ', $restrictions),
                            // num is used for the lock/unlock mechanism
                            'num' => $row['num'],
                        );

                        //update next iteration start hour
                        $start_hour = $end_hour;
                    } while ($end_hour < $row['end_hour']);
                }

                //increment the date add 1 dat
                $d = new DateTime($date);
                $d->add(new DateInterval('P1D'));
                $date = $d->format('Y-m-d');
            } while($date <= $row['date_to']);
        }

        return $schedule;
    }

    /**
     * Get medical exam data from document Schedule (type 14)
     *
     * @param string $hin_id - the nomenclature id (type 8)
     * @return array - the result and the diagnostic data
     */
    private function _getMedicalExamData($hin_id) {
        // get data from schedule document
        //Todo: define variables names/IDs as settings
        $query = 'SELECT ni1.name as insured_person_name, ni2.name as hospital, nc5.value as hospital_address,' . "\n" .
                 '       dc3.value as contact_id, dc4.value as contact_name, dc5.value as contact_phone, dc6.value as contact_email' . "\n" .
                 'FROM ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
                 //909: policy_id (nom type 8)
                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc3' . "\n" .
                 '  ON n.active AND ' . "\n" .
                 '     n.deleted_by=0 AND n.id=' . $hin_id . "\n" .
                 '     AND n.id=nc3.model_id AND nc3.var_id=909' . "\n" .
                 //916: insured_person_id (nom type 8)
                  'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc4' . "\n" .
                 '  ON n.id=nc4.model_id AND nc4.var_id=916' . "\n" .
                 //name: (nom type 37)
                 'JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as ni1' . "\n" .
                 '  ON nc4.value=ni1.parent_id AND ni1.lang="' . $this->registry['lang'] . '"' . "\n" .
                 //3016: policy_id (doc type 14)
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                 '  ON dc1.var_id=3016 AND dc1.value=nc3.value' . "\n" .
                 //documents (type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                 '  ON dc1.model_id=d.id AND d.active=1 AND d.deleted_by=0 AND d.status="opened"' . "\n" .
                 //3005: hospital_id (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc2' . "\n" .
                 '  ON dc2.var_id=3005 AND dc1.model_id=dc2.model_id' . "\n" .
                 //3019: aon_employee_id (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc3' . "\n" .
                 '  ON dc3.var_id=3019 AND dc1.model_id=dc3.model_id' . "\n" .
                 //3020: aon_employee_name (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc4' . "\n" .
                 '  ON dc4.var_id=3020 AND dc1.model_id=dc4.model_id' . "\n" .
                 //3021: aon_employee_gsm (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc5' . "\n" .
                 '  ON dc5.var_id=3021 AND dc1.model_id=dc5.model_id' . "\n" .
                 //3022: aon_employee_email (doc type 14)
                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc6' . "\n" .
                 '  ON dc6.var_id=3022 AND dc1.model_id=dc6.model_id' . "\n" .
                 //name: (nom type 39)
                 'JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as ni2' . "\n" .
                 '  ON ni2.parent_id=dc2.value  AND ni2.lang="' . $this->registry['lang'] . '"' . "\n" .
                 //3803: hospital_address (nom type 39)
                 'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc5' . "\n" .
                 '  ON dc2.value=nc5.model_id AND nc5.var_id=3803' . "\n" .
                 'ORDER BY d.date DESC, d.added DESC';
        return $this->registry['db']->GetRow($query);
    }

}

?>
