<?php
DEFINE('MERCHANTID', '0001403');
DEFINE('STAN', '591535');
DEFINE('AID', '700020');
//DEFINE('CHECKSUM_SECRET', 'cUAMSp39r6B2pPh9PjSh5Rd6gYvDVudG'); //sandbox secret
DEFINE('CHECKSUM_SECRET', '********************************');   //production secret
//DEFINE('PAYMENT_SERVER_PAY_INIT_URL', 'https://nzoom.creditins.bg/pay/init');
//DEFINE('PAYMENT_SERVER_PAY_CONFIRM_URL', 'https://nzoom.creditins.bg/pay/confirm');
DEFINE('PAYMENT_SERVER_PAY_INIT_URL', 'https://tt.n-zoom.com/creditins/pay/init');
DEFINE('PAYMENT_SERVER_PAY_CONFIRM_URL', 'https://tt.n-zoom.com/creditins/pay/confirm');
DEFINE('MOCKUP', false);?>
<html>
 <head>
  <title>ePay, EasyPay Demo</title>
  <meta name="Author" content="<EMAIL>">
  <meta name="Keywords" content="">
  <meta name="Description" content="">
  <style type="text/css">
    * {
        font-family: Tahoma;
    }
    fieldset {
        width: 800px;
    }
    input[type="text"], select {
        width: 600px;
        display: block;
        margin: 3px 0;
    }
    textarea {
        width: 600px;
        display: block;
        margin: 3px 0;
        font-size: 10px;
    }
    #init_response {
        height: 150px;
    }
  </style>
  <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
  <script type="text/javascript">
      function manageInvoices() {
        if ($('input:checked').length == 0) {
           var all_invoices_total = 0;
           $('input[type="checkbox"]').each(function() {
                all_invoices_total += parseInt($(this).attr('data-amount'));
           });
           $('#CONFIRM_TOTAL').val(all_invoices_total);
        } else {
           var invoices_total = 0;
           $('input:checked').each(function() {
                invoices_total += parseInt($(this).attr('data-amount'));
           });
           $('#CONFIRM_TOTAL').val(invoices_total);
        }
      }
      $( document ).ready(function() {
          $( "#INIT_TYPE" )
            .change(function() {
              if ($(this).val() != 'DEPOSIT') {
                    $('label[for="INIT_TOTAL"]').hide();
                    $('#INIT_TOTAL').hide();
              } else {
                    $('label[for="INIT_TOTAL"]').show();
                    $('#INIT_TOTAL').show();
              }
           })
          .change();

          $( "#CONFIRM_TYPE" )
            .change(function() {
              if ($(this).val() == 'PARTIAL') {
                    $('#CONFIRM_TOTAL').attr("readonly", false);
                    $('.invoices').hide();
              } else {
                    if ($(this).val() == 'BILLING') {
                        manageInvoices();
                    }
                    $('#CONFIRM_TOTAL').attr("readonly", true);
                    $('.invoices').show();
              }
           })
          .change();
           
           $('input[type="checkbox"]').click(manageInvoices);

           if ($( "#CONFIRM_TYPE" ).val() == 'BILLING') {
               manageInvoices();
           }

           if ($( "#INIT_RESPONSE" )) {
               var json_str = $( "#INIT_RESPONSE" ).val();
               var json_obj = JSON.parse(json_str);
               $( "#INIT_RESPONSE" ).val(JSON.stringify(json_obj, undefined, 4));
           }
      });

  </script>
 </head>

 <body>

<?php

if ($_POST) {
    //trace($_POST);
    foreach($_POST as $key => $value) {
        if (preg_match('#^(INIT|CONFIRM)_(.*)$#', $key, $matches)) {
            $data[$matches[1]][$matches[2]] = $value;
        } else {
            $data[$key] = $value;
        }
    }
    $command = $_POST['COMMAND'];
    switch($command) {
        case 'INIT':
            $request = array(
                'TYPE' => $data['INIT']['TYPE'],
                'IDN' => $data['INIT']['IDN'],
            );
            if ($data['INIT']['TYPE'] != 'CHECK') {
                $request['TID'] = composeTID();
                //omit the type when it is CHECK because it is default type
                $request['TYPE'] = $data['INIT']['TYPE'];
                $data['TID'] = $request['TID'];
            } else {
                $data['TID'] = '';
            }
            if ($data['INIT']['TYPE'] == 'DEPOSIT') {
                $request['TOTAL'] = $data['INIT']['TOTAL'];
            }


            $response = getResponse($command, $request);
            $data['INIT_REQUEST'] = getRequest($command, $request);
            $data['INIT_RESPONSE'] = json_decode($response, true);
            $data['INIT_RESPONSE']['JSON'] = $response;
            $data['INVOICES'] = '';
            $data['INVOICES_JSON'] = '';
            if ($data['INIT']['TYPE'] != 'DEPOSIT' && !empty($data['INIT_RESPONSE']['INVOICES'])) {
                $data['INVOICES'] = $data['INIT_RESPONSE']['INVOICES'];
                $data['INVOICES_JSON'] = json_encode($data['INIT_RESPONSE']['INVOICES']);
            }
            if (!isset($data['INIT_RESPONSE']['AMOUNT'])) {
                $data['INIT_RESPONSE']['AMOUNT'] = '';
            }
            if (!isset($data['INIT_RESPONSE']['VALIDTO'])) {
                $data['INIT_RESPONSE']['VALIDTO'] = '';
            }
            if (!isset($data['INIT_RESPONSE']['SHORTDESC'])) {
                $data['INIT_RESPONSE']['SHORTDESC'] = '';
            }
            if (!isset($data['INIT_RESPONSE']['LONGDESC'])) {
                $data['INIT_RESPONSE']['LONGDESC'] = '';
            }

            if ($data['INIT']['TYPE'] != 'CHECK') {
                $data['CONFIRM']['TYPE'] = $data['INIT']['TYPE'];
            }
            $data['CONFIRM']['IDN'] = $data['INIT']['IDN'];
            $data['CONFIRM']['TOTAL'] = $data['INIT']['TOTAL'];
            break;
        case 'CONFIRM':
            $request = array(
                'IDN' => $data['CONFIRM']['IDN'],
                'TID' => $data['TID'],
                'TYPE' => $data['CONFIRM']['TYPE'],
                'TOTAL' => $data['CONFIRM']['TOTAL'],
            );
            if ($data['CONFIRM']['TYPE'] == 'BILLING' && !empty($data['CONFIRM']['INVOICES'])) {
                $request['INVOICES'] = implode(',', $data['CONFIRM']['INVOICES']);
            }
            if ($data['CONFIRM']['TYPE'] != 'DEPOSIT') {
                $request['DATE'] = date('YmdHis');
            }

            $response = getResponse($command, $request);
            $data['CONFIRM_REQUEST'] = getRequest($command, $request);
            $data['CONFIRM_RESPONSE'] = json_decode($response, true);
            $data['CONFIRM_RESPONSE']['JSON'] = $response;
            break;
    }

} else {
    //default PAY_INIT params
    $data['INIT'] = array(
        'IDN' => '',
        'TYPE' => '',
        'TOTAL' => '',
    );
}

function getStatusText($status_code) {
    switch($status_code) {
        case  '0': $label = 'Успех!'; break;
        case '00': $label = 'Успех!'; break;
        case  '1': $label = 'Невалидни потребител и/или парола!'; break;
        case  '2': $label = 'Нотификация за плащане към несъществуваща заявка за задължение!'; break;
        case  '3': $label = 'Сумата от нотификацията за плащане е различна от сумата върната при заявката за задължение!'; break;
        case  '4': $label = 'Грешка при добавяне на плащане!'; break;
        case  '5': $label = 'Грешка при добавяне на фактура!'; break;
        case  '6': $label = 'Грешка при разпределяне на плащане по фактура!'; break;
        case  '7': $label = 'Невалидна операция - депозит за всички задължения!'; break;
        case '13': $label = 'Невалидна сума'; break;
        case '14': $label = 'Невалиден номер( idn)'; break;
        case '15': $label = 'Няма достатъчно фактури, за да покрият сумата подадена през кеш терминал'; break;
        case '62': $label = 'Няма задължение'; break;
        case '80': $label = 'Заявката временно не може да бъде изпълнена'; break;
        case '93': $label = 'Невалидна CHECKSUM'; break;
        case '94': $label = 'Повторение на вече получена нотификация'; break;
        case '96': $label = 'Обща грешка'; break;
        default: $label = 'UNKNOWN'; break;
    }
    
    return $label;
}
function composeTID() {
    return date('YmdHis') . STAN . AID;
}
function calculateChecksum($data) {
    //sort the params by key
    ksort($data);
    $checksum_calculated = '';
    foreach($data as $k => $v) {
        $checksum_calculated .= $k . $v . "\n";
    }
    return hash_hmac("sha1", $checksum_calculated, CHECKSUM_SECRET);
}
function getRequest($command, $request) {
    $request['MERCHANTID'] = MERCHANTID;
    $request['CHECKSUM'] = calculateChecksum($request);
    $source = CONSTANT('PAYMENT_SERVER_PAY_' . $command . '_URL');
    $source .= '?' . http_build_query($request);
    return $source;
}
function getResponse($command, $request) {
    if (MOCKUP) {
        return getMockupResponse($command, $request);
    } else {
        $source = getRequest($command, $request);
        // DO NOT verify peer, hostname and certificate in SSL mode
        // From PHP 5.6 on these are verified by default
        // Most of the nZoom installation do not pass verification as they use self-signed certificates
        $context = stream_context_create(
            array(
                'ssl'=>array(
                    'verify_peer' => false,
                    'verify_peer_name' =>false,
                    'allow_self_signed' => true
                )
            )
        );
        return file_get_contents($source, false, $context);
    }
}
function getMockupResponse($command, $request) {
    switch($command) {
        case 'INIT':
            if ($request['TYPE'] == 'DEPOSIT') {
                $response = array(
                    'STATUS' => '00',
                    'SHORTDESC' => 'Това е някакво кратко описание за депозита',
                    'LONGDESC' => 'Това е дълго описание за депозита\nна няколко реда\nс дълъг текст най-вероятно',
                );
            } else {
                $response = array(
                    'STATUS' => '00',
                    'IDN' => $request['IDN'],
                    'SHORTDESC' => 'Това е някакво кратко описание на задължението',
                    'LONGDESC' => 'Това е дълго описание на задължението\nна няколко реда\nс дълъг текст най-вероятно',
                    'AMOUNT' => '20470',
                    'VALIDTO' => date('Ymd'),
                    'INVOICES' => array(
                        array(
                            'IDN' => $request['IDN'] . '.00045',
                            'AMOUNT' => '12470',
                            'SHORTDESC' => 'Kратко описание на фактура 00045',
                            'LONGDESC' => 'Това е дълго описание на фактура 00045\nна няколко реда\nс дълъг текст най-вероятно',
                            'VALIDTO' => date('Ymd'),
                        ),
                        array(
                            'IDN' => $request['IDN'] . '.00058',
                            'AMOUNT' => '8000',
                            'SHORTDESC' => 'Kратко описание на фактура 00058',
                            'LONGDESC' => 'Това е дълго описание на фактура 00058\nна няколко реда\nс дълъг текст най-вероятно',
                            'VALIDTO' => date('Ymd'),
                        )
                    ),
                );
            }
            break;
        case 'CONFIRM':
            $response = array(
                'STATUS' => '00'
            );
            break;
    }
    return json_encode($response);
}

function trace($value, $title = '', $backtrace = array()) {
    if (!headers_sent()) {
        header('Content-Type: text/html; charset=utf-8');
    }
    echo("<pre>\n $title\n");
    print_r($value);
    echo("\n</pre>\n");

    if (!empty($backtrace)) {
        $bt = debug_backtrace(false);
        if (!is_array($backtrace)) {
            $backtrace = array($backtrace);
        }
        if (in_array('noargs', $backtrace)) {
            foreach ($bt as $k => $v) {
                unset($bt[$k]['args']);
            }
        }
        trace($bt, 'Backtrace:', false);
    }
}

?>
  <form method="post" action="<?php echo $_SERVER['PHP_SELF'];?>">
      <fieldset>
        <legend>Проверка:</legend>

        <input type="hidden" name="COMMAND" id="COMMAND_INIT" value="INIT" />

        <label for="INIT_IDN">IDN(ЕГН)</label>
            <input type="text" name="INIT_IDN" id="INIT_IDN" value="<?php echo $data['INIT']['IDN'];?>" />

        <label for="INIT_TYPE">ДЕЙСТВИЕ</label>
            <select name="INIT_TYPE" id="INIT_TYPE">
                <option value="CHECK"<?php echo ($data['INIT']['TYPE'] == 'CHECK' ? ' selected' : '');?>>CHECK (само проверка)</option>
                <option value="BILLING"<?php echo ($data['INIT']['TYPE'] == 'BILLING' ? ' selected' : '');?>>BILLING (проверка с възможно плащане)</option>
                <option value="DEPOSIT"<?php echo ($data['INIT']['TYPE'] == 'DEPOSIT' ? ' selected' : '');?>>DEPOSIT (проверка за депозиране на сума)</option>
            </select>

        <label for="INIT_TOTAL">ДЕПОЗИТ СУМА ЗА ПРОВЕРКА</label>
            <input type="text" name="INIT_TOTAL" id="INIT_TOTAL" value="<?php echo $data['INIT']['TOTAL'];?>" />

        <input type="submit" value="OK" class="INIT" onclick="$('#COMMAND_CONFIRM').prop('disabled', true)" />
      </fieldset>
<?php if (!empty($data['INIT_RESPONSE'])) { ?>
      <fieldset>
        <legend>Резултати от проверка <?php echo $data['INIT']['TYPE'];?></legend>

        <label for="INIT_REQUEST">REQUEST</label>
            <textarea name="INIT_REQUEST" id="INIT_REQUEST" readonly><?php echo $data['INIT_REQUEST'];?></textarea>

        <label for="INIT_RESPONSE">RESPONSE (JSON)</label>
            <textarea name="INIT_RESPONSE" id="INIT_RESPONSE" readonly><?php echo $data['INIT_RESPONSE']['JSON'];?></textarea>

        <label for="INIT_STATUS:">STATUS</label>
            <input type="text" name="INIT_STATUS" id="INIT_STATUS" value="<?php echo $data['INIT_RESPONSE']['STATUS'];?>: <?php echo getStatusText($data['INIT_RESPONSE']['STATUS']);?>" disabled />

<?php if ($data['INIT']['TYPE'] != 'DEPOSIT') { ?>
        <label for="INIT_AMOUNT:">AMOUNT</label>
            <input type="text" name="INIT_AMOUNT" id="INIT_AMOUNT" value="<?php echo $data['INIT_RESPONSE']['AMOUNT'];?>" disabled />

        <label for="INIT_VALIDTO:">VALIDTO</label>
            <input type="text" name="INIT_VALIDTO" id="INIT_VALIDTO" value="<?php echo $data['INIT_RESPONSE']['VALIDTO'];?>" disabled />
<?php } ?>
        <label for="INIT_SHORTDESC">SHORTDESC</label>
            <textarea name="INIT_SHORTDESC" id="INIT_SHORTDESC" disabled><?php echo $data['INIT_RESPONSE']['SHORTDESC'];?></textarea>

        <label for="INIT_LONGDESC">LONGDESC</label>
            <textarea name="INIT_LONGDESC" id="INIT_LONGDESC" disabled><?php echo str_replace('\n', "\n", $data['INIT_RESPONSE']['LONGDESC']);?></textarea>
      </fieldset>
<?php if ($data['INIT']['TYPE'] != 'CHECK') { ?>
      <fieldset>
        <legend>Плащане</legend>

        <input type="hidden" name="TID" id="TID" value="<?php echo $data['TID'];?>" />
        <input type="hidden" name="COMMAND" id="COMMAND_CONFIRM" value="CONFIRM" />

        <label for="CONFIRM_IDN">IDN(ЕГН)</label>
            <input type="text" name="CONFIRM_IDN" id="CONFIRM_IDN" value="<?php echo $data['CONFIRM']['IDN'];?>" readonly />

        <label for="CONFIRM_TYPE">ДЕЙСТВИЕ</label>
            <select name="CONFIRM_TYPE" id="CONFIRM_TYPE">
<?php if ($data['INIT']['TYPE'] != 'DEPOSIT') { ?>
                <option value="BILLING"<?php echo ($data['CONFIRM']['TYPE'] == 'BILLING' ? ' selected' : '');?>>BILLING (плащане по фактури или пълно плащане)</option>
                <option value="PARTIAL"<?php echo ($data['CONFIRM']['TYPE'] == 'PARTIAL' ? ' selected' : '');?>>PARTIAL (частично плащане)</option>
<?php } else { ?>
                <option value="DEPOSIT"<?php echo ($data['CONFIRM']['TYPE'] == 'DEPOSIT' ? ' selected' : '');?>>DEPOSIT (депозиране на сума)</option>
<?php } ?>
            </select>

<?php if (!empty($data['INVOICES'])) { ?>
        <div class="invoices">
            <textarea name="INVOICES" id="INVOICES" style="display: none"><?php echo $data['INVOICES_JSON'];?></textarea>
        <label>INVOICES</label><br />
        <?php foreach($data['INVOICES'] as $invoice) { ?>
            <input type="checkbox" name="CONFIRM_INVOICES[]" id="inv_<?php echo $invoice['IDN'];?>" data-amount="<?php echo $invoice['AMOUNT'];?>" value="<?php echo $invoice['IDN'];?>"<?php echo (!empty($data['CONFIRM']['INVOICES']) && in_array($invoice['IDN'], $data['CONFIRM']['INVOICES']) ? ' checked' : '');?> />
            <label for="inv_<?php echo $invoice['IDN'];?>"><?php echo $invoice['IDN'] . ': ' . '(' . $invoice['AMOUNT'] . ') ' . $invoice['SHORTDESC'];?></label><br />
        <?php } ?>
        </div>
<?php } else { ?>
    <input type="checkbox" name="CONFIRM_INVOICES[]" data-amount="<?php echo $data['INIT_RESPONSE']['AMOUNT'];?>" style="display: none" />
<?php } ?>

        <label for="CONFIRM_TOTAL">СУМА</label>
            <input type="text" name="CONFIRM_TOTAL" id="CONFIRM_TOTAL" value="<?php echo $data['CONFIRM']['TOTAL'];?>"<?php echo ($data['CONFIRM']['TYPE'] == 'DEPOSIT' ? ' readonly' : '');?> />

        <input type="submit" value="OK" class="CONFIRM" />
      </fieldset>
<?php } ?>
<?php } ?>
<?php if (!empty($data['CONFIRM_RESPONSE'])) { ?>
  <fieldset>
    <legend>Резултати от плащане/депозиране:</legend>

    <label for="CONFIRM_REQUEST">REQUEST</label>
        <textarea name="CONFIRM_REQUEST" id="CONFIRM_REQUEST" readonly><?php echo $data['CONFIRM_REQUEST'];?></textarea>

    <label for="CONFIRM_RESPONSE">RESPONSE (JSON)</label>
        <textarea name="CONFIRM_RESPONSE" id="CONFIRM_RESPONSE" readonly><?php echo $data['CONFIRM_RESPONSE']['JSON'];?></textarea>

    <label for="CONFIRM_STATUS:">STATUS</label>
        <input type="text" name="CONFIRM_STATUS" id="CONFIRM_STATUS" value="<?php echo $data['CONFIRM_RESPONSE']['STATUS'];?>: <?php echo getStatusText($data['CONFIRM_RESPONSE']['STATUS']);?>" disabled />

  </fieldset>
<?php } ?>
  </form>
</body>
</html>
<?php

?>