calendar = Calendar
calendars_year = An
calendars_month = Lună
calendars_week = Săptămână
calendars_day = Zi
calendars_events = Evenimente pentru
calendars_events_today = Evenimentele ASTĂZI
calendars_events_tomorrow = Evenimentele MÂINE
calendars_events_list_month = Lista evenimentelor pentru luna
calendars_events_all = Toate evenimentele
calendars_previous_day = Ziua precedentă
calendars_previous_week = Săptămână precedentă
calendars_previous_month = Lună precedentă
calendars_previous_year = Anul precedent
calendars_next_day = Ziua următoare
calendars_next_week = Săptămână următoare
calendars_next_month = Lună următoare
calendars_next_year = Anul următor
calendars_week_number = Număr săptămână
calendars_add_event = Apăsați pentru a ADĂUGA eveniment pentru
calendars_view_event = Apăsați pentru a VIZUALIZA evenimentele pentru
calendars_remaining_event = încă 1 eveniment
calendars_remaining_events = încă %s evenimente
calendars_dashlet_remaining_event = 1 eveniment
calendars_dashlet_remaining_events = %s evenimente
calendars_private_event = eveniment personal
calendars_private_event2 = Evenimentul este personal și informația despre acesta este limitată
calendars_no_events = nu există evenimente pentru perioada definită
calendars_personal_settings = setări personale pentru calendarul
calendars_week_days_number = Zile în săptămână
calendars_week_start_day = Începutul săptămânii
calendars_day_time_grid = Ore pe "Zi"
calendars_week_time_grid = Ore pe "Săptpmână"
calendars_show_events = Afișează evenimentele pentru
calendars_events_types = Tipuri de evenimente
calendars_search_departments_and_users = căutare/selectare departamente și utilizatori
weekday_0 = Duminică
weekday_1 = Luni
weekday_2 = Marți
weekday_3 = Miercuri
weekday_4 = Joi
weekday_5 = Vineri
weekday_6 = Sămbâtă
weekday_shorten_0 = Dum
weekday_shorten_1 = Lun
weekday_shorten_2 = Mar
weekday_shorten_3 = Mier
weekday_shorten_4 = Joi
weekday_shorten_5 = Vin
weekday_shorten_6 = Sămb
weekday_short_0 = D
weekday_short_1 = L
weekday_short_2 = M
weekday_short_3 = M
weekday_short_4 = J
weekday_short_5 = V
weekday_short_6 = S
message_settings_saved_success = setările au fost păstrare cu succes
help_calendars_week_days_number = Se setează câte zile din săptămâna se vor afișa. Sunt permise numai vederi cu 5, 6 și 7 zile. Se folosesc numai regimele ”Zi” și ”Săptămână”.
help_calendars_week_start_day = Se setează ziua cu care începe săptămâna. În Europa săptămâna începe de Luni, iar în SUA și alte țări vorbitoare de limbă engleză, săptămâna începe de duminică. <b>SE RECOMANDĂ</b> să setați săptămâna a începe de  luni.
help_calendars_day_time_grid = Se setează intervalul de ore, vizibile în regim ”ZI”. Ora inițială în mod obligatoriu trebuie să fie înaintea orei finale.
help_calendars_week_time_grid = Se setează intervalul orelor, vizibile în regim "Săptămână". Ora inițială în mod obligatoriu trebuie să fie înaintea orei finale.
warning_more_events = Pentru ziua există mai multe evenimente (total %s), decât pot fi afișate pe un ecran.<br />Vă rugăm să limitați afișarea evenimentelor cu ajutorul filtrelor din dreaptă.
warning_week_more_events = Pentru %s există mai multe evenimente (total %s), decât pot fi afișate pe un ecran.<br />Vă rugăm să limitați afișarea evenimentelor cu ajutorul filtrelor din dreaptă.

error_less_than_one_days = Numărul de zile de concediu nu poate fi mai mic de una (1)!
error_invalid_date = Въведената дата е невалидна!
