<?php

class Documents_Filter_Viewer extends Viewer {
    public $template = 'filter.html';

    public function prepare() {
        require_once $this->modelsDir . 'documents.factory.php';

        $request = &$this->registry['request'];

        $sort_base = '';
        //we open this from autocompleter's select button
        $autocomplete_filter = $request->get('autocomplete_filter');
        if ($autocomplete_filter) {
            if ($autocomplete_filter != 'session') {
                $filters = $this->data['autocomplete_filters'];
                unset($filters['display']);
                $this->registry['session']->remove('filter_document');
            } else {
                $filters = array();
            }

            //set sort link
            $sort_base = sprintf("%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s",
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'], $this->module,
                $this->registry['action_param'], $this->action,
                'autocomplete_filter', 'session',
                'uniqid', $request['uniqid']);

        } else {
            $filters = array();
            // filters for documents with system task
            if ($request->get('generate_system_task')) {
                $filters = array('where' => array(
                                    'd.active = \'1\'',
                                    'd.status != \'closed\''
                                ));
                $system_types = array();
                require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
                $documentTypes = Documents_Types::search($this->registry, array('where' => array('dt.generate_system_task = \'1\'', 'dt.active = 1')));
                if ($documentTypes) {
                    foreach ($documentTypes as $idx => $document_type) {
                        $system_types[] = $document_type->get('id');
                        $filters['where'][] = 'd.type = \'' . $document_type->get('id') . '\' ' . ($idx < count($documentTypes) - 1 ? 'OR' : 'AND');
                    }
                }
                $this->data['generate_system_task'] = $request->get('generate_system_task');
            }
        }

        //'open_from' - module that popup is opened from, 'model_id' - id of opening model,
        //'customer' - ID of selected customer when adding document (if any)
        //parameters signify that predefined filter should be loaded (only this time)
        if ($request->get('open_from') && ($request->get('model_id') || $request->isRequested('customer'))
            && !preg_match('#(save|del)filter#', $request->get('filters_action'))) {
            $where = array('f.module = \'documents\'', 'f.controller = \'documents\'', 'f.active = 1', 'f.user_defined = 0');

            // connect documents to document, task or event
            if ($request->get('open_from') && in_array($request->get('open_from'), array('documents', 'tasks', 'events'))) {
                $where[] = 'f.module_from = \'' . $request->get('open_from') . '\'';
                $where[] = 'f.controller_from = \'' . $request->get('open_from') . '\'';
            }

            //search conditions for calling module and controller are set
            $saved_filters = array();
            if (count($where) == 6) {
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');

                $pred_filters = array(
                    'where' => $where,
                    'model_lang' => $this->registry->get('model_lang'),
                    'sanitize' => true);

                $predefined_filter = Filters::searchOne($this->registry, $pred_filters);

                if ($predefined_filter) {
                    //set these properties in order to load predefined filter into session
                    $request->set('filters_action', 'loadfilter', 'get', true);
                    $request->set('filter_name', $predefined_filter->get('id'), 'get', true);

                    //load filters from the DB
                    $saved_filters = Documents::saveSearchParams($this->registry, array(), 'filter_');

                    $request->remove('filters_action');
                    $request->remove('filter_name');

                    //clear variable
                    $filters = array();
                }

                if (!empty($saved_filters) && preg_match('#currentCustomer#', implode('', $saved_filters['where']))) {
                    $saved_filters = $this->registry['session']->get('filter_document');

                    $customer = '';
                    $customer_values_autocomplete = '';
                    if ($request->get('model_id')) {
                        $module = $request->get('open_from');
                        $factory_name = ucfirst($module);
                        require_once PH_MODULES_DIR . $module . '/models/' . $module . '.factory.php';
                        $alias = $factory_name::getAlias($module, $module);
                        $model = $factory_name::searchOne(
                            $this->registry,
                            array(
                                'where' => array($alias . '.id = ' . $request->get('model_id')),
                                'sanitize' => true
                            ));
                        if ($model) {
                            $customer = $model->get('customer');
                            $customer_values_autocomplete = sprintf('[%s] %s',
                                $model->get('customer_code'), $model->get('customer_name'));
                        }
                    } elseif ($request->isRequested('customer')) {
                        $customer = $request->get('customer');
                        $customer_values_autocomplete = '';

                        if ($customer) {
                            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                            $customer_model = Customers::searchOne($this->registry, array(
                                'sanitize' => true,
                                'model_lang' => $this->registry->get('model_lang'),
                                'where' => array('c.id = ' . $customer)));
                            if (!empty($customer_model)) {
                                $customer_values_autocomplete = sprintf('[%s] %s',
                                    $customer_model->get('code'),
                                    $customer_model->get('name') .
                                        (!$customer_model->get('is_company') ? ' ' . $customer_model->get('lastname') : ''));
                            }
                        }
                    }

                    foreach ($saved_filters['compare_options'] as $idx => $filter) {
                        if (preg_match('#currentCustomer#', $filter)) {
                            $saved_filters['compare_options'][$idx] = "= '%s'";
                            $saved_filters['values'][$idx] = $customer;
                            $saved_filters['values_autocomplete'][$idx] = $customer_values_autocomplete;
                        }
                    }
                    $this->registry['session']->remove('filter_document');
                    $this->registry['session']->set('filter_document', $saved_filters, '', true);
                }

                if (empty($saved_filters)) {
                    // set default filters
                    $filters = array('where' => array('d.active = \'1\''));
                }
            }
        }

        $filters = Documents::saveSearchParams($this->registry, $filters, 'filter_');

        //when searching for customer-dependent document from autocompleter, prepare customer name for session filters
        if ($autocomplete_filter && $autocomplete_filter != 'session' && $request->isRequested('filters')) {
            $customer = '';
            $customer_values_autocomplete = '';

            $request_filters = $request->get('filters');
            if (is_array($request_filters) && array_key_exists('<customer>', $request_filters)) {
                $customer = $request_filters['<customer>'];
            }

            if ($customer) {
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $customer_model = Customers::searchOne($this->registry, array(
                    'sanitize' => true,
                    'model_lang' => $this->registry->get('model_lang'),
                    'where' => array('c.id = ' . $customer)));
                if (!empty($customer_model)) {
                    $customer_values_autocomplete = sprintf('[%s] %s',
                        $customer_model->get('code'),
                        $customer_model->get('name') .
                            (!$customer_model->get('is_company') ? ' ' . $customer_model->get('lastname') : ''));
                } else {
                    $customer = '';
                }
            }

            if ($customer) {
                $session_filters = $this->registry['session']->get('filter_document');
                foreach ($session_filters['search_fields'] as $idx => $fld) {
                    if ($fld == 'd.customer') {
                        $session_filters['values_autocomplete'][$idx] = $customer_values_autocomplete;
                        break;
                    }
                }
                $this->registry['session']->set('filter_document', $session_filters, '', true);
            }
        }

        if ($request->get('model_lang')) {
            $filters['model_lang'] = $request->get('model_lang');
            $this->data['model_lang'] = $request->get('model_lang');
            $sort_base .= '&amp;model_lang=' . $request->get('model_lang');
        }

        if (!empty($filters['where'])) {
            $customize = array();
            $found = 0;
            foreach ($filters['where'] as $where) {
                if (preg_match('/d\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/d\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'type', 'value' => $val);
                    $found++;

                    //get type for multi actions
                    $type = intval($val);
                }
                if (preg_match('/dt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/dt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'section', 'value' => $val);
                    $found++;
                }
            }
            if ($found == 1 && $customize) {
                $this->setCustomTemplate($customize);
            } else {
                $this->setCustomTemplate();
            }
        } else {
            $this->setCustomTemplate();
        }

        // modelFields contains visible columns
        if (!empty($this->modelFields)) {
            if (isset($filters['get_fields'])) {
                $filters['get_fields'] = array_merge($filters['get_fields'], $this->modelFields);
            } else {
                $filters['get_fields'] = $this->modelFields;
            }

            if (in_array('tags', $this->modelFields)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }
        }

        list($documents, $pagination) = Documents::pagedSearch($this->registry, $filters);

        $this->data['documents'] = $documents;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters, $sort_base);

        $this->prepareTitleBar();

        if($this->theme->isModern()) {
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.min.js');
            $this->registry->push('custom_js', PH_JAVASCRIPT_URL . '/ej2/ej2.helper.js');
            $this->registry->push('custom_css', PH_JAVASCRIPT_URL . '/ej2/material.css');

            $this->data['dont_wrap_content'] = true;

            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
    }

    public function prepareTitleBar() {
        $title = $this->i18n('documents');
        $this->data['title'] = $title;
    }
}

?>
