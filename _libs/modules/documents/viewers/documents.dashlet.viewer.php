<?php

class Documents_Dashlet_Viewer extends Viewer {
    public $template = 'dashlet.html';
    public $filters = array();

    public function prepare() {
        require_once $this->modelsDir . 'documents.factory.php';

        $dashlets_filters = array('where' => array('d.active = 1',
                                                   'd.id = ' . $this->registry['request']->get('dashlet')),
                                  'sanitize' => true);
        require_once PH_MODULES_DIR . 'dashlets/models/dashlets.factory.php';
        $dashlet = Dashlets::searchOne($this->registry, $dashlets_filters);
        $all_columns = Dashlets::getModuleFields($this->registry, array('module' => 'documents', 'controller' => 'documents'));

        $filters = $dashlet->get('filters');
        $settings = $dashlet->get('settings');
        unset($dashlet->properties['filters']);
        unset($dashlet->properties['settings']);
        $filters['display'] = ($dashlet->get('records_per_page') ? $dashlet->get('records_per_page') : PH_DASHLETS_MAX_ROWS);

        $session_param = 'dashlets_' . $dashlet->get('id') . '_document';
        $this->registry['session']->set($session_param, $filters, '', true);

        $filters = Documents::saveSearchParams($this->registry, array(), 'dashlets_' . $dashlet->get('id') . '_');

        if (!empty($filters['where'])) {
            $customize = array();
            $found = 0;
            $model_types = array();
            foreach ($filters['where'] as $where) {
                if (preg_match('/d\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/d\.type\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'type', 'value' => $val);
                    $model_types[] = $val;
                    $found++;
                }
                if (preg_match('/dt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', $where)) {
                    $val = trim(preg_replace('/dt\.type_section\s*=\s*\'?(\d+)\'?(\s+(AND|OR))?/iu', '$1', $where));
                    $customize = array('name' => 'section', 'value' => $val);
                    $found++;
                }
            }
            if ($found != 1) {
                $customize = array();
            }
            $basic_vars_labels = $this->getBasicVarsLabels('Document', $customize);

            // Get all common additional columns
            $additional_vars = Dashlets::getAdditionalColumns($this->registry, 'Document', $model_types);
            $additional_vars_labels = array();
            if (!empty($additional_vars) && is_array($additional_vars)) {
                foreach ($additional_vars as $additional_var) {
                    $additional_vars_labels[$additional_var['option_value']] = $additional_var['label'];
                }
            }

            // Prepare the labels of all basic and additional columns
            $this->data['vars_labels'] = array_merge($basic_vars_labels, $additional_vars_labels);
        } else {
            $this->data['vars_labels'] = $this->getBasicVarsLabels('Document', array());
        }

        foreach ($settings['columns'] as $key => $column) {
            if (!isset($settings['visible'][$key])) {
                unset($settings['columns'][$key]);
            }
        }

        if (in_array('tags', $settings['columns'])) {
            //set flag to get tags for current model
            $this->registry->set('getTags', true, true);
        }

        // get necessary fields according to visible columns
        $filters['get_fields'] = $settings['columns'];

        list($documents, $pagination) = Documents::pagedSearch($this->registry, $filters);

        // Get the additional vars for each model
        if (array_filter($settings['columns'], function($a) { return strpos($a, 'a__') === 0; })) {
            $this->registry->set('get_old_vars', true, true);
            foreach ($documents as $document) {
                $document->getAssocVars();
            }
        }

        $this->data['dashlet_id'] = $dashlet->get('id');
        $this->data['columns'] = $settings['columns'];
        $this->data['documents'] = $documents;
        $this->data['pagination'] = $pagination;
        $this->data['all_columns'] = $all_columns;
        $this->data['session_param'] = $session_param;
        $this->setFrameset('frameset_blank.html');

        //action that will be executed on model when its table row is clicked
        $this->data['row_link_action'] = $this->registry['config']->getParam('documents', 'row_link_action');

        //prepare sort array for the listing
        $secondary_controller = '';
        if ($this->registry['module'] != $this->registry['controller']) {
            $secondary_controller = $this->registry['controller'];
        }

        //set sort link
        $sort_base = sprintf("%s?%s=%s%s&amp;%s=%s&amp;%s=%s",
        $_SERVER['PHP_SELF'],
        $this->registry['module_param'], $this->module,
        ($secondary_controller) ?
                                    '&amp;' . $this->registry['controller_param'] . '=' . $secondary_controller : '',
        $this->registry['action_param'], $this->action, $this->action, $dashlet->get('id'));
        $this->prepareAjaxSort($filters, $session_param, 'content_dashlet_' . $dashlet->get('id'), $sort_base);
    }
}

?>
