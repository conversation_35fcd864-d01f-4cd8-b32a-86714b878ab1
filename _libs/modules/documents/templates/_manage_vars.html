            {assign var='layout_id' value=$layout.id}
            {assign var='vars' value=$layouts_vars.$layout_id}
            {if $layout.id}
            <tr id="layout_{$layout.id}_box"{if $layout.cookie eq 'off'} style="display: none;"{/if}>
              <td class="nopadding" colspan="3">
                <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
            {/if}
            {foreach name='j' from=$vars item='var'}
              {if $var.type}
                {strip}
                  {capture assign='info'}{if $var.help}{$var.help}{else}{$var.label}{/if}{/capture}
                {/strip}
                {if $layout.edit}
                  {* var=$var SHOULD BE REMOVED LATER *}
                  {if $var.type eq 'gt2'}
                    <tr>
                      <td colspan="3">
                        {include file=`$theme->templatesDir`_gt2_edit.html table=$var}
                      </td>
                    </tr>
                  {else}
                    {include file="input_`$var.type`.html"
                      var=$var
                      standalone=false
                      var_id=$var.id
                      name=$var.name
                      custom_id=$var.custom_id
                      label=$var.label
                      help=$var.help
                      back_label=$var.back_label
                      back_label_style=$var.back_label_style
                      value=$var.value
                      value_id=$var.value_id
                      options=$var.options
                      optgroups=$var.optgroups
                      option_value=$var.option_value
                      first_option_label=$var.first_option_label
                      onclick=$var.onclick
                      on_change=$var.on_change
                      sequences=$var.sequences
                      check=$var.check
                      scrollable=$var.scrollable
                      calculate=$var.calculate
                      readonly=$var.readonly
                      source=$var.source
                      onchange=$var.onchange
                      map_params=$var.map_params
                      width=$var.width
                      hidden=$var.hidden
                      really_required=$var.required
                      required=$var.required
                      disabled=$var.disabled
                      options_align=$var.options_align
                      autocomplete=$var.autocomplete
                      js_methods=$var.js_methods
                      restrict=$var.js_filter
                      deleteid=$var.deleteid
                      show_placeholder=$var.show_placeholder
                      text_align=$var.text_align
                      custom_class=$var.custom_class
                    }
                  {/if}
                {else}
                  {if $var.type eq 'gt2'}
                    <tr>
                      <td colspan="3">
                        {include file=`$theme->templatesDir`_gt2_view.html table=$var}
                      </td>
                    </tr>
                  {else}
                    {include file="view_`$var.type`.html"}
                  {/if}
                {/if}
              {/if}
            {/foreach}
            {if $layout.id}
                </table>
              </td>
            </tr>
            {/if}
