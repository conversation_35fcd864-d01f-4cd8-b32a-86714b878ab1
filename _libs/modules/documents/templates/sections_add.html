<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="documents" enctype="multipart/form-data" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$documents_section->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='sections_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$documents_section->get('name')|escape}" title="{#documents_sections_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_position"><label for="position"{if $messages->getErrors('position')} class="error"{/if}>{help label='sections_position'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="position" id="position" value="{$documents_section->get('position')|escape}" title="{#documents_sections_position#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='sections_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <textarea class="areabox" name="description" id="description" title="{#documents_sections_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$documents_section->get('description')}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_icon"><label for="icon_file"{if $messages->getErrors('icon')} class="error"{/if}>{help label='sections_icon_file'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="file" name="icon_file" id="icon_file" class="filebox" />
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$documents_section}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
