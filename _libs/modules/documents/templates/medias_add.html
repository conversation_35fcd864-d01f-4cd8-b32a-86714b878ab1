<h1>{$title}</h1>

<div id="form_container">

{include file=`$theme->templatesDir`actions_box.html}
{include file=`$theme->templatesDir`translate_box.html}

<form name="documents" action="{$submitLink}" method="post">
<input type="hidden" name="model_lang" id="model_lang" value="{$documents_media->get('model_lang')|default:$lang}" />
<table border="0" cellpadding="0" cellspacing="0" class="t_table">
  <tr>
    <td class="t_footer"></td>
  </tr>
  <tr>
    <td>
      <table cellspacing="0" cellpadding="0" border="0" class="t_layout_table">
        <tr>
          <td class="labelbox"><a name="error_name"><label for="name"{if $messages->getErrors('name')} class="error"{/if}>{help label='medias_name'}</label></a></td>
          <td class="required">{#required#}</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox" name="name" id="name" value="{$documents_media->get('name')|escape}" title="{#documents_medias_name#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_description"><label for="description"{if $messages->getErrors('description')} class="error"{/if}>{help label='medias_description'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
             <textarea class="areabox" name="description" id="description" title="{#documents_medias_description#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)">{$documents_media->get('description')}</textarea>
          </td>
        </tr>
        <tr>
          <td class="labelbox"><a name="error_position"><label for="position"{if $messages->getErrors('position')} class="error"{/if}>{help label='medias_position'}</label></a></td>
          <td>&nbsp;</td>
          <td nowrap="nowrap">
            <input type="text" class="txtbox short" name="position" id="position" value="{$documents_media->get('position')|escape}" title="{#documents_medias_position#|escape}" onfocus="highlight(this)" onblur="unhighlight(this)" />
          </td>
        </tr>
        <tr>
          <td colspan="3">&nbsp;</td>
        </tr>
        <tr>
          <td colspan="3">
            <button type="submit" name="saveButton1" class="button">{#add#|escape}</button>{include file=`$theme->templatesDir`cancel_button.html}
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
{include file=`$theme->templatesDir`help_box.html}
{include file=`$theme->templatesDir`system_settings_box.html object=$documents_media}
{include file=`$theme->templatesDir`after_actions_box.html}
</form>
</div>
