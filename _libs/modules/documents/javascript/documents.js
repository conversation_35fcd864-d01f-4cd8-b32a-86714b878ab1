function manageGT2Layouts(element) {

    if (element.disabled) return false;

    if (!element.checked) {
        $('layouts_row').style.display = 'none';
        $('vat_row').style.display = 'none';
        //$('calc_price_row').style.display = 'none';
        $('gt2_layout').disabled = true;
    } else {
        $('layouts_row').style.display = '';
        $('vat_row').style.display = '';
       // $('calc_price_row').style.display = '';
        $('gt2_layout').disabled = false;
    }
}

/**
 * Sets default VAT value on/off
 *
 * @param object el - the check box we toggle
 */
function setDefaultVat(el) {

    // there is just one element, unlike other modules where fields are in a grouping table
    if (el.checked) {
        $('default_VAT').disabled = false;
        $('default_VAT').style.display = '';
    } else {
        $('default_VAT').disabled = true;
        $('default_VAT').style.display = 'none';
    }
}

/**
 * Reload options for document statuses and substatuses in dependent dropdowns
 * whend selection in document types dropdown is changed.
 *
 * @param element - dropdown for document types
 * @param dropdowns - array of names of dependent dropdowns and flags for first option
 */
function getDocTypeStatuses(element, dropdowns) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var type_id = element.value;
    var index = element.id.match(/[0-9]+$/);
    var opt = {
        asynchronous:false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            // dependent dropdowns are passed as an associative array
            // (name as key, flag whether to display an empty first option as value)
            for (var s_id in dropdowns) {
                var select_obj = $(s_id + '_' + index);
                if (!select_obj) {
                    continue;
                }
                var first = dropdowns[s_id];
                select_obj.options.length = 0;
                if (result.length > 0) {
                    if (first) {
                        select_obj.options[0] = new Option(('['+i18n['labels']['all']+']').toLowerCase(), '', false, false);
                        addClass(select_obj.options[0], 'undefined');
                    }
                    removeClass(select_obj, 'missing_records');
                } else {
                    select_obj.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                    addClass(select_obj, 'missing_records');
                }
                for (var j = 0; j < result.length; j++) {
                    select_obj.options[j+first] = new Option(result[j]['label'], result[j]['option_value'], false, false);
                }
                toggleUndefined(select_obj);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    new Ajax.Request(env.base_url + '?' + env.module_param + '=documents&controller=statuses&statuses=ajax_getstatuses&type='+type_id, opt);
}
