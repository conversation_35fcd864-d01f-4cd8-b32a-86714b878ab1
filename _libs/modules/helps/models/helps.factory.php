<?php

require_once('helps.model.php');

/**
 * Helps model class
 */
Class Helps extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Help';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @param array $sql -
     * @return array - IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array()) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#h.active#', $sort)) {
                $sort = 'ORDER BY h.active DESC, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;

            }
        } else {
            $sort = 'ORDER BY h.active desc, h.id DESC';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT(h.id)' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_HELPS . ' AS h' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_HELPS_I18N . ' AS hi18n' . "\n" .
                       '  ON (hi18n.parent_id=h.id AND hi18n.lang="' . $model_lang . '")' . "\n";

        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to users to fetch added by info
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                '  ON (h.added_by=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to users to fetch modified by info
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                '  ON (h.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $model_lang . '")' . "\n";
        }

        //where clause
        $sql['where'] = $where;

        //order by clause
        $sql['order'] = $sort;

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $ids = $registry['db']->GetCol($query);
        return ($ids);
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        //ORDER BY clause
        if (!empty($filters['sort'])) {
            $sort = implode(', ', $filters['sort']);
            if (!preg_match('#h.active#', $sort)) {
                $sort = 'ORDER BY h.active DESC, ' . $sort;
            } else {
                $sort = 'ORDER BY ' . $sort;

            }
        } else {
            $sort = 'ORDER BY h.active DESC, h.id DESC';
        }

        //select clause
        $sql['select'] = 'SELECT h.*, hi18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_HELPS . ' AS h' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_HELPS_I18N . ' AS hi18n' . "\n" .
                       '  ON (hi18n.parent_id=h.id AND hi18n.lang="' . $model_lang . '")' . "\n" .
                       //relate to users to fetch added by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (h.added_by=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n" .
                        //relate to users to fetch modified by info
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (h.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $model_lang . '")' . "\n";

        //where clause
        $sql['where'] = $where;

        //order by clause
        $sql['order'] = $sort;

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

        //search basic details with current lang parameters
        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }
        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            //get the total count
            if ($sql['limit']) {
                //get the total number of records for this search
                $sql['select'] = 'SELECT COUNT(*) AS total';
                $sql['limit'] = '';
                $query = implode("\n", $sql);
                $total = $registry['db']->GetOne($query);
            } else {
                //there is no limit set,
                //get the count from the found records
                $total = count($models);
            }

            $results = array($models, $total);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param array $filters - search filters
     * @return array $where - the prepare where array
     */
    public static function constructWhere(&$registry, $filters = array()) {

        $where[] = 'WHERE (';

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where []= '(' . implode(" OR \n\t", $key_where) . ')';
            }
        } elseif (isset($filters['where'])) {
            $current_user_id = $registry['currentUser']->get('id');
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);

                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);

        return $where;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionParam = 'list_') {
        $sessionParam = strtolower($sessionParam . self::$modelName);

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']      = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']    = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_HELPS . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models
     * Deletion is real
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids);

        if (!$deleted) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models by specified table, id, or where clause.
     * ATTENTION: Deletion is real.
     *
     * @param object $registry - the main registry
     * @param mixed $ids - array of ids or single id
     * @param string $dbTable - table to delete from
     * @param string $field - field name to be used as basic where clause
     * @param mixed $additionalWhere - string or array expected; additional where clause to filter more the results to delete
     * @return bool - result of the operations
     */
    public static function deleteMultiple(&$registry, $ids, $dbTable = '', $field = 'id', $additionalWhere = '') {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        //checks if the $ids are an array or an integer
        if (is_array($ids)) {
            $list_ids = implode(', ', $ids);
        } else {
            $list_ids = $ids;
        }

        //creating query to delete help(s) from the main table
        $query_for_main_table = 'DELETE FROM ' . DB_TABLE_HELPS . ' WHERE id IN (' . $list_ids . ')';
        $result = $db->Execute($query_for_main_table);
        if (! $result) {
            return $result;
        }

        //creating query to delete the settings for the helps
        $query_for_lang_table = 'DELETE FROM ' . DB_TABLE_HELPS_I18N . ' WHERE parent_id IN (' . $list_ids . ')';
        $result = $db->Execute($query_for_lang_table);

        return $result;
    }

    /**
     * Gets available actions that help texts can be added for
     * for specified module name or for all.
     *
     * @param record $registry - the main registry
     * @param string $module - optional; module(+controller) name
     * @return array - array with results; module name as key, array of actions as value
     */
    public static function getHelpModules(&$registry, $module = '') {

        $help_modules = array();
        if ($module) {
            $help_modules[$module] = array();
        }

        $helps_actions = array('add', 'edit', 'view', 'translate', 'history',
                               'assign', 'attachments', 'generate',
                               'relatives', 'dependencies', 'timesheets', 'assignments',
                               'communications', 'transform', 'distribute', 'permissions', 'remind',
                               'branches', 'contactpersons', 'trademarks', 'statistics',
                               'addannex', 'listinvoices',
                               'viewtopic', 'viewclause', 'viewfinance',
                               'edittopic', 'editclause', 'editfinance',
                               'stageactivities', 'profile', 'mynzoom',
                               'displaysettings', 'printsettings', 'input', 'revision',
                               'printform', 'balance', 'allocate');

        $user_permissions = $registry['currentUser']->getRights();
        if ($module) {
            $user_permissions = isset($user_permissions[$module]) ?
                                array($module => $user_permissions[$module]) :
                                array();
        }

        // modules to skip
        $skip_modules = array('customers_branches', 'customers_contactpersons',
                              'minitasks');
        foreach ($user_permissions as $module => $permissions) {
            if (!preg_match('/\d+$/', $module) && !in_array($module, $skip_modules)) {
                // actions to change or add additional action based on them
                foreach ($permissions as $action => $permission) {
                    if (in_array($action, $helps_actions)) {
                        if ($action == 'transform') {
                            $action = 'transformations';
                        } elseif ($action == 'add' && $module == 'emails_campaigns') {
                            // no add action screen for emails campaigns
                            continue;
                        } elseif ($action == 'remind' && $module != 'nomenclatures') {
                            // only nomenclatures have a real tab for 'remind' action
                            continue;
                        }

                        $help_modules[$module][] = $action;
                    }
                }

                // additional actions (not in roles definitions)
                switch ($module) {
                case 'contracts':
                    $help_modules[$module][] = 'parties';
                    $help_modules[$module][] = 'listhandovers';
                    break;
                case 'documents':
                case 'tasks':
                    $help_modules[$module][] = 'timesheets';
                    break;
                case 'events':
                    $help_modules[$module][] = 'edit';
                    $help_modules[$module][] = 'relatives';
                    $help_modules[$module][] = 'remind';
                    break;
                case 'finance_budgets':
                    $help_modules[$module][] = 'enter';
                    $help_modules[$module][] = 'control';
                    break;
                case 'finance_companies':
                    $help_modules[$module][] = 'offices';
                    break;
                case 'finance_expenses_reasons':
                    $help_modules[$module][] = 'payments';
                    $help_modules[$module][] = 'addpayment';
                    $help_modules[$module][] = 'addcorrect';
                    $help_modules[$module][] = 'addcredit';
                    $help_modules[$module][] = 'adddebit';
                    $help_modules[$module][] = 'addhandover';
                   break;
                case 'finance_incomes_reasons':
                    $help_modules[$module][] = 'payments';
                    $help_modules[$module][] = 'addpayment';
                    $help_modules[$module][] = 'addcorrect';
                    $help_modules[$module][] = 'addinvoice';
                    $help_modules[$module][] = 'addproformainvoice';
                    $help_modules[$module][] = 'addcreditdebit';
                    $help_modules[$module][] = 'addhandover';
                    $help_modules[$module][] = 'commodities_reservation';
                    $help_modules[$module][] = 'advances';
                    $help_modules[$module][] = 'proformaadvances';
                   break;
                case 'projects':
                    $help_modules[$module][] = 'timesheets';
                    $help_modules[$module][] = 'phases';
                    break;
                case 'roles':
                    $help_modules[$module][] = 'menu';
                    //$help_modules[$module][] = 'companiesandoffices';
                    //$help_modules[$module][] = 'cachboxesandbankaccounts';
                    break;
                case 'users':
                    $help_modules[$module][] = 'password';
                    break;
                default:
                    break;
                }
            }
        }

        return $help_modules;
    }

    /**
     * Sorts array with the modules or actions dropdown options by label
     *
     * @param array $a - dropdown option
     * @param array $b - dropdown option
     */
    public static function _labelSort($a, $b) {
        if ($a['label'] > $b['label']) {
            return true;
        }
        return false;
    }

    /**
     * Get action name label from module and action.
     *
     * @param record $registry - the main registry
     * @param string $module_name - module(+controller) name
     * @param string $action_name - action name
     * @return string - translated label
     */
    public static function getActionLabel(&$registry, $module_name, $action_name) {
        // default label is the same as text displayed in the menu tab for the action
        $label = $registry['translater']->translate($action_name);

        // prepare custom labels for some actions
        switch ($module_name) {
        case 'contracts':
            if (preg_match('#^(view|edit)(topic|clause|finance)$#', $action_name, $matches)) {
                $label = sprintf('%s %s', $registry['translater']->translate($matches[1]), $label);
            }
            break;
        case 'turnovers':
            if ($action_name == 'input') {
                $label = $registry['translater']->translate('enter');
            }
            break;
        case 'customers':
            if ($action_name == 'branches') {
                $label = $registry['translater']->translate('default_branches');
            }
            break;
        default:
            break;
        }

        return $label;
    }
}

?>
