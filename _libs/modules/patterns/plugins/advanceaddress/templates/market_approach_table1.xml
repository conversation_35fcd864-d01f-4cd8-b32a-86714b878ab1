<w:tbl>
<w:tblPr>
  <w:tblStyle w:val="TableGrid"/>
  <w:tblLayout w:type="fixed"/>
  <w:tblW w:w="{$table_width|default:$smarty.const.TABLES_WIDTH_FULL_PAGE}" w:type="dxa"/>
  <w:tblBorders>
    <w:top w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:left w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:bottom w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:right w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:insideH w:val="single" w:sz="4" w:space="0" w:color="auto"/>
    <w:insideV w:val="single" w:sz="4" w:space="0" w:color="auto"/>
  </w:tblBorders>
  <w:tblLook w:val="04A0" w:firstRow="1" w:lastRow="0" w:firstColumn="1" w:lastColumn="0" w:noHBand="0" w:noVBand="1"/>
</w:tblPr>
{if $widths}
  <w:tblGrid>
    {foreach name='i' key='key' from=$widths item='width'}
      {$width|convert2docx_width:'w:gridCol'}
    {/foreach}
  </w:tblGrid>
{else}
  <w:tblGrid>
    {foreach from=$grid_widths item='grid_width'}
      <w:gridCol w:w="{$grid_width}"/>
    {/foreach}
  </w:tblGrid>
{/if}
{capture assign='style_tags_num'}{strip}
  <w:p>
    <w:pPr>
      <w:spacing w:after="0"/>
      <w:jc w:val="right"/>
    </w:pPr>
    <w:r>
      <w:rPr>
        <w:b/>
      </w:rPr>
{/strip}{/capture}
{capture assign='style_tags_num2'}{strip}
  <w:p>
    <w:pPr>
      <w:spacing w:after="0"/>
      <w:jc w:val="right"/>
    </w:pPr>
    <w:r>
      <w:rPr>
      </w:rPr>
{/strip}{/capture}


{* Table Header Row*}
{if $header}
<w:tr>
  <w:trPr>
    <w:tblHeader />
  </w:trPr>
  {foreach name='i' key='key' from=$header item='th'}
    {capture assign='style_tags'}{strip}
    <w:p>
      <w:pPr>
        <w:spacing w:after="0"/>
        <w:jc w:val="center"/>
      </w:pPr>
      <w:r>
        <w:rPr>
          <w:b/>
        </w:rPr>
    {/strip}{/capture}
    <w:tc><w:tcPr>{$widths[$key]|convert2docx_width:'w:tcW'}<w:shd w:val="clear" w:color="auto" w:fill="{$bg_color_th}"/><w:vAlign w:val="center"/></w:tcPr>{$style_tags}<w:t>{$th}</w:t></w:r></w:p></w:tc>
  {/foreach}
</w:tr>
{/if}
{if $header2}
    <w:tr>
      {foreach name='i' key='key' from=$header2 item='tc'}
        <w:tc>
          <w:tcPr>
            {$widths[$key]|convert2docx_width:'w:tcW'}
            {if $key==0}
              <w:shd w:val="clear" w:color="auto" w:fill="{$bg_color_th}"/>
            {else}
              <w:shd w:val="clear" w:color="auto" w:fill="{$bg_color_td}"/>
            {/if}
            <w:vAlign w:val="center"/>
          </w:tcPr>
          <w:p>
            <w:pPr>
              <w:spacing w:after="0"/>
              <w:jc w:val="{if $alignment[$key]}{$alignment[$key]}{else}center{/if}"/>
            </w:pPr>
            <w:r>
              <w:rPr>
                {if $key!=0}
                <w:b/>
                <w:bCs/>
                {/if}
              </w:rPr>
              <w:t>{$tc}</w:t>
            </w:r>
          </w:p>
        </w:tc>
    {/foreach}
  </w:tr>
{/if}
{foreach key='i' from=$rows item='row'}
  <w:tr>
    {foreach key='key' from=$row item='col'}
        {capture assign='style_tags'}{strip}
        <w:p>
          <w:pPr>
            <w:spacing w:after="0"/>
            <w:jc w:val="{if $alignment[$key]}{$alignment[$key]}{else}center{/if}"/>
          </w:pPr>
          <w:r>
            {if isset($bold) and !empty($bold[$i]) and !empty($bold[$i][$key])}
            <w:rPr>
              <w:b/>
            </w:rPr>
            {/if}
        {/strip}{/capture}
        <w:tc><w:tcPr><w:vAlign w:val="center"/>{$widths[$key]|convert2docx_width:'w:tcW'}<w:shd w:val="clear" w:color="auto" w:fill="{if $bgcolors[$key]}{$bgcolors[$key]}{else}{$bg_color_td}{/if}"/></w:tcPr>{$style_tags}<w:t>{strip}
          {if preg_match('#^\d\d\d\d-\d\d-\d\d$#', $col)}
            {$col|date_format:#date_short#}
          {elseif preg_match('#^\d\d\d\d-\d\d-\d\d \d\d:\d\d:\d\d$#', $col)}
            {$col|date_format:#date_mid#}
          {elseif preg_match('#^\d\d:\d\d:\d\d$#', $col)}
            {$col|date_format:#time_short#}
          {else}
            {$col}
          {/if}
        {/strip}</w:t></w:r></w:p></w:tc>
    {/foreach}
  </w:tr>
{/foreach}
</w:tbl>
