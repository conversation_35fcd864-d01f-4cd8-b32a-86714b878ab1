<?php

/**
 *
 */
class DocxTableViewer
{
    /**
     * @var Viewer
     */
    private Viewer $viewer;
    /**
     * @var
     */
    private $registry;
    /**
     * @var
     */
    private $paths;

    /**
     * @param $registry
     * @param $paths
     */
    public function __construct($registry, $paths)
    {
        $this->registry = $registry;
        $this->paths = $paths;
    }

    /**
     * @param  DocxTable $tbl
     * @return array|string|string[]|null
     */
    public function renderTableXml(DocxTable $tbl)
    {
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset(dirname(__DIR__) . '/templates/_table.xml');
        $viewer->data['paths'] = $this->paths;
        $viewer->data['table'] = $tbl;
        $tblXml = $viewer->fetch();
        $viewer->data['table'] = null;
        return $tblXml;
    }
}
