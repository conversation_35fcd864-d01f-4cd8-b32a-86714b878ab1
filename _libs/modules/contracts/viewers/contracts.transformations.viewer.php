<?php

class Contracts_Transformations_Viewer extends Viewer {
    public $template = 'transformations.html';

    public function prepare() {

        //structure the layout vars into two-dimensional array
        $this->model->getLayoutVars();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'),
                            'transform', $this->registry['request']->get('transform'));
        $this->data['submitLink'] = $this->submitLink;

        if ($this->registry->get('status_after_transform')) {
            $this->data['set_status'] = $this->registry->get('status_after_transform');
        }
        if ($this->registry->get('substatus_after_transform')) {
            $this->data['set_substatus'] = $this->registry->get('substatus_after_transform');
        }

        if ($this->model->get('bb_transform')) {
            //remove all layouts without bb vars
            $layout_vars = $this->model->get('vars');
            foreach($layout_vars as $layout_id => $vars) {
                $layout_contains_bb = false;
                foreach($vars as $key => $var) {
                    if ($var['type'] == 'bb') {
                        $layout_contains_bb = true;
                    } else {
                        //remove the variables that are not bb
                        unset($layout_vars[$layout_id][$key]);
                    }
                }
                if (!$layout_contains_bb) {
                    //remove the entire layout
                    unset($layout_vars[$layout_id]);
                }
            }
            $this->model->set('vars', $layout_vars, true);

            $this->template = 'transform_bb.html';
        } else {
            $this->data['multi_count'] = $this->registry['multi_count'];
            $this->data['dif_count'] = $this->registry['dif_count'];
            $this->data['found_counts'] = $this->registry['found_counts'];
        }

        // prepare layout index
        $this->prepareLayoutIndex();

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = sprintf($this->i18n('contracts_transform'), $this->model->getModelTypeName());
        $this->data['title'] = $title;
    }
}

?>
