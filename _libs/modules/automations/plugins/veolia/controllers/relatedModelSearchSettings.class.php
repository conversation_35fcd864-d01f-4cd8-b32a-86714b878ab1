<?php

class RelatedModelSearchSettings {
    /**
     * @var string The module of the related model to be searched
     */
    private string $module;
    /**
     * @var string The controller with which the search is conducted
     */
    private string $controller;
    /**
     * @var array The filters that are used to find the related model
     */
    private array $filters;
    /**
     * All the related model to main model var conversion.
     *
     * Structure: K<MainModelProperty> => V<RelatedModelProperty>
     *
     * @var array The conversion mapping
     */
    private $fieldMapping;
    /**
     * @var string|null The name of the method to execute at the end of the processing
     */
    private ?string $finalMethod;
    /**
     * @var RelatedModelSearchSettings[]|null Whether there are any nzoom models that are related to the current related one.
     */
    private ?array $moreRelated;
    /**
     * @var boolean Whether to use caching of the related model. Default is false.
     */
    private bool $useCache;
    /**
     * @var boolean Whether to get the Assoc vars of the related model. Default is true.
     */
    private bool $getAssoc;
    /**
     * @var string|null Some related models are searched only on a condition
     */
    private ?string $conditionField;

    public function __construct(array $settings)
    {
        $this->module = $settings['module'];
        $this->controller = $settings['controller'];
        $this->filters = $settings['filters'];
        $this->fieldMapping = $settings['fields'];
        $this->finalMethod = $settings['finally'] ?? null;
        $this->moreRelated = $settings['related'] ?? null;
        $this->useCache = $settings['useCache'] ?? false;
        $this->getAssoc = $settings['getAssoc'] ?? true;
        $this->conditionField = $settings['condition'] ?? null;
    }

    public function getModule()
    {
        return $this->module;
    }

    public function getController()
    {
        return $this->controller;
    }

    public function getFilters()
    {
        return $this->filters;
    }

    public function getFieldMapping()
    {
        return $this->fieldMapping;
    }

    public function hasFinalMethod()
    {
        return !is_null($this->finalMethod);
    }

    public function getFinalMethod()
    {
        return $this->finalMethod;
    }

    public function hasFurtherRelatedModels()
    {
        return !empty($this->moreRelated);
    }

    public function getFurtherRelatedSettings()
    {
        return $this->moreRelated;
    }

    public function useCache()
    {
        return $this->useCache;
    }

    public function getAssoc()
    {
        return $this->getAssoc;
    }

    public function getConditionField()
    {
        return $this->conditionField;
    }

    public function hasCondition()
    {
        return !is_null($this->conditionField);
    }
}
