error_sizes_cannot_be_unselected = You cannot remove sizes (%s)
error_sizes_save_failed = Error saving sizes
messages_variants_saved = Sizes successfully saved!
messages_variants_added = Added: %s
messages_variants_modified = Modified: %s
warning_generating_file_failed = Generating File failed! Please, contact the nZoom support!
messages_box_number_composed = Added box number and barcode

byfar_error_automations_copygroupvars_missing_fields = The field '%s' was not found while trying to copy the value from field '%s' to field '%s'.
byfar_error_automations_copygroupvars_failed = Error occurred while trying to save data into database.
byfar_error_automations_sync_failed = The synchronization of %s "%s" to website <a href="%s" target="_blank">%s</a> failed due to the following reasons:
byfar_message_automations_sync_success = The synchronization of %s "%s" to website <a href="%s" target="_blank">%s</a> is successful!

byfar_plugin_ajur_vat_reason_eu = paragraph 53, item 1 from ZDDS
byfar_plugin_ajur_vat_reason_outside_eu = paragraph 28, item 1 from ZDDS
byfar_plugin_ajur_measure_bg = бр
byfar_plugin_ajur_measure_en = pcs
byfar_plugin_ajur_tax_group = Б
byfar_plugin_ajur_tag_failed = Setting tag exported to AJUR failed for incomes reason ID %d!
byfar_plugin_ajur_sdelkatext = Продажби

byfar_invoices_notifications_num = Num
byfar_invoices_notifications_full_num = Invoice

error_shopify_error_field1 = Please select "%s" in table "%s" at row: %s
error_shopify_error_field2 = Please select "%s" and "%s" in table "%s" at row: %s
error_shopify_error_dates1 = Please select "%s" in table "%s" at row: %s
error_shopify_error_dates2 = The "%s" is after "%s" in table "%s" at row: %s
error_shopify_error_dates3 = There can be only one row with empty period in table "%s". Duplicate found at row: %s
error_shopify_error_dates4 = No crossing periods allowed. Such is found in table "%s" at row: %s

error_cannot_issue_advance_invoice = Error issuing advance invoice
error_item_not_found = Item with SKU number "%s" and shopify name "%s" was not found.
error_box_number_composition_failed = Error setting box number

warning_no_handover_added_for_sell = Adding handover failed for sell with ID: %d! Check errors bellow:
warning_no_warehouse_for_the_selected_office = No selected warehouse for the sell's office!

plugin_errors_for_document = Errors occurred when processing %s of customer %s:
plugin_error_employee_no_warehouse = No warehouse employee %s is responsible for!
plugin_error_handover_exists = Handover already created for %s for document!
plugin_no_warehouse_quantity_of_nomenclature = Not enough quantity of article "%s" in warehouse %s!
plugin_no_article_selected = Please, select article in row %s
plugin_no_quantity_inserted = Please, specify quantity in row %s
plugin_no_articles_selected = Please, select at least one article
plugin_outgoing_handover =  Outgoing handover

plugin_error_delivery_no_data = %s contains no data!
plugin_error_delivery_warehouse_invalid = Selected warehouse does not match warehouses specified in orders!
plugin_error_delivery_invoice_invalid = There should be exactly one active and finished delivery invoice to store commodities for, %d found!

plugin_error_fulfillment_handover_failed = Articles cannot be handed over!
plugin_error_fulfillment_not_invoiced = Fulfillment has not been invoiced!
plugin_error_fulfillment_invalid_invoice = Invoice is not valid or does not match fulfillment data!
plugin_error_fulfillment_invalid_contract = Fulfillment is not related to a valid order!
plugin_error_fulfillment_contract_exceeded = Fulfillment data exceeds unsupplied quanitities for order!
plugin_error_fulfillment_availability_exceeded = Fulfillment data exceeds available quantities in warehouse!
plugin_error_fulfillment_contract_overinvoiced = Fulfillment data exceeds not invoiced data of order!
plugin_error_overinvoiced_quantity = %s - quantity: %s > %s
plugin_error_overinvoiced_subtotal = %s - amount: %s > %s
plugin_error_contract_invoicing_complete = Invoicing of order has been completed! No more invoices can be issued for it!

error_enola_invalid_credentials = Alog credentials are not set
error_enola_sync_document_failed = The document did not sync successfully to Alog
error_enola_sync_document_attachments_failed = The document attachments did not sync successfully to Alog
error_enola_failed_update_nzoom_document = Failed to update Alog id
message_enola_sync_document_success = The document has successfully been synced to Alog
warning_document_already_synced_to_enola = The document has already been synced to Alog

message_success_update_related_noms = Successfully changing of %s nomenclatures
error_update_related_noms_update_fail = Updating the related nomenclature %s (id %d) failed! Please contact nZoom support!

error_sync_joor_to_nzoom_nom_ids = Error syncing JOOR to nZoom ids (joor id: %s, nzoom id: %s)

message_add_returning_handover_success = The commodities are returned!
error_returning_handover_not_created = Unable to return the commodities!
error_no_outoging_handover_found = No delivery handover found!
error_no_contract_found = No contract found!

error_automations_fail_to_issue_credit_note = Error crediting the invoice %s
error_automations_fail_to_release_reservation = Error releasing the reserved commodities
error_automations_fail_to_return_commodities = Error annulling the handover
error_automations_fail_to_deliver_to_enola = Error delivering the commodties to Enola
