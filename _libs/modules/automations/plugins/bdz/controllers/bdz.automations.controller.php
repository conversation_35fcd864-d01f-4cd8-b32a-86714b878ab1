<?php

class Bdz_Automations_Controller extends Automations_Controller {

    /**
     * Make some checks before saving the list of assignment users
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function validateAssignments($params) {
        // The default result is true
        $result = true;

        // Check if the automation should be executed
        if (in_array($this->registry['action'], array('add', 'edit')) && $this->registry['request']->isPost()) {
            // Get the request
            $request = &$this->registry['request'];

            // Check for assignment users duplication
            if ($request->isRequested('agreement_with') && count($request->get('agreement_with')) != count(array_unique($request->get('agreement_with')))) {
                // Raise an error message
                $this->registry['messages']->setError($this->i18n('error_document_assignment_users_duplicating'));

                // Fail the automation (this will prevent saving the additional vars)
                $result = false;
            }

            // Check if there's a row without a role
            $query = 'SELECT `name`' . "\n" .
                     '  FROM `' . DB_TABLE_FIELDS_META . '`' . "\n" .
                     '  WHERE `model` = \'Document\'' . "\n" .
                     '    AND `model_type` = \'' . $params['model']->get('type') . '\'' . "\n" .
                     '    AND `name` LIKE \'type_act_%\'';
            $role_field_name = $this->registry['db']->getOne($query);
            if (!empty($role_field_name) && $request->isRequested($role_field_name) && count($request->get($role_field_name)) != count(array_filter($request->get($role_field_name)))) {
                // Raise an error message
                $this->registry['messages']->setError($this->i18n('error_document_select_roles'));

                // Fail the automation (this will prevent saving the additional vars)
                $result = false;
            }
        }

        return $result;
    }

    /**
     * Changes the assignments of the document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function setDocumentAssignments($params) {
        // Prepare the registry
        $registry = &$this->registry;
        // Prepare the database object
        $db = &$registry['db'];
        // Get the current document
        $document = $params['model'];
        // Get the setting for this automation
        $settings = $this->settings;
        // Get the id of the automations user
        $automations_user_id = $this->automationUser->get('id');

        // Set a flag to check if the automation should be executed
        $automation_should_execute = false;

        // Get the document status and substatus
        $document_status    = $document->get('status');
        $document_substatus = $document->get('substatus');

        // Get the current action
        $action = $registry['action'];

        // Prepare a flag to check if the current user sets one of the "Approved" statuses
        $current_user_sets_approved_status = false;

        // Prepare flags for successful actions
        $assignments_success     = true;
        $status_success          = true;
        $answers_history_success = true;

        // Prepare flag to check if we are looking for one answer per position
        $one_answer_per_position = !empty($settings['one_answer_per_position']);
        $one_answer_per_position_only_for_roles = [];
        if (!empty($settings['one_answer_per_position_only_for_roles'])) {
            $one_answer_per_position_only_for_roles = preg_split('/\s*,\s*/', $settings['one_answer_per_position_only_for_roles']);
            $one_answer_per_position_only_for_roles = array_combine($one_answer_per_position_only_for_roles, $one_answer_per_position_only_for_roles);
        }

        // The role of the current status
        $currentStatusRole = 0;

        // If the document's status is the one for starting the approval process
        if ($document_status == $settings['automation_start_status'] && (empty($settings['automation_start_substatus']) || $document_substatus == $settings['automation_start_substatus'])) {
            // Set that the automation should be executed
            // If we're not currently setting the start status, we should
            // execute the automation to see if the current user answered something
            $automation_should_execute = true;

            // If the status is currently being set
            if ($action == 'setstatus') {
                /*
                 * Prepare for starting the approval process
                 */
                // Clear the answers of all approving users
                $this->_clearApprovingUsersAnswers($document);

                // Remove all observers (except the creator of the document)
                // and all owners
                $query  = "
                    DELETE FROM " . DB_TABLE_DOCUMENTS_ASSIGNMENTS . "
                      WHERE parent_id = '{$document->get('id')}'
                        AND (assignments_type = '" . PH_ASSIGNMENTS_OBSERVER . "'
                            AND assigned_to != '{$document->get('added_by')}'
                            OR assignments_type = '" . PH_ASSIGNMENTS_OWNER . "')";
                if (!$db->Execute($query)) {
                    $assignments_success = false;
                }
            }
        } else {
            // For each role there is an "Approved" status
            // If the document's status is in any of them, then some actions
            // should be taken
            foreach ($settings as $setting_key => $setting_value) {
                if (preg_match('/role_(\d)_status/', $setting_key, $matches)) {
                    $role = $matches[1];
                    if ($document_status == $setting_value && (empty($settings["role_{$role}_substatus"]) || $document_substatus == $settings["role_{$role}_substatus"])) {
                        // Set that the automation should be executed
                        // I.e. we're in status, for which we should check if
                        // the user has answered something
                        $automation_should_execute = true;

                        // If the user is currently setting a status
                        if ($action == 'setstatus') {
                            // Mark that the current user sets one of the "Approved" statuses
                            $current_user_sets_approved_status = true;
                        }

                        // Get the role of the current status
                        $currentStatusRole = $role;

                        // Stop checking, because this is enough - the status of
                        // the document is among the statuses, for which the
                        // automation should be executed
                        // The other roles statuses should normally be different
                        // and there should be no other matches, so stop the loop
                        break;
                    }
                }
            }
        }

        // If the document is in "Returned" status
        if ($document_status == $settings['assignments_returned_status'] && (empty($settings['assignments_returned_substatus']) || $document_substatus == $settings['assignments_returned_substatus'])) {
            /**
             * Do the same as when someone answers with NO
             */
            if (!$this->_setAnswerNo($params, $document)) {
                $assignments_success = false;
            }
        }

        // If the automation should NOT be executed
        if (!$automation_should_execute) {
            // Clear the current answer
            // and exit the automation
            $query = 'UPDATE `' . DB_TABLE_FIELDS_META . '` AS `fm`' . "\n" .
                     '  JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc`' . "\n" .
                     '    ON (`fm`.`model` = \'Document\'' . "\n" .
                     '      AND `fm`.`model_type` = \'' . $document->get('type') . '\'' . "\n" .
                     '      AND `fm`.`name` IN (\'agreement\', \'agreement_comment\')' . "\n" .
                     '      AND `dc`.`var_id` = `fm`.`id`' . "\n" .
                     '      AND `dc`.`model_id` = \'' . $document->get('id') . '\')' . "\n" .
                     '  SET `dc`.`value` = \'\',' . "\n" .
                     '    `dc`.`modified` = NOW(),' . "\n" .
                     '    `dc`.`modified_by` = \'' . $automations_user_id . '\'';
            return $db->Execute($query) ? true : false;
        }

        // If the document is sanitized, then unsanitize it
        if ($document->isSanitized()) {
            $document->unsanitize();
        }

        // Get the vars of the document as an associative array for easier access
        $get_old_vars = $registry->get('get_old_vars');
        $registry->set('get_old_vars', true, true);
        $document->getAssocVars();
        $registry->set('get_old_vars', $get_old_vars, true);
        $assoc_vars = $document->get('assoc_vars');

        // Get the id of the current user
        $current_user_id = $registry['originalUser']->get('id');

        // Get the names of the roles fields
        $query = 'SELECT `name`' . "\n" .
                 '  FROM `' . DB_TABLE_FIELDS_META . '`' . "\n" .
                 '  WHERE `model` = \'Document\'' . "\n" .
                 '    AND `model_type` = \'' . $document->get('type') . '\'' . "\n" .
                 '    AND (`name` LIKE \'type_act_%\'' . "\n" .
                 '      OR `name` LIKE \'prepared_type_act_%\')';
        $type_act_fields = $db->getCol($query);
        $roles_fields_names = array();
        foreach ($type_act_fields as $type_act_field) {
            if (preg_match('/^type_act_\d$/', $type_act_field)) {
                $roles_fields_names['type_act']          = $type_act_field;
            } elseif (preg_match('/^prepared_type_act_\d$/', $type_act_field)) {
                $roles_fields_names['prepared_type_act'] = $type_act_field;
            }
        }

        // Prepare the order which will be followed when assigning the users
        // i.e. read the agreement users table
        $assignments_order = array();
        $approving_users   = array();
        $positions         = $assoc_vars['agreement_consistency']['value'];
        $customers         = $assoc_vars['agreement_with']['value'];
        $roles             = $assoc_vars[$roles_fields_names['type_act']]['value'];
        // Prepare to collect YES rows (i.e. rows, for which the users answered YES)
        $yes_rows = array();
        if (!empty($positions)      && is_array($positions)
              && !empty($customers) && is_array($customers)
              && !empty($roles)     && is_array($roles)) {
            // Get the users of the selected customers
            $query = 'SELECT `employee`, `id`' . "\n" .
                     '  FROM `' . DB_TABLE_USERS . '`' . "\n" .
                     '  WHERE `employee` IN (\'' . implode('\', \'', $customers) . '\')';
            $users = $db->getAssoc($query);

            // Get the users current answers
            $answers = $assoc_vars['agreement_current_answer']['value'];

            // Go through all selected customers
            foreach ($customers as $agreement_group_row_num => $customer_id) {
                // Process only customers, which has users
                if (!empty($users[$customer_id])) {
                    // Collect the agreement table row numbers where the current user is set
                    if ($users[$customer_id] == $current_user_id) {
                        // Later this will be used only if the user answered YES
                        $yes_rows[] = $agreement_group_row_num;
                    }

                    // Build the position from the role value and the selected position
                    $position = (($positions[$agreement_group_row_num] == '' || $positions[$agreement_group_row_num] <= 1) ? 1 : $positions[$agreement_group_row_num]);
                    $position = $roles[$agreement_group_row_num] . '_' . str_replace('_', '', $position);

                    // Set the customer into his position
                    $assignments_order[$position]['users'][$agreement_group_row_num] = $users[$customer_id];
                    // Get all users answers
                    $assignments_order[$position]['answers'][$agreement_group_row_num] = $answers[$agreement_group_row_num];
                    // Collect all users
                    $approving_users[$users[$customer_id]] = $users[$customer_id];

                    /**
                     * Define if all users for the current position have answered
                     */
                    // By default, mark that all users have answered
                    if (!isset($assignments_order[$position]['all_users_answered'])) {
                        $assignments_order[$position]['all_users_answered'] = true;
                    }
                    // If anyone from this position said something different from YES or NO
                    if (!in_array($answers[$agreement_group_row_num], array(1, 2))) {
                        // Set that not all users have answered
                        $assignments_order[$position]['all_users_answered'] = false;
                    }

                    /**
                     * Check if there is at least one answer for the current position
                     */
                    if (in_array($answers[$agreement_group_row_num], [1, 2])) {
                        // $last_answered_position = $position;
                        $assignments_order[$position]['has_answer'] = true;
                    }
                }
            }
            // Sort the positions
            ksort($assignments_order, SORT_NATURAL);
        }

        // Start transaction
        $db->StartTrans();

        // If there are any users to be assigned (i.e. if there are any approvers)
        if (count(array_filter($assignments_order)) > 0) {
            // Get the current user max position
            $current_user_max_position = '';
            foreach ($assignments_order as $position => $ao) {
                if (in_array($current_user_id, $ao['users'])) {
                    $current_user_max_position = $position;
                }
            }

            /*
             * Define current position and next position
             */
            $current_position_users = array();
            $current_role = '';
            foreach ($assignments_order as $position => $ao) {
                // Get the current assignments order role
                $ao_role = self::extractRoleFromPosition($position);
                // Check if one answer is enough for the current position
                $ao_one_answer_per_position = ($one_answer_per_position
                    && (!$one_answer_per_position_only_for_roles
                        || array_key_exists($ao_role, $one_answer_per_position_only_for_roles)));
                // Check if the current assignments order is answered
                $ao_answered = (!$ao_one_answer_per_position && $ao['all_users_answered']
                    || $ao_one_answer_per_position && !empty($ao['has_answer'])
                    || $ao_role < $currentStatusRole);

                /*
                 * Define the last answered position, which is before the next answered position
                 *
                 * This is used for properly changing the status
                 */
                if (!empty($current_position_users) && $ao_answered) {
                    $last_answered_position_before_next_not_answered_position = $position;
                }

                /*
                 * Define the next position for which we are expecting to answer
                 *
                 * The next position is the one that answers of three conditions:
                 * 1) is after the current
                 * 2) the current assignments order is not answered
                 * 3) the current user is not in it, or he is in it, but there are other users too (this is done to skip
                 *    the current user's answer, because we expect it to have answer (if he actually has no answer the
                 *    $next_not_answered_position var will not be used, so we won't have troubles))
                 */
                if (!empty($current_position_users)
                        && !$ao_answered
                        && (!in_array($current_user_id, $ao['users'])
                            || count(array_unique($ao['users'])) > 1)) {
                    $next_not_answered_position = $position;
                    break;
                }

                /*
                 * Define the current position
                 *
                 * If the user is not setting an approved status then get the first not answered position
                 * but if he's setting an approved status, and he is one of the approving users, get his max position
                 */
                if (empty($current_position_users)
                        && (!$current_user_sets_approved_status && !$ao_answered
                            || $current_user_sets_approved_status
                            && array_key_exists($current_user_id, $approving_users)
                            && $position == $current_user_max_position)) {
                    // This is the current position
                    $current_position_users = $ao['users'];
                    $current_role = $ao_role;
                }
            }

            // Get the current position executors
            $current_position_executors = array_intersect(
                array_keys($document->getAssignments('owner')),
                $current_position_users
            );

            // Prepare a flag to check if the answer of the current user should be checked
            $check_current_user_answer = false;

            // If the current approver is setting an "Approved" status
            if ($current_user_sets_approved_status && array_key_exists($current_user_id, $approving_users)) {
                // Set the user's answer to YES
                $assoc_vars['agreement']['value'] = 1;
                // Set to check its answer
                $check_current_user_answer = true;
                // No other executors (answers) needed
                $current_position_executors = array();

                foreach ($assignments_order as $position => $ao) {
                    // Get the current assignments order role
                    $ao_role = self::extractRoleFromPosition($position);
                    // Check if one answer is enough for the current position
                    $ao_one_answer_per_position = ($one_answer_per_position
                        && (!$one_answer_per_position_only_for_roles
                            || array_key_exists($ao_role, $one_answer_per_position_only_for_roles)));

                    // If we're looking for one answer pes position for the current assignments order
                    if ($ao_one_answer_per_position) {
                        // Mark that the current position has answer
                        $assignments_order[$position]['has_answer'] = true;
                    } else {
                        // Fill YES for all users from the current assignments order
                        $assignments_order[$position]['answers'] = array_fill_keys(array_keys($ao['answers']), 1);
                        $answers = array_replace($answers, $assignments_order[$position]['answers']);

                        // Mark that all users have answered
                        // (we just did that)
                        $assignments_order[$position]['all_users_answered'] = true;

                        // Collect this rows as YES row
                        $yes_rows = array_merge($yes_rows, array_keys($assignments_order[$position]['answers']));
                    }

                    // If this is the highest position for the current user
                    // (one user can be in several positions (just in theory -
                    // there is no use of this))
                    if ($position == $current_user_max_position) {
                        break;
                    }
                }
                // TODO: тук е добре да се има предвид, че ако последната група от оценяващи ($position) на текущия потребител (който в момента задава статус) е минала
                // TODO: т.е. той е от по-задна група, която вече е одобрила, не е предвидено да се зачистят отговорите на другите от групите след него
                // TODO: т.е. ако задаващия статус върне към по-заден - няма да се зачистят отговорите на другите (мога да го направя, но в момента няма време и нужда)
            } else {
                // If the current user is not setting an "Approved" status
                // check if he's one of the current position executors, which will mean that
                // we should check he's answer
                // If there are already any assigned executors
                // I.e. if the approval process has begun
                if ($current_position_executors) {
                    // Check the current user's answer, if he's one of the executors
                    if (in_array($current_user_id, $current_position_executors)) {
                        $check_current_user_answer = true;
                    }
                } else {
                    // Assign the first users
                    $first_assignments = array_keys($assignments_order);
                    $first_assignments = array_shift($first_assignments);
                    $first_assignments = array_unique($assignments_order[$first_assignments]['users']);
                    $assignments_params = array(
                        'model_id'     => $params['model_id'],
                        'module'       => $params['module'],
                        'model'        => $document,
                        'assign_owner' => implode(',', $first_assignments)
                    );
                    if (!$this->assign($assignments_params)) {
                        $assignments_success = false;
                    }

                    // Check the current user's answer, if he's one of the first executors
                    if (in_array($current_user_id, $first_assignments)) {
                        $check_current_user_answer = true;
                    }
                }
            }

            // If the answer of the current user should be checked
            if ($check_current_user_answer) {
                /*
                 * Check current answer
                 */
                // If the current user said YES or NO
                if (in_array($assoc_vars['agreement']['value'], array(1, 2))) {
                    // Get the current user
                    $current_user = $registry['originalUser'];

                    // Get all users who said YES, or we don't expect them to answer anymore
                    $users_answered_yes = array();
                    $users_not_expect_to_answer_more = [];
                    foreach ($assignments_order as $position => $ao) {
                        // Get the current assignments order role
                        $ao_role = self::extractRoleFromPosition($position);
                        // Check if one answer is enough for the current position
                        $ao_one_answer_per_position = ($one_answer_per_position
                            && (!$one_answer_per_position_only_for_roles
                                || array_key_exists($ao_role, $one_answer_per_position_only_for_roles)));
                        foreach ($ao['answers'] as $agreement_group_row_num => $answer) {
                            if ($answer == 1) {
                                $users_answered_yes[] = $ao['users'][$agreement_group_row_num];
                            }
                            if (in_array($answer, [1, 2])
                                    || $ao_one_answer_per_position && (!empty($ao['has_answer']) || in_array($current_user_id, $ao['users']))
                                    || $ao_role <= $currentStatusRole) {
                                $users_not_expect_to_answer_more[$ao['users'][$agreement_group_row_num]] = $ao['users'][$agreement_group_row_num];
                            }
                        }
                    }
                    $users_not_expect_to_answer_more[$current_user_id] = $current_user_id;
                    if ($assoc_vars['agreement']['value'] == 1) {
                        $users_answered_yes[] = $current_user_id;
                    }

                    // Remove from the executors all users from which we don't expect answers anymore
                    $query  = 'DELETE FROM `'. DB_TABLE_DOCUMENTS_ASSIGNMENTS . '`' . "\n" .
                              '  WHERE `parent_id` = \'' . $params['model_id'] . '\'' . "\n" .
                              '    AND `assignments_type` = \'' . PH_ASSIGNMENTS_OWNER . '\'' . "\n" .
                              '    AND `assigned_to` IN (\'' . implode("', '", $users_not_expect_to_answer_more) . '\')';
                    if (!$db->Execute($query)) {
                        $assignments_success = false;
                    }
                    $current_position_executors = array_diff($current_position_executors, $users_not_expect_to_answer_more);

                    // If the current user said YES
                    if ($assoc_vars['agreement']['value'] == 1) {
                        // Get the assigned observers
                        $assigned_observers = $document->getAssignments('observer');
                        $assigned_observers = array_keys($assigned_observers);

                        // Update observers with all users who said YES
                        $assigned_observers = array_unique(array_merge($assigned_observers, $users_answered_yes));
                        $assignments_params = array(
                            'model_id'            => $params['model_id'],
                            'module'              => $params['module'],
                            'model'               => $document,
                            'new_assign_observer' => implode(',', $assigned_observers)
                        );
                        if(!$this->assign($assignments_params)) {
                            $assignments_success = false;
                        }
                    }

                    ///////////////////////////////////////////////////////////////////
                    // Transfer the current answer into the history table of answers //
                    ///////////////////////////////////////////////////////////////////
                    // Get some additional fields for the document
                    $query = 'SELECT `name`, `id`' . "\n" .
                             '  FROM `' . DB_TABLE_FIELDS_META . '`' . "\n" .
                             '  WHERE `model` = \'Document\'' . "\n" .
                             '    AND `model_type` = \'' . $document->get('type') . '\'' . "\n" .
                             '    AND `name` IN (\'prepared_id\', \'prepared_department\', \'agreement_answer\', ' . "\n" .
                             '    \'date_time\', \'prepared_comment\', \'agreement\', \'agreement_comment\', ' . "\n" .
                             '    \'prepared_text\', \'' . $roles_fields_names['prepared_type_act'] . '\', ' . "\n" .
                             '    \'agreement_current_answer\')';
                    $fields = $db->getAssoc($query);

                    // Get the row num that should be used
                    $query = 'SELECT `num`, `value`' . "\n" .
                             '  FROM `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                             '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                             '    AND `var_id` = \'' . $fields['prepared_id'] . '\'' . "\n" .
                             '  ORDER BY `num` DESC' . "\n" .
                             '  LIMIT 0, 1';
                    $prepared_id_max_row = $db->getRow($query);
                    $make_update = false;
                    if (empty($prepared_id_max_row)) {
                        $num = 1;
                    } else {
                        if (empty($prepared_id_max_row['value'])) {
                            $num = $prepared_id_max_row['num'];
                            // Set that an UPDATE should be made (i.e. this means that the last row of the table should be used (updated))
                            $make_update = true;
                        } else {
                            $num = $prepared_id_max_row['num'] + 1;
                        }
                    }

                    /////////////////////////////////////////
                    // Add a row to the history of answers //
                    /////////////////////////////////////////
                    // Prepare the user
                    $history_prepared_id = $current_user->get('employee');

                    // Prepare the user's department and position
                    $history_prepared_department_label = '';
                    foreach ($assoc_vars['agreement_with']['value'] as $agreement_customer_key => $agreement_customer_value) {
                        if ($agreement_customer_value == $current_user->get('employee')) {
                            // Get the department name of the current user
                            $history_prepared_department_label = $assoc_vars['agreement_department']['value'][$agreement_customer_key];
                            // Get the position of the user into the company
                            $history_agreement_position = $assoc_vars['agreement_position']['value'][$agreement_customer_key];
                            break;
                        }
                    }

                    // Prepare the user's answer
                    $history_agreement_answer = $assoc_vars['agreement']['value'];

                    // Prepare the current date and time
                    $history_date_time        = date('Y-m-d H:i:s', time());

                    // Prepare the user's comment
                    $history_prepared_comment = $assoc_vars['agreement_comment']['value'];

                    // Prepare a history text
                    $history_prepared_text = sprintf($this->i18n('documents_prepared_text'),
                        trim($current_user->get('firstname') . ' ' . $current_user->get('lastname')),
                        $history_agreement_position,
                        $history_prepared_department_label,
                        $settings['role_' . $current_role . '_action_label_' . ($assoc_vars['agreement']['value'] == 1 ? 'yes' : 'no')],
                        General::strftime('%d.%m.%Y, %H:%M', $history_date_time),
                        ($history_prepared_comment ?: $this->i18n('documents_prepared_text_no_comment')));

                    // Add a record into the answers history table
                    if ($make_update) {
                        // Update the last (empty) history row
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value`     = \'' . $history_prepared_id . '\',' . "\n" .
                                 '    `modified`    = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id`   = \'' . $fields['prepared_id'] . '\'' . "\n" .
                                 '    AND `num`      = \'' . $num . '\'';
                        if (!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value`     = \'' . $history_prepared_department_label . '\',' . "\n" .
                                 '    `modified`    = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id`   = \'' . $fields['prepared_department'] . '\'' . "\n" .
                                 '    AND `num`      = \'' . $num . '\'';
                        if (!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value`     = \'' . $history_agreement_answer . '\',' . "\n" .
                                 '    `modified`    = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id`   = \'' . $fields['agreement_answer'] . '\'' . "\n" .
                                 '    AND `num`      = \'' . $num . '\'';
                        if (!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value`     = \'' . $history_date_time . '\',' . "\n" .
                                 '    `modified`    = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id`   = \'' . $fields['date_time'] . '\'' . "\n" .
                                 '    AND `num`      = \'' . $num . '\'';
                        if (!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value`     = \'' . $history_prepared_comment . '\',' . "\n" .
                                 '    `modified`    = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id`   = \'' . $fields['prepared_comment'] . '\'' . "\n" .
                                 '    AND `num`      = \'' . $num . '\'';
                        if (!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value`     = \'' . $history_prepared_text . '\',' . "\n" .
                                 '    `modified`    = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id`   = \'' . $fields['prepared_text'] . '\'' . "\n" .
                                 '    AND `num`      = \'' . $num . '\'';
                        if (!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value`     = \'' . $current_role . '\',' . "\n" .
                                 '    `modified`    = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id`   = \'' . $fields[$roles_fields_names['prepared_type_act']] . '\'' . "\n" .
                                 '    AND `num`      = \'' . $num . '\'';
                        if (!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                    } else {
                        // Insert a new history row
                        $query = 'INSERT INTO `' . DB_TABLE_DOCUMENTS_CSTM . '` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES' . "\n" .
                                 '  (\'' . $params['model_id'] . '\', \'' . $fields['prepared_id']                            . '\', \'' . $num . '\', \'' . $history_prepared_id               . '\', NOW(), \'' . $automations_user_id . '\', NOW(), \'' . $automations_user_id . '\', \'\'),' . "\n" .
                                 '  (\'' . $params['model_id'] . '\', \'' . $fields['prepared_department']                    . '\', \'' . $num . '\', \'' . $history_prepared_department_label . '\', NOW(), \'' . $automations_user_id . '\', NOW(), \'' . $automations_user_id . '\', \'\'),' . "\n" .
                                 '  (\'' . $params['model_id'] . '\', \'' . $fields['agreement_answer']                       . '\', \'' . $num . '\', \'' . $history_agreement_answer          . '\', NOW(), \'' . $automations_user_id . '\', NOW(), \'' . $automations_user_id . '\', \'\'),' . "\n" .
                                 '  (\'' . $params['model_id'] . '\', \'' . $fields['date_time']                              . '\', \'' . $num . '\', \'' . $history_date_time                 . '\', NOW(), \'' . $automations_user_id . '\', NOW(), \'' . $automations_user_id . '\', \'\'),' . "\n" .
                                 '  (\'' . $params['model_id'] . '\', \'' . $fields['prepared_comment']                       . '\', \'' . $num . '\', \'' . $history_prepared_comment          . '\', NOW(), \'' . $automations_user_id . '\', NOW(), \'' . $automations_user_id . '\', \'\'),' . "\n" .
                                 '  (\'' . $params['model_id'] . '\', \'' . $fields['prepared_text']                          . '\', \'' . $num . '\', \'' . $history_prepared_text             . '\', NOW(), \'' . $automations_user_id . '\', NOW(), \'' . $automations_user_id . '\', \'\'),' . "\n" .
                                 '  (\'' . $params['model_id'] . '\', \'' . $fields[$roles_fields_names['prepared_type_act']] . '\', \'' . $num . '\', \'' . $current_role                      . '\', NOW(), \'' . $automations_user_id . '\', NOW(), \'' . $automations_user_id . '\', \'\')';
                        if(!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                    }
                    /////////////////////////////////////////////////
                    // END OF: Add a row to the history of answers //
                    /////////////////////////////////////////////////

                    /*
                     * Write the current answer
                     */
                    // If the current answer is YES
                    if ($assoc_vars['agreement']['value'] == 1) {
                        // Write YES for all YES rows (i.e. for the current user
                        // and may be for all users before him (if he has set
                        // an "Approved" status))
                        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS_CSTM . '`' . "\n" .
                                 '  SET `value` = \'1\',' . "\n" .
                                 '    `modified` = NOW(),' . "\n" .
                                 '    `modified_by` = \'' . $automations_user_id . '\'' . "\n" .
                                 '  WHERE `model_id` = \'' . $params['model_id'] . '\'' . "\n" .
                                 '    AND `var_id` = \'' . $fields['agreement_current_answer'] . '\'' . "\n" .
                                 '    AND `num` IN (\'' . implode('\', \'', $yes_rows) . '\')';
                        if(!$db->Execute($query)) {
                            $answers_history_success = false;
                        }
                    } else if ($assoc_vars['agreement']['value'] == 2) {
                        // If the current answer is NO
                        // remove all answers, because the agreement process will be reset
                        if (!$this->_clearApprovingUsersAnswers($document)) {
                            $answers_history_success = false;
                        }
                    }
                    ///////////////////////////////////////////////////////////////////////////
                    // END OF: Transfer the current answer into the history table of answers //
                    ///////////////////////////////////////////////////////////////////////////
                }

                // If the current user said nothing
                if (empty($assoc_vars['agreement']['value']) || !in_array($assoc_vars['agreement']['value'], array(1, 2))) {
                    // Do nothing
                    // I.e.: Exit the automation
                } else if ($assoc_vars['agreement']['value'] == 1) {
                    /*
                     * If he said YES
                     */
                    // If there are any other executors
                    if ($current_position_executors) {
                        // Do nothing more, so the others can tell their answers too
                    } else {
                        // If there is a next position (i.e. if there are any users left to be assigned)
                        if (isset($next_not_answered_position)) {
                            // If the next role is different from the current one
                            // then change the status to the corresponding for the current role
                            if (self::extractRoleFromPosition($next_not_answered_position) != $current_role) {
                                if (isset($last_answered_position_before_next_not_answered_position)) {
                                    $last_answered_role_before_next_not_answered_position = self::extractRoleFromPosition($last_answered_position_before_next_not_answered_position);
                                } else {
                                    $last_answered_role_before_next_not_answered_position = $current_role;
                                }
                                $status_params = array(
                                    'model_id'      => $params['model_id'],
                                    'module'        => $params['module'],
                                    'model'         => $document,
                                    'new_status'    => $settings["role_{$last_answered_role_before_next_not_answered_position}_status"],
                                    'new_substatus' => (empty($settings["role_{$last_answered_role_before_next_not_answered_position}_substatus"]) ? '' : $settings["role_{$last_answered_role_before_next_not_answered_position}_substatus"])
                                );
                                if ($this->status($status_params)) {
                                    $registry['messages']->setMessage($this->i18n('message_documents_status_success', array($document->getModelTypeName())));
                                } else {
                                    $status_success = false;
                                }
                            }

                            // Assign the next users as executors
                            $next_executors = array_filter($assignments_order[$next_not_answered_position]['answers'], function ($v) { return !in_array($v, array(1, 2)); });
                            $next_executors = array_intersect_key($assignments_order[$next_not_answered_position]['users'], $next_executors);
                            $assignments_params = array(
                                'model_id'   => $params['model_id'],
                                'module'     => $params['module'],
                                'model'      => $document,
                                'assign_owner' => implode(',', $next_executors)
                            );
                            if (!$this->assign($assignments_params)) {
                                $assignments_success = false;
                            }
                        } else {
                            // Lock the document (i.e. Finish)
                            $status_params = array(
                                'model_id'      => $params['model_id'],
                                'module'        => $params['module'],
                                'model'         => $document,
                                'new_status'    => $settings['assignments_finished_status'],
                                'new_substatus' => (empty($settings['assignments_finished_substatus']) ? '' : $settings['assignments_finished_substatus'])
                            );
                            if ($this->status($status_params)) {
                                $registry['messages']->setMessage($this->i18n('message_documents_status_success', array($document->getModelTypeName())));
                            } else {
                                $status_success = false;
                            }
                        }
                    }
                } else if ($assoc_vars['agreement']['value'] == 2) {
                    /*
                     * If he said NO - reset the approval process and set the "Returned" status
                     */
                    if (!$this->_setAnswerNo($params, $document)) {
                        $assignments_success = false;
                    }
                    $status_params = array(
                        'model_id'      => $params['model_id'],
                        'module'        => $params['module'],
                        'model'         => $document,
                        'new_status'    => $settings['assignments_returned_status'],
                        'new_substatus' => (empty($settings['assignments_returned_substatus']) ? '' : $settings['assignments_returned_substatus'])
                    );
                    if ($this->status($status_params)) {
                        $registry['messages']->setMessage($this->i18n('message_documents_status_success', array($document->getModelTypeName())));
                    } else {
                        $status_success = false;
                    }
                }
            }
        } elseif(!isset($settings['change_status_on_empty_agreement']) || $settings['change_status_on_empty_agreement'] != 'DO_NOT_CHANGE') {
            // Lock the document (i.e. Finish)
            $this->setOriginalUserAsCurrent();
            $status_params = array(
                'model_id'      => $params['model_id'],
                'module'        => $params['module'],
                'model'         => $document,
                'new_status'    => $settings['assignments_finished_status'],
                'new_substatus' => (empty($settings['assignments_finished_substatus']) ? '' : $settings['assignments_finished_substatus'])
            );
            if ($this->status($status_params)) {
                $this->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');
                $doc_status_name = $this->i18n('documents_status_' . $status_params['new_status']);
                $doc_substatus_name = $db->GetOne('SELECT name FROM ' . DB_TABLE_DOCUMENTS_STATUSES . " WHERE id = '{$status_params['new_substatus']}' AND lang = '{$document->get('model_lang')}'");
                if (!empty($doc_substatus_name)) {
                    $registry['messages']->setMessage($this->i18n('documents_auto_status_substatus', array($doc_status_name, $doc_substatus_name)));
                } else {
                    $registry['messages']->setMessage($this->i18n('documents_auto_status', array($doc_status_name)));
                }
            } else {
                $registry['messages']->setError($this->i18n('error_documents_status_automation'));
            }
            $this->setAutomationUserAsCurrent();
        }

        // Always clear the current answer
        $query = 'UPDATE `' . DB_TABLE_FIELDS_META . '` AS `fm`' . "\n" .
                 '  JOIN `' . DB_TABLE_DOCUMENTS_CSTM . '` AS `dc`' . "\n" .
                 '    ON (`fm`.`model` = \'Document\'' . "\n" .
                 '      AND `fm`.`model_type` = \'' . $params['model']->get('type') . '\'' . "\n" .
                 '      AND `fm`.`name` IN (\'agreement\', \'agreement_comment\')' . "\n" .
                 '      AND `dc`.`var_id` = `fm`.`id`' . "\n" .
                 '      AND `dc`.`model_id` = \'' . $params['model_id'] . '\')' . "\n" .
                 '  SET `dc`.`value` = \'\',' . "\n" .
                 '    `dc`.`modified` = NOW(),' . "\n" .
                 '    `dc`.`modified_by` = \'' . $automations_user_id . '\'';
        if (!$db->Execute($query)) {
            $answers_history_success = false;
        }

        // If any of the actions has failed
        if (!$assignments_success || !$status_success || !$answers_history_success) {
            // Raise an error messages
            if (!$assignments_success) {
                $registry['messages']->setError($this->i18n('error_documents_assignments_automation'));
            }
            if (!$status_success) {
                $registry['messages']->setError($this->i18n('error_documents_status_automation'));
            }
            if (!$answers_history_success) {
                $registry['messages']->setError($this->i18n('error_documents_answers_history_automation'));
            }
        }

        // Check for missed errors
        if ($registry['messages']->getErrors()) {
            // Fail the transaction
            $db->FailTrans();
        } else if ($db->HasFailedTrans()) {
            $registry['messages']->setError($this->i18n('error_technical_error_please_contact_nzoom_support'));
        }

        // Complete the transaction
        $db->CompleteTrans();

        // Insert the messages into the session
        $registry['messages']->insertInSession($registry);

        // Reflect the changes made by the current automation to the model, so it can be used from subsequent automations
        $new_model = Documents::searchOne($registry, array('where' => array("d.id = '{$params['model']->get('id')}'"), 'model_lang' => $params['model']->get('model_lang')));
        $params['model']->set('status',    $new_model->get('status'),    true);
        $params['model']->set('substatus', $new_model->get('substatus'), true);
        $assignment_types = array('owner', 'responsible', 'observer', 'decision');
        foreach ($assignment_types as $at) {
            $at = 'assignments_' . $at;
            if ($new_model->isDefined($at)) {
                $params['model']->set($at, $new_model->get($at), true);
            }
        }
        $registry->set('get_old_vars', true, true);
        $params['model']->set('plain_vars', null, true);
        $params['model']->getVars();
        $registry->set('get_old_vars', false, true);

        // If the document is not opened, redirect to 'view', because 'edit' will be not allowed
        // This helps when the status was changed manually from user (in this case, the 'setstatus' action will set
        // 'edit' as after_action) and after that the automation will change the status to locked (for example) and
        // then 'edit' will be not accessible, but the after_action will redirect to 'edit' and then an error will be
        // shown, that the user has no access to this action
        if ($new_model->get('status') != 'opened') {
            $registry['request']->set('after_action', 'view', 'get', true);
        }

        // Exit the automation
        return true;
    }

    /**
     * Extract the role from the position string, used into setDocumentAssignments
     * @param string $position Example: 6_3, where 6 is a role and 3 is a row num
     * @return string The role, which in the current example is 6
     */
    private static function extractRoleFromPosition($position) {
        [$role] = explode('_', $position);
        return $role;
    }

    /**
     * Do what's needed when a user answered "No"
     *
     * @param $params - the automations params
     * @param $document - the document, for which the user said NO
     *
     * @return bool - the result of the assignments changes
     */
    private function _setAnswerNo($params, $document) {
        // Prepare the result of the assignments changes
        $assignments_success = true;

        // Prepare the database object
        $db = $this->registry['db'];

        // Clear the answers of all approving users
        if (!$this->_clearApprovingUsersAnswers($document)) {
            $assignments_success = false;
        }

        // Remove all observers except the creator of the document
        $query  = 'DELETE FROM `'. DB_TABLE_DOCUMENTS_ASSIGNMENTS . '`' . "\n" .
                  '  WHERE `parent_id` = \'' . $params['model_id'] . '\'' . "\n" .
                  '    AND `assignments_type` = \'' . PH_ASSIGNMENTS_OBSERVER . '\'' . "\n" .
                  '    AND `assigned_to` != \'' . $document->get('added_by') . '\'';
        if (!$db->Execute($query)) {
            $assignments_success = false;
        }

        // Assign the creator of the document as the only executor
        $assignments_params = array(
            'model_id'     => $params['model_id'],
            'module'       => $params['module'],
            'model'        => $document,
            'assign_owner' => $document->get('added_by')
        );
        if (!$this->assign($assignments_params)) {
            $assignments_success = false;
        }

        return $assignments_success;
    }

    /**
     * Clear the answers of all approving users
     *
     * @param $document - the document in which to clear the answers
     *
     * @return bool - the result of the operation
     */
    private function _clearApprovingUsersAnswers($document) {
        $query = "
            UPDATE " . DB_TABLE_FIELDS_META . " AS fm
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                ON (fm.model = 'Document'
                  AND fm.model_type = '{$document->get('type')}'
                  AND fm.name = 'agreement_current_answer'
                  AND dc.var_id = fm.id
                  AND dc.model_id = '{$document->get('id')}')
              SET dc.value = '',
                dc.modified = NOW(),
                dc.modified_by = '{$this->automationUser->get('id')}'";
        return $this->registry['db']->Execute($query);
    }

    /**
     * Set default switchboard for a document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function setDocumentDefaultSwitchboard($params) {
        // Check the conditions to execute the automation
        if ($params['module'] == 'documents' && $this->registry['action'] == 'add') {
            // If there is an employee saved for this document
            if ($params['model']->get('employee')) {
                // Get the employee (i.e. the customer)
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $customer_filters = array(
                    'where'      => array('c.id = \'' . $params['model']->get('employee') . '\''),
                    'model_lang' => $params['model']->get('model_lang')
                );
                $customer = Customers::searchOne($this->registry, $customer_filters);

                // If there is such customer and there is a switchboard selected for it
                if ($customer && $customer->getVarValue('switchboard_id')) {
                    // Get the document
                    $document = &$params['model'];

                    // Get the document vars
                    if (!$document->isDefined('vars')) {
                        $document->getVars();
                    }
                    $document_vars = $document->get('vars');

                    // Set a flag to check if the document vars should be changed
                    $change_document_vars = false;

                    // Go through all document vars
                    foreach ($document_vars as $document_var_key => $document_var) {
                        if ($document_var['name'] == 'switchboard_id') {
                            // Set the switchboard_id (get the value from the switchboard_id of the customer)
                            $document_vars[$document_var_key]['value'] = $customer->getVarValue('switchboard_id');
                            // Set that the document vars should be changed
                            $change_document_vars                      = true;
                        } elseif ($document_var['name'] == 'switchboard') {
                            // Set the switchboard (get the value from the switchboard of the customer)
                            $document_vars[$document_var_key]['value'] = $customer->getVarValue('switchboard');
                            // Set that the document vars should be changed
                            $change_document_vars                      = true;
                        }
                    }

                    // If the document vars should be changed
                    if ($change_document_vars) {
                        // Get the registry
                        $registry = &$this->registry;

                        // Temporarily allow edit of all layouts
                        $registry->set('edit_all', true, true);

                        // Get the old document model
                        $old_document = clone $document;

                        // Set the document vars
                        $document->set('vars', $document_vars, true);

                        // If the document is sanitized, then unsanitize it
                        if ($document->isSanitized()) {
                            $document->unsanitize();
                        }

                        // If the document vars are saved successfully
                        if ($document->saveVars()) {
                            // Make history for this change
                            if (!$document->isSanitized()) {
                                $document->sanitize();
                            }
                            $document_filters = array(
                                'where'                  => array('d.id = \'' . $document->get('id') . '\''),
                                'model_lang'             => $document->get('model_lang'),
                                'skip_assignments'       => true,
                                'skip_permissions_check' => true);
                            $new_document = Documents::searchOne($registry, $document_filters);
                            $registry->set('get_old_vars', true, true);
                            $new_document->getVars();
                            $audit_parent = Documents_History::saveData($registry, array(
                                'model'       => $new_document,
                                'action_type' => 'edit',
                                'new_model'   => $new_document,
                                'old_model'   => $old_document));
                        }

                        // Remove: allow edit of all layouts
                        $registry->remove('edit_all');
                    }
                }
            }
        }

        return true;
    }

    /**
     * Function to custom numerate the document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function customNumerateDocument($params) {
        // Prepare the registry
        $registry = &$this->registry;

        // Get the setting for this automation
        $settings = $this->settings;

        // the current model from the database
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        $filters = array('where'      => array('d.id = ' . $params['model']->get('id')),
                         'model_lang' => $params['model']->get('model_lang'),
                         'sanitize'   => true);
        $document = Documents::searchOne($registry, $filters);

        // execute flag
        $execute = false;

        // check if statuses triggering the automations are selected
        if (!empty($this->settings['automation_start_status']) || !empty($this->settings['automation_start_substatus'])) {
            // if they are selected then checks them
            if ((empty($this->settings['automation_start_status']) || $this->settings['automation_start_status'] == $document->get('status')) && (empty($this->settings['automation_start_substatus']) || $this->settings['automation_start_substatus'] == $document->get('substatus'))) {
                $execute = true;
            }
        }

        if (!$execute || empty($this->settings['document_nom_var']) || empty($this->settings['nom_var_name'])) {
            // if the automations is not supposed to be executed the further actions are canceled
            return false;
        }

        // get the selected nomenclature
        $nomenclature_id = $document->getVarValue($this->settings['document_nom_var']);

        if (!$nomenclature_id) {
            // if no nomenclature is selected then further actions are canceled
            return false;
        }

        // get the required data from the nomenclature
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        $filters_nom = array('where'      => array('n.id = ' . $nomenclature_id),
                             'model_lang' => $params['model']->get('model_lang'),
                             'sanitize'   => true
        );
        $nomenclature = Nomenclatures::searchOne($registry, $filters_nom);

        if (empty($nomenclature)) {
            // if the nomenclature does not exist then further actions are canceled
            return false;
        }

        // get the current value of the nom counter
        $current_counter_value = intval($nomenclature->getVarValue($this->settings['nom_var_name']));

        // increments the current counter
        $current_counter_value++;

        // clone the old model, so it will be used to mark the update in the history
        $old_model = clone $document;

        // $document->unsanitize();

        // update the full num
        $query = 'UPDATE `' . DB_TABLE_DOCUMENTS . '`' . "\n" .
                 '  SET `full_num` = \'' . sprintf('%s-%d', $nomenclature->get('code'), $current_counter_value) . '\'' . "\n" .
                 '  WHERE `id` = \'' . $document->get('id') . '\'';
        $registry['db']->Execute($query);

        if ($registry['db']->ErrorMsg()) {
            // error occurred
            return false;
        }

        // Get the new model
        $filters   = array('where'    => array('d.id = ' . $document->get('id')),
                           'sanitize' => true);
        $new_model = Documents::searchOne($registry, $filters);

        // Prepare the history vars
        $history_vars = array('model'       => $new_model,
                              'action_type' => 'edit',
                              'new_model'   => $new_model,
                              'old_model'   => $old_model);

        // Save this change in the history of the current document
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
        Documents_History::saveData($registry, $history_vars);

        // updates the nomenclature
        $nom_vars = $nomenclature->getAssocVars();
        if (isset($nom_vars[$this->settings['nom_var_name']])) {
            $id_var = $nom_vars[$this->settings['nom_var_name']]['id'];

            // clone the old nomenclature
            $old_nom = clone $nomenclature;

            // Update the counter
            $query = 'UPDATE `' . DB_TABLE_NOMENCLATURES_CSTM . '`' . "\n" .
                     '  SET `value`     = \'' . $current_counter_value . '\',' . "\n" .
                     '    `modified`    = NOW(),' . "\n" .
                     '    `modified_by` = \'' . $registry['currentUser']->get('id') . '\'' . "\n" .
                     '  WHERE `model_id` = \'' . $nomenclature->get('id') . '\'' . "\n" .
                     '    AND `var_id` = \'' . $id_var . '\'';
            $registry['db']->Execute($query);

            // Get the updated nomenclature
            $filters = array('where'    => array('n.id = ' . $nomenclature->get('id')),
                             'sanitize' => true);
            $new_nom = Nomenclatures::searchOne($registry, $filters);

            // Prepare the history vars
            $history_vars = array('model'       => $new_nom,
                                  'action_type' => 'edit',
                                  'new_model'   => $new_nom,
                                  'old_model'   => $old_nom);

            // Save this change in the history of the current nomenclature
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.audit.php';
            Nomenclatures_History::saveData($registry, $history_vars);

            return true;
        }
    }
}
