<?php

class Artstroy_Automations_Controller extends Automations_Controller {

    /**
     * Creates warehouse for project (construction area)
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function createWarehouse($params) {

        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance_warehouses.ini');

        $warehouse = new Finance_Warehouse(
            $this->registry,
            array_merge(
                $this->settings,
                array(
                    'name' => $params['model']->get('name'),
                    'location' => $params['model']->getVarValue($this->settings['location_var']),
                    'group' => PH_ROOT_GROUP,
                    'active' => 1
                )
        ));

        if ($warehouse->save()) {
            // save history
            $filters = array(
                'where' => array('fwh.id = ' . $warehouse->get('id')),
                'model_lang' => $warehouse->get('model_lang')
            );
            $new_warehouse = Finance_Warehouses::searchOne($this->registry, $filters);

            Finance_Warehouses_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'add',
                    'new_model' => $new_warehouse,
                    'old_model' => $warehouse
                )
            );

            // display message with link
            $url = sprintf(
                '%s/index.php?%s=finance&%s=warehouses&warehouses=view&view=%d',
                $this->registry['config']->getParam('crontab', 'base_host'),
                $this->registry['module_param'],
                $this->registry['controller_param'],
                $warehouse->get('id')
            );
            $this->registry['messages']->setMessage(
                $this->i18n('plugin_message_finance_warehouse_add_success',
                    array('<a href="' . $url . '" target="_blank">', '</a>')
                )
            );
        } else {
            // display error message
            $this->registry['messages']->setError($this->i18n('error_finance_warehouse_add_failed'), '', -2);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return $warehouse->isDefined('id');
    }

    /**
     * Automation that sets or removes tag to a project
     * which participate in documents with specified substatus
     * It also changes the customer of the project if necessary
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the execution of the method
     */
    public function tagProject($params) {
        $for_remove = isset($this->settings['remove']);

        $document = $params['model'];
        $model_lang = $document->get('model_lang');
        $db = $this->registry['db'];

        // tag will be added to the project with that id
        $project_id = (int)$document->getPlainVarValue('object_id');

        if (!$project_id) {
            return false;
        }

        // project
        $project = $this->_getProjectById($project_id, $model_lang);
        if (!in_array($project->get('type'), array(3, 4, 5))) {
            return false;
        }
        $old_project = clone $project;
        $old_project->getModelTagsForAudit();
        $old_project->sanitize();

        // tag id that we must add to project
        $project_tag = $this->settings['project_tag'];

        // get current project tags
        $project->getTags();

        $project_tags = $project->get('tags');

        //start transaction
        $db->StartTrans();

        $success = false;
        // if tag does not exists already and must be set
        if (!in_array($project_tag, $project_tags) && !$for_remove) {
            $project->set('tags', array($project_tag), true);
            // save history if operation of updating new tags not fail
            list($success,$project) = $project->updateTags() ? $this->_saveHistory($project, $model_lang, $old_project, 'tag') : false;
            list($success, $project) = $this->_setNewCustomerToProject($project, 5);
        }

        // if tag already exists in a project and must be deleted
        if (in_array($project_tag, $project_tags) && $for_remove && $project->deleteTags(array($project_tag))) {
            // save history if operation of delete tags not fail
            list($success,$project) = $this->_saveHistory($project, $model_lang, $old_project, 'tag');
            list($success, $project) = $this->_setNewCustomerToProject($project, $document->get('customer'));
        }

        // If the tag already exists and must be set
        if (!$for_remove && in_array($project_tag, $project_tags)) {
            // None of the above operations were failed
            $success = true;
            // Message telling us that the tag already was set to this project
            $warning = true;
        }

        if (!$success) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $result = !$db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        $url = sprintf('%s?%s=projects&amp;projects=view&amp;view=%s&amp;model_lang=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], $project_id, $model_lang);

        $project_tag_name = $this->_getTagNameById($project_tag);
        // transaction result
        if (!$result) {
            // transaction failed
            if ($params['automation_type'] != 'crontab') {
                $err_param = $for_remove ? 'automations_artstroy_tagproject_remove_tag_failed' : 'automations_artstroy_tagproject_set_tag_failed';
                $this->registry['messages']->setError(sprintf($this->i18n($err_param), $project_tag_name, $url, $project->getModelTypeName()));
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $action = $for_remove ? 'remove' : 'set';
                $this->executionErrors[] = "Failed to {$action} tag (ID: {$project_tag}) for project with ID: {$project->get('id')}!";
            }
        } else if (!isset($warning) && $params['automation_type'] != 'crontab') {
            $msg_param = $for_remove ? 'automations_artstroy_tagproject_remove_tag_success' : 'automations_artstroy_tagproject_set_tag_success';
            $this->registry['messages']->setMessage(sprintf($this->i18n($msg_param), $project_tag_name, $url, $project->getModelTypeName()));
            $this->registry['messages']->insertInSession($this->registry);
        }

        return $result;
    }

    /**
     * Saves project history
     * @param object $project - current project
     * @param string $model_lang - model language
     * @param object $old_project - old version of the project
     * @param string $action_type - tag, edit etc
     * @return array
     */
    private function _saveHistory($project, $model_lang, $old_project, $action_type) {

        // get the new project from the db cause the history
        $project_id = $project->get('id');
        $new_project = $this->_getProjectById($project_id, $model_lang);

        if ($action_type=='tag') {
            $new_project->getModelTagsForAudit();
        }

        // save history when project tag is changed
        $history = Projects_History::saveData($this->registry, array('model' => $project, 'action_type' => $action_type, 'new_model' => $new_project, 'old_model' => $old_project));
        if ($history) {
            $new_project->sendTagNotification($old_project->get('tag_names_for_audit'));
        }

        return array($history, clone $new_project);
    }

    /**
     * Get a project by id and model_lang
     * @param int $project_id
     * @param string $model_lang
     * @return object
     */
    private function _getProjectById($project_id, $model_lang = 'bg') {
        $filters = array('where' => array('p.id = ' . $project_id), 'model_lang' => $model_lang);
        return Projects::searchOne($this->registry, $filters);
    }

    /**
     * Get a tag name by id
     * @param int $tag_id
     * @return string
     */
    private function _getTagNameById($tag_id) {
        $query = 'SELECT name' . "\n" .
            'FROM ' . DB_TABLE_TAGS_I18N . "\n" .
            'WHERE parent_id=' . $tag_id . "\n" .
            'AND lang="' . $this->registry['lang'] . '" LIMIT 1';
        return $this->registry['db']->GetOne($query);
    }

    /**
     * Changes the customer of the project if necessary
     * @param object $project
     * @param int $document_customer_id
     * @return array
     */
    private function _setNewCustomerToProject($project, $document_customer_id) {
        $success = false;
        if ($document_customer_id != $project->get('customer')) {
            $project->set('customer', $document_customer_id, true);
            if ($project->save()) {
                list($success, $project) = $this->_saveHistory($project, $model_lang, $old_project, 'edit');
            }
        }else{
            $success = true;
        }
        return array($success, $project);
    }

    /**
     * Check if customer has an accauntant note(business card) and if it is completed properly
     * @param array $params - arguments for the method, containing registry
     * @return boolean
     */
    public function beforePotentialClient($params) {
        $model = $params['model'];
        $model_lang = $model->get('model_lang');
        $customer_id = $this->registry['db']->GetOne("SELECT id FROM " . DB_TABLE_CUSTOMERS . " WHERE `type` = 2 AND id = '{$model->get('customer')}'");
        if (!$customer_id) {
            return true;
        }

        // gets the customer's business card
        $query = "
            select cc.value from
            ".DB_TABLE_FIELDS_META." as fm
            JOIN ".DB_TABLE_CUSTOMERS_CSTM." AS cc
              ON(fm.name = 'accountant_notes_id'
                AND fm.model = 'Customer'
                AND fm.model_type = 2
                AND cc.var_id = fm.id
                AND cc.model_id = '{$customer_id}'
                AND cc.num = 1
                AND cc.value != ''
                AND cc.lang IN('', '{$model_lang}'))";

        $accountant_note_id = $this->registry['db']->GetOne($query);
        // If the client does not have a business card, the document fails
        if(!$accountant_note_id){
            $this->registry['messages']->setError(sprintf($this->i18n('automations_artstroy_no_accountant_note')));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        $query = "
          SELECT COUNT(*)
              FROM ".DB_TABLE_DOCUMENTS." AS d
              JOIN ".DB_TABLE_FIELDS_META." AS fm
                ON (d.active = 1
                  AND d.deleted_by = 0
                  AND d.id={$accountant_note_id}
                  AND d.`type` = 18
                  AND fm.model_type = d.`type`
                  AND fm.name = 'budgete__info'
                  AND fm.model = 'Document')
              JOIN ".DB_TABLE_DOCUMENTS_CSTM." AS dc
                ON (dc.var_id = fm.id
                  AND dc.model_id = d.id
                  AND dc.`value` != ''
                  AND dc.num = 1
                  AND dc.lang IN ('', '{$model_lang}'))";
        $required_fields = array('property_type__info', 'level_completion__info', 'level_completion_bds__info', 'bedrooms__info', 'property_size__info');
        foreach ($required_fields as $key => $required) {
            $n = $key+2;
            $query.="
                JOIN ".DB_TABLE_FIELDS_META." AS fm$n
                  ON (fm$n.model_type= d.`type`
                    AND fm$n.name='{$required}'
                    AND fm$n.model='Document')
                JOIN ".DB_TABLE_DOCUMENTS_CSTM." AS dc$n
                  ON (dc$n.var_id = fm$n.id
                    AND dc$n.model_id = d.id
                    AND dc$n.`value`!=''
                    AND dc$n.num = 1
                    AND dc$n.lang IN ('', '{$model_lang}'))";
        }

        // If the card is not completed, the document fails
        if (!$this->registry['db']->GetOne($query)) {
            $url = sprintf('%s?%s=documents&amp;documents=view&amp;view=%s&amp;model_lang=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], $accountant_note_id, $model_lang);
            $this->registry['messages']->setError(sprintf($this->i18n('automations_missing_accountant_fields'), $url));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }
        return true;
    }

    /**
     * Adds a 'potential client' tag to the customer
     * @param array $params
     * @return boolean
     */
    public function potentialClient($params) {
        $model = $params['model'];
        $model_lang = $model->get('model_lang');
        $customer_id = $this->registry['db']->GetOne("SELECT id FROM " . DB_TABLE_CUSTOMERS . " WHERE `type` = 2 AND id = '{$model->get('customer')}'");
        if (!$customer_id) {
            return true;
        }
        $this->settings['potential_client_tag_id'] = 24;
        $this->settings['new_client_tag_id'] = 20;

        $filters = array('where' => array('c.id = ' . $customer_id), 'model_lang' => $model_lang);
        $customer = Customers::searchOne($this->registry, $filters);
        $old_customer = clone $customer;
        $old_customer->getModelTagsForAudit();
        $new_customer_tags = $old_customer->get('tag_names_for_audit');
        $tags_have_changes = false;
        if (!array_key_exists($this->settings['potential_client_tag_id'], $new_customer_tags)) {
            $new_customer_tags[$this->settings['potential_client_tag_id']] = true;
            $tags_have_changes = true;
        }
        if (array_key_exists($this->settings['new_client_tag_id'], $new_customer_tags)) {
            unset($new_customer_tags[$this->settings['new_client_tag_id']]);
            $tags_have_changes = true;
        }
        $old_customer->sanitize();
        $customer->getTags();
        $customer_tags = $customer->get('tags');

        $result = true;
        if ($tags_have_changes) {
            $result = false;
            $customer->set('tags', array_keys($new_customer_tags), true);

            $this->registry['db']->StartTrans();

            if ($customer->updateTags()) {
                $new_customer = Customers::searchOne($this->registry, $filters);
                $new_customer->getModelTagsForAudit();
                // save history
                $history = Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'tag', 'new_model' => $new_customer, 'old_model' => $old_customer));
                if ($history) {
                    //$new_customer->sendTagNotification($old_customer->get('tag_names_for_audit'));
                    $this->executeActionAutomations($old_customer, $new_customer, 'tag');
                    $result = true;
                }
            }
            if (!$result) {
                $this->registry['db']->FailTrans();
            }
            $result = !$this->registry['db']->HasFailedTrans();
            $this->registry['db']->CompleteTrans();
            if ($result) {
                $tag_name = $new_customer->get('tag_names_for_audit');
                $tag_name = $tag_name[$potential_client_tag_id];
                $url = sprintf('%s?%s=customers&amp;customers=history&amp;history=%s&amp;model_lang=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], $customer_id, $model_lang);
                $this->registry['messages']->setMessage(sprintf($this->i18n('automations_artstroy_success'), $tag_name, $url));
            } else {
                // something go wrong and trans failed
                $this->registry['messages']->setError(sprintf($this->i18n('automations_artstroy_fail_transaction')));
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
        return $result;
    }

    /**
     * Sends audit changes in custom e-mail
     *
     * @param string $module - module of automation
     * @param object $new_model - model
     */
    public function sendAuditNotification($params) {
        $settings = $this->getSettings($params);

        // execute only if the required settings are completed
        if (!empty($settings['email_template']) && !empty($settings['recipients']) && !empty($settings['recipients_type'])) {

            $module = $params['module'];
            $controller = $params['controller'];

            $file_name = sprintf('%s.audit.viewer.php', ($module . ($controller ? '.' . $controller : '')));
            require_once PH_MODULES_DIR . $module . '/viewers/' . $file_name;

            // find the needed history table
            if ($module == 'finance') {
                $db_table = DB_TABLE_FINANCE_HISTORY;
            } else {
                $db_table = constant('DB_TABLE_' . strtoupper($params['module']) . '_HISTORY');
            }

            $sql = 'SELECT h_id, action_type' . "\n" .
                   'FROM ' . $db_table . "\n" .
                   'WHERE `model`="' . $params['model']->modelName . '" AND ' .
                   '      `model_id`="' . $params['model']->get('id') . '" AND ' .
                   '      `h_date`>"' . General::strftime($this->i18n('date_iso'), strtotime('-10 seconds')) .  '" AND ' .
                   '      `h_date`<="' . General::strftime($this->i18n('date_iso')) .  '"';
            $history_data = $this->registry['db']->GetAll($sql);

            // define history class name
            $class_name_elements = array($params['module']);
            $class_name_elements = array_merge($class_name_elements, explode('_', $params['controller']));

            foreach ($class_name_elements as $key => $class_name_element) {
                $class_name_elements[$key] = ucfirst($class_name_element);
            }
            $class_name_elements = array_filter($class_name_elements);

            $class_name = implode('_', $class_name_elements) . '_Audit';

            $history_records = array();
            foreach ($history_data as $hist) {
                $history_records[] = $class_name::getData(
                    $this->registry,
                    array(
                        'parent_id'  => $hist['h_id'],
                        'model_name' => $params['model']->modelName,
                        'archive'    => $params['model']->get('archive')
                    )
                );
            }

            // construct the correct history array
            $history_data = array();
            foreach ($history_records as $history_recs) {
                foreach ($history_recs['vars'] as $history_vars) {
                    if (!isset($history_data[$history_vars['field_name']])) {
                        // var is still not set so set it
                        $history_data[$history_vars['field_name']] = $history_vars;
                    } else {
                        // var is already set so just set the new value
                        $history_data[$history_vars['field_name']]['field_value'] = $history_vars['field_value'];
                        $history_data[$history_vars['field_name']]['label'] = $history_vars['label'];
                    }
                }
            }
            unset($history_records);

            // check the history vars for the equal current and old var
            // if such exists, unset it
            foreach ($history_data as $var_name => $var_history) {
                if ($var_history['field_value'] === $var_history['old_value']) {
                    unset($history_data[$var_name]);
                }
            }

            // if any history vars are recorded send e-mail
            if (!empty($history_data)) {
                // prepare the data for the e-mail
                require_once PH_MODULES_DIR . $module . '/viewers/' . $module  . ($controller ? '.' . $controller : '') . '.audit.viewer.php';
                $viewer_class_name = implode('_', $class_name_elements) . '_Audit_Viewer';

                $auditViewer = new Viewer($this->registry);
                $auditViewer->templatesDir = $this->registry['theme']->templatesDir;
                $auditViewer->setTemplate('_audit_email.html');
                $auditViewer->setFrameset('frameset_blank.html');

                //prepare the title for the audit table
                $audit_title = sprintf($this->i18n('record_changed_by_at'), sprintf('%s %s', $this->registry['originalUser']->get('firstname'), $this->registry['originalUser']->get('lastname')), date('d.m.Y, H:i'));
                $auditViewer->data['audit_title'] = $audit_title;
                $auditViewer->data['audit'] = array('vars' => $history_data);

                $audit_data = $auditViewer->fetch();

                // get the email template
                $query = sprintf(
                    'SELECT ei18n.parent_id AS id, ei18n.subject, ei18n.body FROM %s as e LEFT JOIN %s as ei18n ON (ei18n.parent_id=e.id AND ei18n.lang="%s") WHERE e.name="%s"',
                    DB_TABLE_EMAILS,
                    DB_TABLE_EMAILS_I18N,
                    $this->registry['lang'],
                    $settings['email_template']
                );
                $email_template = $this->registry['db']->GetRow($query);

                if ($email_template) {
                    // define the recipients and recipients' e-mails
                    $recipients = preg_split('#\s*,\s*#', $settings['recipients']);
                    $recipients = array_filter($recipients);

                    $get_old_vars = $this->registry->get('get_old_vars');
                    $this->registry->set('get_old_vars', true, true);
                    $assoc_vars = $params['model']->getAssocVars();
                    $this->registry->set('get_old_vars', $get_old_vars, true);
                    $recipients_ids = array();

                    $additional_vars_ids = array();
                    foreach ($recipients as $rec) {
                        if (preg_match('#^a_#', $rec)) {
                            if (!empty($assoc_vars[preg_replace('#^a_#', '', $rec)])) {
                                $additional_vars_ids[] = $assoc_vars[preg_replace('#^a_#', '', $rec)]['id'];
                            }
                        } elseif (preg_match('#^b_#', $rec)) {
                            $recipients_ids[] = $params['model']->get(preg_replace('#^b_#', '', $rec));
                        }
                    }

                    if (!empty($additional_vars_ids)) {
                        $sql = 'SELECT `value` FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' WHERE `model_id`="' . $params['model']->get('id') . '" AND `var_id` IN ("' . implode('","', $additional_vars_ids) . '") AND `value` IS NOT NULL AND `value`!="" AND `value`!="0"' . "\n";
                        $recipients_ids = array_merge($recipients_ids, $this->registry['db']->GetCol($sql));
                    }
                    unset($additional_vars_ids);

                    $recipients_ids = array_unique(array_filter($recipients_ids));

                    if (!empty($recipients_ids)) {
                        // get the emails
                        $key_search = '';
                        if ($settings['recipients_type'] == 'users') {
                            $key_search = 'id';
                        } elseif ($settings['recipients_type'] == 'customers') {
                            $key_search = 'employee';
                        }
                        $sql = 'SELECT u.id, u.email, CONCAT(ui18n.firstname, " ", ui18n.lastname) as name' . "\n" .
                               'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                               ' ON (ui18n.parent_id=u.id AND ui18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                               'WHERE u.deleted_by=0 AND u.active=1 AND u.' . $key_search . ' IN ("' . implode('","', $recipients_ids) . '")' . "\n";
                        $recipients_ids = $this->registry['db']->GetAssoc($sql);
                    }

                    // Prepare the mails data
                    // Send the mails
                    set_time_limit(0);
                    $get_old_vars = $this->registry->get('get_old_vars');

                    foreach ($recipients_ids as $recp) {
                        // Prepare the mails data
                        $params['model']->set('customer_email', $recp['email'], true);
                        $params['model']->set('email_subject', $email_template['subject'], true);
                        $params['model']->set('body', $email_template['body'], true);
                        $params['model']->set('email_template', $email_template['id'], true);

                        $placeholder_vars = array(
                            'user_name'         => $recp['name'],
                            'customer_name'     => sprintf('%s %s', $params['model']->get('name'), $params['model']->get('lastname')),
                            'last_audit'        => $audit_data,
                            'customer_view_url' => sprintf('%s://%s%sindex.php?%s=%s&%s=%s&%s=%d',
                                                           (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                                           $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'], 'customers', 'customers', 'view', 'view', $params['model']->get('id'))
                        );
                        $params['model']->set('additionalEmailVars', $placeholder_vars, true);

                        $sanitize_after = false;
                        if ($params['model']->sanitized) {
                            $params['model']->unsanitize();
                            $sanitize_after = true;
                        }

                        $get_old_vars = $this->registry->get('get_old_vars');
                        $this->registry->set('get_old_vars', true, true);
                        $mails = $params['model']->sendAsMail();
                        $this->registry->set('get_old_vars', $get_old_vars, true);
                    }
                }
            }
        }

        return true;
    }

    /**
     * Sends emails to the external accounting
     * with attached files
     * @param array $params
     * @return boolean
     */
    public function sendNotificationToExternalAccounting($params) {

        // Prepares the settings
        $settings = $this->settings;
        $fin_reasons_tag = $settings['fin_reasons_tag'];
        $annulments_tag = $settings['annulments_tag'];
        $customer_emails = $settings['customer_emails'];
        $email_template_id = $settings['notification_template'];
        $file_pattern_id = $settings['print_templates'];
        $fin_income_reasones_types = array(
            PH_FINANCE_TYPE_INVOICE,
            PH_FINANCE_TYPE_CREDIT_NOTICE,
            PH_FINANCE_TYPE_DEBIT_NOTICE,
            PH_FINANCE_TYPE_ANNULMENT
        );

        // Checks if there are missed settings
        if (empty($fin_reasons_tag) || empty($annulments_tag) || empty($customer_emails) || empty($email_template_id) || empty($file_pattern_id)) {
            $this->executionErrors[] ='Some required settings missing';
            return false;
        }

        // Preparing query that takes all files generated fin_income_reasons
        // from previous day that are untagged with the specified tags
        $query = "SELECT fir.id as document_id, f.id as file_id, f.path
                    FROM ". DB_TABLE_FINANCE_INCOMES_REASONS ." AS fir
                    JOIN ". DB_TABLE_FILES ." as f
                      ON(
                        f.model_id=fir.id
                        AND f.model='Finance_Incomes_Reason'
                        AND f.origin='generated'
                        AND f.pattern_id IN({$file_pattern_id})
                        AND fir.issue_date=CURDATE() - INTERVAL 1 DAY
                        AND fir.`type` IN(" . implode(', ', $fin_income_reasones_types) . ")
                      )
                    LEFT JOIN ". DB_TABLE_TAGS_MODELS ." AS tm
                      ON(
                        fir.id = tm.model_id
                        AND tm.tag_id = {$fin_reasons_tag}
                        AND tm.model='Finance_Incomes_Reason'
                      )
                    WHERE tm.tag_id IS NULL";

        // Gets array with all paths of the fin_incomes_reasons flies
        $documents_files = $this->registry['db']->GetAll($query);

        // Preparing query that takes all files generated fin_annulments
        // from previous day that are untagged with the specified tags
        $query = "SELECT fa.id as document_id, f.id as file_id, f.path
                    FROM ". DB_TABLE_FINANCE_ANNULMENTS ." AS fa
                    JOIN ". DB_TABLE_FILES ." AS f
                      ON(
                        f.model_id=fa.id
                        AND f.model='Finance_Annulment'
                        AND f.origin='generated'
                        AND f.pattern_id IN({$file_pattern_id})
                        AND fa.issue_date=CURDATE() - INTERVAL 1 DAY
                      )
                    LEFT JOIN tags_models AS tm
                      ON(
                        fa.id = tm.model_id
                        AND tm.tag_id={$annulments_tag}
                        AND tm.model='Finance_Annulment'
                      )
                    WHERE
                      tm.tag_id IS NULL";

        // Gets array with all paths of the fin_annulments flies
        $annulments_files = $this->registry['db']->GetAll($query);

        // Not necessary to send emails or adds tags if there are no documents and files
        if ( empty($documents_files) && empty($annulments_files) ) {
            return true;
        }

        // Get customers emails
        $query = 'SELECT
                      c.email,
                      CONCAT(ci.name, " ", ci.lastname) as name
                    FROM '. DB_TABLE_CUSTOMERS .' as c
                    JOIN '. DB_TABLE_CUSTOMERS_I18N .' as ci
                      ON(ci.parent_id=c.id
                        AND c.id='.$customer_emails.'
                      )';
        $email = $this->registry['db']->GetRow($query);

        // Extract emails from string wtih white space between values
        $emails = preg_split('/(\r\n|\r|\n)/', $email['email']);
        if(empty($emails)){
            $this->executionErrors[] = 'Required emails are missing';
            return false;
        }

        // Init Mailer
        $mailer = new Mailer($this->registry);
        // Set default sender data for sending email from automation
        $mailer->templateName = 'custom_template';
        $mailer->template['sent_by'] = $this->registry['currentUser']->get('id');
        $mailer->template['sender'] = $this->registry['currentUser']->get('email');
        $mailer->template['from_name'] = $this->registry['currentUser']->get('display_name');
        $mailer->template['replyto_email'] = $this->registry['config']->getParam('emails', 'replyto_email') ? : $this->registry['config']->getParam('emails', 'from_email');
        $mailer->template['replyto_name'] = $this->registry['config']->getParam('emails', 'replyto_name') ? : $this->registry['config']->getParam('emails', 'from_name');
        // Emails to
        $mailer->template['recipient'] = $emails;
        $names_to = array();
        $ce = count( $emails );
        for ( $i = 0 ; $i < $ce ; $i++ ) {
            array_push( $names_to, $email['name'] );
        }
        $mailer->template['names_to'] = $names_to;

        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';

        // Get email template
        $email = Emails::searchOne($this->registry, array( 'where' => array('e.id='.$email_template_id.''), 'sanitize' => true ));
        $mailer->template['subject'] = $email->get('subject');
        $mailer->template['body'] = $email->get('body');

        $files = array_merge($documents_files, $annulments_files);

        $files_paths = $this->extractKeyFromArray($files, 'path', 'file_id');
        // Attach files to the email
        $mailer->attach($files_paths);

        $send_result = $mailer->send();

        // Check are there any success of sending emails
        if (!empty($send_result['sent'])) {
            $documents_ids = $this->extractKeyFromArray($documents_files, 'document_id');
            $annulments_ids = $this->extractKeyFromArray($annulments_files, 'document_id');
            $doc_res = true;
            $ann_res = true;
            // Add tag 25 or 26 to the documents if mails are sent
            if (!empty($documents_ids)) {
                $doc_res = $this->_setDocumentsTagByIds($documents_ids, $fin_reasons_tag, 'Finance_Incomes_Reasons', 'incomes_reasons');
            }
            // Add tag 25 or 26 to the documents if mails are sent
            if (!empty($annulments_ids)) {
                $ann_res = $this->_setDocumentsTagByIds($annulments_ids, $annulments_tag, 'Finance_Annulments', 'annulments');
            }
            $result = $doc_res && $ann_res;
        }else{
            $this->executionErrors[] = 'Failed email sending';
            $result = false;
        }

        $this->updateAutomationHistory($params, 0, $result);
    }

    /**
     * Extract the specific values from array by key
     * @param array $data
     * @param string $key
     * @return array
     */
    function extractKeyFromArray($data, $key, $key2 = null) {
        $return = array();
        if ($key2 == null){
            foreach ($data as $d) {
                $return[] = $d[$key];
            }
        }else{
            foreach ($data as $d) {
                $return[$d[$key2]] = $d[$key];
            }
        }
        return array_unique($return);
    }

    /**
     * Sets the given tag and save history to the relevant document
     * @param array $documents_ids - the new tag will be set to those documents
     * @param int $new_tag_id - the id of the new tag
     * @param string $factory - used for factory,alias and history file
     * @return boolean
     */
    private function _setDocumentsTagByIds ($documents_ids, $new_tag_id, $factory, $controller) {
        if(!is_array($documents_ids) || empty($documents_ids)) {
            return false;
        }
        $modul = 'finance';
        $alias = $factory::getAlias($modul, $controller);

        // Get the allias of the current factory
        $words = preg_split("/[\s,_-]+/", $factory);
        $alias = '';
        foreach ($words as $word) {
            $alias.=$word[0];
        }
        $alias = strtolower($alias);
        $filters = array(
            'where' => array($alias.'.id IN('.implode(', ', $documents_ids).')')
        );
        $models = $factory::search($this->registry, $filters);

        $result = true;
        foreach ($models as $model) {
            $params = array(
                'module' => $modul,
                'controller' => $controller,
                'model' => $model,
                'model_id' => $model->get('id'),
                'new_tags' => $new_tag_id,
            );
            if (!$this->tag($params)) {
                $this->executionErrors[] = 'Failed to add tag '. $new_tag_id .' to the model:'.$model.', model_id:'.$model->get('id');
                $result = false;
            }
        }
        return $result;
    }

}
