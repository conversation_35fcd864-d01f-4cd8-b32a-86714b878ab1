################################################################################
### SQL nZoom Specific Updates Vista AWT (http://vistaawt.n-zoom.com/) ###
################################################################################

########################################################################
# 2014-03-27 - New dashlet plugin has been added for visits

# PRE-DEPLOYED # New dashlet plugin has been added for campaigns calls
#INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
#    (NULL, 'vistaawt_visit', NULL, '0', '1');
#INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Посещение', NULL, 'bg'),
#  (LAST_INSERT_ID(), 'Visit', NULL, 'en');

########################################################################
# 2014-04-10 - Added new automation for dates filling in a schedule document

# PRE-DEPLOYED # Added new automation for dates filling in a schedule document
#INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
#(NULL, 'Попълване на дати за седмичен график', 0, NULL, 1, 'documents', NULL, 'before_action', '3', '', 'condition := 1', 'plugin := vistaawt\nmethod := addScheduleDates', NULL, 0, 0);

########################################################################
# 2014-05-08 - Added new dashlet plugin Schedule (it replaces the old one 'vistaawt_visit')

# PRE-DEPLOYED # Added new dashlet plugin Schedule (it replaces the old one 'vistaawt_visit')
#UPDATE `dashlets_plugins` SET type='vistaawt_schedule', settings = 'weekly_schedule_type_id := 3\r\nweekly_schedule_confirmed_status_id := 3\r\nweekly_schedule_failed_status_id := 4\r\nweekly_schedule_vars_prefixes := m, t, w, th, f, s, sn\r\nweekly_schedule_group_name := object_visits_list_grp\r\n\r\nvisit_report_type_id := 1\r\narticles_list_type_id := 2\r\narticles_list_valid_status_id := 1\r\nnom_object_type_id := 1\r\nnom_article_type_id := 5\r\nnom_category_type_id := 7\r\n' WHERE type='vistaawt_visit';
#UPDATE `dashlets_plugins_i18n` dpi18n, `dashlets_plugins` dp SET name='График', description='Седмичен график за посещения на търговски представители' WHERE dpi18n.parent_id=dp.id AND dpi18n.lang='bg' AND dp.type='vistaawt_schedule';
#UPDATE `dashlets_plugins_i18n` dpi18n, `dashlets_plugins` dp SET name='Schedule', description='Weekly schedule of sales representatives' WHERE dpi18n.parent_id=dp.id AND dpi18n.lang='en' AND dp.type='vistaawt_schedule';

#UPDATE `dashlets` SET controller='vistaawt_schedule' WHERE controller='vistaawt_visit';
#UPDATE `dashlets_i18n` SET name=REPLACE(name, 'Посещение', 'График');

# PRE-DEPLOYED # Add cancel on fail for the validation automation
#UPDATE automations SET `after_action` = 'cancel_action_on_fail := 1' WHERE method like '%addScheduleDates%';


######################################################################################
# 2014-05-08 - Added new report - 'vistaawt_articles_management' - for VISTAAWT

# Added new report - 'vistaawt_articles_management' - for VISTAAWT
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(292 , 'vistaawt_articles_management', '#article nomenclature type\r\narticle_type := 5\r\n#object group is customer type\r\nobject_group_type := 4\r\n#object is nomenclature type\r\nobject_type := 1\r\n#document type listing the object articles\r\ndocument_articles_type := 2\r\ndocument_articles_status_valid := 1\r\ndocument_articles_status_expired := 2' , 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(292, 'Управление на артикули', NULL, NULL, 'bg'),
(292, 'Articles Management', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 292, 0, 1),
('reports', 'export', 292, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=292;

######################################################################################
# 2014-07-29 - Replaced subcategories with categories in the vistaawt_schedule dashlet plugin

# PRE-DEPLOYED # Replaced subcategories with categories in the vistaawt_schedule dashlet plugin
#UPDATE `dashlets_plugins` SET settings = REPLACE(settings, 'nom_category_type_id := 7', 'nom_category_type_id := 10') WHERE type='vistaawt_schedule' AND settings LIKE '%nom_category_type_id := 7%';

######################################################################################
# 2014-09-15 - Replaced categories with SUBcategories in the TARGET section (right part) of vistaawt_schedule dashlet plugin

# AGAIN: Replaced categories with SUBcategories in the TARGET section (right part) of vistaawt_schedule dashlet plugin
UPDATE `dashlets_plugins` SET `settings`='weekly_schedule_type_id := 3\r\nweekly_schedule_confirmed_status_id := 3\r\nweekly_schedule_failed_status_id := 4\r\nweekly_schedule_vars_prefixes := m, t, w, th, f, s, sn\r\nweekly_schedule_group_name := object_visits_list_grp\r\n\r\nvisit_report_type_id := 1\r\narticles_list_type_id := 2\r\narticles_list_valid_status_id := 1\r\nnom_object_type_id := 1\r\nnom_article_type_id := 5\r\nnom_category_type_id := 10\r\nnom_subcategory_type_id := 7' WHERE type='vistaawt_schedule';

######################################################################################
# 2014-09-30 - Add automation to remind for car ATI

# Add automation to remind for car ATI
INSERT INTO automations
  SET automation_type = 'crontab',
    module = 'nomenclatures',
    start_model_type = '11',
    name = 'Напомняния за ГТП',
    settings = 'period := 1 day\r\nstart_time := 03:00\r\nrequired_employees := 56\r\nreminders_days := 10,5\r\ncustomer_type_employee := 1\r\nfield_nom_date_next := date_next\r\nfield_nom_driver_name_id := driver_name_id\r\nfield_nom_region_name := region_name\r\nfield_cus_city_name_id := city_name_id\r\nfield_cus_manager_region := manager_region',
    conditions = 'condition := 1',
    method = 'plugin := vistaawt\r\nmethod := remindCarATI',
    nums = 0;

ALTER TABLE `emails`  AUTO_INCREMENT =1001;
INSERT INTO emails
  SET model = 'Nomenclature',
    model_type = 11,
    name = 'automations_plugin_vistaawt_remindcarati',
    active = 1,
    `group` = 1,
    added = NOW(),
    added_by = 1,
    modified = NOW(),
    modified_by = 1;
SELECT @pattern_id := LAST_INSERT_ID();
INSERT INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
  (@pattern_id, 'Напомняне за [nomenclature_type_name]', '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n <tbody>\r\n     <tr>\r\n            <td>Здравейте, [user_name],</td>\r\n        </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>Имате напомняне за <a href="[nomenclature_view_url]">[nomenclature_type_name] [nomenclature_code]</a> (добавил/а [nomenclature_added_by])</td>\r\n      </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>Относно: [reminder_about]</td>\r\n      </tr>\r\n       <tr>\r\n            <td>Описание: [reminder_description]</td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL! Той е генериран и изпратен от автоматичната система за известяване на nZoom.</td>\r\n      </tr>\r\n       <tr>\r\n            <td>При възникнали проблеми можете да <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">изпратите e-mail</a> на екипа за поддръжка на nZoom.</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n   </tbody>\r\n</table>\r\n', 'Напомняне за номенклатура.', 'bg', NOW()),
  (@pattern_id, 'Reminder for [nomenclature_type_name]', '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n <tbody>\r\n     <tr>\r\n            <td>Hello, [user_name],</td>\r\n        </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>You have a reminder for <a href="[nomenclature_view_url]">[nomenclature_type_name] [nomenclature_code]</a> <em>(added by [nomenclature_added_by])</em></td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td><span style="color:rgb(128, 128, 128)"><em>Title:</em></span> [reminder_about]</td>\r\n     </tr>\r\n       <tr>\r\n            <td><span style="color:rgb(128, 128, 128)"><em>Description:</em></span> [reminder_description]</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td><strong>PLEASE, DO NOT REPLY TO THIS MESSAGE!</strong> It was sent by the <strong>nZoom</strong>&#39;s notification system from an unmonitored e-mail address. Mail sent to this address cannot be answered.</td>\r\n       </tr>\r\n       <tr>\r\n            <td><em>Should you encounter any problems, please do not hesitate to <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">send an e-mail back</a> to the <strong>nZoom</strong> support team.</em></td>\r\n      </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n   </tbody>\r\n</table>\r\n', 'Reminder of nomenclature.', 'en', NOW());

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_type_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Име на типа номенклатура', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature type name', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('user_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Имена на потребителя', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Name of user', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_code', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Код на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature code', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_view_url', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Адресът за достъпване на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'The address for accessing the nomenclature', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_added_by', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Имена на потребителя добавил номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Name of the user added the nomenclature', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('reminder_about', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Напомняне: тносно', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Reminder: about', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('reminder_description', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Напомняне: описание', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Reminder: description', NULL, 'en');

######################################################################################
# 2014-11-05 - Added automation that creates tasks from meeting protocol upon manual status change or 3 days after beeing added (crontab)
#            - Added automation that validates task fields in  meeting protocol

# Added automation that creates tasks from meeting protocol upon manual status change or 3 days after beeing added (crontab)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Създаване на (мини) задачи от протокол от среща', 0, NULL, 1, 'documents', NULL, 'before_action', '4', 'create_model := minitask\r\ntask_name := discussion_theme\r\ntask_finish_date := discussion_term\r\ntask_description := discussion_decision\r\ntask_assignee := performer_name_id\r\n', 'condition := \'[request_is_post]\' == \'1\'\r\ncondition := [action] == \'setstatus\'\r\ncondition := $this->registry->get(\'request\')->get(\'status\') == \'closed\'\r\n', 'plugin := bgservice\r\nmethod := createTasks\r\nnew_status := closed\r\n', 'cancel_action_on_fail := 1', 1, 1, 1),
(NULL, 'Създаване на (мини) задачи от протокол от среща (3 дни след добавяне)', 0, NULL, 1, 'documents', NULL, 'crontab', '4', 'create_model := minitask\r\ntask_name := discussion_theme\r\ntask_finish_date := discussion_term\r\ntask_description := discussion_decision\r\ntask_assignee := performer_name_id\r\n#send_to_email := <EMAIL>', 'condition := "[b_status]" != "closed"\r\ncondition := strtotime("[b_added]")+3*24*60*60 <= time()\r\nfilter_sql_condition := status != "closed"\r\nfilter_sql_condition := DATE_ADD(added, INTERVAL 3 DAY) <= NOW()', 'plugin := bgservice\r\nmethod := createTasks\r\nnew_status := closed\r\n', '', 1, 1, 1),
(NULL, 'Валидация на данни за задачи в протокол от среща', 0, NULL, 1, 'documents', NULL, 'before_action', '4', 'task_finish_date := discussion_term\r\ntask_assignee := performer_name_id', 'condition := \'[request_is_post]\' == \'1\'\r\ncondition := in_array(\'[action]\', array(\'add\', \'edit\'))\r\n', 'plugin := bgservice\r\nmethod := validateProtocolTaskFields\r\n', 'cancel_action_on_fail := 1', 1, 0, 1);

######################################################################################
# 2014-12-19 - Added department filter variable for the QMS Management report

# Added department filter variable for the QMS Management report
UPDATE reports SET settings=REPLACE(settings, 'nomenclature_type_id := 13', 'nomenclature_type_id := 13\nnomenclature_department_var := depart_chbx') WHERE `type` = 'qms_management' AND settings NOT LIKE '%nomenclature_department_var%';

######################################################################################
# 2015-02-23 - Created automation for different models creation. Automations for (mini)tasks creation have been substituted

# Created automation for different models creation. Automations for (mini)tasks creation have been substituted
DELETE FROM automations WHERE id IN (6, 7);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
    (6, 'Създаване на (мини) задачи от протокол от среща', 0, NULL, 1, 'documents', NULL, 'before_action', '4', 'create_model := minitask\r\n\r\nmodel := Document\r\nmodel_id := [b_id]\r\ncustomer := [b_customer]\r\ndescription := php(''[a_discussion_theme]'' . "\\n\\n" . ''[a_discussion_decision]'')\r\nassigned_to := [a_performer_name_id]\r\nstatus := opened\r\nseverity := normal\r\ndeadline := [a_discussion_term]\r\n\r\nfield_defining_multiple := [a_discussion_decision]\r\n', 'condition := ''[request_is_post]'' && ''[action]'' == ''setstatus'' && $this->registry->get(''request'')->get(''status'') == ''closed'' && ''[b_status]'' != ''closed''\r\n', 'method := createModels\r\n', 'cancel_action_on_fail := 1', 1, 1, 1),
    (7, 'Създаване на (мини) задачи от протокол от среща (3 дни след добавяне)', 0, NULL, 1, 'documents', NULL, 'crontab', '4', 'create_model := minitask\r\n\r\nmodel := Document\r\nmodel_id := [b_id]\r\ncustomer := [b_customer]\r\ndescription := php(''[a_discussion_theme]'' . "\\n\\n" . ''[a_discussion_decision]'')\r\nassigned_to := [a_performer_name_id]\r\nstatus := opened\r\nseverity := normal\r\ndeadline := [a_discussion_term]\r\n\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\n#send_to_email := <EMAIL>', 'condition := "[b_status]" != "closed"\r\ncondition := strtotime("[b_added]")+3*24*60*60 <= time()\r\nfilter_sql_condition := status != "closed"\r\nfilter_sql_condition := DATE_ADD(added, INTERVAL 3 DAY) <= NOW()', 'method := createModels\r\n', '', 1, 1, 1);

######################################################################################
# 2015-03-19 - Changed settings for create models automation

# Changed settings for create models automation
UPDATE automations SET settings = 'create_model := minitask\r\n\r\nmodel := Document\r\nmodel_id := [b_id]\r\ncustomer := [b_customer]\r\ndescription := php(''[a_discussion_theme]'' . "\\n\\n" . ''[a_discussion_decision]'')\r\nassigned_to := [a_performer_name_id]\r\nstatus := opened\r\nseverity := normal\r\ndeadline := [a_discussion_term]\r\n\r\ncreate_condition := !empty(''[a_performer_name_id]'')\r\n\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\nvalidate := !empty([description]) && [deadline] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!' WHERE id = 6;
UPDATE automations SET settings = 'create_model := minitask\r\n\r\nmodel := Document\r\nmodel_id := [b_id]\r\ncustomer := [b_customer]\r\ndescription := php(''[a_discussion_theme]'' . "\\n\\n" . ''[a_discussion_decision]'')\r\nassigned_to := [a_performer_name_id]\r\nstatus := opened\r\nseverity := normal\r\ndeadline := [a_discussion_term]\r\n\r\ncreate_condition := !empty(''[a_performer_name_id]'')\r\n\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\nvalidate := !empty([description]) && [deadline] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!\r\n\r\nsend_to_email := <EMAIL>' WHERE id = 7;
DELETE FROM automations WHERE id = 8;

######################################################################################
# 2015-03-24 - Changed settings for create models automation

# Changed settings for create models automation
UPDATE automations SET settings = 'create_model := minitask\r\n\r\nmodel := Document\r\nmodel_id := [b_id]\r\ncustomer := [b_customer]\r\ndescription := php(''[a_discussion_theme]'' . "\\n\\n" . ''[a_discussion_decision]'')\r\nassigned_to := [a_performer_name_id]\r\nstatus := opened\r\nseverity := normal\r\ndeadline := [a_discussion_term]\r\n\r\ncreate_condition := ''[a_performer_name_id]'' != ''''\r\n\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\nvalidate := !empty([description]) && [deadline] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!' WHERE id = 6;
UPDATE automations SET settings = 'create_model := minitask\r\n\r\nmodel := Document\r\nmodel_id := [b_id]\r\ncustomer := [b_customer]\r\ndescription := php(''[a_discussion_theme]'' . "\\n\\n" . ''[a_discussion_decision]'')\r\nassigned_to := [a_performer_name_id]\r\nstatus := opened\r\nseverity := normal\r\ndeadline := [a_discussion_term]\r\n\r\ncreate_condition := ''[a_performer_name_id]'' != ''''\r\n\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\nvalidate := !empty([description]) && [deadline] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!\r\n\r\nsend_to_email := <EMAIL>' WHERE id = 7;

########################################################################
# 2015-04-23 - Added another setting to the qms_management report: check_permissions_for_nom_urls := 1

# Added another setting to the qms_management report: check_permissions_for_nom_urls := 0/1
UPDATE reports SET settings=REPLACE(settings, 'check_permissions_for_nom_urls := 0', 'check_permissions_for_nom_urls := 1') WHERE type = 'qms_management' AND settings LIKE '%check_permissions_for_nom_urls := 0%';

########################################################################
# 2015-04-24 - Added new report 'vistaawt_target_categories'

# Added new report 'vistaawt_target_categories'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(315, 'vistaawt_target_categories', 'customer_type_chain := 4\r\ndocument_type_visit := 1\r\n\r\ncategory_tag := 5\r\n\r\ncategory_var := checked_subcategory_id\r\ncategory_name_var := checked_subcategory_name\r\ncface_var := checked_targets_cface\r\nvface_var := checked_targets_vface\r\npicture_var := checked_targets_fileupload\r\nnotes_var := checked_targets_notes ', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(315, 'Целеви подкатегории', NULL, NULL, 'bg'),
(315, 'Target subcategories', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 315, 0, 1),
('reports', 'export', 315, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND action IN ('generate_report', 'export') AND model_type=315;

########################################################################
# 2015-06-16 - Deleted values for miscalculated perventage column (checked_targets_summary_perc)
#            - Added values for the newly added column (checked_targets_summary_perc)

# Deleted values for miscalculated perventage column (checked_targets_summary_perc)
DELETE dc3.*
FROM documents d
JOIN documents_cstm dc1
  ON d.id=dc1.model_id AND dc1.var_id=112
JOIN documents_cstm dc2
  ON d.id=dc2.model_id AND dc2.var_id=113 AND dc1.num=dc2.num
JOIN documents_cstm dc3
  ON d.id=dc3.model_id AND dc3.var_id=116 AND dc1.num=dc3.num
WHERE (dc1.value!='' OR dc2.value!='') AND (dc3.value='' OR FORMAT((dc1.value/(dc1.value+dc2.value))*100, 2)!=dc3.value);

# Added values for the newly added column (checked_targets_summary_perc)
INSERT IGNORE INTO documents_cstm
SELECT d.id, 116, dc1.num,  FORMAT((dc1.value/(dc1.value+dc2.value))*100, 2), dc1.added, dc1.added_by, dc1.modified, dc1.modified_by, dc1.lang
FROM documents d
LEFT JOIN documents_cstm dc1
  ON d.id=dc1.model_id AND dc1.var_id=112
LEFT JOIN documents_cstm dc2
  ON d.id=dc2.model_id AND dc2.var_id=113 AND dc1.num=dc2.num
LEFT JOIN documents_cstm dc3
  ON d.id=dc3.model_id AND dc3.var_id=116 AND dc1.num=dc3.num
WHERE (dc1.value!='' OR dc2.value!='') AND (dc3.value='' OR dc3.value IS NULL);

########################################################################
# 2016-01-05 - Added some settings to the 'vistaawt_schedule' dashlet (used in ASKMERCHANDISER installation Bug 4089, comment 35)

# Added some settings to the 'vistaawt_schedule' dashlet (used in ASKMERCHANDISER installation Bug 4089, comment 35)
UPDATE dashlets_plugins SET settings='weekly_schedule_type_id := 3\r\nweekly_schedule_confirmed_status_id := 3\r\nweekly_schedule_failed_status_id := 4\r\nweekly_schedule_vars_prefixes := m, t, w, th, f, s, sn\r\nweekly_schedule_group_name := object_visits_list_grp\r\n\r\nvisit_report_type_id := 1\r\narticles_list_type_id := 2\r\narticles_list_valid_status_id := 1\r\nnom_object_type_id := 1\r\nnom_article_type_id := 5\r\nnom_category_type_id := 7\r\nnom_subcategory_type_id := 5\r\nnom_city_type_id := 9\r\n\r\n# Use the following settings in ASKMERCHANDISER only!\r\nstep3_targets := \r\n\r\nvisit_report_longitude_field := \r\nvisit_report_latitude_field := \r\n\r\nlabel_stockproducer := \r\nlabel_subcategory := \r\nlabel_vface := \r\n' WHERE type='vistaawt_schedule';

########################################################################
# 2016-02-29 - Added optional settings to 'target_categories' report

# Added optional settings to 'target_categories' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\n# optional settings for protocol customer\r\ncustomer_id_var := \r\ncustomer_name_var := \r\ncustomer_type_client := ')
WHERE `type` = 'target_categories' AND `settings` NOT LIKE '%customer_id_var%';

########################################################################
# 2016-03-09 - Changed some of the settings in the schedule dashlet to add ASKMERCHANDISER specific protocol customer and lock substatus

# Changed some of the settings in the schedule dashlet to add ASKMERCHANDISER specific protocol customer and lock substatus
UPDATE `dashlets_plugins` SET settings='weekly_schedule_type_id := 3\r\nweekly_schedule_confirmed_status_id := 3\r\nweekly_schedule_failed_status_id := 4\r\nweekly_schedule_vars_prefixes := m, t, w, th, f, s, sn\r\nweekly_schedule_group_name := object_visits_list_grp\r\n\r\nvisit_report_type_id := 1\r\narticles_list_type_id := 2\r\narticles_list_valid_status_id := 1\r\nnom_object_type_id := 1\r\nnom_article_type_id := 5\r\nnom_category_type_id := 7\r\nnom_subcategory_type_id := 5\r\nnom_city_type_id := 9\r\n\r\n# Use the following settings in ASKMERCHANDISER only!\r\nstep3_targets := \r\nprotocol_customer_id := \r\nprotocol_customer_name := \r\n\r\nvisit_report_longitude_field := \r\nvisit_report_latitude_field := \r\n\r\nvisit_report_substatus_officially_closed := \r\n\r\nlabel_stockproducer := \r\nlabel_subcategory := \r\nlabel_vface := ' WHERE type='vistaawt_schedule';

########################################################################
# 2016-03-22 - Changed the settings of the remindCarATI automation (added driver_name field)

# Changed the settings of the remindCarATI automation (added driver_name field)
UPDATE automations SET settings='period := 1 day\r\nstart_time := 03:00\r\nrequired_employees := 29\r\nreminders_days := 10,5\r\ncustomer_type_employee := 1\r\nfield_nom_date_next := date_next\r\nfield_nom_driver_name := driver_name\r\nfield_nom_driver_name_id := driver_name_id\r\nfield_nom_region_name := region_name\r\nfield_cus_city_name_id := city_name_id\r\nfield_cus_manager_region := manager_region' WHERE method LIKE '%remindCarATI%' AND settings NOT LIKE '%field_nom_driver_name := driver_name%';

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 3 DAY) <= NOW()' WHERE `id` IN (3,7,14);

######################################################################################
# 2022-05-12 - Added new automation to create table with levels of approval

# Added new automation to create table with levels of approval
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Създаване на нива на одобрение', 0, NULL, 1, 'documents', NULL, 'action', '12', 'document_approval_form_type_id := 12\r\nposition_for_approval := position_id\r\napproval_table_row_var := agreement_consistency\r\napproval_table_employee_name_var := agreement_name\r\napproval_table_employee_id_var := agreement_with\r\napproval_table_employee_role_var := type_act_1\r\napproval_table_employee_position_var := agreement_position\r\napproval_table_employee_department_var := agreement_department\r\n\r\ncustomer_position_var := position_id\r\n\r\nnomenclature_position_type_id := 14\r\nlevels := 3\r\nskip_levels :=\r\nskip_all_level_positions_matching_current_employee := 1\r\nskip_same_position_employees := 1\r\nlevel_1_position_var := ci_employees_id\r\nlevel_1_role_position_employees :=\r\nlevel_2_position_var := des_employees_id\r\nlevel_2_role_position_employees :=\r\nlevel_3_position_var := level_appr_id\r\nlevel_3_role_position_employees :=', 'condition := \'[b_status]\' == \'opened\' && \'[request_is_post]\' == 1', 'plugin := vistaawt\r\nmethod := completeApprovalLevels', NULL, 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%vistaawt%' AND `method` LIKE '%completeApprovalLevels%');

########################################################################
# 2022-06-06 - Added REST settings for vista
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'allowed_rest_user_agents', 'vista-app'),
  ('rest', 'filter_vars_departments', 'all'),
  ('rest', 'filter_vars_documents_12', 'all'),
  ('rest', 'filter_vars_documents_13', 'all'),
  ('rest', 'filter_vars_users', 'id, username, firstname, lastname, employee, rights')
ON DUPLICATE KEY UPDATE
                     `value` = VALUES(`value`);

######################################################################################
# 2022-10-04 - Added automation to create customer from completed approval form

# Added automation to create customer from completed approval form
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
    SELECT NULL, 'Създаване на контрагент от форма за одобрение', 0, NULL, 1, 'documents', NULL, 'action', '12', 'b_type := 1\r\nb_is_company := 0\r\nb_active := 1\r\nb_name := [a_applicant_first_name]\r\nb_lastname := [a_applicant_second_name] [a_applicant_third_name]\r\na_city_name := [a_city_name]\r\na_city_id := [a_city_name_id]\r\nb_department := [b_department]\r\na_division := [a_division]\r\na_sub_division := [a_sub_division]\r\na_position_id := [a_position_id]\r\na_position_name := [a_position_name]', 'condition := \'[action]\' == \'setstatus\' && \'[prev_b_substatus]\' != \'34\' && \'[b_substatus]\'==\'34\'', 'plugin := vistaawt\r\nmethod := approvalCreateCustomer', NULL, 1, 1, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE method LIKE '%plugin := vistaawt\r\nmethod := approvalCreateCustomer%');

######################################################################################
# 2022-11-04 - Added aditional settings for the approvalCreateCustomer automation

# Added aditional settings for the approvalCreateCustomer automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nuse_updated_model := 1'), `position`=10, `conditions`='condition := \'[prev_b_substatus]\' != \'34\' && \'[b_substatus]\'==\'34\'' WHERE `settings` NOT LIKE '%use_updated_model%' AND `method` LIKE '%approvalCreateCustomer%';

######################################################################################
# 2023-01-16 - Added aditional settings the var that will be used for employee data

# Added aditional settings the var that will be used for employee data
UPDATE `automations` SET `settings`=CONCAT('approval_var := b_employee\r\n\r\n', `settings`) WHERE `settings` NOT LIKE '%approval_var%' AND `method` LIKE '%completeApprovalLevels%';
