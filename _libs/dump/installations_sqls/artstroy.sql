#################################################################################
### SQL nZoom Specific Updates - Артстрой (http://artstroy.n-zoom.com/) ###
#################################################################################

######################################################################################
# 2016-02-19 - Added report 'timesheets_per_employees_and_customers' to ARTSTROY
#            - Added placeholders 'timesheets_per_employees_and_customers' report to ARTSTROY
#            - Added report - 'warehouse_availabilities' to ARTSTROY
#            - Added new report - 'goods_movement' to ARTSTROY

# Added report 'timesheets_per_employees_and_customers' to ARTSTROY
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (62, 'timesheets_per_employees_and_customers', 'documents_types :=\r\nprojects_types :=\r\ntasks_types :=\r\n\r\nshow_billing_time :=\r\ninclude_partial_periods :=\r\n\r\nlawyer_installation :=\r\n\r\nallows_files_generation := 1\r\nfiles_origin := self', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (62, 'Отчетено време', NULL, NULL, 'bg'),
  (62, 'Timesheets per employees and customers', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '62', '1'),
  ('reports', 'export', '62', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '62';

# Added placeholders 'timesheets_per_employees_and_customers' report to ARTSTROY
# placeholders for the tables in 'timesheets_per_employees_and_customers' report
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'timesheet_per_employees_and_customers_main_report', 'Report', 'basic', 'all', ',62,', 'timesheet_per_employees_and_customers_main_report', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Основна справка', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Main report', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'timesheet_per_employees_and_customers_employee_report', 'Report', 'basic', 'all', ',62,', 'timesheet_per_employees_and_customers_employee_report', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разбивка по служители', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Employee report', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'timesheet_per_employees_and_customers_project_report', 'Report', 'basic', 'all', ',62,', 'timesheet_per_employees_and_customers_project_report', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разбивка по проекти', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Project report', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'timesheet_per_employees_and_customers_customer_report', 'Report', 'basic', 'all', ',62,', 'timesheet_per_employees_and_customers_customer_report', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разбивка по контрагенти', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Customers report', NULL, 'en');

# placeholders for the graphics in 'timesheets_per_employees_and_customers' report
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'timesheet_per_employees_and_customers_customer_report_graph1', 'Report', 'basic', 'all', ',62,', 'timesheet_per_employees_and_customers_graph1', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разбивка по контрагенти (графика 1)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Customers report (graph 1)', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'timesheet_per_employees_and_customers_customer_report_graph2', 'Report', 'basic', 'all', ',62,', 'timesheet_per_employees_and_customers_graph2', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разбивка по контрагенти (графика 2)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Customers report (graph 2)', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'timesheet_per_employees_and_customers_customer_report_graph3', 'Report', 'basic', 'all', ',62,', 'timesheet_per_employees_and_customers_graph3', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разбивка по отчетено време (графика 3)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Employee report (graphic 3)', NULL, 'en');


# Added report - 'warehouse_availabilities' to ARTSTROY
INSERT IGNORE INTO `reports` (`id` ,`type` ,`settings` , `position`, `is_portal` ,`visible`) VALUES
 ('112', 'warehouse_availabilities', 'min_stock_var := \r\narticle_measure := measure\r\nshow_depleted_articles_filter := \r\nsearch_type_nomenclature := 8\r\nshow_include_zero_availabilities_filter := 1\r\nnom_cats_order := 1\r\nshow_moving_articles_filter :=\r\narticles_transfer_protocol :=\r\nsee_only_current_user_office_warehouses :=\r\nclear_warehouses_without_quantities := 1\r\n\r\nroles_see_all_warehouses :=\r\nusers_see_all_warehouses :=', '0', '0', '1');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
 ('112', 'Наличности',     NULL, 'bg'),
 ('112', 'Availabilities', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
 ('reports', '', 'generate_report', '112', '0', '1'),
 ('reports', '', 'export',          '112', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
 SELECT '1', `id`, 'all'
   FROM `roles_definitions`
   WHERE `module` = 'reports'
     AND `action` IN ('generate_report', 'export')
     AND `model_type` = '112';

# Added new report - 'goods_movement' to ARTSTROY
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
 (261, 'goods_movement', 'sale_types := 101\r\ndelivery_types := 20,22,23,102\r\nofficial_marking_types := 101\r\nnomenclature_types := 8\r\ncustomer_types := \r\nwarehouses := \r\ndefault_warehouse_roles := \r\nofficial_marking_label := \r\nofficial_marking_label_bg := покупка\r\nofficial_marking_label_en :=', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
 (261, 'Движение на стоки', NULL, NULL, 'bg'),
 (261, 'Goods movement', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
 ('reports', '', 'generate_report', '261', '0', '1'),
 ('reports', '', 'export',          '261', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
 SELECT '1', `id`, 'all'
   FROM `roles_definitions`
   WHERE `module` = 'reports'
     AND `action` IN ('generate_report', 'export')
     AND `model_type` = '261';

######################################################################################
# 2016-03-22 - Added report: 'inventory'

# Added report: 'inventory'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (318, 'inventory', 'doc_type_employee_inventory_card := 11\r\nfield_doc_card_inventory_description_id := inventory_description_id\r\nfield_doc_card_date_received := date_received\r\nfield_doc_card_date_return := date_return\r\n\r\ncus_type_employee := 1\r\nfield_cus_empl_work_place_id := work_place_id\r\nfield_cus_empl_work_place := work_place\r\n\r\nnom_type_inventory := 28\r\nfield_nom_inv_build_id := build_id\r\nfield_nom_inv_build_name := build_name\r\nfield_nom_inv_room_id := room_id\r\nfield_nom_inv_room_name := room_name\r\nfield_nom_category_inventory_id := category_inventory_id\r\nfield_nom_category_inventory := category_inventory\r\nfield_nom_subcategory_inventory_id := subcategory_inventory_id\r\nfield_nom_subcategory_inventory := subcategory_inventory\r\n\r\nnom_type_building := 25\r\nnom_type_room := 26\r\nnom_type_working_place := 27\r\nnom_type_category_inventory := 23\r\nnom_type_subcategory_inventory := 24', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (318, 'Управление на инвентар', NULL, NULL, 'bg'),
  (318, 'Inventory management', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '318', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     = 'generate_report'
      AND `model_type` = '318';

######################################################################################
# 2016-04-07 - Added report: 'artstroy_shedule_reservations'
#            - Added new automation (and variables-values) for invoices templates generation for contract type 1 (Bug 3940)

# Added report: 'artstroy_shedule_reservations'
INSERT INTO `reports` (id, `type`, settings, position, is_portal, visible) VALUES
  (347, 'artstroy_shedule_reservations', 'free_color := #65AA55\r\nbusy_color := #C25555\r\nobject_types := 1,2\r\nsubobject_type := 3,4,5\r\nsubobject_tags := 2\r\ndocument_type_shedule_reservations := 10', 0, 0, 1);
INSERT INTO `reports_i18n` (parent_id, name, section, description,lang) VALUES
  (347, 'График на резервации', NULL, NULL, 'bg'),
  (347, 'Shedule reservations', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '347', '1'),
  ('reports', 'export', '347', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN('generate_report','export')
      AND `model_type` = '347';

#  Added new automation (and variables-values) for invoices templates generation for contract type 1 (Bug 3940)
INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_bank', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Банкова сметка', 'bg'),
(LAST_INSERT_ID(), 'label', 'Bank account', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '1', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoice_generate_pattern', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Шаблон за печат на фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice generate pattern', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '5', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'proforma_invoice_generate_pattern', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Шаблон за печат на проформа фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Proforma invoice generate pattern', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '4', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_observer', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Отговорник по фактуриране', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoices observer', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '3', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_payment_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на падеж', 'bg'),
(LAST_INSERT_ID(), 'label', 'Date of payment', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 15\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := issue', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_fiscal_event_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на данъчно събитие', 'bg'),
(LAST_INSERT_ID(), 'label', 'Fiscal event date', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 0\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_period_rows', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Начин на фактуриране', 'bg'),
(LAST_INSERT_ID(), 'label', 'Period rows invoicing', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '#one_one\none_all\n#all_one', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoice_email_template', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Шаблон за известяване - Фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice e-mail pattern', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '1004', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoice_first_period', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Фактуриране на първи период', 'bg'),
(LAST_INSERT_ID(), 'label', 'First period invoice', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'full', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_single_period', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Единичен период на фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice single period', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 1\nperiod := month\nlength := 0', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_first_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на първа фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'First invoice date', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 0\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_issue_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Дата на фактуриране', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice issue date', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 0\nperiod_type := calendar\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 1, 'invoices_proforma', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Издай проформа фактура', 'bg'),
(LAST_INSERT_ID(), 'label', 'Issue proforma invoice', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '1', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 0, 'invoices_unique_key', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Уникални полета', 'bg'),
(LAST_INSERT_ID(), 'label', 'Invoice unique fields', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'article_deliverer', NULL, NULL, NOW(), 1, NOW(), 1, '');

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Създаване на шаблони за фактуриране от договори', 0, NULL, 1, 'contracts', '', 'before_action', '1', '', 'condition := 1', 'plugin := fm_europe\r\nmethod := createInvoicesTemplatesService', 'cancel_action_on_fail := 1', 0, 0, 1);

######################################################################################
# 2016-04-18 - Added automation for creation of a warehouse on tag change of a project

# Added automation for creation of a warehouse on tag change of a project
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Създава склад за проект при задаване на таг "В строеж"', 0, NULL, 1, 'projects', NULL, 'action', '0', 'company := 1\r\noffice := 1\r\nlocation_var := object_address', 'condition := in_array(''[action]'', array(''tag'', ''multitag''))\r\ncondition := (!$old_model->get(''tags'') || !in_array(3, $old_model->get(''tags'')))\r\ncondition := $new_model->getTags() && in_array(3, $new_model->get(''tags''))\r\ncondition := in_array(''[b_type]'', array(1, 2))', 'plugin := artstroy\r\nmethod := createWarehouse', NULL, 1, 1, 1);

######################################################################################
# 2016-05-05 - Add new pattern plugin (and placeholders for it) for printing of documents of type "Offer" in DOCX format.

# PRE-DEPLOYED # Add new pattern plugin (and placeholders for it) for printing of documents of type "Offer" in DOCX format.
#INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
#  (78, 'Document', 3, 'artstroy', 'prepareOffer', '', NULL, NOW(), NOW());
#INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
#  (78, 'Подготовка на "Оферта"', 'Подготвят се данни за печат на документ от тип "Оферта".', 'bg', NOW()),
#  (78, 'Preparation for "Offer"', 'Prepare data for printing of "Offer" type documents.', 'en', NOW());
#INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  ('properties', 'Document', 'basic', 'pattern_plugins', ',78,', '', 0);
#INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Таблица с данни и изборажения на имотите', 'bg'),
#  (LAST_INSERT_ID(), 'Property details', 'en');

######################################################################################
# 2015-05-10 - Added descriptions to some variables used by the pattern plugin "prepareOffer"
#            - Replaced checkboxes with radio buttons in a grouping table of property(project) images

# Added descriptions to some variables
DELETE fi.* FROM _fields_i18n fi, _fields_meta fm
WHERE fi.parent_id = fm.id AND fi.content_type = 'description' AND model='Project' AND model_type IN (3,4,5) AND
(name LIKE 'type_%'
  OR name IN ('object_address', 'object_build_type', 'build_year', 'level_completion_name', 'total_area_with_cellar', 'total_area_without_cellar', 'zp_name',
 'rzp_name', 'belong_to_property', 'view', 'distribution', 'lighting', 'optical_cabling', 'structured_cabling', 'ventilation',
 'security', 'video_record', 'access_control', 'elevator', 'bms_system', 'fire_alarm_system', 'conference_room', 'cafe_restaurant',
 'property_promotional_price', 'parking_price', 'maintenance_fee_price', 'maintenance_conditions'));
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Тип имот', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name LIKE 'type_%';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Местоположение', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='object_address';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Вид строителство', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='object_build_type';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Година на строителство', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='build_year';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Степен на завършеност', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='level_completion_name';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Обща площ (с изба)', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='total_area_with_cellar';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Обща площ (без изба)', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='total_area_without_cellar';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'ЗП', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='zp_name';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'РЗП', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='rzp_name';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Принадлежащо към имот', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='belong_to_property';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Изглед', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='view';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Разпределение', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='distribution';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Осветление', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='lighting';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Оптично окабеляване', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='optical_cabling';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Структорно окабеляване', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='structured_cabling';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Отопление, климатизация и вентилация', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='ventilation';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Охрана', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='security';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Видеонаблюдение', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='video_record';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Контрол на достъп', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='access_control';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Асансьор', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='elevator';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Система за управление на сграда (BMS)', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='bms_system';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Пожароизвестителна система', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='fire_alarm_system';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Конферентна зала', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='conference_room';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Кафе/Ресторант', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='cafe_restaurant';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Промо офертна цена (EUR)', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='property_promotional_price';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Паркомясто (EUR)', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='parking_price';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Такса поддръжка (EUR/мес.)', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='maintenance_fee_price';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Такса поддръжка (условия)', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='maintenance_conditions';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Възможност за довършителни дейности', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (3,4,5) AND name='possibility_finishing_name';

DELETE fi.* FROM _fields_i18n fi, _fields_meta fm
WHERE fi.parent_id = fm.id AND fi.content_type = 'description' AND model='Project' AND model_type IN (1,2) AND name='object_property_desc';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Допълнителна информация', 'bg' FROM _fields_meta WHERE model='Project' AND model_type IN (1,2) AND name='object_property_desc';

DELETE fi.* FROM _fields_i18n fi, _fields_meta fm
WHERE fi.parent_id = fm.id AND fi.content_type = 'description' AND model='Document' AND model_type=3 AND name IN ('offer_date_val', 'include_scheme', 'include_plan_floor');
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Валидност на оферта', 'bg' FROM _fields_meta WHERE model='Document' AND model_type=3 AND name='offer_date_val';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'Схема на имота', 'bg' FROM _fields_meta WHERE model='Document' AND model_type=3 AND name='include_scheme';
INSERT IGNORE INTO _fields_i18n
SELECT id, 'description', 'План на етажа', 'bg' FROM _fields_meta WHERE model='Document' AND model_type=3 AND name='include_plan_floor';

# Replaced checkboxes with radio buttons in a grouping table of property(project) images
UPDATE `_fields_options` SET `extended_value`='приложена към оферта' WHERE parent_name='include_scheme';
UPDATE `_fields_options` SET `extended_value`='приложен към оферта' WHERE parent_name='include_plan_floor';

INSERT IGNORE INTO `_fields_options` (`id`, `parent_name`, `label`, `option_value`, `child_name`, `extended_value`, `position`, `active_option`, `lang`) VALUES (120, 'yes_no', 'да', '1', '', '', 1, 1, 'bg');
INSERT IGNORE INTO `_fields_options` (`id`, `parent_name`, `label`, `option_value`, `child_name`, `extended_value`, `position`, `active_option`, `lang`) VALUES (121, 'yes_no', 'не', '0', '', '', 2, 1, 'bg');

UPDATE `_fields_i18n` SET `content`='За печат' WHERE  `parent_id` IN (SELECT id FROM _fields_meta WHERE name = 'print_picture') AND `content_type`='label' AND `lang`='bg';
UPDATE `_fields_meta` SET type='radio', source='field := yes_no\noptions_align := horizontal', width=200 WHERE model='Project' AND model_type IN (3,4,5) AND name = 'print_picture';
UPDATE `_fields_meta` SET width=482 WHERE type='group' AND grouping IN (25,26,27);

######################################################################################
# 2016-06-02 - Automations that set and remove tag to a project which participate in documents with specified substatus

# Automations that set and remove tag to a project which participate in documents with specified substatus
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
  ('Тагване на проект при задаване на статус на анекс',                 0, NULL, 1, 'documents', NULL, 'action', '17', 'project_tag := 22', 'condition := \'[b_substatus]\' == 23 && \'[a_object_id]\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 1, 1),
  ('Тагване на проект при задаване на статус на резервационна бланка',  0, NULL, 1, 'documents', NULL, 'action', '19', 'project_tag := 21', 'condition := \'[b_substatus]\' == 27 && \'[a_object_id]\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 1, 1),
  ('Тагване на проект при задаване на статус на подготовка нотариат',   0, NULL, 1, 'documents', NULL, 'action', '20', 'project_tag := 23', 'condition := \'[b_substatus]\' == 28 && \'[a_object_id]\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 1, 1),
  ('Премахване на таг на проект при деактивиране или изтриване на анекс',                   0, NULL, 1, 'documents', NULL, 'crontab', '17', 'minutes_interval := 30\r\nproject_tag := 22\r\nremove := true', 'where := d.substatus = 23\r\nwhere := (d.active = 0 OR d.deleted_by > 0)\r\nwhere := a__object_id != \'\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 0, 1),
  ('Премахване на таг на проект при деактивиране или изтриване на резервационна бланка',    0, NULL, 1, 'documents', NULL, 'crontab', '19', 'minutes_interval := 30\r\nproject_tag := 21\r\nremove := true', 'where := d.substatus = 27\r\nwhere := (d.active = 0 OR d.deleted_by > 0)\r\nwhere := a__object_id != \'\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 0, 1),
  ('Премахване на таг на проект при деактивиране или изтриване на подготовка нотариат ',    0, NULL, 1, 'documents', NULL, 'crontab', '20', 'minutes_interval := 30\r\nproject_tag := 23\r\nremove := true', 'where := d.substatus = 28\r\nwhere := (d.active = 0 OR d.deleted_by > 0)\r\nwhere := a__object_id != \'\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 0, 1),
  ('Тагване на проект при активиране на анекс',                 0, NULL, 1, 'documents', NULL, 'crontab', '17', 'minutes_interval := 30\r\nproject_tag := 22', 'where := d.active = 1\r\nwhere := d.substatus = 23\r\nwhere := d.deleted_by = 0\r\nwhere := a__object_id != \'\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 0, 1),
  ('Тагване на проект при активиране на резервационна бланка',  0, NULL, 1, 'documents', NULL, 'crontab', '19', 'minutes_interval := 30\r\nproject_tag := 21', 'where := d.active = 1\r\nwhere := d.substatus = 27\r\nwhere := d.deleted_by = 0\r\nwhere := a__object_id != \'\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 0, 1),
  ('Тагване на проект при активиране на подготовка нотариат',   0, NULL, 1, 'documents', NULL, 'crontab', '20', 'minutes_interval := 30\r\nproject_tag := 23', 'where := d.active = 1\r\nwhere := d.substatus = 28\r\nwhere := d.deleted_by = 0\r\nwhere := a__object_id != \'\'', 'plugin := artstroy\r\nmethod := tagProject', NULL, 0, 0, 1);

#########################################################################################
# 2016-06-17 - Added new report - 'artstroy_history_by_client_project' - for Artstroy installation (ARTSTROY)

# Added new report - 'artstroy_history_by_client_project' - for Artstroy installation (ARTSTROY)
  INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
    (277, 'artstroy_history_by_client_project', 'included_customers_types := \r\nincluded_projects_types := \r\nincluded_tasks_types := ', 0, 0, 1);
  INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
    (277, 'История по клиент/дело', NULL, NULL, 'bg'),
    (277, 'History by client/project', NULL, NULL, 'en');
  INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
    ('reports', 'generate_report', 277, 0, 1),
    ('reports', 'export', 277, 0, 2);
  INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
    SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=277;

#########################################################################################
# 2016-06-28 - Added new automatic actions that check whether the customer has a card and if it is completed properly and sets tag 'potential customer'
#            - Added potential client tag and new client tag as settings

# Added new automatic actions that check whether the customer has a card and if it is completed properly and sets tag 'potential customer'
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `nums`) VALUES
  ('Проверяване за попълнена визитна картичка на контрагент', 'documents', 'before_action', '2', '', 'condition := \'[action]\' == \'add\'', 'plugin := artstroy\r\nmethod := beforePotentialClient', 'cancel_action_on_fail := 1', 0),
  ('Тагване на контрагент при създаване на протокол от среща', 'documents', 'action', '2', '', 'condition := \'[action]\' == \'add\'', 'plugin := artstroy\r\nmethod := potentialClient', NULL, 0);

# Added potential client tag and new client tag as settings
UPDATE `automations` SET `settings`='potential_client_tag_id := 24\r\nnew_client_tag_id := 20' WHERE `method` LIKE 'plugin := artstroy%method := potentialClient';

#########################################################################################
# 2016-07-06 - Added new automation for Artstroy (ARTSTROY) installation for notifing users for changes in customer data
#            - Insert new custom email template for sending the audit changes
#            - Update the usage of some placeholders so they will appear in the emails placeholder table in the interface
#            - Insert new placeholder for the audit changes

# Added new automation for Artstroy (ARTSTROY) installation for notifing users for changes in customer data
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
  ('Известяване за промяна на контрагент', 0, NULL, 1, 'customers', NULL, 'action', '2', 'email_template := email_custom_audit_notification\r\nrecipients := a_person_charge_one_id, a_person_charge_two_id, a_person_charge_three_id\r\nrecipients_type := users', 'condition := (''[action]'' == ''edit'' && ''[request_is_post]'' == ''1'') || ''[action]'' == ''tag''', 'plugin := artstroy\r\nmethod := sendAuditNotification', NULL, 1, 0, 1);

# Insert new custom email template for sending the audit changes
INSERT INTO `emails` (`model`, `model_type`, `name`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
  ('System', 0, 'email_custom_audit_notification', 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
SET @email_id := LAST_INSERT_ID();
INSERT INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
(@email_id, 'Редактиране на [customer_name]', '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n <tbody>\r\n     <tr>\r\n            <td>Здравейте, [user_name],</td>\r\n        </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>Извършена е редакция на <a href="[customer_view_url]">[customer_name]</a>.</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>[last_audit]</td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td><strong>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на <strong>nZoom</strong>.</td>\r\n        </tr>\r\n       <tr>\r\n            <td><em>При възникнали проблеми можете да <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">изпратите e-mail</a> на екипа за поддръжка на <strong>nZoom</strong>.</em></td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n     </tr>\r\n   </tbody>\r\n</table>\r\n\r\n<p>&nbsp;</p>\r\n', '', 'bg', NOW());

# Update the usage of some placeholders so they will appear in the emails placeholder table in the interface
UPDATE `placeholders` SET `pattern_id`=CONCAT(`pattern_id`, ',', @email_id, ',') WHERE
  (`varname`='customer_view_url' AND `model`='Customer' AND `type`='basic' AND `usage`='emails') OR
  (`varname`='user_name' AND `model`='Model' AND `type`='basic' AND `usage`='emails') OR
  (`varname`='customer_view_url' AND `model`='Model' AND `type`='basic' AND `usage`='emails');

# Insert new placeholder for the audit changes
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('last_audit', 'Model', 'basic', 'emails', CONCAT(',', @email_id, ','), '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Таблица с историята на промените', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Table with the changes', NULL, 'en');

#########################################################################################
# 2016-07-12 - Added settings for the generate pattern of contracts with company with id 1 (pattern: 14)
#            - Set default pattern id (for all but company 1) to be with id 17

# Added settings for the generate pattern of contracts with company with id 1 (pattern: 14)
INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 0, 'invoice_company_1_generate_pattern', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '14', NULL, NULL, NOW(), 1, NOW(), 1, '');

# Set default pattern id (for all but company 1) to be with id 17
UPDATE _variables_meta vm
JOIN _variables_cstm vc ON vm.id=vc.var_id AND vm.name='invoice_generate_pattern'
SET vc.value=17;

#########################################################################################
# 2016-07-27 - Insert new placeholder for the footer of the finance documents

# Insert new placeholder for the footer of the finance documents
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`)  VALUES
  ('finance_incomes_reason_invoiced_to', 'Finance_Incomes_Reason', 'basic', 'patterns', NULL, 'invoiced_to', 1);
INSERT INTO `placeholders_i18n`(`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Универсална настройка на футър за финансови документи', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Footer setting for finanace documents', NULL, 'en');

# 2016-08-11 - Add new automation sendNotificationToExternalAccounting

# Add new automation sendNotificationToExternalAccounting
INSERT INTO `automations` (`name`, `automation_type`, `settings`, `conditions`, `method`, `nums`, `active`)  VALUES
  ('Известяване към външно счетоводство', 'crontab', 'start_after := 01:00\r\nstart_before := 04:00\r\nfin_reasons_tag := 25\r\nannulments_tag := 26\r\ncustomer_emails := 1190\r\nnotification_template := 1008\r\nprint_templates := 1, 5, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 45', 'condition := 1', 'plugin := artstroy\r\nmethod := sendNotificationToExternalAccounting', 0, 1);

######################################################################################
# 2016-08-24 - Added setting default variable defining the auto_issue flag of finance invoices templates per installation. The default value is 1.

# Added setting default variable defining the auto_issue flag of finance invoices templates per installation. The default value is 1.
INSERT IGNORE INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 0, 'invoices_auto_issue', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT IGNORE INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'Автоматично издаване', 'bg'),
(LAST_INSERT_ID(), 'label', 'Auto issue', 'en');
INSERT IGNORE INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '1', NULL, NULL, NOW(), 1, NOW(), 1, '');

