#################################################################################
### SQL nZoom Specific Updates ICTVP (https://nzoom.ictvp-bg.com/)      ###
#################################################################################

######################################################################################
# 2017-04-23 - Added new report - 'ictvp_activity_analysis' - for ICTVP installation

# Added new report - 'ictvp_activity_analysis' - for ICTVP installation
SET @id := 362;
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@id, 'ictvp_activity_analysis', 'doc_type_request := 104\r\ndoc_type_report := 105\r\n\r\ndefault_currency := EUR\r\n\r\nrequest_course_type_var := course_type\r\nrequest_assignor_reference_var := reference_client\r\nrequest_truck_var := truck_reg_num\r\nrequest_truck_id_var := truck_reg_num_id\r\nrequest_trailer_var := trailer_reg_num\r\nrequest_trailer_id_var := trailer_reg_num_id\r\nrequest_driver_var := driver_name\r\nrequest_driver_id_var := driver_name_id\r\nrequest_second_driver_var := second_driver\r\nrequest_second_driver_id_var := second_driver_id\r\nrequest_price_var := price_value\r\nrequest_currency_var := kind_currency\r\n\r\nreport_truck_var := truck_reg_num\r\nreport_truck_id_var := truck_reg_num_id\r\nreport_mileage_start_var := old_mileage\r\nreport_mileage_end_var := final_mileage\r\nreport_distance_travelled_var := kilometers_traveled\r\nreport_fuel_start_var := fuel_start_round\r\nreport_fuel_end_var := fuel_finish_round\r\nreport_fuel_filled_var := loaded_fuel\r\nreport_fuel_cost_average_var := cost_liters\r\nreport_total_cost_var := total_cost\r\n\r\nreport_course_var := course_num\r\nreport_course_id_var := course_num_id\r\nreport_date_course_var := date_course\r\nreport_driver_var := driver\r\nreport_second_driver_var := second_driver\r\nreport_assignor_id_var := course_customer\r\nreport_assignor_num_var := num_customer\r\nreport_trailer_var := trailer_reg_num\r\nreport_trailer_id_var := trailer_reg_num_id\r\nreport_course_mileage_end_var := endkm_course\r\nreport_groupage_var := groupage_kind\r\nreport_cmr_var := waybill\r\nreport_cmr_file_var := waybill_file', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@id, 'Анализ на дейността', NULL, NULL, 'bg'),
(@id, 'Activity analysis', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @id, 0, 1),
(NULL, 'reports', '', 'export', @id, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @id;

######################################################################################
# 2019-02-11 - Added new dashlet - 'ictvp_loading_unloading' - for ICTVP installation

# Added new dashlet - 'ictvp_loading_unloading' - for ICTVP installation
INSERT IGNORE INTO `dashlets_plugins` (`type`, `settings`, `is_portal`, `visible`) VALUES
('ictvp_loading_unloading', 'document_transport_request := 104\r\n\r\ncargo_ref_num := reference_number\r\ntruck_id := truck_reg_num_id\r\ntrailer_id := trailer_reg_num_id\r\ndriver_id := driver_name_id\r\nsecond_driver_id := second_driver_id\r\n\r\nloading_planned_date := direction_kind_date\r\nloading_planned_hour := direction_kind_hour\r\nloading_date := move_down_date\r\nloading_hour := move_down_hour\r\nloading_country := loading_country\r\nloading_city := loading_city\r\nloading_address := loading_address\r\n\r\nunloading_planned_date := course_up_date\r\nunloading_planned_hour := course_up_time\r\nunloading_date := fall_date\r\nunloading_hour := fall_time\r\nunloading_country := country_offload\r\nunloading_city := city_offload\r\nunloading_address := address_offload\r\n\r\nstatus_load_started := locked_488\r\nstatus_load_complete := locked_489\r\nstatus_unload_started := locked_490\r\nstatus_unload_complete := locked_491\r\n\r\nconfig_table_iv := trailer_reg_num_id\r\n', 0, 1);

INSERT IGNORE INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`)
SELECT `id`, 'Товарене / разтоварване', NULL, 'bg' FROM `dashlets_plugins` WHERE `type` = 'ictvp_loading_unloading';
INSERT IGNORE INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`)
SELECT `id`, 'Loading / unloading', NULL, 'en' FROM `dashlets_plugins` WHERE `type` = 'ictvp_loading_unloading';

######################################################################################
# 2019-02-18 - Added new report - 'ictvp_machine_availability' - for ICTVP installation
#            - Update the settings of the 'ictvp_machine_availability' report

# Added new report - 'ictvp_machine_availability' - for ICTVP installation
SET @id := 398;
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@id, 'ictvp_machine_availability', '', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@id, 'Заетост на ППС/Машина', NULL, NULL, 'bg'),
(@id, 'Machine availability', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @id, 0, 1),
(NULL, 'reports', '', 'export', @id, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @id;

# Update the settings of the 'ictvp_machine_availability' report
UPDATE `reports` SET `settings`='nom_type_machine_id := 2\r\nnom_kind_machine := kind_pps\r\nnom_kind_machine_k := ppd_kind\r\nnom_type_machine := ppd_type_id\r\nnom_tag_repair := 22\r\n\r\ndocument_transport_request := 104\r\ntruck_id := truck_reg_num_id\r\ntrailer_id := trailer_reg_num_id\r\nunloading_planned_date := course_up_date\r\nunloading_date := fall_date\r\nunloading_country := country_offload\r\nunloading_city := city_offload\r\nunloading_address := address_offload\r\nloading_planned_date := direction_kind_date\r\nloading_date := move_down_date\r\nloading_country := loading_country\r\nloading_city := loading_city\r\nloading_address := loading_address\r\n\r\nnext_course_days_check := 5' WHERE `type`='ictvp_machine_availability' AND `settings`='';
