######################################################################################
### SQL nZoom Specific Updates ДМ Арматурен/HAWLE (http://hawle.n-zoom.com/) ###
######################################################################################

########################################################################
# 2015-11-30 - Added campaigns calls dashlet plugin to the HAWLE installation (Bug 4024)
#            - Added report 'bgservice_calls' to the HAWLE installation (Bug 3075)
#            - Added automation which get the list calls from the previous day and makes events and minitasks from their rows (Bug 3149)
#            - Add automation to remind for cars dates (Bug 4304)
#            - Add new report - 'bgservice_cars' for BGService installation (Bug 3607)

# Added report 'bgservice_calls' to the HAWLE installation (Bug 4024)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
    (NULL, 'campaign_calls', NULL, '0', '1');
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Обаждания по кампании', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Campaign calls', NULL, 'en');

# Added report 'bgservice_calls' to the HAWLE installation (Bug 3075)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (172, 'bgservice_calls', 'document_calls_list := 8\r\nproject_comercial_campaign := 1\r\nproject_request := \r\ncustomer_id := customer_id\r\ncall_status := list_call_status\r\ncall_date := list_to_date\r\ncall_date_next_call := list_datenext_call\r\ncall_minutes := list_call_minute\r\ncall_description := call_description\r\ncall_comment := list_coment\r\n', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (172, 'Обаждания', NULL, NULL, 'bg'),
  (172, 'Calls', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 172, 0, 1),
  ('reports', 'export', 172, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=172;

# Added automation which get the list calls from the previous day and makes events and minitasks from their rows (Bug 3149)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES 
(NULL, 'Лист обаждания - добавяне на събитие и назаначаване на участници', 0, NULL, 1, 'documents', NULL, 'crontab', '8', 'start_time := 01:00\r\nstart_before := 03:00\r\ncall_type := list_call_status\r\ncall_meeting := call_status_second\r\ncall_minitasks := call_status_fift,call_status_first\r\ncall_minitasks_special := call_status_fourth\r\ncustomer_name := customer_name\r\ncustomer_id := customer_id\r\ncall_list_next_step := list_datenext_call\r\ncall_description := call_description\r\ncall_next_step_comment := list_coment\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := bgservice\r\nmethod := callListMeetings', NULL, 0, 0, 1);

# Add automation to remind for cars dates (Bug 4304)
INSERT INTO automations
  SET automation_type = 'crontab',
    module = 'nomenclatures',
    start_model_type = '2',
    name = 'Напомняния за наближаващи дати: ГО, ГТП и данък',
    settings = 'period := 1 day\r\nstart_time := 03:00\r\n\r\nreminders_days := 10,5\r\nremind_users := \r\n\r\nfield_nom_vehicle_insurer_date_finish := insurer_date_finish\r\nfield_nom_vehicle_insurer_payment := insurer_payment\r\nfield_nom_vehicle_technical_review_finish := technical_review_finish\r\nfield_nom_vehicle_tax_finish := tax_finish',
    conditions = 'condition := 1',
    method = 'plugin := bgservice\r\nmethod := remindCarDates',
    nums = 0;

INSERT INTO emails
  SET model = 'Nomenclature',
    model_type = 5,
    name = 'automations_plugin_bgservice_remindcardates',
    active = 1,
    `group` = 1,
    added = NOW(),
    added_by = 1,
    modified = NOW(),
    modified_by = 1;
SELECT @pattern_id := LAST_INSERT_ID();
INSERT INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
  (@pattern_id, 'Напомняне за [nomenclature_type_name] „[nomenclature_name]“ с рег. №: [nomenclature_code]', '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n  <tbody>\r\n    <tr>\r\n      <td>Здравейте, [user_name],</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>Имате напомняне за <a href="[nomenclature_view_url]">[nomenclature_type_name] „[nomenclature_name]“ с рег. №: [nomenclature_code]</a> <em>(добавил/а [nomenclature_added_by])</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>[reminder_description]</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td><strong>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на <strong>nZoom</strong>.</td>\r\n    </tr>\r\n    <tr>\r\n      <td><em>При възникнали проблеми можете да <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">изпратите e-mail</a> на екипа за поддръжка на <strong>nZoom</strong>.</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n  </tbody>\r\n</table>\r\n', 'Напомняне за номенклатура.', 'bg', NOW()),
  (@pattern_id, 'Reminder for [nomenclature_type_name] "[nomenclature_name]" with reg. No: [nomenclature_code]', '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n  <tbody>\r\n    <tr>\r\n      <td>Hello, [user_name],</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>You have a reminder for <a href="[nomenclature_view_url]">[nomenclature_type_name] "[nomenclature_name]" with reg. No: [nomenclature_code]</a> <em>(added by [nomenclature_added_by])</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>[reminder_description]</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td><strong>PLEASE, DO NOT REPLY TO THIS MESSAGE!</strong> It was sent by the <strong>nZoom</strong>&#39;s notification system from an unmonitored e-mail address. Mail sent to this address cannot be answered.</td>\r\n    </tr>\r\n    <tr>\r\n      <td><em>Should you encounter any problems, please do not hesitate to <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">send an e-mail back</a> to the <strong>nZoom</strong> support team.</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n  </tbody>\r\n</table>\r\n', 'Reminder of nomenclature.', 'en', NOW());

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_type_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Име на типа номенклатура', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature type name', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('user_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Имена на потребителя', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Name of user', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Име на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature name', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_code', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Код на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature code', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_view_url', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Адресът за достъпване на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'The address for accessing the nomenclature', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_added_by', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Имена на потребителя добавил номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Name of the user added the nomenclature', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('reminder_description', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Напомняне: описание', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Reminder: description', NULL, 'en');

# Add new report - 'bgservice_cars' for HAWLE installation (Bug 3607)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (227, 'bgservice_cars', 'nom_type_car := 5\r\nnom_type_part_service := 7\r\ncustomer_car_owner := 8\r\ncustomer_insurers := 2,6,7\r\n\r\ncar_owned_by := firm_bgs_id\r\ncar_insured_by := insurer_id\r\ncar_insured_until := insurer_date_finish\r\ncar_inspected_until := technical_review_finish\r\ncar_tax_paid_until := tax_finish\r\ncar_insurance_payment_type := insurer_payment\r\n\r\ndoc_type_fix_card := 7\r\nfix_card_car := car_id\r\nfix_card_total := total\r\n\r\ninvoice_total := total\r\n\r\nexpenses_reason_type_id := \r\nexpenses_reason_total := total\r\n\r\ndoc_request_expense := 6\r\nrequest_expense_total := total', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (227, 'Автомобили', NULL, NULL, 'bg'),
  (227, 'Cars', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 227, 0, 1),
  ('reports', 'export', 227, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 227;

########################################################################
# 2015-12-16 - Add automation to remind for cars dates (Bug 4304)

# Fixed the start model type of the automation that reminds of cars dates (Bug 4304)
UPDATE `automations` SET `start_model_type`='5' WHERE  method like '%remindCarDates%';
