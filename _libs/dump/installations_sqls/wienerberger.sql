#######################################################################################
### SQL nZoom Specific Updates <PERSON><PERSON><PERSON> (http://wienerberger.n-zoom.com/) ###
#######################################################################################

########################################################################
# 2014-11-12 - Added automation that changes the current user groups thus allowing access to certain layouts

# Added automation that changes the current user groups thus allowing access to certain layouts
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
VALUES (1, 'Задава на текущия потребител времнно група за да осигури достъп до конкретна секция (при отметнат таг)', 0, NULL, 1, 'projects', NULL, 'before_action', '1', 'group_id := 2\r\ntag_id := 9', 'condition := in_array(\'[action]\', array(\'add\', \'edit\', \'view\'))\r\n', 'plugin := wienerberger\r\nmethod := changeCurrentuserGroups', NULL, 0, 0, 1);

########################################################################
# 2014-11-21 - Added automation for creating table in schedule project document
#            - Added automation for checking if the product sums in Project schedule document are the same as the product sums in the related project

# Added automation for creating table in schedule project document
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Създаване на график за проект', 0, NULL, 1, 'projects', NULL, 'action', '1', 'tag_id := 9\r\nschedule_document := 4\r\nnew_document_substatus := 11\r\n\r\ndocument_period := period_field\r\ndocument_product_id := product_field_id\r\ndocument_product := product_field\r\ndocument_measure := measure_field\r\ndocument_amount := amount_field\r\ndocument_distributor_id := distributor_field_id\r\ndocument_distributor := distributor_field\r\ndocument_validity := validity_to\r\n\r\nproject_product := product_object_id\r\nproject_product_name := product_object\r\nproject_distributor := distributor_object_id\r\nproject_distributor_name := distributor_object\r\nproject_measure := measure_product\r\nproject_amount := amount_name\r\nproject_period := use_month_name\r\nproject_validity := date_validation', 'condition := 1', 'plugin := wienerberger\r\nmethod := createSchedule', NULL, 1, 0, 1);

# Added automation for checking if the product sums in Project schedule document are the same as the product sums in the related project
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация на общото количество в график', 0, NULL, 1, 'documents', NULL, 'before_action', '4', 'doc_product := product_field_id\r\ndoc_distributor := distributor_field_id\r\ndoc_validity := validity_to\r\ndoc_amount := amount_field\r\n\r\nproject_type_id := 1\r\nproject_product := product_object_id\r\nproject_distributor := distributor_object_id\r\nproject_validity := date_validation\r\nproject_amount := amount_name', 'condition := in_array(''[action]'', array(''add'', ''edit''))\r\n', 'plugin := wienerberger\r\nmethod := validateTotalQuantity', 'cancel_action_on_fail := 1', 0, 0, 1);

######################################################################################
# 2014-11-26 - Added pattern plugin "prepareOffer" for "wienerberger" used for printing of "Offer" type documents
#            - Added placeholders for pattern plugin "prepareOffer" for "wienerberger"

# Added pattern plugin "prepareOffer" for "wienerberger" used for printing of "Offer" type documents
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `added`, `modified`) VALUES
  (71, 'Document', 1, 'wienerberger', 'prepareOffer', 'categories := 2,3,4,5,6', NOW(), NOW());
  INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (71, 'Подготовка на "Оферта"', 'Подготвят се данни за печат на документ от тип "Оферта".', 'bg', NOW()),
  (71, 'Preparation for "Offer"', 'Prepare data for printing of "Offer" type documents.', 'en', NOW());

# Added placeholders for pattern plugin "prepareOffer" for "wienerberger"
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('offer_table', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Таблица в офертата', 'bg'),
  (LAST_INSERT_ID(), 'Offer Table', 'en');
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('offer_notes', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Условия и бележки по тях', 'bg'),
  (LAST_INSERT_ID(), 'Notes', 'en');
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('employee_signature', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подпис (изображение) на служителя', 'bg'),
  (LAST_INSERT_ID(), 'Employee Signature (image)', 'en');
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('employee_position', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Длъжност на служителя', 'bg'),
  (LAST_INSERT_ID(), 'Employee Position', 'en');
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('employee_phone', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Телефон на служителя', 'bg'),
  (LAST_INSERT_ID(), 'Employee Phone', 'en');
INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('employee_email', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Имейл адрес на служителя', 'bg'),
  (LAST_INSERT_ID(), 'Employee Email', 'en');

######################################################################################
# 2014-12-10 - Added dashlet for managing meeting protocols

# PRE-DEPLOYED # Added dashlet for managing meeting protocols
#INSERT INTO dashlets_plugins
#  SET `type` = 'wienerberger_meeting_protocol',
#    settings = 'document_type_meeting_protocol := 3';
#INSERT INTO dashlets_plugins_i18n (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Добавяне/Редакция на Протокол от среща', 'Инфо панел за добавяне и редакция на протоколи от среща.', 'bg'),
#  (LAST_INSERT_ID(), 'Adding/Editing Meeting protocol', 'Dashlet for adding and editing meeting protocols.', 'en');

######################################################################################
# 2014-12-23 - Added two automations for managing customer bonus points in WIENERBERGER installation

# Added two automations for managing customer bonus points in WIENERBERGER installation
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Калкулиране на бонус точки от ''Заявление за получаване на точки''', 0, NULL, 1, 'documents', NULL, 'action', '9', 'request_receiving_points := 9\r\nrequest_receiving_rewards := 7\r\n\r\ndoc_total_points := all_points\r\n\r\ncustomer_total_points := bonus_all\r\nrequest_getting_rewards_points_left := bonus_leave', 'condition := "[prev_b_substatus]" != "28"\r\ncondition := "[b_substatus]" == "28"', 'plugin := wienerberger\r\nmethod := calculateBonusPoints', NULL, 1, 1, 1),
('Калкулиране на бонус точки от ''Заявка за получаване на награди''', 0, NULL, 1, 'documents', NULL, 'action', '7', 'request_receiving_points := 9\r\nrequest_receiving_rewards := 7\r\n\r\ndoc_total_points := bonus_leave\r\n\r\ncustomer_total_points := bonus_all\r\nrequest_getting_rewards_points_left := bonus_leave', 'condition := "[prev_b_substatus]" != "32"\r\ncondition := "[b_substatus]" == "32"', 'plugin := wienerberger\r\nmethod := calculateBonusPoints', NULL, 1, 1, 1);

######################################################################################
# 2015-01-14 - Added additional settings for createSchedule automation so the automation to complete the document related to the project

# PRE-DEPLOYED # Added additional settings for createSchedule automation so the automation to complete the document related to the project
#UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\nproject_schedule_id := schedule_id\r\nproject_schedule_name := schedule_doc') WHERE `method` LIKE '%method := createSchedule%';

######################################################################################
# 2015-01-27 - Added new import for invoices data in projects' schedules

# Added new import for invoices data in projects' schedules
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(16, 'wienerberger_sap_schedule', '#columns relations\r\n\r\ninvoice_number := A\r\nproduct_code := B\r\ninvoice_type := D\r\nregion_code := E\r\nincoterms := F\r\ndeliverer_code := G\r\ninvoice_position_ref := H\r\ninvoice_position := I\r\nwarehouse_name := K\r\nregion_ps_name := L\r\nbuilder_code := M\r\nproject := O\r\ncontract_number := P\r\ninvoice_quantity := Q\r\ninvoice_date := R\r\ninvoice_price := S\r\n\r\nprice_diff_percent := 0.25', 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(16, 'Invoices data into schedule import', 'Import of invoices data exported via SAP into documents of type Schedule', ''),
(16, 'Импорт на данни от фактури в график', 'Импорт на данни от фактури експортирани през SAP в документ от тип График', 'bg');

######################################################################################
# 2015-02-23 - Added new automation for models creation

# Added new automation for models creation
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Създаване на задачи от заявление за обект (преди приключване от АД 19)', 0, NULL, 1, 'documents', NULL, 'action', '5', '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := [b_name]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\nplanned_start_date := [b_date]\r\nplanned_finish_date := [b_deadline]\r\ndescription := [b_name]\r\ndepartment := 1\r\ngroup := 1\r\n\r\nnew_assign_owner := [assignments_owner]\r\nnew_assign_observer := [assignments_observer]\r\nnew_assign_decision := [assignments_decision]\r\nnew_assign_responsible := [assignments_responsible]\r\n\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]', 'condition := "[prev_b_substatus]" != "13"\ncondition := "[b_substatus]" == "13"\ncondition := [a_distributor_rate] <= [a_ocl_first]', 'method := createModels\r\n', '', 1, 1, 1),
(NULL, 'Създаване на задачи от заявление за обект', 0, NULL, 1, 'documents', NULL, 'before_action', '5', '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := [b_name]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\nplanned_start_date := [b_date]\r\nplanned_finish_date := [b_deadline]\r\ndescription := [b_name]\r\ndepartment := 1\r\ngroup := 1\r\n\r\nnew_assign_owner := [assignments_owner]\r\nnew_assign_observer := [assignments_observer]\r\nnew_assign_decision := [assignments_decision]\r\nnew_assign_responsible := [assignments_responsible]\r\n\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]\r\n\r\n\r\n', 'condition := ''[request_is_post]'' && ''[action]'' == ''setstatus'' && $this->registry->get(''request'')->get(''substatus'') == ''closed_38'' && ''[b_substatus]'' != ''38''\r\n', 'method := createModels\r\n', 'cancel_action_on_fail := 1', 1, 1, 1);

######################################################################################
# 2015-02-26 - Added new report for wienerberger - wienerberger_realized_quantities

# Added new report for wienerberger - wienerberger_realized_quantities
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(306, 'wienerberger_realized_quantities', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(306, 'Реализирани количества', NULL, NULL, 'bg'),
(306, 'Realized quantities', NULL, NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '306', '0', '1'),
  ('reports', 'export', '306', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '306';

######################################################################################
# 2015-03-09 - Added new report 'wienerberger_monthly_report'

# Added new report 'wienerberger_monthly_report'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
    (307, 'wienerberger_monthly_report', 'wmr_nomenclatures_type_region := 8\r\nwmr_documents_year := report_for_year\r\nwmr_documents_month := report_for_month\r\nwmr_documents_region := region_name_id\r\nwmr_documents_type := 6\r\nwmr_assestment := assessment_month_scale\r\nwmr_assestment_adv := assessment_month\r\nwmr_trends := trends_name\r\nwmr_resources := resources\r\nwmr_risks := risks\r\nwmr_builders := builders_name\r\nwmr_investors := investors_name\r\nwmr_traders := trade_name\r\nwmr_designers := designers_name\r\nwmr_pth := pth_name__first\r\nwmr_for := for_name__first\r\nwmr_lintels := lintels_name__first\r\nwmr_total_bricks := total_bricks__first\r\nwmr_factory := factory_name\r\nwmr_status := status_name\r\nwmr_date_stop_start := Date_start_stop\r\nwmr_quantity := availability_num\r\nwmr_city := city_name\r\nwmr_cpt_price := cpt_price\r\nwmr_cpt_tnf := cpt_tnf\r\nwmr_competitor := competitor_name\r\nwmr_exw_price := exw_pcs\r\nwmr_exw_tnf := exw_tnf\r\nwmr_cities := 5\r\nwmr_meeting_contact := person_id\r\nwmr_employee_contact := contact_person_wiener_id\r\nwmr_document_type := 3', 0, 0, 1);

INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
    (307, 'Месечен отчет на ТП' , NULL, 'Месечен отчет на търговски представител.', 'bg'),
    (307, 'Monthly report of reseller', NULL, 'Monthly report of reseller', 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '307', '0', '1'),
  ('reports', 'export', '307', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '307';

######################################################################################
# 2015-03-25 - Added pattern plugin "prepareStatement" for "wienerberger" used for printing of "Object statement" type documents

# PRE-DEPLOYED # Added pattern plugin "prepareStatement" for "wienerberger" used for printing of "Object statement" type documents
# INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `added`, `modified`) VALUES
#   (73, 'Document', 5, 'wienerberger', 'prepareStatement', '', NOW(), NOW());
# INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
#   (73, 'Подготовка на "Заявление за обект"', 'Подготвят се данни за печат на документ от тип "Заявление за обект".', 'bg', NOW()),
#   (73, 'Preparation for "Object statement"', 'Prepare data for printing of "Object statement" type documents.', 'en', NOW());
#
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('employee_plosition', 'Document', 'basic', 'pattern_plugins', ',73,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Длъжност на служителя', 'bg'),
#   (LAST_INSERT_ID(), 'Employee position', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('project_num', 'Document', 'basic', 'pattern_plugins', ',73,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), '№ на проект', 'bg'),
#   (LAST_INSERT_ID(), 'Project #', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('project_city', 'Document', 'basic', 'pattern_plugins', ',73,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Населено място', 'bg'),
#   (LAST_INSERT_ID(), 'City', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('project_zip', 'Document', 'basic', 'pattern_plugins', ',73,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Пощенски код', 'bg'),
#   (LAST_INSERT_ID(), 'ZIP', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('project_address', 'Document', 'basic', 'pattern_plugins', ',73,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Адрес', 'bg'),
#   (LAST_INSERT_ID(), 'Address', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('project_region', 'Document', 'basic', 'pattern_plugins', ',73,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Регион', 'bg'),
#   (LAST_INSERT_ID(), 'Region', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('builders_distriutors_table', 'Document', 'basic', 'pattern_plugins', ',73,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Таблица със строители и дистрибутори', 'bg'),
#   (LAST_INSERT_ID(), 'Table with builders and distributors', 'en');

######################################################################################
# 2015-03-27 - Changed the placeholders for pattern plugin "prepareOffer" for "wienerberger"

# PRE-DEPLOYED # Changed the placeholders for pattern plugin "prepareOffer" for "wienerberger"
# DELETE pi.* FROM placeholders p, placeholders_i18n pi WHERE p.id=pi.parent_id AND `pattern_id`=',71,' AND `usage`='pattern_plugins';
# DELETE p.* FROM placeholders p WHERE `pattern_id`=',71,' AND `usage`='pattern_plugins';
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('offer_table', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Таблица в офертата', 'bg'),
#   (LAST_INSERT_ID(), 'Offer Table', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('employee_signature', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Подпис (изображение) на служителя', 'bg'),
#   (LAST_INSERT_ID(), 'Employee Signature (image)', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('employee_position', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Длъжност на служителя (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Employee Position', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('employee_phone', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Телефон на служителя (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Employee Phone', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('employee_email', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Имейл адрес на служителя (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Employee Email', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('notes_pay', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Бележки плащания (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Notes payments', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('min_quantity', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Бележки минимални количества (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Notes payments', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('notes_term', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Бележки условия (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Notes payments', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('notes_price', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Бележки цени (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Notes payments', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('notes_delivery', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Бележки доставка (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Notes payments', 'en');
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('notes_additional', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Бележки допълнителни (Важно: добавя нов ред преди стойността)', 'bg'),
#   (LAST_INSERT_ID(), 'Notes payments', 'en');

######################################################################################
# 2015-06-15 - Added new report "wienerber_partner_program"

INSERT INTO `reports` (`type`, `settings`, `position`, `is_portal`, `visible`) VALUES
    ('wienerberger_partner_program', 'report_filters_required := period_from\r\nreport_model_type := Customer, Document, Document, Customer, Document, Nomenclature, Nomenclature\r\nreport_document_type := 3,7,8,3,9,1,1\r\nreport_additional_vars := region_dist, total, total, bonus_all, total, reward_price, price_amount\r\nwpp_filters_partner_type_id := 3\r\nwpp_filters_article_type_id := 1\r\nwpp_filters_sale_agent_type_id := 1\r\nwpp_filters_region_type_id := 8\r\nwpp_filters_permission_ids := 1\r\nwpp_filters_multi := partners,prizes\r\nwpp_query_filter_date := date\r\nwpp_query_filter_partner := customer\r\nwpp_query_filter_prize_id := article_id\r\nwpp_query_filter_prize_name := article_name\r\nwpp_query_filter_tp := employee\r\nwpp_query_filter_region := region_dist\r\nwpp_query_doc_rpr := 7\r\nwpp_query_doc_rpo := 9\r\nwpp_query_doc_repr := 8\r\nwpp_query_object_model_id := 1\r\nwpp_query_doc_rpr_model := Document\r\nwpp_query_doc_rpo_model := Document\r\nwpp_query_doc_repr_model := Document\r\nwpp_query_object_model := Project\r\nwpp_total_points := total\r\nwpp_query_rpo_wanted_substatus := 28\r\nwpp_query_rpo_wanted_status := closed\r\nwpp_query_rpr_wanted_substatus := 32\r\nwpp_query_rpr_wanted_status := closed\r\nwpp_query_filter_bonus := bonus_all\r\nwpp_query_prize_nom_type := 1\r\nwpp_query_reward_price := reward_price\r\nwpp_query_price_amount := price_amount', 0, 0, 1);

INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
    (LAST_INSERT_ID(), 'Програма „Партньор“', NULL, NULL, 'bg'),
    (LAST_INSERT_ID(), '"Partner" Program', NULL, NULL, 'en');

######################################################################################
# 2015-06-16 - Modified and removed bugs from report "wienerber_partner_program"

UPDATE `reports`
SET `settings` = 'report_filters_required := period_from\r\nreport_model_type := Customer, Document, Document, Customer, Document, Nomenclature, Nomenclature, Nomenclature\r\nreport_document_type := 3,7,8,3,9,1,1,1\r\nreport_additional_vars := region_dist, total, total, bonus_all, all_points, reward_price, price_amount, bonus_points\r\nwpp_filters_partner_type_id := 3\r\nwpp_filters_article_type_id := 1\r\nwpp_filters_sale_agent_type_id := 1\r\nwpp_filters_region_type_id := 8\r\nwpp_filters_permission_ids := 1\r\nwpp_filters_multi := partners,prizes\r\nwpp_query_filter_date := date\r\nwpp_query_filter_partner := customer\r\nwpp_query_filter_prize_id := article_id\r\nwpp_query_filter_prize_name := article_name\r\nwpp_query_filter_tp := employee\r\nwpp_query_add_region := region_dist_3\r\nwpp_query_doc_rpr := 7\r\nwpp_query_doc_rpo := 9\r\nwpp_query_doc_repr := 8\r\nwpp_query_object_model_id := 1\r\nwpp_query_doc_rpr_model := Document\r\nwpp_query_doc_rpo_model := Document\r\nwpp_query_doc_repr_model := Document\r\nwpp_query_object_model := Project\r\nwpp_query_rpo_wanted_substatus := 28\r\nwpp_query_rpo_wanted_status := closed\r\nwpp_query_rpr_wanted_substatus := 32\r\nwpp_query_rpr_wanted_status := closed\r\nwpp_query_add_bonus := bonus_all_3\r\nwpp_query_prize_nom_type := 1\r\nwpp_query_add_reward_price := reward_price_1\r\nwpp_query_add_price_amount := price_amount_1\r\nwpp_query_add_total_points_rpr := total_7\r\nwpp_query_add_total_points_rpo := total_8\r\nwpp_query_add_total_points_repr := all_points_9\r\nwpp_query_tag_section := 4\r\nwpp_query_add_bonus_points := bonus_points_1\r\n'
WHERE `reports`.`type` = 'wienerberger_partner_program';

######################################################################################
# 2015-06-22 - Modified and removed bugs from report "wienerberger_partner_program"
#            - Adding roles permissions and definitions for "wienerber_partner_program"
#            - Updating the ID of the report because I've made a mistake
#            - Updating the report settings of "wienerberger_partner_program"
#            - Updating parent_id of the report "wienerberger_partner_program" i18n table

# Updating the ID of the report because I've made a mistake
SELECT @winerberger_partner_program_id := `id` FROM `reports` WHERE `type` = 'wienerberger_partner_program';

UPDATE `reports`
  SET `id` = 319
  WHERE `id` = @winerberger_partner_program_id;

# Adding roles permissions and definitions for "wienerber_partner_program"
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '319', '0', '1'),
  ('reports', 'export', '319', '0', '2');

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '319';

# Updating the report settings of "wienerberger_partner_program"
UPDATE `reports`
  SET `settings` = 'report_filters_required := period_from\r\nreport_model_type := Customer, Customer, Document, Document, Customer, Document, Nomenclature, Nomenclature, Nomenclature\r\nreport_document_type := 3,3,7,8,3,9,1,1,1\r\nreport_additional_vars := region_dist, region_dist_id, total, total, bonus_all, all_points, reward_price, price_amount, bonus_points\r\nwpp_filters_partner_type_id := 3\r\nwpp_filters_article_type_id := 1\r\nwpp_filters_sales_agent_type_id := 1\r\nwpp_filters_region_type_id := 8\r\nwpp_filters_permission_ids := 1\r\nwpp_filters_multi := partners,prizes\r\nwpp_query_filter_date := date\r\nwpp_query_filter_partner := customer\r\nwpp_query_filter_prize_id := article_id\r\nwpp_query_filter_prize_name := article_name\r\nwpp_query_filter_tp := employee\r\nwpp_query_add_region := region_dist_3\r\nwpp_query_add_region_id := region_dist_id_3\r\nwpp_query_doc_rpr := 7\r\nwpp_query_doc_rpo := 9\r\nwpp_query_doc_repr := 8\r\nwpp_query_object_model_id := 1\r\nwpp_query_doc_rpr_model := Document\r\nwpp_query_doc_rpo_model := Document\r\nwpp_query_doc_repr_model := Document\r\nwpp_query_object_model := Project\r\nwpp_query_rpo_wanted_substatus := 28\r\nwpp_query_rpo_wanted_status := closed\r\nwpp_query_rpr_wanted_substatus := 32\r\nwpp_query_rpr_wanted_status := closed\r\nwpp_query_add_bonus := bonus_all_3\r\nwpp_query_prize_nom_type := 1\r\nwpp_query_add_reward_price := reward_price_1\r\nwpp_query_add_price_amount := price_amount_1\r\nwpp_query_add_total_points_rpr := total_7\r\nwpp_query_add_total_points_rpo := all_points_9\r\nwpp_query_add_total_points_repr := total_8\r\nwpp_query_tag_section := 4\r\nwpp_query_add_bonus_points := bonus_points_1'
  WHERE `type` = 'wienerberger_partner_program';

# Updating parent_id of the report "wienerberger_partner_program" i18n table
UPDATE `reports_i18n`
  SET `parent_id` = 319
  WHERE `name` IN ('Програма „Партньор“', '"Partner" Program');

######################################################################################
# 2015-06-25 - Adding javascript functionality to some of the fields from document type (9)

# Adding javascript functionality to some of the fields from document type (9)
UPDATE `_fields_meta`
  SET `source` = CONCAT(`source`, "\r\non_change := calculateReceivedPoints()")
  WHERE `model` = 'Document' AND `model_type` = 9 AND `name` = 'first_porotherm' AND `source` NOT LIKE '%calculateReceivedPoints%';

UPDATE `_fields_meta`
  SET `source` = CONCAT(`source`, "\r\non_change := calculateReceivedPoints()")
  WHERE `model` = 'Document' AND `model_type` = 9 AND `name` = 'used_lintel' AND `source` NOT LIKE '%calculateReceivedPoints%';

 UPDATE `_fields_meta`
   SET `source` = CONCAT(`source`, "\r\nautocomplete_execute_after := calculateReceivedPoints")
   WHERE `model` = 'Document' AND `model_type` = 9 AND `name` = 'article_name' AND `source` NOT LIKE '%calculateReceivedPoints%';

 UPDATE `_fields_meta`
   SET `source` = IF(`source` RLIKE 'gt2calc', REPLACE(`source`, 'js_method := onkeyup => gt2calc(this)', 'js_method := onkeyup => calculateReceivedPoints(0,0,this)'), CONCAT(`source`, "\r\njs_method := onkeyup => calculateReceivedPoints(0,0,this)"))
   WHERE `model` = 'Document' AND `model_type` = 9 AND `name` = 'price' AND `source` NOT LIKE '%calculateReceivedPoints%';

UPDATE `_fields_meta`
  SET `source` = CONCAT(`source`, "\r\non_change := calculateReceivedPoints()")
  WHERE `model` = 'Document' AND `model_type` = 9 AND `name` = 'article_id' AND `source` NOT LIKE '%calculateReceivedPoints%';

UPDATE `_fields_meta`
  SET `source` = CONCAT(`source`, "\r\nsequence :=\r\nonclick := calculateReceivedPoints()")
    WHERE `model` = 'Document' AND `model_type` = 9 AND `name` = 'before_save' AND `source` NOT LIKE '%calculateReceivedPoints%';

 UPDATE `_fields_meta`
   SET `source` = CONCAT(`source`, "\r\njavascript := function calculateReceivedPoints(e,i,t){if(t&&void 0!=t){var l=window.event.which;if(48>l&&l>57||96>l&&l>105)return}var a={base:[26,39,41,42,47],gold:[43,44]},d={first_radio:50,second_radio:50},r={base:'free_field2',silver:'free_field3',gold:'free_field4',first_radio:'first_porotherm_2',second_radio:'used_lintel_1',id_field:'article_id',total_bonus_points:'quantity',program_field:'program_field',all_points:'all_points',total_points:'total',deleted_field:'deleted'},o=!1,n=0,_=function(e){if(/.*_([0-9]+)/.test(e)){var i=$$('input[id^='+r.deleted_field+'_]');if(i){var t=$(r.deleted_field+'_'+e.match(/.*_([0-9]+)/)[1]).value;if(t)return!0}}return!1},u=function(){return $$('input[name^='+r.id_field+']').each(function(e){if(!_(e.id)){if($(a.gold).include(e.value))throw o='gold',$break;$(a.base).include(e.value)&&n++}}),o||1!=n?!o&&n>1&&(o='silver'):o='base',window.program=o,o},f=function(){var e=0;return $(r.first_radio).checked&&(e+=d.first_radio),$(r.second_radio).checked&&(e+=d.second_radio),e},s=function(){var e=u();$$('input[name^='+r.id_field+']').each(function(i){if(/.*_([0-9]+)$/.test(i.id)&&$$('input[name^='+r.id_field+']').length){var t=i.id.match(/.*_([0-9]+)$/)[1];if(_(i.id))return;var l={base:{title:$(r.base+'_'+t).title,value:$(r.base+'_'+t).value},silver:{title:$(r.silver+'_'+t).title,value:$(r.silver+'_'+t).value},gold:{title:$(r.gold+'_'+t).title,value:$(r.gold+'_'+t).value}};$(r.total_bonus_points+'_'+t).value=l[e].value,$(r.program_field).value=l[e].title,gt2calc()}});var i=f(),t=$(r.total_points).value;$(r.all_points).value=parseFloat(t)+parseFloat(i)};$$('input[name^='+r.id_field+']').length>0&&s()}")
   WHERE `model` = 'Document' AND `model_type` = 9 AND `name` = 'group_table_2' AND `source` NOT LIKE '%calculateReceivedPoints%';

######################################################################################
# 2015-07-21 - Added automation for validation of 'setstatus' action in documents type (8)

# Added automation for validation of 'setstatus' action in documents type (8)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Коригиране на наличности в Награди', 0, NULL, 1, 'documents', NULL, 'before_action', '8', 'automation_meta_names := amount_taken,amount_from_group,available_amount_sum\r\nautomation_meta_models := Nomenclature,Nomenclature,Nomenclature\r\nautomation_meta_model_types := 1,1,1\r\ndocument_substatus_annuled := 43\r\ndocument_substatus_done := 40', 'condition := ''[action]'' == ''setstatus''', 'plugin := wienerberger\r\nmethod := quantityCorrector', 'cancel_action_on_fail := 1', 2, 0, 1);

######################################################################################
# 2015-07-28 - Optimized automation for status validation of prize protocols

# Optimized automation for status validation of prize protocols
UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\nautomation_add_name_1 := amount_taken\r\nautomation_add_name_2 := amount_from_group\r\nautomation_add_name_3 := available_amount_sum') WHERE `method` LIKE '%method := quantityCorrector%' AND `settings` NOT LIKE '%automation_add_name_1%';

######################################################################################
# 2015-08-07 - Added two automations to manage the documents for correcting bonus points

# Added two automations to manage the documents for correcting bonus points
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация на корекцията за получени точки', 0, NULL, 1, 'documents', NULL, 'before_action', '11', 'request_points_correction_field := all_points\r\nrequest_points_type := 9\r\nrequest_points_filed := all_points\r\ncustomer_bonus_points := bonus_all', 'condition := ''[request_is_post]'' && ''[action]'' == ''setstatus'' && $this->registry->get(''request'')->get(''substatus'') == ''closed_44'' && ''[b_substatus]'' != ''44''', 'plugin := wienerberger\r\nmethod := validateBonusPointsCorrection', 'cancel_action_on_fail := 1', 1, 0, 1),
('Корекция на получени точки', 0, NULL, 1, 'documents', NULL, 'action', '11', 'request_points_type := 9\r\nrequest_points_bonus_points := all_points\r\nrequest_points_program := program_field\r\nrequest_points_used_lintel := used_lintel\r\nrequest_points_first_porotherm := first_porotherm\r\n\r\nrequest_points_correction_bonus_points := all_points\r\nrequest_points_correction_program := program_field\r\nrequest_points_correction_used_lintel := used_lintel\r\nrequest_points_correction_first_porotherm := first_porotherm', 'condition := "[prev_b_substatus]" != "44"\r\ncondition := "[b_substatus]" == "44"', 'plugin := wienerberger\r\nmethod := applyBonusCorrection', NULL, 1, 1, 1);

######################################################################################
# 2015-08-10 - Added additional field to be changed by the calculateBonusPoints automation

# Added additional field to be changed by the calculateBonusPoints automation
UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\nrequest_getting_rewards_points_available := points_view') WHERE `method` LIKE '%method := calculateBonusPoints%' AND `settings` NOT LIKE '%request_getting_rewards_points_available%';

######################################################################################
# 2016-03-22 - Change settings of report 'wienerberger_monthly_report'

# Change settings of report 'wienerberger_monthly_report'
UPDATE reports
  SET settings = CONCAT(settings, '\r\nwmr_agreed := agreed_name\r\nwmr_started := started_name\r\nwmr_partners := partners_name')
  WHERE type = 'wienerberger_monthly_report'
    AND settings NOT LIKE '%wmr_agreed%';

######################################################################################
# 2016-04-05 - Changed the pattern of Wieneberberger Offer (type 1) pattern to be used:
#            - both for companies and persons and in both formats: PDF and DOCX
#            - Inserted new placeholders for salutation
#            - Fixed employee_position placeholder spelling error

# Changed the pattern of Wieneberberger Offer (type 1) pattern to be used both for companies and persons and in both formats: PDF and DOCX
UPDATE `patterns_i18n` SET name='Оферта pdf', content = '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n	<tbody>\r\n		<tr>\r\n			<td style="width:5%">&nbsp;</td>\r\n			<td style="width:98%">\r\n			<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n				<tbody>\r\n					<tr>\r\n						<td style="vertical-align:top; width:20%"><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">До</span></span></td>\r\n						<td style="width:85%"><span style="font-size:20px"><strong><span style="font-family:arial,helvetica,sans-serif">[salutation1]</span></strong></span></td>\r\n					</tr>\r\n					<tr>\r\n						<td>&nbsp;</td>\r\n						<td>&nbsp;</td>\r\n					</tr>\r\n					<tr>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Номер на оферта:</span></span></td>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[document_num]</span></span></td>\r\n					</tr>\r\n					<tr>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Дата:</span></span></td>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[document_added_date|date_format:%d.%m.%Y] г.</span></span></td>\r\n					</tr>\r\n					<tr>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Страници:</span></span></td>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[system_pages]</span></span></td>\r\n					</tr>\r\n				</tbody>\r\n			</table>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td style="text-align:center"><span style="font-size:48px"><span style="font-family:arial,helvetica,sans-serif"><strong>ОФЕРТА</strong></span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-family:arial,helvetica,sans-serif"><span style="font-size:20px">&nbsp;&nbsp;&nbsp; [salutation2],</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px">&nbsp;&nbsp;&nbsp; <span style="font-family:arial,helvetica,sans-serif">Имам удоволствието да Ви представя нашата оферта за доставка на керамични блокове, произведени от Винербергер ЕООД и съпътстващи продукти за изпълнението на Вашия обект в гр. [object_city], като предлагаме следните условия:</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>\r\n			<ol style="list-style-type:upper-roman">\r\n				<li>Цени и начин на плащане\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>Цени<br />\r\n					[offer_table]</li>\r\n					<li>Посочените цени са без начислен ДДС[notes_pay]</li>\r\n					<li>Към посочените цени на керамичните блокове, ще бъде допълнително калкулирана единична цена от 12,80 лв. без ДДС за палет за многократна употреба, който ще бъде изкупен на същата цена при връщането му здрав на доставчика.</li>\r\n					<li>Винербергер ЕООД си запазва правото за промяна на цените на продуктите при промяна на официалната ценова листа на фирмата.</li>\r\n				</ol>\r\n				</li>\r\n				<li>Доставка\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>[min_quantity]Цената на стоките е при условие доставка до строителен обект на цяла транспортна единица (тир с ремарке &ndash; 24 тона), без разтоварване или до склад на наш дистрибутор в гр. [a_delivery_town][notes_term][notes_price]</li>\r\n					<li>Доставката на посочените продукти ще бъде извършена от избран от Вас дистрибутор на Винербергер ЕООД.</li>\r\n				</ol>\r\n				</li>\r\n				<li>Валидност на офертата.\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>Офертата е валидна до [document_validity_term|date_format:%d.%m.%Y] г.[notes_delivery][notes_additional]</li>\r\n				</ol>\r\n				</li>\r\n			</ol>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">&nbsp;&nbsp;&nbsp; Моля да ни информирате за Вашето решение най-късно до валидността на настоящата оферта или най-малко 7 дни преди първите доставки.</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">&nbsp;&nbsp;&nbsp; При приемане на оферта бихме искали да ни информирате за Вашия избор на доставчик</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Благодаря за проявения интерес!</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">С уважение</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>[employee_signature]</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[bg_document_employee][employee_position][employee_phone][employee_email]</span></span></td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p>&nbsp;</p>\r\n' WHERE parent_id = 11;
UPDATE `patterns` SET active = 0 WHERE id = 13;

# Inserted new placeholders for salutation
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('salutation1', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Обръщение 1 (За ЮЛ - [document_contact_person_salutation] + [bg_customer_name]; За ФЛ: [customer_salutation])', 'bg'),
  (LAST_INSERT_ID(), 'Salutation 1', 'en');
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('salutation2', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Обръщение 2 (За ЮЛ - [document_contact_person_salutation|formal]; За ФЛ: [customer_salutation|formal])', 'bg'),
  (LAST_INSERT_ID(), 'Salutation 2', 'en');

# Fixed employee_position placeholder spelling error
DELETE pli18n.*, pl.* FROM placeholders pl, placeholders_i18n pli18n WHERE pl.id=pli18n.parent_id AND pl.varname='employee_plosition';
UPDATE placeholders SET pattern_id=',71,73,' WHERE varname='employee_position' AND pattern_id=',71,';

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 10 HOUR) <= NOW()' WHERE `id` = 4;

######################################################################################
# 2016-06-01 - Added new pattern plugin for "Offer Tondach" (12) type of documents. The plugin uses the existing method prepareOffer which has been modified
#            - Added placeholders for the pattern plugin
#            - Modified the content of the prepared pattern including the placeholders needed
#            - Added extended values to the "notes" checkboxes

# Added new pattern plugin for "Offer Tondach" (12) type of documents
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (79, 'Document', 12, 'wienerberger', 'prepareOffer', '', NULL, NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (79, 'Подготовка на "Оферта Тондах"', 'Подготвят се данни за печат на документ от тип "Оферта Тондах".', 'bg', NOW()),
  (79, 'Preparation for "Offer Tondach"', 'Prepare data for printing of "Offer Tondach" type documents.', 'en', NOW());

# Added placeholders for the pattern plugin
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('notes_payment', 'Document', 'basic', 'pattern_plugins', ',79,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Условия на плащане – авансово към избран...', 'bg'),
  (LAST_INSERT_ID(), 'Notes payments', 'en');
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('notes_valid', 'Document', 'basic', 'pattern_plugins', ',79,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Офертата е валидна при закупуване ...', 'bg'),
  (LAST_INSERT_ID(), 'Notes valid', 'en');
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('notes_freeone', 'Document', 'basic', 'pattern_plugins', ',79,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Свободно избираема клауза 1', 'bg'),
  (LAST_INSERT_ID(), 'Notes free 1', 'en');
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('notes_freetwo', 'Document', 'basic', 'pattern_plugins', ',79,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Свободно избираема клауза 2', 'bg'),
  (LAST_INSERT_ID(), 'Notes free 2', 'en');

UPDATE placeholders SET pattern_id=',71,79,' WHERE varname IN ('offer_table', 'employee_signature', 'employee_phone', 'employee_email', 'salutation1', 'salutation2') AND pattern_id=',71,';
UPDATE placeholders SET pattern_id=',71,73,79,' WHERE varname IN ('employee_position') AND pattern_id=',71,73,';

# Modified the content of the prepared pattern including the placeholders needed
UPDATE patterns SET plugin=79 WHERE id=16;
UPDATE `patterns_i18n` SET content='<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n	<tbody>\r\n		<tr>\r\n			<td style="width:5%">&nbsp;</td>\r\n			<td style="width:98%">\r\n			<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n				<tbody>\r\n					<tr>\r\n						<td style="vertical-align:top; width:20%"><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">До</span></span></td>\r\n						<td style="width:85%"><span style="font-size:20px"><strong><span style="font-family:arial,helvetica,sans-serif">[salutation1]</span></strong></span></td>\r\n					</tr>\r\n					<tr>\r\n						<td>&nbsp;</td>\r\n						<td>&nbsp;</td>\r\n					</tr>\r\n					<tr>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Номер на оферта:</span></span></td>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[document_num]</span></span></td>\r\n					</tr>\r\n					<tr>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Дата:</span></span></td>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[document_added_date|date_format:%d.%m.%Y] г.</span></span></td>\r\n					</tr>\r\n					<tr>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Страници:</span></span></td>\r\n						<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[system_pages]</span></span></td>\r\n					</tr>\r\n				</tbody>\r\n			</table>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td style="text-align:center"><span style="font-size:48px"><span style="font-family:arial,helvetica,sans-serif"><strong>ОФЕРТА</strong></span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-family:arial,helvetica,sans-serif"><span style="font-size:20px">&nbsp;&nbsp;&nbsp; [salutation2],</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>\r\n			<p><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">&nbsp;&nbsp;&nbsp; Имаме удоволствието да Ви &nbsp;представим нашата оферта за доставка на керамична покривна система TONDACH&nbsp; за изпълнението на Вашия обект в гр. [a_populated_location], като предлагаме следните условия:</span></span></p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>\r\n			<ol style="list-style-type:upper-roman">\r\n				<li>Продукти и цени\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>Таблица Оферта - Калкулация за необходимите продукти и количества<br />\r\n					[offer_table]<br />\r\n					&nbsp;</li>\r\n					<li>Изображение на основната керемида и цвета на системата.</li>\r\n					<li>Количествата в горната таблица са изчислени на база следните входни данни:\r\n					<table border="0" cellpadding="1" cellspacing="1">\r\n						<tbody>\r\n							<tr>\r\n								<td style="text-align:right; width:40px">i.</td>\r\n								<td style="width:500px">Наклон на покрива</td>\r\n								<td style="width:100px">[a_roof_angle]%, [a_roof_angle]&deg;</td>\r\n							</tr>\r\n							<tr>\r\n								<td style="text-align:right">ii.</td>\r\n								<td>Обща квадратура на покрива</td>\r\n								<td>[a_total_area_roof]m<sup>2</sup></td>\r\n							</tr>\r\n							<tr>\r\n								<td style="text-align:right">iii.</td>\r\n								<td>Обща дължина на билата</td>\r\n								<td>[a_total_length_horizontal_bila]m&#39;</td>\r\n							</tr>\r\n							<tr>\r\n								<td style="text-align:right">iv.</td>\r\n								<td>Хоризонтално било</td>\r\n								<td>[a_horizontal_bilo]m&#39;</td>\r\n							</tr>\r\n							<tr>\r\n								<td style="text-align:right">v.</td>\r\n								<td>Дължина на уламите</td>\r\n								<td>[a_total_length_ulami]m&#39;</td>\r\n							</tr>\r\n							<tr>\r\n								<td style="text-align:right">vi.</td>\r\n								<td>Обиколка на покрива</td>\r\n								<td>[a_roof_angle]m&#39;</td>\r\n							</tr>\r\n						</tbody>\r\n					</table>\r\n					</li>\r\n					<li>Посочените цени са без начислен ДДС</li>\r\n					<li>Посочените количества в калкулацията са индикативни и не отразяват промени настъпили по време на изпълнението.</li>\r\n					<li>В цените посочени в точка I.a е включена цената на палет за многократна употреба.[notes_payment]</li>\r\n					<li>ТОНДАХ България ЕООД, си запазва правото за промяна на цените на продуктите при промяна на официалната ценова листа на фирмата.</li>\r\n				</ol>\r\n				</li>\r\n				<li>Доставка\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>Цената на стоките е при условие INCOTERMS 2000 &ndash; CPT доставка до склад на наш оторизиран дистрибутор на ТОНДАХ България ЕООД.</li>\r\n					<li>Срок на доставка се определя след приемане на офертата от Ваша страна, и избор на дистрибутора.</li>\r\n					<li>Доставката на посочените продукти до обекта Ви ще бъде извършена от избрания от Вас дистрибутор.</li>\r\n				</ol>\r\n				</li>\r\n				<li>Валидност на офертата.\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>Офертата е валидна в срок от един месец от издаването ѝ.[notes_valid]</li>\r\n				</ol>\r\n				</li>\r\n				<li>Допълнителни условия.\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>Установените явни недостатъци в продуктите или опаковката им се съобщават при доставка, а скрити недостатъци се съобщават незабавно след откриването им. Ако при доставката/предаването на продуктите не са съобщени явни недостатъци, претенции за такива не може да бъдат правени на по-късен етап.[notes_freeone][notes_freetwo]</li>\r\n				</ol>\r\n				</li>\r\n				<li>Предоставяне на гаранция\r\n				<ol style="list-style-type:lower-alpha">\r\n					<li>Гаранцията се предоставя само в случай, че бъде използвана цялостна покривна система на TONDACH съгласно условията на гаранционната карта.</li>\r\n					<li>Конструкцията на покрива и вентилацията на покривното покритие трябва да съответстват на техническата документация на ТОНДАХ България ЕООД и указания за ползване на&nbsp; керемиди TONDACH, които ще получите от Вашия дистрибутор или търговския ни представител .</li>\r\n					<li>Гаранцията не се признава във връзка с други щети и претенции, които са извън упоменатите в гаранционното свидетелство.</li>\r\n				</ol>\r\n				</li>\r\n			</ol>\r\n\r\n			<p>&nbsp;</p>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">&nbsp;&nbsp;&nbsp; Моля да ни информирате за Вашето решение най-малко [a_notes_deliveryth__amount] преди първите доставки. </span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">&nbsp;&nbsp;&nbsp; При приемане на оферта бихме искали да ни информирате за Вашия избор на доставчик.</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">Благодаря за проявения интерес!</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">С уважение</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>[employee_signature]</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td><span style="font-size:20px"><span style="font-family:arial,helvetica,sans-serif">[bg_document_employee][employee_position][employee_phone][employee_email]</span></span></td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p>&nbsp;</p>\r\n' WHERE parent_id=16 AND lang='bg' AND content NOT LIKE '%[notes_payment]%';

# Added extended values to the "notes" checkboxes
UPDATE _fields_options SET extended_value='Условия на плащане – авансово към избран от Вас доставчик от листата на ТОНДАХ България ЕООД.' WHERE parent_name='notes_payment' AND lang='bg';
UPDATE _fields_options SET extended_value='Офертата е валидна при закупуване [a_notes_valid__amount].' WHERE parent_name='notes_valid' AND lang='bg';
UPDATE _fields_options SET extended_value='[a_notes_freeone__amount]' WHERE parent_name='notes_freeone' AND lang='bg';
UPDATE _fields_options SET extended_value='[a_notes_freetwo__amount]' WHERE parent_name='notes_freetwo' AND lang='bg';

######################################################################################
# 2016-09-01 - Added new images table in pattern plugin for "Offer Tondach" (12) type of documents.

# PRE-DEPLOYED # Added new images table in pattern plugin for "Offer Tondach" (12) type of documents.
#UPDATE patterns_i18n SET content=REPLACE(content, '<span style="background-color:#FFFF00">ТАБЛИЦА С ИЗОБРАЖЕНИЯ ТУК</span>', '[images_table]');
# PRE-DEPLOYED # Added placeholder for images_table for pattern plugin "prepareOffer" for "wienerberger"
#INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  ('images_table', 'Document', 'basic', 'pattern_plugins', ',71,', '', 0);
#INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Таблица с изображенията за покрив и цвят', 'bg'),
#  (LAST_INSERT_ID(), 'Images table', 'en');

######################################################################################
# 2016-09-28 - Added/modified fields according to Bug 4415, comment 33

# Added/modified fields according to Bug 4415, comment 33
# Added hidden columns for weight of each element to the three tables of the offer
INSERT INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(3197, 'Document', 12, 'otch_teglo_br', 'text', '', 0, 'text_align := right\r\ncustom_class := to_teglo_br', 'js_filter := insertOnlyFloats', 0, 1, 1, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 1587, 42, '100', '100', ''),
(3198, 'Document', 12, 'otch_teglo_br2', 'text', '', 0, 'text_align := right\r\ncustom_class := to_teglo_br', 'js_filter := insertOnlyFloats', 0, 1, 1, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 1588, 87, '100', '100', ''),
(3199, 'Document', 12, 'otch_teglo_br3', 'text', '', 0, 'text_align := right\r\ncustom_class := to_teglo_br', 'js_filter := insertOnlyFloats', 0, 1, 1, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 1589, 132, '100', '100', '');
INSERT INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(3197, 'label', 'Тегло на брой (кг)', 'bg'),
(3198, 'label', 'Тегло на брой (кг)', 'bg'),
(3199, 'label', 'Тегло на брой (кг)', 'bg');

# Added readonly columns for calculated price (including transport) to the three tables of the offer
INSERT INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(3121, 'Document', 12, 'otch_price_calc', 'text', '', 0, 'text_align := right\r\ncustom_class := to_price_calc', 'js_filter := insertOnlyFloats', 0, 0, 1, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 1587, 41, '100', '100', ''),
(3162, 'Document', 12, 'otch_price_calc2', 'text', '', 0, 'text_align := right\r\ncustom_class := to_price_calc', 'js_filter := insertOnlyFloats', 0, 0, 1, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 1588, 86, '100', '100', ''),
(3163, 'Document', 12, 'otch_price_calc3', 'text', '', 0, 'text_align := right\r\ncustom_class := to_price_calc', 'js_filter := insertOnlyFloats', 0, 0, 1, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 1589, 131, '100', '100', '');
DELETE FROM _fields_i18n WHERE parent_id IN (3121, 3162, 3163);
INSERT INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(3121, 'label', 'Цена (изчислена)', 'bg'),
(3162, 'label', 'Цена (изчислена)', 'bg'),
(3163, 'label', 'Цена (изчислена)', 'bg');

# The discount, profit fields are made required and allow only floats
UPDATE _fields_meta set validate='js_filter := insertOnlyFloats', source='js_method := onkeyup => calcTondachOffer(\'\')', required=2 WHERE model='document' and model_type=12 and name IN ('to_dealer', 'otch_profit');
# The truck weight and price are made required allowing zero and allow only floats
UPDATE _fields_meta set validate='js_filter := insertOnlyFloats', source='js_method := onkeyup => calcTondachOffer(\'\')', required=1 WHERE model='document' and model_type=12 and name IN ('truck_weight', 'truck_price');
# Fixed the truck weight and price back labels
UPDATE _fields_i18n set content='лв.' WHERE parent_id='3196' AND content_type='back_label';
UPDATE _fields_i18n set content='кг.' WHERE parent_id='3195' AND content_type='back_label';

# Added the entire custom javascript (minimized with jscompress.com)
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price",b),e=b.select(".to_total")[0];e.value=(d*c).toFixed(2);var f=$("offer_total_value"),g={tiles:0,ceramic:0,nonceramic:0},h=0,i=getElFloatVal("#total_area_roof"),j=getElFloatVal("#horizontal_bilo"),k=getElFloatVal("#total_length_horizontal_bila_projection"),l=getElFloatVal("#total_length_strehi"),m=getElFloatVal("#total_length_ulami"),n=getElFloatVal("#to_dealer",""),o=getElFloatVal("#otch_profit",""),p=getElFloatVal("#truck_weight",""),q=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(h+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),k=b.select(".to_price_calc")[0],m=b.select(".to_total")[0],r=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=p?q*s/p:0;f=(f*(1-n/100)+t)*(1+o/100),k.value=f.toFixed(3),r=r?r:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);v=v?v:1;var w=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=mround(i*r/u-h,v),d.value=e,m.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=w?0:Math.floor(i/5),d.value=e,m.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=w?0:Math.round(j*r/u*2),d.value=e,m.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=w?0:Math.round(l*r/u),d.value=e,m.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=w?0:r,d.value=e,m.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}g.tiles+=getElFloatVal(".to_total",b)});var r=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),h=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],l=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),s=p?q*m/p:0;f=(f*(1-n/100)+s)*(1+o/100),h.value=f.toFixed(3),l=l?l:1;var t=getElFloatVal(".to_coef",b);t=t?t:1;var u=getElFloatVal(".to_round",b);switch(u=u?u:1,Number(c)){case 1111:case 1112:case 1113:case 1114:r=e=mround((j+k)*l/t,u),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}g.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),h=b.select(".to_price_calc")[0],s=b.select(".to_total")[0],t=getElFloatVal(".to_measure",b),u=getElFloatVal(".to_teglo_br",b),v=p?q*u/p:0;f=(f*(1-n/100)+v)*(1+o/100),h.value=f.toFixed(3),t=t?t:1;var w=getElFloatVal(".to_coef",b);w=w?w:1;var x=getElFloatVal(".to_round",b);x=x?x:1;var y=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1123:e=y?0:Math.round(i*t/w),d.value=e,s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1120:e=y?0:r,d.value=e,s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=y?0:Math.round((j+k)/.6),d.value=e,s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1122:e=y?0:Math.round(j*t/w),d.value=e,s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=y?0:Math.round(j*t/w),d.value=e,s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=y?0:Math.round(l*t/w),s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=y?0:Math.round(m*t/w),d.value=e,s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=y?0:t,d.value=e,s.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}g.nonceramic+=getElFloatVal(".to_total",b)}),f.value=(g.tiles+g.ceramic+g.nonceramic).toFixed(2)}else $$(".to_price").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];f=c.getValue()}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value=\'\' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","cover": "\'+$("type_cover").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="(SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5024 JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc2.value=<color> OR nc2.value=\'\') JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 LEFT JOIN tags_models as tm  ON n.id=tm.model_id AND tm.model=\'Nomenclature\' AND tm.tag_id=47 WHERE n.type=24 AND (tm.model_id IS NOT NULL "+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+") ORDER BY nc4.value)"+(h.length>0?"UNION(SELECT DISTINCT(nc5.value) FROM nom_cstm nc5 WHERE nc5.var_id=5045 AND nc5.value!=\'\' AND nc5.model_id IN ("+h.join(", ")+"))":""),c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(\'Error 404: location "\'+a.statusText+\'" was not found.\')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(\'[id^="\'+c+\'"]\').each(function(a){var c=a.readOnly==b;if(!c){if(a.readOnly=b,b?addClass(a,"readonly"):removeClass(a,"readonly"),a.tagName.match(/select/i)){if(b){var d=document.createElement("input");d.type="hidden",d.id=a.id,d.name=a.name,d.value=a.getValue(),a.name=a.name.replace(/(.*)(\\[[0-9]*\\])/,"$1_readonly$2"),a.id=a.id.replace(/(.*)(_[0-9]*)/,"$1_readonly$2"),a.disabled=!0,a.parentNode.appendChild(d)}else{var d=$(a.id.replace(/_readonly/g,""));d&&d.tagName.match(/input/i)&&(a.parentNode.removeChild(d),a.name=a.name.replace(/_readonly/g,""),a.id=a.id.replace(/_readonly/g,""),a.disabled=!1)}toggleUndefined(a)}a.id.match(/otch_article/)&&a.className.match(/autocompletebox/)&&a.parentNode.select(".icon_button").each(function(c){c.parentNode.style.display=b?"none":"",c.style.display=b?"none":"";var d=b?1:-1,e=parseInt(a.style.width.replace(/px/g,""))+22*d;a.style.width=e+"px"})}})});var c=a.select(".to_quantity")[0];1==getElFloatVal(".to_is_calculable:checked",a)?(c.readOnly=!0,addClass(c,"readonly")):(c.readOnly=!1,removeClass(c,"readonly"))}function mround(a,b){return b*Math.round(a/b)}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id=3126;

# Fixed the autocompleters selecting articles in the three tables
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := <name>\r\nautocomplete_fill_options := $otch_article_id => <id>\r\nautocomplete_fill_options := $otch_article => <name>\r\nautocomplete_fill_options := $otch_code => <code>\r\nautocomplete_fill_options := $otch_me =>\r\nautocomplete_fill_options := $otch_price => <sell_price>\r\nautocomplete_fill_options := $otch_teglo_br => <a_teglo_br>\r\nautocomplete_fill_options := $otch_element_h => <a_system_element_id>\r\nautocomplete_fill_options := $coeff_transport_h => <a_koef_transport>\r\nautocomplete_fill_options := $br_me_razhod => <a_br_me_razhod>\r\nautocomplete_fill_options := $koef_br_me => <a_koef_br_me>\r\nautocomplete_fill_options := $otch_me => 1\r\nautocomplete_fill_options := $rounding => <a_rounding>\r\nautocomplete_fill_options := $offer_calc_quantity => <a_offer_calc_quantity>\r\nautocomplete_filter := <type> => 21\r\nautocomplete_filter := <a__k_system_id> => IN ($otch_system_id, )\r\nautocomplete_filter := <a__k_color_id> => IN ($otch_color_id, )\r\nautocomplete_optional_filter := <a__system_element_id> => $otch_element_h\r\nautocomplete_clear := 1\r\ncustom_class := to_article\r\nautocomplete_execute_after := calcTondachOffer();void\r\n\r\n' WHERE  `id`=3120;
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := <name>\r\nautocomplete_fill_options := $otch_article2_id => <id>\r\nautocomplete_fill_options := $otch_article2 => <name>\r\nautocomplete_fill_options := $otch_code2 => <code>\r\nautocomplete_fill_options := $otch_me2 =>\r\nautocomplete_fill_options := $otch_price2 => <sell_price>\r\nautocomplete_fill_options := $otch_teglo_br2 => <a_teglo_br>\r\nautocomplete_fill_options := $otch_element_h2 => <a_system_element_id>\r\nautocomplete_fill_options := $coeff_transport_h2 => <a_koef_transport>\r\nautocomplete_fill_options := $br_me_razhod2 => <a_br_me_razhod>\r\nautocomplete_fill_options := $koef_br_me2 => <a_koef_br_me>\r\nautocomplete_fill_options := $otch_me2 => 1\r\nautocomplete_fill_options := $rounding2 => <a_rounding>\r\nautocomplete_fill_options := $offer_calc_quantity2 => <a_offer_calc_quantity>\r\nautocomplete_filter := <type> => 23\r\n#autocomplete_filter := <a__k_system_id> => IN ($otch_system_id, )\r\nautocomplete_filter := <a__k_color_id> => IN ($otch_color_id, )\r\nautocomplete_optional_filter := <a__system_element_id> => $otch_element_h2\r\nautocomplete_clear := 1\r\ncustom_class := to_article\r\nautocomplete_execute_after := calcTondachOffer();void\r\n' WHERE  `id`=3130;
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := <name>\r\nautocomplete_fill_options := $otch_article3_id => <id>\r\nautocomplete_fill_options := $otch_article3 => <name>\r\nautocomplete_fill_options := $otch_code3 => <code>\r\nautocomplete_fill_options := $otch_me3 =>\r\nautocomplete_fill_options := $otch_price3 => <sell_price>\r\nautocomplete_fill_options := $otch_teglo_br3 => <a_teglo_br>\r\nautocomplete_fill_options := $otch_element_h3 => <a_system_element_id>\r\nautocomplete_fill_options := $coeff_transport_h3 => <a_koef_transport>\r\nautocomplete_fill_options := $br_me_razhod3 => <a_br_me_razhod>\r\nautocomplete_fill_options := $koef_br_me3 => <a_koef_br_me>\r\nautocomplete_fill_options := $otch_me3 => 1\r\nautocomplete_fill_options := $rounding3 => <a_rounding>\r\nautocomplete_fill_options := $offer_calc_quantity3 => <a_offer_calc_quantity>\r\nautocomplete_filter := <type> => 24\r\n#autocomplete_filter := <a__k_system_id> => IN ($otch_system_id, )\r\nautocomplete_filter := <a__k_color_id> => IN ($otch_color_id, )\r\nautocomplete_optional_filter := <a__system_element_id> => $otch_element_h3\r\nautocomplete_clear := 1\r\ncustom_class := to_article\r\nautocomplete_execute_after := calcTondachOffer();void\r\n' WHERE  `id`=3139;

######################################################################################
# 2016-11-02 - Added/modified fields according to Bug 4415, comment 40

# Added/modified fields according to Bug 4415, comment 40
# Added 3 fields for "manually" input price
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
  (3155, 'Document', 12, 'otch_price_manual', 'text', '', 0, 'text_align := right\r\ncustom_class := to_price_manual', 'js_filter := insertOnlyFloats', 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 1587, 42, '100', '100', ''),
  (3157, 'Document', 12, 'otch_price_manual2', 'text', '', 0, 'text_align := right\r\ncustom_class := to_price_manual', 'js_filter := insertOnlyFloats', 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 1588, 87, '100', '100', ''),
  (3169, 'Document', 12, 'otch_price_manual3', 'text', '', 0, 'text_align := right\r\ncustom_class := to_price_manual', 'js_filter := insertOnlyFloats', 0, 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 1589, 132, '100', '100', '');
DELETE FROM _fields_i18n WHERE parent_id IN (3155, 3157, 3169);
INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
  (3155, 'label', 'Цена', 'bg'),
  (3157, 'label', 'Цена', 'bg'),
  (3169, 'label', 'Цена', 'bg');

# Hide the price fields that store the price from the nomenclature
UPDATE _fields_meta SET hidden=1 WHERE model='Document' AND model_type=12 AND name IN ('otch_price', 'otch_price2', 'otch_price3');

# Added the entire custom javascript (minimized with jscompress.com)
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),e=(getElFloatVal(".to_price_calc",b),b.select(".to_price_manual")[0]),f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(e*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila_projection"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:1;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=mround(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:Math.floor(j/5),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:Math.round(k*n/v*2),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:Math.round(m*n/v),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:n,d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:1,Number(c)){case 1111:case 1112:case 1113:case 1114:s=e=mround((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],t=getElFloatVal(".to_measure",b),u=getElFloatVal(".to_teglo_br",b),v=b.select(".to_price_manual")[0],w=q?r*u/q:0;f=(f*(1-o/100)+w)*(1+p/100),g.value=f=f.toFixed(3),v&&""!=v.value&&(f=getElFloatVal(".to_price_manual",b)),t=t?t:1;var x=getElFloatVal(".to_coef",b);x=x?x:1;var y=getElFloatVal(".to_round",b);y=y?y:1;var z=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1123:e=z?0:Math.round(j*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1120:e=z?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=z?0:Math.round((k+l)/.6),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1122:e=z?0:Math.round(k*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=z?0:Math.round(k*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=z?0:Math.round(m*t/x),i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=z?0:Math.round(n*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=z?0:t,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];f=c.getValue()}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value='''' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","cover": "''+$("type_cover").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="(SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5024 JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc2.value=<color> OR nc2.value='''') JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 LEFT JOIN tags_models as tm  ON n.id=tm.model_id AND tm.model=''Nomenclature'' AND tm.tag_id=47 WHERE n.type=24 AND (tm.model_id IS NOT NULL "+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+") ORDER BY nc4.value)"+(h.length>0?"UNION(SELECT DISTINCT(nc5.value) FROM nom_cstm nc5 WHERE nc5.var_id=5045 AND nc5.value!='''' AND nc5.model_id IN ("+h.join(", ")+"))":""),c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(''Error 404: location "''+a.statusText+''" was not found.'')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(''[id^="''+c+''"]'').each(function(a){var c=a.readOnly==b;if(!c){if(a.readOnly=b,b?addClass(a,"readonly"):removeClass(a,"readonly"),a.tagName.match(/select/i)){if(b){var d=document.createElement("input");d.type="hidden",d.id=a.id,d.name=a.name,d.value=a.getValue(),a.name=a.name.replace(/(.*)(\[[0-9]*\])/,"$1_readonly$2"),a.id=a.id.replace(/(.*)(_[0-9]*)/,"$1_readonly$2"),a.disabled=!0,a.parentNode.appendChild(d)}else{var d=$(a.id.replace(/_readonly/g,""));d&&d.tagName.match(/input/i)&&(a.parentNode.removeChild(d),a.name=a.name.replace(/_readonly/g,""),a.id=a.id.replace(/_readonly/g,""),a.disabled=!1)}toggleUndefined(a)}a.id.match(/otch_article/)&&a.className.match(/autocompletebox/)&&a.parentNode.select(".icon_button").each(function(c){c.parentNode.style.display=b?"none":"",c.style.display=b?"none":"";var d=b?1:-1,e=parseInt(a.style.width.replace(/px/g,""))+22*d;a.style.width=e+"px"})}})});var c=a.select(".to_quantity")[0];1==getElFloatVal(".to_is_calculable:checked",a)?(c.readOnly=!0,addClass(c,"readonly")):(c.readOnly=!1,removeClass(c,"readonly"))}function mround(a,b){return b*Math.round(a/b)}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id=3126;

######################################################################################
# 2016-11-07 - Added the entire custom javascript (minimized with jscompress.com) according to Bug 4415, comment 42

# Added the entire custom javascript (minimized with jscompress.com) according to Bug 4415, comment 42
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price_calc",b),e=b.select(".to_price_manual")[0],f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(d*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila_projection"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:1;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=mround(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:Math.floor(j/5),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:Math.round(k*n/v*2),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:Math.round(m*n/v),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:n,d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:1,Number(c)){case 1111:case 1112:case 1113:case 1114:s=e=mround((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],t=getElFloatVal(".to_measure",b),u=getElFloatVal(".to_teglo_br",b),v=b.select(".to_price_manual")[0],w=q?r*u/q:0;f=(f*(1-o/100)+w)*(1+p/100),g.value=f=f.toFixed(3),v&&""!=v.value&&(f=getElFloatVal(".to_price_manual",b)),t=t?t:1;var x=getElFloatVal(".to_coef",b);x=x?x:1;var y=getElFloatVal(".to_round",b);y=y?y:1;var z=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1123:e=z?0:Math.round(j*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1120:e=z?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=z?0:Math.round((k+l)/.6),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1122:e=z?0:Math.round(k*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=z?0:Math.round(k*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=z?0:Math.round(m*t/x),i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=z?0:Math.round(n*t/x),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=z?0:t,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];f=c.getValue()}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value=\'\' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","cover": "\'+$("type_cover").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="(SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5024 JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc2.value=<color> OR nc2.value=\'\') JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 LEFT JOIN tags_models as tm  ON n.id=tm.model_id AND tm.model=\'Nomenclature\' AND tm.tag_id=47 WHERE n.type=24 AND (tm.model_id IS NOT NULL "+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+") ORDER BY nc4.value)"+(h.length>0?"UNION(SELECT DISTINCT(nc5.value) FROM nom_cstm nc5 WHERE nc5.var_id=5045 AND nc5.value!=\'\' AND nc5.model_id IN ("+h.join(", ")+"))":""),c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(\'Error 404: location "\'+a.statusText+\'" was not found.\')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(\'[id^="\'+c+\'"]\').each(function(a){var c=a.readOnly==b;if(!c){if(a.readOnly=b,b?addClass(a,"readonly"):removeClass(a,"readonly"),a.tagName.match(/select/i)){if(b){var d=document.createElement("input");d.type="hidden",d.id=a.id,d.name=a.name,d.value=a.getValue(),a.name=a.name.replace(/(.*)(\\[[0-9]*\\])/,"$1_readonly$2"),a.id=a.id.replace(/(.*)(_[0-9]*)/,"$1_readonly$2"),a.disabled=!0,a.parentNode.appendChild(d)}else{var d=$(a.id.replace(/_readonly/g,""));d&&d.tagName.match(/input/i)&&(a.parentNode.removeChild(d),a.name=a.name.replace(/_readonly/g,""),a.id=a.id.replace(/_readonly/g,""),a.disabled=!1)}toggleUndefined(a)}a.id.match(/otch_article/)&&a.className.match(/autocompletebox/)&&a.parentNode.select(".icon_button").each(function(c){c.parentNode.style.display=b?"none":"",c.style.display=b?"none":"";var d=b?1:-1,e=parseInt(a.style.width.replace(/px/g,""))+22*d;a.style.width=e+"px"})}})});var c=a.select(".to_quantity")[0];1==getElFloatVal(".to_is_calculable:checked",a)?(c.readOnly=!0,addClass(c,"readonly")):(c.readOnly=!1,removeClass(c,"readonly"))}function mround(a,b){return b*Math.round(a/b)}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id=3126;

######################################################################################
# 2016-12-07 - Fixed the possible division by zero in the equations of variables

# Fixed the possible division by zero in the equations of variables
UPDATE _fields_meta SET source=REPLACE(source, 'equation := $a/$b', 'equation := ($b != 0) ? $a/$b : 0') WHERE source like '%equation := $a/$b%';
UPDATE _fields_meta SET source=REPLACE(source, 'equation := $a+$b/$c', 'equation := ($c != 0) ? $a+$b/$c : 0') WHERE source like '%equation := $a+$b/$c%';
UPDATE _fields_meta SET source=REPLACE(source, 'equation := (1- (($b-($c/$d))/$a))*100', 'equation := ($d != 0 && $a != 0) ? (1- (($b-($c/$d))/$a))*100 : 0') WHERE source like '%(1- (($b-($c/$d))/$a))*100%';
UPDATE _fields_meta SET source=REPLACE(source, 'equation := ($a/$b)*100', 'equation := ($b != 0) ? ($a/$b)*100 : 0') WHERE source like '%equation := ($a/$b)*100%';

######################################################################################
# 2016-12-08 - Added the entire custom javascript (minimized with jscompress.com) according to Bug 4307, comment 9

# Added the entire custom javascript (minimized with jscompress.com) according to Bug 4307, comment 9
UPDATE _fields_meta SET source='text_align := left\npermissions_edit := 1\npermissions_view := 1\ntotals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total_discount_surplus_field, total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\njavascript := function calculateReceivedPoints(a,b,c){if(c&&void 0!=c){var d=window.event.which;if(d<48&&d>57||d<96&&d>105)return}var e={base:[26,39,41,42,47],gold:[43,44]},f={first_radio:50,second_radio:50},g={base:"free_field2",silver:"free_field3",gold:"free_field4",first_radio:"first_porotherm_2",second_radio:"used_lintel_1",id_field:"article_id",total_bonus_points:"quantity",program_field:"program_field",all_points:"all_points",total_points:"total",deleted_field:"deleted"},h=!1,i=0,j=function(a){if(/.*_([0-9]+)/.test(a)){var b=$$("input[id^="+g.deleted_field+"_]");if(b){var c=$(g.deleted_field+"_"+a.match(/.*_([0-9]+)/)[1]).value;if(c)return!0}}return!1},k=function(a){return!!/.*_([0-9]+)/.test(a)&&!!$(g.id_field+"_"+a.match(/.*_([0-9]+)/)[1]).value},l=function(){return $$("input[name^="+g.id_field+"]").each(function(a){if(!j(a.id)){if($(e.gold).include(a.value))throw h="gold",$break;$(e.base).include(a.value)&&i++}}),h||1!=i?!h&&i>1&&(h="silver"):h="base",h},m=function(){var a=0;return $(g.first_radio).checked&&(a+=f.first_radio),$(g.second_radio).checked&&(a+=f.second_radio),a},n=function(){var a=l();$$("input[name^="+g.id_field+"]").each(function(b){if(/.*_([0-9]+)$/.test(b.id)&&$$("input[name^="+g.id_field+"]").length){var c=b.id.match(/.*_([0-9]+)$/)[1];if(j(b.id))return;var d={base:{title:$(g.base+"_"+c).title,value:$(g.base+"_"+c).value},silver:{title:$(g.silver+"_"+c).title,value:$(g.silver+"_"+c).value},gold:{title:$(g.gold+"_"+c).title,value:$(g.gold+"_"+c).value}};$(g.total_bonus_points+"_"+c).value=k(b.id)?d[a].value:"",$(g.program_field).value=a?d[a].title:"",gt2calc()}});var b=m(),c=$(g.total_points).value;$(g.all_points).value=parseFloat(c)+parseFloat(b)};$$("input[name^="+g.id_field+"]").length>0&&n()}' WHERE id IN (102441, 102563);
# Added clear_fields setting to article_name so that the price is cleared as well
UPDATE _fields_meta SET source='text_align := left\npermissions_edit := 1\npermissions_view := 1\nautocomplete := nomenclatures\nautocomplete_suggestions := <name>\nautocomplete_fill_options := $article_id => <id>\nautocomplete_fill_options := $article_name => <name>\nautocomplete_fill_options := $free_field2 => <a_program_main>\nautocomplete_fill_options := $free_field3 => <a_program_silver>\nautocomplete_fill_options := $free_field4 => <a_program_gold>\nautocomplete_filter := <type> => 6\nautocomplete_filter := <category> => 2\nautocomplete_filter := <id> => NOT IN (45, 46)\nautocomplete_buttons:= search clear\nautocomplete_currency := $currency\nautocomplete_clear_fields := $price, $article_id, $article_name, $free_field2, $free_field3, $free_field4\nautocomplete_execute_after := calculateReceivedPoints' WHERE id IN (102442, 102564);

######################################################################################
# 2016-12-12 - Swapped the total_length_horizontal_bila/total_length_horizontal_bila_projection formulas so that the "_projection" field is the input and the other is calculable
#            - Swapped the total_length_ulami/total_length_ulami_projection formulas so that the "_projection" field is the input and the other is calculable
#            - Added the entire custom javascript for prepareRoofFittings and calcObjectProject (minimized with jscompress.com) according to Bug 4415, comment 50
#            - Added the entire custom javascript (minimized with jscompress.com) according to Bug 4307, comment 11

# Swapped the total_length_horizontal_bila/total_length_horizontal_bila_projection formulas so that the "_projection" field is the input and the other is calculable
UPDATE _fields_meta SET name='total_length_horizontal_bila_projection1' WHERE id IN ('3105', '9065');
UPDATE _fields_meta SET name='total_length_horizontal_bila' WHERE id IN ('3176', '9069');
UPDATE _fields_meta SET name='total_length_horizontal_bila_projection' WHERE id IN ('3105', '9065');
UPDATE _fields_i18n SET content='Обща дължина на наклонените била (маии)' WHERE parent_id IN ('3176', '9069') AND content_type='label' AND lang='bg';
UPDATE _fields_i18n SET content='Обща дължина на проекциите на наклонените била (маии)' WHERE parent_id IN ('3105', '9065') AND content_type='label' AND lang='bg';
UPDATE _fields_meta SET `position` = 32 WHERE id IN ('3106');
UPDATE _fields_meta SET `position` = 30 WHERE id IN ('3177');
UPDATE _fields_meta SET position=17 WHERE id IN ('9069');

# Swapped the total_length_ulami/total_length_ulami_projection formulas so that the "_projection" field is the input and the other is calculable
UPDATE _fields_meta SET name='total_length_ulami1', readonly=1, source='text_align := right\ncustom_class := small', validate='', `position` = 20 WHERE id IN ('9070');
UPDATE _fields_meta SET name='total_length_ulami_projection', readonly=0, source='text_align := right\ncustom_class := small\njs_method := onkeyup => calcObjectProject(this)', validate='js_filter := insertOnlyFloats', `position` = 18 WHERE id IN ('9066');
UPDATE _fields_meta SET name='total_length_ulami' WHERE id IN ('9070');
UPDATE _fields_meta SET name='total_length_ulami1', position=32 WHERE id IN ('3177');
UPDATE _fields_meta SET name='total_length_ulami_projection', position=30 WHERE id IN ('3106');
UPDATE _fields_meta SET name='total_length_ulami' WHERE id IN ('3177');

# Added the entire custom javascript for prepareRoofFittings and calcObjectProject (minimized with jscompress.com) according to Bug 4415, comment 50
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price_calc",b),e=b.select(".to_price_manual")[0],f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(d*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:0;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=MROUND(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:ROUNDDOWN(j*n/v,0),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:ROUNDUP(k*n/v*2,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:ROUNDUP(m*n,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:0,Number(c)){case 1111:case 1112:s=e=MROUND((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],o=getElFloatVal(".to_measure",b),q=(getElFloatVal(".to_teglo_br",b),b.select(".to_price_manual")[0]);g.value=f,q&&""!=q.value&&(f=getElFloatVal(".to_price_manual",b)),o=o?o:1;var r=getElFloatVal(".to_coef",b);r=r?r:1;var t=getElFloatVal(".to_round",b);t=t?t:0;var u=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1120:e=u?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1123:e=u?0:ROUNDUP(j*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=u?0:ROUNDUP(m*o,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=u?0:ROUNDUP(m*o,t),i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=u?0:ROUNDUP(n*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 6318:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];f=c.getValue()}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value=\'\' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","cover": "\'+$("type_cover").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="SELECT DISTINCT(nonceramic.id) FROM ((SELECT DISTINCT(id) as id, nc4.value as `position` FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5024 JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc2.value=<color> OR nc2.value=\'\') JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 LEFT JOIN tags_models as tm  ON n.id=tm.model_id AND tm.model=\'Nomenclature\' AND tm.tag_id=47 WHERE n.type=24 AND (tm.model_id IS NOT NULL "+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+") ORDER BY nc4.value)"+(h.length>0?"UNION(SELECT DISTINCT(nc5.value) as id, nc7.value as `position` FROM nom_cstm nc5 JOIN nom_cstm nc6 ON nc5.value=nc6.model_id AND nc6.var_id=2435 JOIN nom_cstm nc7 ON nc6.value=nc7.model_id AND nc7.var_id=2012 WHERE nc5.var_id=5045 AND nc5.value!=\'\' AND nc5.model_id IN ("+h.join(", ")+")))":")")+" as nonceramic ORDER BY nonceramic.`position`)",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(\'Error 404: location "\'+a.statusText+\'" was not found.\')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(\'[id^="\'+c+\'"]\').each(function(a){var c=a.readOnly==b;if(!c){if(a.readOnly=b,b?addClass(a,"readonly"):removeClass(a,"readonly"),a.tagName.match(/select/i)){if(b){var d=document.createElement("input");d.type="hidden",d.id=a.id,d.name=a.name,d.value=a.getValue(),a.name=a.name.replace(/(.*)(\\[[0-9]*\\])/,"$1_readonly$2"),a.id=a.id.replace(/(.*)(_[0-9]*)/,"$1_readonly$2"),a.disabled=!0,a.parentNode.appendChild(d)}else{var d=$(a.id.replace(/_readonly/g,""));d&&d.tagName.match(/input/i)&&(a.parentNode.removeChild(d),a.name=a.name.replace(/_readonly/g,""),a.id=a.id.replace(/_readonly/g,""),a.disabled=!1)}toggleUndefined(a)}a.id.match(/otch_article/)&&a.className.match(/autocompletebox/)&&a.parentNode.select(".icon_button").each(function(c){c.parentNode.style.display=b?"none":"",c.style.display=b?"none":"";var d=b?1:-1,e=parseInt(a.style.width.replace(/px/g,""))+22*d;a.style.width=e+"px"})}})});var c=a.select(".to_quantity")[0];1==getElFloatVal(".to_is_calculable:checked",a)?(c.readOnly=!0,addClass(c,"readonly")):(c.readOnly=!1,removeClass(c,"readonly"))}function MROUND(a,b){return b*Math.round(a/b)}function ROUNDUP(a,b){var c=Math.pow(10,b);return Math.ceil(a*c)/c}function ROUNDDOWN(a,b){var c=Math.pow(10,b);return Math.floor(a*c)/c}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id IN (3126, 3410);
UPDATE `_fields_meta` SET `source`='dont_copy_values := 1\r\njavascript := function calcObjectProject(a){var b=a.id.replace(/.*(_[0-9]+)$/,"$1"),c={roof_angle:$("roof_angle"),roof_angle_perc:$("roof_angle_perc"),roof_area:$("roof_area"),total_area_roof:$("total_area_roof"),total_length_horizontal_bila:$("total_length_horizontal_bila"),total_length_horizontal_bila_projection:$("total_length_horizontal_bila_projection"),total_length_ulami:$("total_length_ulami"),total_length_ulami_projection:$("total_length_ulami_projection"),discount_name:$("discount_name"+b),amount_name:$("amount_name"+b),use_month_name:$("use_month_name"+b),use_qual_name:$("use_qual_name"+b),use_qual_subtotal:$("use_qual_subtotal")},d={};for(var e in c)c[e]&&(d[e]=c[e].getValue()?parseFloat(c[e].getValue()):0);var f={total_area_roof:function(){c.total_area_roof.value=(d.roof_area/Math.cos(d.roof_angle*Math.PI/180)).toFixed(2)},roof_angle:function(){c.roof_angle.value=d.roof_angle_perc?(180/Math.PI*Math.atan(d.roof_angle_perc/100)).toFixed(2):0},roof_angle_perc:function(){c.roof_angle_perc.value=d.roof_angle?(100*Math.tan(d.roof_angle*Math.PI/180)).toFixed(2):0},use_qual_name:function(){c.use_qual_name.value=d.use_month_name?(d.amount_name/d.use_month_name).toFixed(2):0},use_qual_subtotal:function(){var a=0;$$(\'[name^="use_qual_name["]\').each(function(b){!b.disabled&&b.value&&(a+=parseFloat(b.value))}),c.use_qual_subtotal.value=a.toFixed(2)},discount_name:function(){c.discount_name.value=d.discount_name.toFixed(3)},total_length_horizontal_bila:function(){c.total_length_horizontal_bila.value=(d.total_length_horizontal_bila_projection/Math.cos(Math.atan(Math.tan(d.roof_angle*Math.PI/180)/Math.sqrt(2)))).toFixed(2)},total_length_ulami:function(){c.total_length_ulami.value=(d.total_length_ulami_projection/Math.cos(Math.atan(Math.tan(d.roof_angle*Math.PI/180)/Math.sqrt(2)))).toFixed(2)}},g={roof_area:["total_area_roof","total_length_ulami_projection"],roof_angle:["roof_angle_perc","total_area_roof","total_length_horizontal_bila","total_length_ulami"],roof_angle_perc:["roof_angle","total_area_roof","total_length_horizontal_bila","total_length_ulami"],total_length_horizontal_bila_projection:["total_length_horizontal_bila"],total_length_ulami_projection:["total_length_ulami"],amount_name:["use_qual_name","use_qual_subtotal"],use_month_name:["use_qual_name","use_qual_subtotal"],discount_name:["discount_name"]};if(b!=a.id)var h=a.id.replace(new RegExp(b+"$"),"");else var h=a.id;g[h]&&g[h].each(function(a){f[a](c,d)})}' WHERE id = 9013;

# Added the entire custom javascript (minimized with jscompress.com) according to Bug 4307, comment 11
UPDATE `_fields_meta` SET `source`='custom_class := tondach' WHERE `id`=3601;
UPDATE `_fields_meta` SET `source`='custom_class := wienerberger' WHERE `id`=2902;
UPDATE `_fields_meta` SET `source`='text_align := left\npermissions_edit := 1\npermissions_view := 1\nmethod := getCustomDropdown\ntable := DB_TABLE_NOMENCLATURES\ntable_i18n := DB_TABLE_NOMENCLATURES_I18N\nwhere := type = \'12\'\nactive := 1\norder_by := id ASC\njs_method := onchange => calculateReceivedPoints()' WHERE `id`=203158;
UPDATE `_fields_meta` SET source='text_align := left\npermissions_edit := 1\npermissions_view := 1\ntotals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total_discount_surplus_field, total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\njavascript := function calculateReceivedPoints(a,b,c){if(c&&void 0!=c){var d=window.event.which;if(d<48&&d>57||d<96&&d>105)return}var e={WIENERBERGER:{program_ids:{base:[26,39,41,42,47],gold:[43,44]},program_points:{first_radio:50,second_radio:50},program_fields:{base:"free_field2",silver:"free_field3",gold:"free_field4",first_radio:"first_porotherm_2",second_radio:"used_lintel_1",id_field:"article_id",total_bonus_points:"quantity",program_field:"program_field",all_points:"all_points",total_points:"total",deleted_field:"deleted"}},TONDACH:{program_points:{first_radio:50},program_fields:{program:"free_field5",base:"free_field2",silver:"free_field3",gold:"free_field4",first_radio:"used_lintel_1",id_field:"article_id",total_bonus_points:"quantity",program_field:"program_field",all_points:"all_points",total_points:"total",deleted_field:"deleted"},program_options:{113:"base",114:"silver",115:"gold"}}},f=$("all_points").hasClassName("tondach")?"TONDACH":"WIENERBERGER",g=e[f],h=!1,i=0,j=function(a){if(/.*_([0-9]+)/.test(a)){var b=$$("input[id^="+g.program_fields.deleted_field+"_]");if(b){var c=$(g.program_fields.deleted_field+"_"+a.match(/.*_([0-9]+)/)[1]).value;if(c)return!0}}return!1},k=function(a){return!!/.*_([0-9]+)/.test(a)&&!!$(g.program_fields.id_field+"_"+a.match(/.*_([0-9]+)/)[1]).value},l=function(){return $$("input[name^="+g.program_fields.id_field+"]").each(function(a){if(!j(a.id)){if($(g.program_ids.gold).include(a.value))throw h="gold",$break;$(g.program_ids.base).include(a.value)&&i++}}),h||1!=i?!h&&i>1&&(h="silver"):h="base",h},m=function(){var a=0;return $(g.program_fields.first_radio)&&$(g.program_fields.first_radio).checked&&(a+=g.program_points.first_radio),$(g.program_fields.second_radio)&&$(g.program_fields.second_radio).checked&&(a+=g.program_points.second_radio),a},n=function(){switch(f){case"WIENERBERGER":var a=l();$$("input[name^="+g.program_fields.id_field+"]").each(function(b){if(/.*_([0-9]+)$/.test(b.id)&&$$("input[name^="+g.program_fields.id_field+"]").length){var c=b.id.match(/.*_([0-9]+)$/)[1];if(j(b.id))return;var d={base:{title:$(g.program_fields.base+"_"+c).title,value:$(g.program_fields.base+"_"+c).value},silver:{title:$(g.program_fields.silver+"_"+c).title,value:$(g.program_fields.silver+"_"+c).value},gold:{title:$(g.program_fields.gold+"_"+c).title,value:$(g.program_fields.gold+"_"+c).value}};$(g.program_fields.total_bonus_points+"_"+c).value=k(b.id)?d[a].value:"",$(g.program_fields.program_field).value=a?d[a].title:"",gt2calc()}});break;case"TONDACH":$$("input[name^="+g.program_fields.id_field+"]").each(function(b){if(/.*_([0-9]+)$/.test(b.id)&&$$("input[name^="+g.program_fields.id_field+"]").length){var c=b.id.match(/.*_([0-9]+)$/)[1];if(j(b.id))return;a=$(g.program_fields.program+"_"+c).value,a&&(a=g.program_options[a]);var d={base:{value:$(g.program_fields.base+"_"+c).value},silver:{value:$(g.program_fields.silver+"_"+c).value},gold:{value:$(g.program_fields.gold+"_"+c).value}};$(g.program_fields.total_bonus_points+"_"+c).value=k(b.id)&&a?d[a].value:"",gt2calc()}})}var b=m(),c=$(g.program_fields.total_points).value;$(g.program_fields.all_points).value=parseFloat(c)+parseFloat(b)};$$("input[name^="+g.program_fields.id_field+"]").length>0&&n()}' WHERE id IN (102441, 203101);

UPDATE _fields_i18n SET content='Обща дължина на проекциите на уламите' WHERE parent_id IN ('3106', '9066') AND content_type='label' AND lang='bg';
UPDATE _fields_i18n SET content='Обща дължина на уламите' WHERE parent_id IN ('3177', '9070') AND content_type='label' AND lang='bg';
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price_calc",b),e=b.select(".to_price_manual")[0],f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(d*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:0;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=MROUND(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:ROUNDDOWN(j*n/v,0),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:ROUNDUP(k*n/v*2,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:ROUNDUP(m*n,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:0,Number(c)){case 1111:case 1112:s=e=MROUND((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],o=getElFloatVal(".to_measure",b),q=(getElFloatVal(".to_teglo_br",b),b.select(".to_price_manual")[0]);g.value=f,q&&""!=q.value&&(f=getElFloatVal(".to_price_manual",b)),o=o?o:1;var r=getElFloatVal(".to_coef",b);r=r?r:1;var t=getElFloatVal(".to_round",b);t=t?t:0;var u=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1120:e=u?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1123:e=u?0:ROUNDUP(j*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=u?0:ROUNDUP(m*o,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=u?0:ROUNDUP(m*o,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=u?0:ROUNDUP(n*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 6318:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];f=c.getValue()}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value='''' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","cover": "''+$("type_cover").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="SELECT DISTINCT(nonceramic.id) FROM ((SELECT DISTINCT(id) as id, nc4.value as `position` FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5024 JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc2.value=<color> OR nc2.value='''') JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 LEFT JOIN tags_models as tm  ON n.id=tm.model_id AND tm.model=''Nomenclature'' AND tm.tag_id=47 WHERE n.type=24 AND (tm.model_id IS NOT NULL "+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+") ORDER BY nc4.value)"+(h.length>0?"UNION(SELECT DISTINCT(nc5.value) as id, nc7.value as `position` FROM nom_cstm nc5 JOIN nom_cstm nc6 ON nc5.value=nc6.model_id AND nc6.var_id=2435 JOIN nom_cstm nc7 ON nc6.value=nc7.model_id AND nc7.var_id=2012 WHERE nc5.var_id=5045 AND nc5.value!='''' AND nc5.model_id IN ("+h.join(", ")+")))":")")+" as nonceramic ORDER BY nonceramic.`position` ",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(''Error 404: location "''+a.statusText+''" was not found.'')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(''[id^="''+c+''"]'').each(function(a){var c=a.readOnly==b;if(!c){if(a.readOnly=b,b?addClass(a,"readonly"):removeClass(a,"readonly"),a.tagName.match(/select/i)){if(b){var d=document.createElement("input");d.type="hidden",d.id=a.id,d.name=a.name,d.value=a.getValue(),a.name=a.name.replace(/(.*)(\[[0-9]*\])/,"$1_readonly$2"),a.id=a.id.replace(/(.*)(_[0-9]*)/,"$1_readonly$2"),a.disabled=!0,a.parentNode.appendChild(d)}else{var d=$(a.id.replace(/_readonly/g,""));d&&d.tagName.match(/input/i)&&(a.parentNode.removeChild(d),a.name=a.name.replace(/_readonly/g,""),a.id=a.id.replace(/_readonly/g,""),a.disabled=!1)}toggleUndefined(a)}a.id.match(/otch_article/)&&a.className.match(/autocompletebox/)&&a.parentNode.select(".icon_button").each(function(c){c.parentNode.style.display=b?"none":"",c.style.display=b?"none":"";var d=b?1:-1,e=parseInt(a.style.width.replace(/px/g,""))+22*d;a.style.width=e+"px"})}})});var c=a.select(".to_quantity")[0];1==getElFloatVal(".to_is_calculable:checked",a)?(c.readOnly=!0,addClass(c,"readonly")):(c.readOnly=!1,removeClass(c,"readonly"))}function MROUND(a,b){return b*Math.round(a/b)}function ROUNDUP(a,b){var c=Math.pow(10,b);return Math.ceil(a*c)/c}function ROUNDDOWN(a,b){var c=Math.pow(10,b);return Math.floor(a*c)/c}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id IN (3126, 3410);

######################################################################################
# 2016-12-15 - Update the settings of the applyBonusCorrection automation to correspond to the document whish is supposed to be connected to TONDACH bonus corrections
#            - Update the settings of the calculateBonusPoints automation to contain also the TONDACH documents
#            - Added the entire custom javascript (minimized with jscompress.com) according to Bug 4307, comment 14
# Update the settings of the applyBonusCorrection automation to correspond to the document whish is supposed to be connected to TONDACH bonus corrections
UPDATE automations SET `settings`=REPLACE(`settings`, 'request_points_type := 9', 'request_points_type := 17') WHERE `method` LIKE '%applyBonusCorrection%' AND `start_model_type`='18' AND `settings` LIKE '%request_points_type := 9%';

# Update the settings of the calculateBonusPoints automation to contain also the TONDACH documents
UPDATE automations SET `settings`=REPLACE(`settings`, 'request_receiving_points := 9\r\n', 'request_receiving_points := 9,17\r\n') WHERE `method` LIKE '%calculateBonusPoints%' AND `settings` LIKE '%request_receiving_points := 9\r\n%';

# Added the entire custom javascript (minimized with jscompress.com) according to Bug 4307, comment 14
UPDATE `_fields_meta` SET `source`='custom_class := tondach' WHERE `id`=3701;
UPDATE `_fields_meta` SET `source`='custom_class := wienerberger' WHERE `id`=3001;
UPDATE `_fields_meta` SET `source`='text_align := left\npermissions_edit := 1\npermissions_view := 1\nmethod := getCustomDropdown\ntable := DB_TABLE_NOMENCLATURES\ntable_i18n := DB_TABLE_NOMENCLATURES_I18N\nwhere := type = \'12\'\nactive := 1\norder_by := id ASC\njs_method := onchange => calculateReceivedPoints()' WHERE `id`=203218;
UPDATE `_fields_meta` SET source='text_align := left\npermissions_edit := 1\npermissions_view := 1\ntotals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total_discount_surplus_field, total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\njavascript := function calculateReceivedPoints(a,b,c){if(c&&void 0!=c){var d=window.event.which;if(d<48&&d>57||d<96&&d>105)return}var e={WIENERBERGER:{program_ids:{base:[26,39,41,42,47],gold:[43,44]},program_points:{first_radio:50,second_radio:50},program_fields:{base:"free_field2",silver:"free_field3",gold:"free_field4",first_radio:"first_porotherm_2",second_radio:"used_lintel_1",id_field:"article_id",total_bonus_points:"quantity",program_field:"program_field",all_points:"all_points",total_points:"total",deleted_field:"deleted"}},TONDACH:{program_points:{first_radio:50},program_fields:{program:"free_field5",base:"free_field2",silver:"free_field3",gold:"free_field4",first_radio:"used_lintel_1",id_field:"article_id",total_bonus_points:"quantity",program_field:"program_field",all_points:"all_points",total_points:"total",deleted_field:"deleted"},program_options:{113:"base",114:"silver",115:"gold"}}},f=$("all_points").hasClassName("tondach")?"TONDACH":"WIENERBERGER",g=e[f],h=!1,i=0,j=function(a){if(/.*_([0-9]+)/.test(a)){var b=$$("input[id^="+g.program_fields.deleted_field+"_]");if(b){var c=$(g.program_fields.deleted_field+"_"+a.match(/.*_([0-9]+)/)[1]).value;if(c)return!0}}return!1},k=function(a){return!!/.*_([0-9]+)/.test(a)&&!!$(g.program_fields.id_field+"_"+a.match(/.*_([0-9]+)/)[1]).value},l=function(){return $$("input[name^="+g.program_fields.id_field+"]").each(function(a){if(!j(a.id)){if($(g.program_ids.gold).include(a.value))throw h="gold",$break;$(g.program_ids.base).include(a.value)&&i++}}),h||1!=i?!h&&i>1&&(h="silver"):h="base",h},m=function(){var a=0;return $(g.program_fields.first_radio)&&$(g.program_fields.first_radio).checked&&(a+=g.program_points.first_radio),$(g.program_fields.second_radio)&&$(g.program_fields.second_radio).checked&&(a+=g.program_points.second_radio),a},n=function(){switch(f){case"WIENERBERGER":var a=l();$$("input[name^="+g.program_fields.id_field+"]").each(function(b){if(/.*_([0-9]+)$/.test(b.id)&&$$("input[name^="+g.program_fields.id_field+"]").length){var c=b.id.match(/.*_([0-9]+)$/)[1];if(j(b.id))return;var d={base:{title:$(g.program_fields.base+"_"+c).title,value:$(g.program_fields.base+"_"+c).value},silver:{title:$(g.program_fields.silver+"_"+c).title,value:$(g.program_fields.silver+"_"+c).value},gold:{title:$(g.program_fields.gold+"_"+c).title,value:$(g.program_fields.gold+"_"+c).value}};$(g.program_fields.total_bonus_points+"_"+c).value=k(b.id)?d[a].value:"",$(g.program_fields.program_field).value=a?d[a].title:"",gt2calc()}});break;case"TONDACH":$$("input[name^="+g.program_fields.id_field+"]").each(function(b){if(/.*_([0-9]+)$/.test(b.id)&&$$("input[name^="+g.program_fields.id_field+"]").length){var c=b.id.match(/.*_([0-9]+)$/)[1];if(j(b.id))return;a=$(g.program_fields.program+"_"+c).value,a&&(a=g.program_options[a]);var d={base:{value:$(g.program_fields.base+"_"+c).value},silver:{value:$(g.program_fields.silver+"_"+c).value},gold:{value:$(g.program_fields.gold+"_"+c).value}};$(g.program_fields.total_bonus_points+"_"+c).value=k(b.id)&&a?d[a].value:"",gt2calc()}})}var b=m(),c=$(g.program_fields.total_points).value;$(g.program_fields.all_points).value=parseFloat(c)+parseFloat(b)};$$("input[name^="+g.program_fields.id_field+"]").length>0&&n()}' WHERE id IN (102563, 203161);

######################################################################################
# 2016-12-16 - Added the entire custom javascript for prepareRoofFittings and calcObjectProject (minimized with jscompress.com) according to Bug 4415, comment 58
#            - Added the entire custom javascript for prepareRoofFittings and calcObjectProject (minimized with jscompress.com) according to Bug 4415, comment 61

# Added the entire custom javascript for prepareRoofFittings and calcObjectProject (minimized with jscompress.com) according to Bug 4415, comment 58
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price_calc",b),e=b.select(".to_price_manual")[0],f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(d*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:0;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=MROUND(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:ROUNDDOWN(j*n/v,0),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:ROUNDUP(k*n/v*2,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:ROUNDUP(m*n,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:0,Number(c)){case 1111:case 1112:s=e=MROUND((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],o=getElFloatVal(".to_measure",b),q=(getElFloatVal(".to_teglo_br",b),b.select(".to_price_manual")[0]);g.value=f,q&&""!=q.value&&(f=getElFloatVal(".to_price_manual",b)),o=o?o:1;var r=getElFloatVal(".to_coef",b);r=r?r:1;var t=getElFloatVal(".to_round",b);t=t?t:0;var u=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1120:e=u?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1123:e=u?0:ROUNDUP(j*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=u?0:ROUNDUP(m*o,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=u?0:ROUNDUP(m*o,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=u?0:ROUNDUP(n*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 6318:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){toggleRowReadonly(a.rows[1],!1);var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];throw f=c.getValue(),$break}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value='''' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","cover": "''+$("type_cover").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="SELECT nonceramic.id FROM ((SELECT DISTINCT(nc3.value) as element_id, id, nc4.value as `position` FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5024 JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc2.value=<color> OR nc2.value='''') JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 LEFT JOIN tags_models as tm  ON n.id=tm.model_id AND tm.model=''Nomenclature'' AND tm.tag_id=47 WHERE n.type=24 AND (tm.model_id IS NOT NULL "+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+") ORDER BY nc4.value)"+(h.length>0?"UNION(SELECT DISTINCT(nc6.value) as element_id, nc5.value as id, nc7.value as `position` FROM nom_cstm nc5 JOIN nom_cstm nc6 ON nc5.value=nc6.model_id AND nc6.var_id=2435 JOIN nom_cstm nc7 ON nc6.value=nc7.model_id AND nc7.var_id=2012 WHERE nc5.var_id=5045 AND nc5.value!='''' AND nc5.model_id IN ("+h.join(", ")+")))":")")+" as nonceramic GROUP BY nonceramic.`element_id` ORDER BY nonceramic.`position` ",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(''Error 404: location "''+a.statusText+''" was not found.'')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(''[id^="''+c+''"]'').each(function(a){toggleReadonly(a,b)})});var c=a.select(".to_quantity")[0];toggleReadonly(c,1==getElFloatVal(".to_is_calculable:checked",a))}function MROUND(a,b){return b*Math.round(a/b)}function ROUNDUP(a,b){var c=Math.pow(10,b);return Math.ceil(a*c)/c}function ROUNDDOWN(a,b){var c=Math.pow(10,b);return Math.floor(a*c)/c}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id IN (3126, 3410);

# Added the entire custom javascript for prepareRoofFittings and calcObjectProject (minimized with jscompress.com) according to Bug 4415, comment 61
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price_calc",b),e=b.select(".to_price_manual")[0],f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(d*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:0;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=MROUND(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:ROUNDDOWN(j*n/v,0),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:ROUNDUP(k*n/v*2,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:0,Number(c)){case 1111:case 1112:s=e=MROUND((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],o=getElFloatVal(".to_measure",b),q=(getElFloatVal(".to_teglo_br",b),b.select(".to_price_manual")[0]);g.value=f,q&&""!=q.value&&(f=getElFloatVal(".to_price_manual",b)),o=o?o:1;var r=getElFloatVal(".to_coef",b);r=r?r:1;var t=getElFloatVal(".to_round",b);t=t?t:0;var u=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1120:e=u?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1123:e=u?0:ROUNDUP(j*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=u?0:ROUNDUP(n*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 6318:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){toggleRowReadonly(a.rows[1],!1);var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];throw f=c.getValue(),$break}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value='''' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","cover": "''+$("type_cover").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="SELECT nonceramic.id FROM ((SELECT DISTINCT(nc3.value) as element_id, id, nc4.value as `position` FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5024 JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc2.value=<color> OR nc2.value='''') JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 LEFT JOIN tags_models as tm  ON n.id=tm.model_id AND tm.model=''Nomenclature'' AND tm.tag_id=47 WHERE n.type=24 AND (tm.model_id IS NOT NULL "+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+") ORDER BY nc4.value)"+(h.length>0?"UNION(SELECT DISTINCT(nc6.value) as element_id, nc5.value as id, nc7.value as `position` FROM nom_cstm nc5 JOIN nom_cstm nc6 ON nc5.value=nc6.model_id AND nc6.var_id=2435 JOIN nom_cstm nc7 ON nc6.value=nc7.model_id AND nc7.var_id=2012 WHERE nc5.var_id=5045 AND nc5.value!='''' AND nc5.model_id IN ("+h.join(", ")+")))":")")+" as nonceramic GROUP BY nonceramic.`element_id` ORDER BY nonceramic.`position` ",c=''{"system": "''+$("otch_system_id").value+''","color": "''+$("otch_color_id").value+''","sql":"''+b+''","rights":"nomenclatures => ajax_select"}''}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(''Error 404: location "''+a.statusText+''" was not found.'')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(''[id^="''+c+''"]'').each(function(a){toggleReadonly(a,b)})});var c=a.select(".to_quantity")[0];toggleReadonly(c,1==getElFloatVal(".to_is_calculable:checked",a))}function MROUND(a,b){return b*Math.round(a/b)}function ROUNDUP(a,b){var c=Math.pow(10,b);return Math.ceil(a*c)/c}function ROUNDDOWN(a,b){var c=Math.pow(10,b);return Math.floor(a*c)/c}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id IN (3126, 3410);

######################################################################################
# 2017-01-30 - Replaced the algorithm of selecting the non ceramic elements (third table)

# Replaced the algorithm of selecting the non ceramic elements (third table)
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price_calc",b),e=b.select(".to_price_manual")[0],f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(d*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:0;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=MROUND(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:ROUNDDOWN(j*n/v,0),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:ROUNDUP(k*n/v*2,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:0,Number(c)){case 1111:case 1112:s=e=MROUND((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],o=getElFloatVal(".to_measure",b),q=(getElFloatVal(".to_teglo_br",b),b.select(".to_price_manual")[0]);g.value=f,q&&""!=q.value&&(f=getElFloatVal(".to_price_manual",b)),o=o?o:1;var r=getElFloatVal(".to_coef",b);r=r?r:1;var t=getElFloatVal(".to_round",b);t=t?t:0;var u=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1120:e=u?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1123:e=u?0:ROUNDUP(j*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=u?0:ROUNDUP(n*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 6318:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){toggleRowReadonly(a.rows[1],!1);var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];throw f=c.getValue(),$break}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value=\'\' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","cover": "\'+$("type_cover").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="SELECT id FROM (  SELECT element_id, id, position, MIN(origin) FROM (   SELECT * FROM (    (SELECT DISTINCT(nc3.value) as element_id, id, nc4.value as `position`, IF(tm2.model_id IS NOT NULL, \'3 - default\', IF(tm1.model_id IS NOT NULL, \'2 - main color (tag)\', \'4 - color\')) as origin       FROM nom n       JOIN nom_cstm nc1         ON n.id=nc1.model_id AND nc1.var_id=5024       JOIN tags_models as tm0        ON tm0.model_id=<color> AND tm0.model=\'Nomenclature\' AND tm0.tag_id IN (53, 54, 55)       JOIN nom_cstm nc3        ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"      JOIN nom_cstm nc2        ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc3.value!=1099 OR nc3.value=1099 AND (nc2.value=<color> OR nc2.value=\'\'))       JOIN nom_cstm nc4        ON nc3.value=nc4.model_id AND nc4.var_id=2012       LEFT JOIN tags_models as tm1        ON tm1.model_id=nc2.model_id AND tm1.model=\'Nomenclature\' AND tm1.tag_id=tm0.tag_id       LEFT JOIN tags_models as tm2        ON n.id=tm2.model_id AND tm2.model=\'Nomenclature\' AND tm2.tag_id=49       WHERE n.type=24 AND       (tm2.model_id IS NOT NULL OR        tm1.model_id IS NOT NULL"+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+")      )"+(h.length>0?"     UNION      (SELECT DISTINCT(nc6.value) as element_id, nc5.value as id, nc7.value as `position`, \'1 - related\' as origin       FROM nom_cstm nc5       JOIN nom_cstm nc6 ON nc5.value=nc6.model_id AND nc6.var_id=2435       JOIN nom_cstm nc7 ON nc6.value=nc7.model_id AND nc7.var_id=2012       WHERE nc5.var_id=5045 AND nc5.value!=\'\' AND nc5.model_id IN ("+h.join(", ")+")))":")")+" as nonceramic  ) as all_nonceramic GROUP BY all_nonceramic.`element_id`) all_nonceramic_grouped_by_element_and_sorted_by_origin ORDER BY position",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(\'Error 404: location "\'+a.statusText+\'" was not found.\')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(\'[id^="\'+c+\'"]\').each(function(a){toggleReadonly(a,b)})});var c=a.select(".to_quantity")[0];toggleReadonly(c,1==getElFloatVal(".to_is_calculable:checked",a))}function MROUND(a,b){return b*Math.round(a/b)}function ROUNDUP(a,b){var c=Math.pow(10,b);return Math.ceil(a*c)/c}function ROUNDDOWN(a,b){var c=Math.pow(10,b);return Math.floor(a*c)/c}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id IN (3126, 3410);

######################################################################################
# 2017-02-21 - Fixed the sorting of non-ceramic elements in 3rd table

# Fixed the sorting of non-ceramic elements in 3rd table
UPDATE `_fields_meta` SET `source`='floating_buttons := 1\r\ndont_copy_values := 1\r\ncustom_class := to_group to_group_tiles\r\nshow_select_buttons := nomenclatures \r\njavascript := function prepareRoofFittings(a){a?(a.select(".to_article_id").each(function(a){var b=a.parentNode.parentNode;a.getValue()&&toggleRowReadonly(b,!0)}),addEmptyRowToTable(a),a.select(".to_quantity").each(function(a){var b=a.parentNode.parentNode;1!=b.select(".to_is_calculable")[0].getValue()&&Event.observe(a,"keyup",function(){calcTondachOffer(this)});var c=b.select(".to_price_manual")[0];Event.observe(c,"keyup",function(){calcTondachOffer(this)});b.select(".to_yes_no").each(function(a){Event.observe(a,"click",function(){calcTondachOffer()})})})):$$("table.to_group").each(function(a){prepareRoofFittings(a)})}function calcTondachOffer(a){if($(a)){var b=$(a).parentNode.parentNode,c=getElFloatVal(".to_quantity",b),d=getElFloatVal(".to_price_calc",b),e=b.select(".to_price_manual")[0],f=b.select(".to_total")[0];e&&""!=e.value?(e=getElFloatVal(".to_price_manual",b),f.value=(e*c).toFixed(2)):(e=null,f.value=(d*c).toFixed(2));var g=$("offer_total_value"),h={tiles:0,ceramic:0,nonceramic:0},i=0,j=getElFloatVal("#total_area_roof"),k=getElFloatVal("#horizontal_bilo"),l=getElFloatVal("#total_length_horizontal_bila"),m=getElFloatVal("#total_length_strehi"),n=getElFloatVal("#total_length_ulami"),o=getElFloatVal("#to_dealer",""),p=getElFloatVal("#otch_profit",""),q=getElFloatVal("#truck_weight",""),r=getElFloatVal("#truck_price","");$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;1093!=a.getValue()&&(i+=getElFloatVal(".to_quantity",b))}),$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],l=b.select(".to_total")[0],n=getElFloatVal(".to_measure",b),s=getElFloatVal(".to_teglo_br",b),t=b.select(".to_price_manual")[0],u=q?r*s/q:0;f=(f*(1-o/100)+u)*(1+p/100),g.value=f=f.toFixed(3),t&&""!=t.value&&(f=getElFloatVal(".to_price_manual",b)),n=n?n:1;var v=getElFloatVal(".to_coef",b);v=v?v:1;var w=getElFloatVal(".to_round",b);w=w?w:0;var x=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1093:e=MROUND(j*n/v-i,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0});break;case 1103:e=x?0:ROUNDDOWN(j*n/v,0),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1106:e=x?0:ROUNDUP(k*n/v*2,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1107:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1109:e=x?0:ROUNDUP(m*n/v,w),d.value=e,l.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.tiles+=getElFloatVal(".to_total",b)});var s=0;$$(".to_group_ceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],j=getElFloatVal(".to_measure",b),m=getElFloatVal(".to_teglo_br",b),n=b.select(".to_price_manual")[0],t=q?r*m/q:0;f=(f*(1-o/100)+t)*(1+p/100),g.value=f=f.toFixed(3),n&&""!=n.value&&(f=getElFloatVal(".to_price_manual",b)),j=j?j:1;var u=getElFloatVal(".to_coef",b);u=u?u:1;var v=getElFloatVal(".to_round",b);switch(v=v?v:0,Number(c)){case 1111:case 1112:s=e=MROUND((k+l)*j/u,v),d.value=e,i.value=(f*e).toFixed(2);default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.ceramic+=getElFloatVal(".to_total",b)}),$$(".to_group_nonceramic .to_element").each(function(a){var b=$(a).parentNode.parentNode,c=a.getValue(),d=b.select(".to_quantity")[0],e=getElFloatVal(".to_quantity",b),f=getElFloatVal(".to_price",b),g=b.select(".to_price_calc")[0],i=b.select(".to_total")[0],o=getElFloatVal(".to_measure",b),q=(getElFloatVal(".to_teglo_br",b),b.select(".to_price_manual")[0]);g.value=f,q&&""!=q.value&&(f=getElFloatVal(".to_price_manual",b)),o=o?o:1;var r=getElFloatVal(".to_coef",b);r=r?r:1;var t=getElFloatVal(".to_round",b);t=t?t:0;var u=b.select(".to_yes_no:checked").length&&1==b.select(".to_yes_no:checked")[0].value?1:0;switch(Number(c)){case 1120:e=u?0:s,d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1123:e=u?0:ROUNDUP(j*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1099:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1121:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1125:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1126:e=u?0:ROUNDUP(m*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 1130:e=u?0:ROUNDUP(n*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;case 6318:e=u?0:ROUNDUP((k+l)*o/r,t),d.value=e,i.value=(f*e).toFixed(2),b.select(".to_yes_no").each(function(a){a.disabled=!1});break;default:b.select(".to_yes_no").each(function(a){2==a.value&&(a.checked=!0),a.disabled=!0})}h.nonceramic+=getElFloatVal(".to_total",b)}),g.value=(h.tiles+h.ceramic+h.nonceramic).toFixed(2)}else $$(".to_price_manual").each(function(a){calcTondachOffer(a)})}function multiSelectFittings(a,b){$$("table.to_group").each(function(a){for(;a.rows.length>2;)removeField(a.id);var b=a.select("tr:last-child")[0].select(".to_article_id")[0];b.getValue()&&b.setValue("");var c=$("otch_system_id").value?getComponentIds(a):[];if(c.length){toggleRowReadonly(a.rows[1],!1);var d=window["params_"+a.select(".to_article")[0].readAttribute("uniqid")];d.select_multiple=!0,d.table=a.id,d.row=a.rows.length-1,updateParentAutocomplete({autocomplete:d,ids:c,context:window}),prepareRoofFittings(a)}else a.rows[1].select("input").each(function(a){a.type.match(/(checkbox|radio)/i)?a.checked=!1:a.setValue("")}),a.rows[1].select("select").each(function(a){a.selectedIndex=0}),toggleRowReadonly(a.rows[1],!1);calcTondachOffer()})}function getComponentIds(a){var b,c,d=[];if(a.hasClassName("to_group_tiles")){var e=$("snow_retention_2").checked?" AND nc3.value!=1109 ":"";b="SELECT DISTINCT(id) FROM nom n JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=2112 AND nc1.value=<system> JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=2114 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2136 "+e+"JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 WHERE n.type=21 ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_ceramic")){var f=0;$$(".to_group_tiles .to_element").each(function(a){var b=$(a).parentNode.parentNode;if(1093==a.getValue()&&b.select(".to_article_id").length>0){var c=b.select(".to_article_id")[0];throw f=c.getValue(),$break}});var g="1111,1115"==$("type_cover").value||"1112,1116"==$("type_cover").value;if(!f&&g)return d;b="SELECT DISTINCT(id) FROM nom n "+(g?"":"JOIN nom_cstm nc1   ON n.id=nc1.model_id AND nc1.var_id=5000 AND nc1.value=<system> ")+"JOIN nom_cstm nc2  ON n.id=nc2.model_id AND nc2.var_id=5002 AND nc2.value=<color> JOIN nom_cstm nc3  ON n.id=nc3.model_id AND nc3.var_id=2347 AND nc3.value IN (<cover>) JOIN nom_cstm nc4  ON nc3.value=nc4.model_id AND nc4.var_id=2012 "+(g?"JOIN nom_cstm nc5  ON n.id=nc5.model_id AND nc5.var_id=5008 JOIN nom_cstm nc6  ON nc6.model_id="+f+" AND nc6.var_id=2120 ":"")+"WHERE n.type=23 "+(g?"      AND (nc6.value=\'\' OR nc6.value IS NULL OR nc6.value=nc5.value)":"")+"ORDER BY nc4.value",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","cover": "\'+$("type_cover").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}else if(a.hasClassName("to_group_nonceramic")){var e=$("snow_retention_1").checked?" nc3.value!=1099 ":" (nc3.value!=1099 OR nc3.value=1099 AND nc1.value=<system>) ",h=[];$$(".to_group_ceramic .to_article_id").each(function(a){(v=a.getValue())&&h.push(v)}),b="SELECT id FROM (  SELECT element_id, id, position, MIN(origin) FROM (   SELECT * FROM (    (SELECT DISTINCT(nc3.value) as element_id, id, nc4.value as `position`, IF(tm2.model_id IS NOT NULL, \'3 - default\', IF(tm1.model_id IS NOT NULL, \'2 - main color (tag)\', \'4 - color\')) as origin       FROM nom n       JOIN nom_cstm nc1         ON n.id=nc1.model_id AND nc1.var_id=5024       JOIN tags_models as tm0        ON tm0.model_id=<color> AND tm0.model=\'Nomenclature\' AND tm0.tag_id IN (53, 54, 55)       JOIN nom_cstm nc3        ON n.id=nc3.model_id AND nc3.var_id=2435 AND nc3.value != 1131 AND "+e+"      JOIN nom_cstm nc2        ON n.id=nc2.model_id AND nc2.var_id=5026 AND (nc3.value!=1099 OR nc3.value=1099 AND (nc2.value=<color> OR nc2.value=\'\'))       JOIN nom_cstm nc4        ON nc3.value=nc4.model_id AND nc4.var_id=2012       LEFT JOIN tags_models as tm1        ON tm1.model_id=nc2.model_id AND tm1.model=\'Nomenclature\' AND tm1.tag_id=tm0.tag_id       LEFT JOIN tags_models as tm2        ON n.id=tm2.model_id AND tm2.model=\'Nomenclature\' AND tm2.tag_id=49       WHERE n.type=24 AND       (tm2.model_id IS NOT NULL OR        tm1.model_id IS NOT NULL"+($("snow_retention_1").checked?"":" OR nc3.value=1099 AND nc1.value=<system>")+")      )"+(h.length>0?"     UNION      (SELECT DISTINCT(nc6.value) as element_id, nc5.value as id, nc7.value as `position`, \'1 - related\' as origin       FROM nom_cstm nc5       JOIN nom_cstm nc6 ON nc5.value=nc6.model_id AND nc6.var_id=2435       JOIN nom_cstm nc7 ON nc6.value=nc7.model_id AND nc7.var_id=2012       WHERE nc5.var_id=5045 AND nc5.value!=\'\' AND nc5.model_id IN ("+h.join(", ")+")))":")")+" as nonceramic  ) as all_nonceramic GROUP BY all_nonceramic.`element_id`) all_nonceramic_grouped_by_element_and_sorted_by_origin ORDER BY `position`*1",c=\'{"system": "\'+$("otch_system_id").value+\'","color": "\'+$("otch_color_id").value+\'","sql":"\'+b+\'","rights":"nomenclatures => ajax_select"}\'}var i={field:"tmp","fill_options[0]":"$tmp_id => <id>","fill_types[0]":"text",id_var:"tmp_id",plugin_params:c,plugin_search:"customQuery",row:"",suggestions:"<id>",uniqid:"",tmp:""};i=Object.toQueryString(i);var j={parameters:i,asynchronous:!1,method:"post",onSuccess:function(a){var b=document.createElement("div");b.innerHTML=a.responseText,b.select("li").each(function(a){a.id.match(/autocompleters_/)&&d.push(a.id.replace(/autocompleters_/g,""))})},on404:function(a){alert(\'Error 404: location "\'+a.statusText+\'" was not found.\')},onFailure:function(a){alert("Error "+a.status+" -- "+a.statusText)}},k=env.base_url+"?"+env.module_param+"=autocompleters&autocompleters=ajax_select";return new Ajax.Request(k,j),d}function addEmptyRowToTable(a){if(a){if(a.select("tr:last-child")[0].select(".to_article_id")[0].readOnly){addField(a.id,!1,!1,!0);var b=a.select("tr:last-child")[0];toggleRowReadonly(b,!1)}}else $$("table.to_group").each(function(a){addEmptyRowToTable(a)})}function toggleRowReadonly(a,b){["otch_code","otch_article","otch_element_h","br_me_razhod","rounding","otch_me","koef_br_me"].each(function(c){a.select(\'[id^="\'+c+\'"]\').each(function(a){toggleReadonly(a,b)})});var c=a.select(".to_quantity")[0];toggleReadonly(c,1==getElFloatVal(".to_is_calculable:checked",a))}function MROUND(a,b){return b*Math.round(a/b)}function ROUNDUP(a,b){var c=Math.pow(10,b);return Math.ceil(a*c)/c}function ROUNDDOWN(a,b){var c=Math.pow(10,b);return Math.floor(a*c)/c}function getElFloatVal(a,b){var c=0;return b&&b.select(a)&&b.select(a).length&&b.select(a)[0].getValue()?c=parseFloat(b.select(a)[0].getValue()):!b&&$$(a)&&$$(a)[0].getValue()&&(c=parseFloat($$(a)[0].getValue())),isNaN(c)?0:c}document.observe("dom:loaded",function(){prepareRoofFittings(),calcTondachOffer()});' WHERE id IN (3126, 3410);

######################################################################################
# 2019-05-13 - Add setting for report wienerberger_monthly_report

# Add setting for report wienerberger_monthly_report
UPDATE reports
  SET settings = REPLACE(settings, 'wmr_started := started_name', 'wmr_started := started_name\r\nwmr_started_count := started_count_name')
  WHERE `type` = 'wienerberger_monthly_report'
    AND settings NOT LIKE '%started_count_name%';
