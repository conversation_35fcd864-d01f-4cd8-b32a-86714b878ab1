###########################################################################
### SQL nZoom Specific Updates - Byfar (http://byfar.n-zoom.com/) ###
###########################################################################

########################################################################
# 2018-06-29 - Added automation for creation/modification of shoe variants
#            - Added automation for validation of shoe
#            - Fixed size checkbox (generation_size)

# Added automation for creation/modification of shoe variants
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES 
(NULL, 'Create variants (sizes) of shoes and boots', 0, NULL, 1, 'nomenclatures', NULL, 'action', '5', 'nom_variant_type := 8\r\nnom_variant_var_barcode := info_barcode\r\n\r\nbarcode_prefix := 3800977\r\nbarcode_leading_zeros := 5\r\nbarcode_first_code := 250\r\n\r\n', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'', 'method := createVariants\r\nplugin := byfar', NULL, 3, 0, 1);

# Added automation for validation of shoe
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Validate shoes', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', '5', '', 'condition := \'[action]\' == \'edit\' && \'[request_is_post]\' == \'1\'', 'method := validateShoes\r\nplugin := byfar', 'cancel_action_on_fail := 1', 0, 0, 1);

# Fixed size checkbox (generation_size)
UPDATE `_fields_meta` 
SET `source`='options_align := horizontal\r\nmethod := getCustomDropdown\r\ntable := DB_TABLE_NOMENCLATURES\r\ntable_i18n := DB_TABLE_NOMENCLATURES_I18N\r\nlabel := name\r\nvalue := name\r\nwhere := type=\'26\'' 
WHERE  `name`='generation_size' and model='Nomenclature' and model_type=5;

########################################################################
# 2018-08-06 - Added automation for generation of barcode for bags

# Added automation for generation of barcode for bags
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Generate barcode for bags', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', '32', 'nom_variant_var_barcode := info_barcode\r\n\r\nbarcode_prefix := 3800977\r\nbarcode_leading_zeros := 5\r\nbarcode_first_code := 250\r\n\r\n', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\'', 'method := generateBarcode\r\nplugin := byfar', NULL, 3, 0, 1);

########################################################################
# 2018-08-20 - Added new automation for generating IFI file for nomenalture based on decx2psd pattern

# Added new automation for generating IFI file for nomenalture based on decx2psd pattern
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Create template file', 0, NULL, 1, 'nomenclatures', NULL, 'action', '5', 'generate_pattern := 3\r\nfile_var := file_ifi\r\nmanufacturer_ref_number_var := ref_number\r\nimporter_stype_var := manuf_stock_no\r\nankle_height_var := hs_height\r\ncountry_origin_var := hs_origin\r\n\r\noption_ankle_height_ankle_or_higher := 2\r\noption_ankle_height_ankle := 3', 'condition := ((\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[request_is_post]\' == \'1\') && \'[a_file_ifi]\' == \'\'', 'plugin := byfar\r\nmethod := createShoesFile', NULL, 0, 0, 1);

########################################################################
# 2018-08-21 - Added automation for copying the values of selected group vars (via settings from DB) as values to other selected group vars from the same model

# Added automation for copying the values of selected group vars (via settings from DB) as values to other selected group vars from the same model
DELETE FROM automations WHERE method LIKE '%copyGroupVars%';
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
VALUES ('Copy values from group var to other group var from the same document', 0, NULL, 1, 'documents', NULL, 'action', '1', '# field_копирано поле := поле приемащо стойността\r\nfield_shoe_hiddenpic := shoe_picture\r\nfield_bag_hiddenpic := bag_picture', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\' \r\ncondition := \'[request_is_post]\' ', 'plugin := byfar\r\nmethod := copyGroupVars', NULL, 0, 0, 1);

########################################################################
# 2018-08-22 - Added transformations for creation of related records to contracts
#            - Added automations for creation of warehouse documents related to contracts

# Added transformations for creation of related records to contracts
INSERT IGNORE INTO `transformations` (`id`, `source_model`, `source_type`, `destination_model`, `destination_type`, `transform_type`, `method`, `settings`, `position`, `display`, `active`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
(1, 'Document', 1, 'Contract', 1, 'one2one', 'transformSimple', 'transform_once := 1', 1, 'both', 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(2, 'Contract', 1, 'Document', 5, 'one2one', 'transformSimple', 'transform_b_id := b_contract', 1, 'both', 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(3, 'Contract', 1, 'Document', 3, 'one2one', 'transformSimple', 'equals_b_customer := ', 2, 'both', 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(4, 'Contract', 2, 'Document', 5, 'one2one', 'transformSimple', 'transform_b_id := b_contract', 1, 'both', 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(5, 'Document', 3, 'Document', 4, 'one2one', 'transformSimple', NULL, 2, 'both', 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(6, 'Document', 3, 'Finance_Expenses_Reason', 20, 'one2one', 'transformSimple', 'transform_once := 1', 3, 'both', 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);

INSERT IGNORE INTO `transformations_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(1, 'B2B Contract', NULL, 'bg', NOW()),
(1, 'B2B Contract', NULL, 'en', NOW()),
(2, 'Fulfillment', NULL, 'bg', NOW()),
(2, 'Fulfillment', NULL, 'en', NOW()),
(3, 'Production Request (temporary)', NULL, 'bg', NOW()),
(3, 'Production Request (temporary)', NULL, 'en', NOW()),
(4, 'Fulfillment', NULL, 'bg', NOW()),
(4, 'Fulfillment', NULL, 'en', NOW()),
(5, 'Delivery Verification Report', NULL, 'bg', NOW()),
(5, 'Delivery Verification Report', NULL, 'en', NOW()),
(6, 'Delivery Invoice', NULL, 'bg', NOW()),
(6, 'Delivery Invoice', NULL, 'en', NOW());

# Added automations for creation of warehouse documents related to contracts
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Creates incoming Handover when delivery is verified', 0, NULL, 1, 'documents', NULL, 'action', '4', 'found_defects_var := free_field5\r\nwarehouse_var := warehouse\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[prev_b_status]\' != \'closed\' && \'[b_status]\' == \'closed\' && \'[a_warehouse]\' > 0', 'plugin := byfar\r\nmethod := addIncomingHandover', NULL, 2, 1, 1),
(NULL, 'Creates multiple Commodity reservations when commodities are automatically stored into warehouse after delivery verification', 0, NULL, 1, 'documents', NULL, 'action', '4', 'warehouse_var := warehouse\r\nfound_defects_var := free_field5\r\ncontract_var := article_alternative_deliverer\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[prev_b_status]\' != \'closed\' && \'[b_status]\' == \'closed\' && \'[a_warehouse]\' > 0', 'plugin := byfar\r\nmethod := addCommoditiesReservation', NULL, 3, 1, 1),
(NULL, 'Creates multiple Commodity reservations when commodities are manually stored into warehouse from Expense Invoice', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', '20', 'contract_var := article_alternative_deliverer', 'condition := \'[request_is_post]\' && \'[action]\' == \'addhandover\'', 'plugin := byfar\nmethod := addCommoditiesReservation', NULL, 2, 0, 1),
(NULL, 'Creates Commodity reservation when B2C contract is completed', 0, NULL, 1, 'contracts', NULL, 'action', '2', 'warehouse_var := warehouse\r\ncontract_var := article_alternative_deliverer\r\n', 'condition := \'[request_is_post]\' && \'[action]\' == \'setstatus\' && \'[prev_b_status]\' != \'closed\' && \'[b_status]\' == \'closed\'', 'plugin := byfar\r\nmethod := addCommoditiesReservation', NULL, 2, 1, 1),
(NULL, 'Creates Commodity reservation when B2B contract is completed (under certain circumstances)', 0, NULL, 1, 'contracts', NULL, 'action', '1', 'warehouse_var := warehouse\ncontract_var := article_alternative_deliverer\n\n\nstock_reserve_var := stock_reserve\nstock_reserve_yes := 1\n\nsales_terms_var := sales_terms\nsales_terms_reserve_values := 227,225\n', 'condition := \'[request_is_post]\' && \'[action]\' == \'setstatus\' && \'[b_status]\' == \'closed\' && \'[b_substatus]\' == \'1\' && \'[prev_b_substatus]\' != \'1\'', 'plugin := byfar\r\nmethod := addCommoditiesReservation', NULL, 2, 1, 1),
(NULL, 'Sets B2B/B2C contract into its topic (GT2 rows)', 0, NULL, 1, 'contracts', NULL, 'action', '0', 'contract_var := article_alternative_deliverer\r\ncontract_num_var := article_alternative_deliverer_name', 'condition := in_array(\'[b_type]\', array(1, 2))\r\ncondition := \'[request_is_post]\'\r\ncondition := in_array(\'[action]\', array(\'add\', \'edittopic\', \'setstatus\'))\r\n', 'plugin := byfar\r\nmethod := setContractIntoGT2', NULL, 1, 0, 1),
(NULL, 'Creates outgoing Handover when Fulfillment to B2B/B2C contract is completed', 0, NULL, 1, 'documents', NULL, 'action', '5', 'warehouse_var := warehouse\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[b_status]\' == \'closed\' &&  \'[b_substatus]\' == \'1\' &&  \'[prev_b_substatus]\' != \'1\' && \'[a_warehouse]\' > 0', 'plugin := byfar\r\nmethod := addOutgoingHandover', NULL, 2, 1, 1);

SELECT @aid := `id`
FROM `automations`
WHERE `module` = 'documents' AND `start_model_type` = 4 AND `method` LIKE 'plugin := byfar\r\nmethod := addIncomingHandover'
ORDER BY `id` DESC
LIMIT 0, 1;

UPDATE `automations`
SET `depend` = @aid
WHERE `module` = 'documents' AND `start_model_type` = 4 AND `method` LIKE 'plugin := byfar\r\nmethod := addCommoditiesReservation';

########################################################################
# 2018-08-24 - Changed the position of generateBarcode automation for bags
#            - Added info_size variable for the createVariants automation

# Changed the position of generateBarcode automation for bags
UPDATE `automations` SET `position`=0 WHERE  method like '%generateBarcode%' AND start_model_type=32;

# Added info_size variable for the createVariants automation
UPDATE `automations` SET `settings`=REPLACE(settings, 'nom_variant_var_barcode := info_barcode\r\n', 'nom_variant_var_barcode := info_barcode\r\nnom_variant_var_size := info_size\r\n') WHERE  method like '%createVariants%' AND settings NOT LIKE '%nom_variant_var_size%';

########################################################################
# 2018-08-27 - Updated copyGroupVars automation condition
#            - Added automation for creation of GT2 table in Draft Order document from its GT data

# Updated copyGroupVars automation condition
UPDATE `automations`
SET `conditions` = 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')\r\ncondition := \'[request_is_post]\''
WHERE `method` LIKE '%method := copyGroupVars%' AND `conditions` LIKE 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'%';

# Added automation for creation of GT2 table in Draft Order document from its GT data
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Creates GT2 table of model from data in its GTs', 0, NULL, 1, 'documents', NULL, 'action', '1', 'currency_var := order_currency\r\n\r\n# keywords of GT fields data will be taken from\r\nkeywords := shoe, bag\r\n\r\n# specific variables for variants\r\nshoe_id_var := shoe_id\r\nshoe_size_var_prefix := size_\r\nshoe_size_var := info_size\r\n\r\n# shoe var mapping\r\ngt2_shoe_article_id := s_a_sku_code_id\r\ngt2_shoe_quantity := \r\ngt2_shoe_price := \r\ngt2_shoe_article_name := d_b_name\r\ngt2_shoe_article_code := d_b_code\r\ngt2_shoe_article_barcode := d_a_info_barcode\r\ngt2_shoe_article_delivery_code := shoe_ref_code\r\ngt2_shoe_article_trademark := s_a_producer_color_id\r\ngt2_shoe_article_measure_name := 1\r\ngt2_shoe_article_height := b2b_priceshoe\r\ngt2_shoe_article_width := landed_priceshoe\r\ngt2_shoe_article_weight := rrp_priceshoe\r\ngt2_shoe_article_volume := markupshoe\r\ngt2_shoe_last_delivery_price := marginshoe\r\ngt2_shoe_free_field1 := shoe_picture\r\ngt2_shoe_free_field2 := shoe_color\r\ngt2_shoe_free_field4 := d_a_info_size\r\n\r\nbag_id_var := bag_id\r\n\r\n# bag var mapping\r\ngt2_bag_article_id := bag_id\r\ngt2_bag_quantity := bag_qty\r\ngt2_bag_price := \r\ngt2_bag_article_name := bag_name\r\ngt2_bag_article_code := bag_code\r\ngt2_bag_article_barcode := s_a_info_barcode\r\ngt2_bag_article_delivery_code := bag_ref_code\r\ngt2_bag_article_trademark := s_a_producer_color_id\r\ngt2_bag_article_measure_name := 1\r\ngt2_bag_article_height := b2b_pricebag\r\ngt2_bag_article_width := landed_pricebag\r\ngt2_bag_article_weight := rrp_pricebag\r\ngt2_bag_article_volume := markupbag\r\ngt2_bag_last_delivery_price := marginbag\r\ngt2_bag_free_field1 := bag_picture\r\ngt2_bag_free_field2 := bag_color\r\ngt2_bag_free_field4 := size_bag\r\n\r\n# price depends on conditions\r\ndelivery_terms_var := delivery_terms\r\ndelivery_terms_222 := b2b_price[keyword]\r\ndelivery_terms_223 := landed_price[keyword]\r\n', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')\r\ncondition := \'[request_is_post]\'', 'plugin := byfar\r\nmethod := createOrderGt2', NULL, 2, 0, 1);

########################################################################
# 2018-09-13 - Updated setContractIntoGT2 automation method and settings, updated position of contracts automations
#            - Added setting for reservation period to addCommoditiesReservation automations
#            - Updated transformation settings
#            - Added 'byfar_request_management' report

# Updated setContractIntoGT2 automation method and settings, updated position of contracts automations
UPDATE `automations`
SET `name` = 'Sets B2B/B2C contract data into its topic (GT2 rows)',
    `settings` = 'copy_b_id := article_alternative_deliverer\r\ncopy_b_num := article_alternative_deliverer_name\r\ncopy_b_customer := article_deliverer\r\ncopy_b_customer_name := article_deliverer_name\r\ncopy_a_delivery_date := free_field3\r\n',
    `conditions` = 'condition := in_array(\'[b_type]\', array(1, 2))\r\ncondition := \'[request_is_post]\'\r\ncondition := in_array(\'[action]\', array(\'add\', \'edit\', \'edittopic\', \'setstatus\'))\r\n',
    `method` = 'plugin := byfar\r\nmethod := setContractIntoGt2',
    `position` = 2
WHERE `module` = 'contracts' AND `method` = 'plugin := byfar\r\nmethod := setContractIntoGT2';

UPDATE `automations`
SET `method` = 'plugin := byfar\r\nmethod := addCommoditiesReservation'
WHERE `method` LIKE 'plugin := byfar%method := addCommoditiesReservation%';

UPDATE `automations`
SET `position` = 3
WHERE `module` = 'contracts' AND `method` = 'plugin := byfar\r\nmethod := addCommoditiesReservation' AND `position` = 2;

# Added setting for reservation period to addCommoditiesReservation automations
UPDATE `automations`
SET `settings` = CONCAT(`settings`, '\r\nreservation_period := 1 month\r\n')
WHERE `method` = 'plugin := byfar\r\nmethod := addCommoditiesReservation' AND `settings` NOT LIKE '%reservation_period%';

# Updated transformation settings
UPDATE `transformations`
SET `active` = 0
WHERE `id` = 3 AND `destination_model` = 'Document' AND `destination_type` = 3;

UPDATE `transformations`
SET `settings` = CONCAT(`settings`, '\r\ntransform_b_office := b_office')
WHERE `id` IN (2, 4) AND `source_model` = 'Contract' AND `destination_model` = 'Document' AND `destination_type` = '5' AND `settings` NOT LIKE '%office%';

# Added 'byfar_request_management' report
SET @id := 395;

INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@id, 'byfar_request_management', 'cust_type_customer := 2,3\r\ncust_type_producer := 4\r\ncon_type_order := 1,2\r\ndoc_type_request := 3\r\nnom_type_article := 5,32\r\nnom_type_variant := 8\r\n\r\nrequest_substatus_supplied := 2\r\n\r\narticle_producer_id_var := producer_id\r\narticle_producer_name_var := producer_name\r\narticle_variant_id_var := sku_code_id\r\narticle_variant_size_var := size_name\r\n\r\norder_warehouse_var := warehouse\r\norder_delivery_date_var := delivery_date\r\n\r\nproducer_vat_rate_var := delivery_vat\r\nproducer_currency_var := delivery_currency\r\n\r\navailability_warehouses := \r\n\r\nreservation_period := 1 month\r\n\r\nfreeze_table_headers := 1\r\n', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@id, 'Управление на заявки', NULL, NULL, 'bg'),
(@id, 'Request management', NULL, NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @id, 0, 1),
(NULL, 'reports', '', 'export', @id, 0, 2);

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @id;

########################################################################
# 2018-09-20 - Added automations for validation and sync with Shopify for shoes (5) and bags (32)

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Validate Shopify data', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', '5', '', 'condition := \'[action]\' == \'edit\' && \'[request_is_post]\' == \'1\'', 'method := validateShopifyData\r\nplugin := byfar', 'cancel_action_on_fail := 1', 0, 0, 1),
('Validate Shopify data', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', '32', '', 'condition := \'[action]\' == \'edit\' && \'[request_is_post]\' == \'1\'', 'method := validateShopifyData\r\nplugin := byfar', 'cancel_action_on_fail := 1', 0, 0, 1),
('nZoom to Shopify Sync', 0, NULL, 1, 'nomenclatures', NULL, 'action', '5', '', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'', 'method := nzoom2ShopifySync\r\nplugin := byfar', NULL, 99, 0, 1),
('nZoom to Shopify Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '5', '', 'condition := 1', 'method := nzoom2ShopifySync\r\nplugin := byfar', NULL, 99, 0, 1),
('nZoom to Shopify Sync', 0, NULL, 1, 'nomenclatures', NULL, 'action', '32', '', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'', 'method := nzoom2ShopifySync\r\nplugin := byfar', NULL, 99, 0, 1),
('nZoom to Shopify Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '32', '', 'condition := 1', 'method := nzoom2ShopifySync\r\nplugin := byfar', NULL, 99, 0, 1),
('Shopify to nZoom Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '5', '#shopify credentials\r\nwebsite_id := 338\r\nsend_to_email := <EMAIL>', 'condition := 1', 'method := shopify2NzoomSync\r\nplugin := byfar', NULL, 10, 1, 1);

########################################################################
# 2018-10-01 - Added size to the name of variants (Nomenclature type 8)
#            - Fixed text_align setting
#            - Updated settings of automation for creation of GT2 table in Draft Order document from its GT data

# Added size to the name of variants (Nomenclature type 8)
UPDATE nom n
JOIN nom_i18n ni
  on n.id=ni.parent_id and n.type=8 and name NOT REGEXP ' [0-9]{2}$'
JOIN nom_cstm nc
  on n.id=nc.model_id and nc.var_id=3108
SET ni.name=CONCAT(ni.name, ' ', nc.value);

# Fixed text_align setting
UPDATE `_fields_meta` SET `source` = REPLACE(`source`, 'text_align := <right>', 'text_align := right') WHERE `source` LIKE '%<right>%';
UPDATE `_fields_meta` SET `source` = REPLACE(`source`, 'text_align := <left>', 'text_align := left') WHERE `source` LIKE '%<left>%';
UPDATE `_fields_meta` SET `source` = REPLACE(`source`, 'text_align := <center>', 'text_align := center') WHERE `source` LIKE '%<center>%';

# Updated settings of automation for creation of GT2 table in Draft Order document from its GT data
UPDATE `automations`
SET `settings` = CONCAT(`settings`, '\r\nshipping_costs_var := shipping_total\r\nshipping_costs_article_id := 3435\r\n')
WHERE `method` LIKE '%createOrderGt2%' AND `settings` NOT LIKE '%shipping_costs_var := shipping_total%';

########################################################################
# 2018-10-03 - Added pattern plugin for labels

# Added pattern plugin for labels
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(84, 'Document', 6, 'byfar', 'prepareLabels', '', '', now(), now());

INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(84, 'Подготовка за печат на етикети', '', 'bg', NOW()),
(84, 'Print labels', '', 'en', NOW());

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'title', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Име на артикул', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Article Name', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'code', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'SKU код', NULL, 'bg'),
  (LAST_INSERT_ID(), 'SKU Code', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'size', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Размер', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Size', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'price', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Цена', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Price', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'currency', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Валута', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Currency', NULL, 'en');

# Set plugin to the Label print pattern
UPDATE patterns SET plugin=84 WHERE id=9;

########################################################################
# 2018-10-24 - Added new placeholders for pattern plugin for labels

# Added new placeholders for pattern plugin for labels
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'made_in', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Произведено в', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Made in', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'composition', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Състав', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Composition', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'lining', 'Document', 'basic', 'patterns', ',84,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Облицовка', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Lining', NULL, 'en');

########################################################################
# 2018-12-10 - Added pattern plugin for labels (A4)

# Added pattern plugin for labels (A4)
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
('85', 'Document', '6', 'byfar', 'prepareLabelsA4', '', now(), now());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(85, 'Подготовка за печат на етикети A4', '', 'bg', NOW()),
(85, 'Print labels', '', 'en', NOW());

UPDATE `placeholders` SET pattern_id=',84,85,' WHERE model = 'Document' AND `usage` = 'patterns' AND `pattern_id` = ',84,';

########################################################################
# 2019-01-11 - Added automation to issue an advance invoice/proforma

# Added automation to issue an advance invoice/proforma
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Съзаване на шаблон за авансови (проформа) фактури', 0, NULL, 1, 'contracts', '', 'before_action', '1', 'invoice_email_template := \r\nproforma_invoice_email_template := \r\ninvoice_pattern := \r\nproforma_invoice_pattern := ', 'condition := 1', 'plugin := byfar\r\nmethod := createAdvanceInvoicesTemplate', 'cancel_action_on_fail := 1', 0, 0, 1);

########################################################################
# 2019-01-17 - Changed the name of pattern plugin for A4

# Changed the name of pattern plugin for A4
UPDATE patterns_plugins_i18n SET name='Print labels A4'
WHERE parent_id=85 AND lang='en';

########################################################################
# 2019-01-21 - New automation to import orders from shopify to nZoom

# New automation to import orders from shopify to nZoom
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
VALUES ('Актуализиране на заявки от Shopify', 0, NULL, 1, 'finance', NULL, 'crontab', '0', 'send_to_email := <EMAIL>\r\n\r\n#shopify credentials\r\nwebsite_ids := 338\r\n\r\nB2C_customer_type := 2\r\nno_marketing_tag := 16\r\n\r\n# Настройки за всеки сайт\r\n338_department_id := 1\r\n338_group_id := 1\r\n338_employee_id := -1\r\n\r\n# ИД на иртикул за доставка\r\nshipping_article_id := 9999\r\nshipping_article_name := Shipping', 'condition := 1', 'plugin := byfar\r\nmethod := recordNewShopifyOrders\r\n', NULL, 0, 0, 0);

########################################################################
# 2019-01-28 - Added automation for creation of interim invoices for B2B contract from data in related Fulfillment document

# Added automation for creation of interim invoices for B2B contract from data in related Fulfillment document
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Creates interim Invoice when Fulfillment to B2B contract is completed', 26, NULL, 1, 'documents', NULL, 'action', '5', 'invoice_email_template := 1002\nproforma_invoice_email_template := 1001\ninvoice_pattern := 14\nproforma_invoice_pattern := 15\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[b_status]\' == \'closed\' &&  \'[b_substatus]\' == \'1\' && \'[prev_b_substatus]\' != \'1\' && \'[a_warehouse]\' > 0 && \'[a_issue_invoice]\' == \'1\'', 'plugin := byfar\r\nmethod := issueInterimInvoice', NULL, 3, 1, 1);

########################################################################
# 2019-02-08 - Updated settings of shopify import automation

# Updated settings of shopify import automation
UPDATE `automations`
SET `settings`= CONCAT(`settings`, '\r\n\r\n# Настройка за извънредно пускане на АД-то за поръчки в период от-до. В нормалната работа на АД, настройката е закоментирана.\r\n# take_orders_from_period := 15.12.2018 - 20.12.2018')
WHERE  `method` LIKE '%recordNewShopifyOrders%';

########################################################################
# 2019-02-27 - Added automations that sync shoes, bags and customers to JOOR

# Added automations that sync shoes, bags and customers to JOOR
INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('nZoom Shoes to JOOR Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '5', '#SANDBOX\r\n#joor_api_url := https://apisandbox.jooraccess.com/v2\r\n#joor_api_token := ********************************\r\n#PRODUCTION\r\njoor_api_url := https://api.jooraccess.com/v2\r\njoor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43\r\ntags := 58\r\nsend_to_email := <EMAIL>', 'condition := 1', 'method := nzoom2JoorNomSync\r\nplugin := byfar', NULL, 99, 0, 0),
('nZoom Bag to JOOR Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '32', '#SANDBOX\r\n#joor_api_url := https://apisandbox.jooraccess.com/v2\r\n#joor_api_token := ********************************\r\n#PRODUCTION\r\njoor_api_url := https://api.jooraccess.com/v2\r\njoor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43\r\ntags := 58\r\nsend_to_email := <EMAIL>', 'condition := 1', 'method := nzoom2JoorNomSync\r\nplugin := byfar', NULL, 99, 0, 0),
('nZoom B2B customers to JOOR Sync', 0, NULL, 1, 'customers', NULL, 'crontab', '3', '#SANDBOX\r\n#joor_api_url := https://apisandbox.jooraccess.com/v2\r\n#joor_api_token := ********************************\r\n#PRODUCTION\r\njoor_api_url := https://api.jooraccess.com/v2\r\njoor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43\r\nsend_to_email := <EMAIL>', 'condition := 1', 'method := nzoom2JoorCustomerSync\r\nplugin := byfar', NULL, 99, 0, 0);

########################################################################
# 2019-03-19 - Added automations that sync customers (connections) and documents (orders) from JOOR to nZoom

# Added automations that sync customers (connections) and documents (orders) from JOOR to nZoom
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('JOOR connections to nZoom B2B customers Sync', 0, NULL, 1, 'customers', NULL, 'crontab', '3', '#SANDBOX\r\n#joor_api_url := https://apisandbox.jooraccess.com/v2\r\n#joor_api_token := ********************************\r\n#PRODUCTION\r\njoor_api_url := https://api.jooraccess.com/v2\r\njoor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43\r\nsend_to_email := <EMAIL>\r\n\r\ntag_new_customer := 59', 'condition := 1', 'method := joor2NzoomCustomerSync\r\nplugin := byfar', NULL, 99, 0, 0),
('JOOR orders to nZoom Draf order shoes/bags Sync', 0, NULL, 1, 'documents', NULL, 'crontab', '0', '#SANDBOX\r\n#joor_api_url := https://apisandbox.jooraccess.com/v2\r\n#joor_api_token := ********************************\r\n#PRODUCTION\r\njoor_api_url := https://api.jooraccess.com/v2\r\njoor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43\r\nsend_to_email := <EMAIL>\r\n\r\ndraft_order_shoes_type := 1\r\ndraft_order_bags_type := 7\r\n#order_type as per the https://api-docs.jooraccess.com/reference#get-orders\r\norder_type := 6\r\n# customer tag to ignore\r\ncustomer_tag_need_review := 59\r\n# customer tag for new billing address\r\ncustomer_tag_new_billing_address := 60\r\n# customer tag for new shipping address\r\ncustomer_tag_new_shipping_address := 61', 'condition := 1', 'method := joor2NzoomOrderSync\r\nplugin := byfar', NULL, 99, 0, 0);

########################################################################
# 2019-05-13 - Added automations that syncs meta fields of products from JOOR to nZoom
#            - Added setting for tag group in nzoom2ShopifySync automation

# Added automations that syncs meta fields of products from JOOR to nZoom
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Shopify to nZoom Sync (meta fields)', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '5', '#shopify credentials\r\nwebsite_id := 338\r\nsend_to_email := <EMAIL>, <EMAIL>', 'condition := 1', 'method := shopify2NzoomMetaFieldsSync\r\nplugin := byfar', NULL, 11, 3, 0);

# Added setting for tag group in nzoom2ShopifySync automation
UPDATE automations SET settings=CONCAT(settings, '\r\ntag_group_shopify := 1') WHERE method like '%nzoom2ShopifySync%' AND settings NOT LIKE '%tag_group_shopify%';

########################################################################
# 2019-05-17 - Added automations that composes box number and barcode
#            - Added plugin to print packing labels

# PRE-DEPLOYED INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# ('Създава номер и баркод за кашон', 0, NULL, 1, 'documents', NULL, 'before_action', '10', 'box_num_shoe_prefix := -SHOE\r\nbox_num_bag_prefix := -BAG\r\nbox_num_shoe_leading_zeros := 5\r\nbox_num_bag_leading_zeros := 6', 'condition := \'[request_is_post]\' && \'[action]\' == \'setstatus\' && $this->registry->get(\'request\')->get(\'status\') == \'closed\' && \'[b_status]\' != \'closed\'', 'plugin := byfar\r\nmethod := composeBoxNumber', 'cancel_action_on_fail := 1', 0, 1, 1);

# PRE-DEPLOYED # Added pattern plugin for labels
#INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
#(88, 'Document', 10, 'byfar', 'preparePackingLabels', '', '', now(), now());

# PRE-DEPLOYED INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
#(88, 'Подготовка за печат на етикети за кашони', '', 'bg', NOW()),
#(88, 'Print packing labels', '', 'en', NOW());

# PRE-DEPLOYED # Set plugin to the Label print pattern
#UPDATE patterns SET model_type=10, plugin=88 WHERE id IN (17, 18, 19);

######################################################################################
# 2019-05-21 - Added bullet to fix the barcode duplicates

# PRE-DEPLOYED # Added bullet to fix the barcode duplicates
#INSERT INTO `bullets` (`bullet`, `description`, `revision`, `position`, `active`, `modified`, `fired`) VALUES
#('byfarFixBarcodeDuplicates', 'fix the barcode duplicates', 15392, 0, 1, NOW(), NOW());

######################################################################################
# 2019-06-13 - Added new automation which will add handovers for the sells added the previous day

# Added new automation which will add handovers for the sells added the previous day
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Изписване на стоки', 0, NULL, 1, 'finance_incomes_reasons', '', 'crontab', '101', 'start_time := 01:00\r\nstart_before := 03:00\r\n\r\nfir_type_sale := 101\r\ndefault_start_date := 2019-05-01\r\n\r\nsend_to_email := <EMAIL>\r\n', 'condition := 1', 'plugin := byfar\r\nmethod := advancedProtocol', NULL, 1, 0, 0);

########################################################################
# 2019-06-25 - Modified the automation that composes box number and barcode
#            - Corrected layout_id of customer record for contracts type 1 in `_fields_meta`
#            - Modified settings of 'byfar_request_management' report

# Modified the automation that composes box number and barcode
UPDATE `automations` SET `conditions`='condition := \'[request_is_post]\' && (\'[action]\' == \'add\' || \'[action]\' == \'edit\')', after_action='', automation_type='action' WHERE  method LIKE '%composeBoxNumber%';

# Corrected layout_id of customer record for contracts type 1 in `_fields_meta`
UPDATE `_fields_meta` fm
JOIN `layouts` l
  ON fm.`model` = l.`model`
    AND fm.`model_type` = l.`model_type`
    AND fm.`name` = l.`keyname`
SET fm.`layout_id` = l.`layout_id`
WHERE fm.`model` = 'Contract'
  AND fm.`model_type` = 1
  AND fm.`name` = 'customer'
  AND l.`system` = 1
  AND fm.`layout_id` != l.`layout_id`;

# Modified settings of 'byfar_request_management' report
UPDATE `reports`
SET `settings` = 'cust_type_customer := 2,3,5\r\ncust_type_producer := 4\r\ncon_type_order := 1,2\r\ndoc_type_request := 3\r\nnom_type_article := 5,32\r\nnom_type_variant := 8\r\nnom_type_season := 27\r\nnom_type_order_type := 43\r\n\r\ncon_tags_exclude := \r\n\r\nrequest_substatus_supplied := 2\r\n\r\narticle_producer_id_var := producer_id\r\narticle_producer_name_var := producer_name\r\narticle_variant_id_var := sku_code_id\r\narticle_variant_size_var := size_name\r\narticle_price_eur_var := deliv_price_eur\r\n\r\norder_warehouse_var := warehouse\r\norder_delivery_date_var := delivery_date\r\norder_line_var := nom_type\r\norder_season_var := season_name_id\r\norder_year_var := year_on_sale\r\norder_order_type_var := order_type\r\n\r\nproducer_vat_rate_var := delivery_vat\r\nproducer_currency_var := delivery_currency\r\n\r\navailability_warehouses := \r\n\r\nreservation_period := 1 month\r\n\r\nfreeze_table_headers := 1\r\n'
WHERE `type` = 'byfar_request_management' AND `settings` NOT LIKE '%con_tags_exclude%';

########################################################################
# 2019-08-12 - Added index to `gt2_details` in order to search relations between rows of records
#            - Fixed a bunch of things that no one else is bothered to fix
#            - Updated settings of 'byfar_request_management' report
#            - Updated undelivered quantity in Production Requests (until now relations with Expense Invoice were 1:many)
#            - Updated substatus of supplied Production Requests
#            - Updated automations for creation of commodity reservations and handovers
#            - Added automation for validation of Delivery Verification Report before completion
#            - Added automation to update undelivered quantity of Production Requests when Handover for Expense Invoice is annulled

# Added index to `gt2_details` in order to search relations between rows of records
ALTER TABLE `gt2_details`
  ADD KEY `model_2` (`model`,`model_id`,`article_id`,`article_alternative_deliverer`);

# Fixed a bunch of things that no one else is bothered to fix
UPDATE `documents_types_i18n`
SET `name_plural` = 'Production requests'
WHERE `name_plural` = 'Proudction requests';

UPDATE `documents_types_i18n`
SET `default_name` = REPLACE(`default_name`, '[customer]', '[customer_name]')
WHERE `default_name` LIKE '%[customer]%';

UPDATE `contracts_types_i18n`
SET `default_name` = '[office_name]'
WHERE `default_name` = 'office_name';

# Updated settings of 'byfar_request_management' report
UPDATE `reports`
SET `settings` = REPLACE(`settings`, 'con_type_order := 1,2', 'con_type_order := 1,2\r\ncon_type_order_reserve := 1')
WHERE `type` = 'byfar_request_management' AND `settings` NOT LIKE '%con_type_order_reserve%';

UPDATE `reports`
SET `settings` = REPLACE(`settings`, 'doc_type_request := 3', 'doc_type_request := 3\r\ndoc_type_delivery := 4')
WHERE `type` = 'byfar_request_management' AND `settings` NOT LIKE '%doc_type_delivery%';

# Updated undelivered quantity in Production Requests (until now relations with Expense Invoice were 1:many)
UPDATE `gt2_details` AS gt2_request
JOIN (
    SELECT gt2_request.`id` AS row_id,
      ROUND(SUM(IFNULL(gt2_handover.`quantity`, 0) * IF(fwd_handover.`direction` = 'incoming' OR fwd_handover.`direction` IS NULL, 1, -1)), 2) AS `delivered_quantity`
    FROM `documents` AS d_request
    JOIN `gt2_details` AS gt2_request
      ON d_request.`id` = gt2_request.`model_id`
        AND gt2_request.`model` = 'Document'
        AND d_request.`type` = 3
        AND d_request.`status` = 'closed'
    LEFT JOIN `fin_reasons_relatives` AS frr_invoice
      ON frr_invoice.`link_to_model_name` = 'Document'
        AND frr_invoice.`link_to` = d_request.`id`
        AND frr_invoice.`parent_model_name` = 'Finance_Expenses_Reason'
    LEFT JOIN `fin_reasons_relatives` AS frr_handover
      ON frr_handover.`link_to_model_name` = 'Finance_Expenses_Reason'
        AND frr_handover.`link_to` = frr_invoice.`parent_id`
        AND frr_handover.`parent_model_name` = 'Finance_Warehouses_Document'
    LEFT JOIN `fin_warehouses_documents` AS fwd_handover
      ON fwd_handover.`id` = frr_handover.`parent_id`
        AND fwd_handover.`type` = 7
        AND fwd_handover.`status` = 'finished'
        AND fwd_handover.`active` = 1 AND fwd_handover.`annulled_by` = 0
    LEFT JOIN `gt2_details` AS gt2_handover
      ON gt2_handover.`model` = 'Finance_Warehouses_Document'
        AND gt2_handover.`model_id` = fwd_handover.`id`
        AND gt2_handover.`article_id` = gt2_request.`article_id`
        AND gt2_handover.`article_alternative_deliverer` = gt2_request.`article_alternative_deliverer`
    GROUP BY gt2_request.`article_id`, gt2_request.`article_alternative_deliverer`, d_request.`id`
) AS t
  ON gt2_request.`id` = t.`row_id`
SET gt2_request.`article_volume` = ROUND(gt2_request.`quantity` - IFNULL(t.`delivered_quantity`, 0), 2);

# Updated substatus of supplied Production Requests
UPDATE `documents` AS d_request
LEFT JOIN `gt2_details` AS gt2_request
  ON d_request.`id` = gt2_request.`model_id` AND gt2_request.`model` = 'Document' AND gt2_request.`article_volume` > 0
SET d_request.`substatus` = 2
WHERE d_request.`type` = 3 AND d_request.`status` = 'closed' AND d_request.`substatus` = 0
  AND gt2_request.`id` IS NULL;

# Updated automations for creation of commodity reservations and handovers
UPDATE `automations`
SET `active` = 0
WHERE `module` = 'contracts' AND `start_model_type` = 2 AND `method` LIKE 'plugin := byfar%method := addCommoditiesReservation%';

UPDATE `automations`
SET `settings` = CONCAT(`settings`, '\r\ncon_type_order_reserve := 1')
WHERE `settings` NOT LIKE '%con_type_order_reserve%' AND `method` LIKE 'plugin := byfar%method := %addCommoditiesReservation%';

UPDATE `automations`
SET `settings` = CONCAT(`settings`, '\r\ndoc_type_request := 3\r\n')
WHERE `settings` NOT LIKE '%doc_type_request%' AND `module` = 'documents' AND `method` LIKE 'plugin := byfar%method := %addCommoditiesReservation%';

UPDATE `automations`
SET `settings` = CONCAT(`settings`, '\r\ndoc_type_request := 3\r\nrequest_substatus_supplied := 2\r\nundelivered_quantity_var := article_volume\r\n')
WHERE `settings` NOT LIKE '%doc_type_request%' AND `module` = 'finance' AND `controller` = 'expenses_reasons' AND `method` LIKE 'plugin := byfar%method := %addCommoditiesReservation%';

UPDATE `automations`
SET `settings` = CONCAT(`settings`, '\r\ncontract_var := article_alternative_deliverer\r\ndoc_type_request := 3\r\nrequest_substatus_supplied := 2\r\nundelivered_quantity_var := article_volume\r\n')
WHERE `settings` NOT LIKE '%contract_var%' AND `method` LIKE 'plugin := byfar%method := %addIncomingHandover%';

UPDATE `automations`
SET `settings` = CONCAT(`settings`, '\r\ncon_type_order_handover := 1'),
    `name` = REPLACE(`name`, '/B2C', '')
WHERE `settings` NOT LIKE '%con_type_order_handover%' AND `method` LIKE 'plugin := byfar%method := %addOutgoingHandover%';

# Added automation for validation of Delivery Verification Report before completion
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Validates delivery before it is verified (closed)', 0, NULL, 1, 'documents', NULL, 'before_action', '4', 'warehouse_var := warehouse\r\ncontract_var := article_alternative_deliverer\r\ndoc_type_request := 3\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[prev_b_status]\' != \'closed\' && $registry->get(\'request\')->get(\'status\') == \'closed\'', 'plugin := byfar\r\nmethod := validateDeliveryVerification', 'cancel_action_on_fail := 1', 1, 0, 1);

# Added automation to update undelivered quantity of Production Requests when Handover for Expense Invoice is annulled
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Updates undelivered quantity of Production Requests when Handover is annulled', 0, NULL, 1, 'finance', 'warehouses_documents', 'action', '7', 'doc_type_request := 3\nrequest_substatus_supplied := 2\ncontract_var := article_alternative_deliverer\nundelivered_quantity_var := article_volume\n', 'condition := \'[action]\' == \'annul\' && \'[b_customer]\' > 0', 'plugin := byfar\r\nmethod := updateProductionRequestUndeliveredQuantity', NULL, 1, 1, 1);

########################################################################
# 2019-08-28 - Added automation for creation of GT2 table in Delivery Verification Report document from its GT data
#            - Added relations between Packing Label documents and articles in them
#            - Updated settings of 'packing_label_name' of Delivery verification report
#            - Updated settings of 'article_second_code' in GT2 of Delivery verification report to hold id of Packing Lable document
#            - Updated settings of 'byfar_request_management' report

# Added automation for creation of GT2 table in Delivery Verification Report document from its GT data
UPDATE `automations`
SET `position` = 3
WHERE `module` = 'documents' AND `automation_type` = 'action' AND `start_model_type` = 4 AND `method` LIKE '%addIncomingHandover%' AND `position` = 2;

UPDATE `automations`
SET `position` = 4
WHERE `module` = 'documents' AND `automation_type` = 'action' AND `start_model_type` = 4 AND `method` LIKE '%addCommoditiesReservation%' AND `position` = 3;

UPDATE `automations`
SET `position` = 3
WHERE `module` = 'documents' AND `automation_type` = 'before_action' AND `start_model_type` = 4 AND `method` LIKE '%validateDeliveryVerification%' AND `position` = 1;

SELECT @aid := `id` FROM `automations` WHERE `method` = 'plugin := byfar\r\nmethod := createDeliveryGt2';

INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(@aid, 'Creates GT2 table of Delivery Verification Report from data in its GT and indirectly related Production Requests', 0, NULL, 1, 'documents', NULL, 'before_action', '4', 'doc_type_request := 3\r\ndoc_type_packing_label := 10\r\n\r\nrequest_substatus_supplied := 2\r\n\r\ndelivery_packing_label_id_var := packing_label_id\r\n\r\nproducer_currency_var := delivery_currency\r\n\r\ncontract_var := article_alternative_deliverer\r\ncustomer_var := article_deliverer\r\ndelivery_date_var := free_field3\r\nsize_var =: free_field4\r\nundelivered_quantity_var := article_volume\r\nparcel_var := article_second_code\r\n\r\n# articles and quantities that exceed Production Requests\r\n# are saved as delivered for this customer and no contract\r\nsurplus_customer := 768\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[prev_b_status]\' != \'locked\' && $registry->get(\'request\')->get(\'status\') == \'locked\'', 'plugin := byfar\r\nmethod := createDeliveryGt2', 'cancel_action_on_fail := 1', 2, 0, 1);

# Added relations between Packing Label documents and articles in them
UPDATE `_fields_meta`
SET `source` = CONCAT(`source`, '\r\nautocomplete_cstm_model := Nomenclature\r\n')
WHERE `model` = 'Document' AND `model_type` = 10 AND `name` = 'article_name' AND `source` LIKE '%autocomplete := autocompleters%' AND `source` NOT LIKE '%autocomplete_cstm_model%';

INSERT IGNORE INTO `cstm_relatives` (`model`, `model_id`, `cstm_model`, `cstm_model_id`, `var_id`, `num`, `lang`)
SELECT gt2.`model`, gt2.`model_id`, 'Nomenclature', gt2.`article_id`, fm.`id`, gt2.`id`, ''
FROM `documents` d
JOIN `gt2_details` gt2
  ON gt2.`model` = 'Document' AND gt2.`model_id` = d.`id` AND d.`type` = 10 AND gt2.`article_id` > 0
JOIN `_fields_meta` fm
  ON fm.`model` = gt2.`model` AND fm.`model_type` = d.`type` AND fm.`name` = 'article_id';

# Updated settings of 'packing_label_name' of Delivery verification report
UPDATE `_fields_meta`
SET `source` = REPLACE(`source`, 'autocomplete_search := <customer_name>', 'autocomplete_search := <a__for_custom_name>'),
    `width` = '300'
WHERE `model` = 'Document' AND `model_type` = 4 AND `name` = 'packing_label_name' AND `source` LIKE '%autocomplete_search := <customer_name>%';

# Updated settings of 'article_second_code' in GT2 of Delivery verification report to hold id of Packing Label document
UPDATE `_fields_meta`
SET `type` = 'dropdown',
    `source` = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nmethod := getCustomDropdown\ntable := DB_TABLE_DOCUMENTS\ntable_i18n := DB_TABLE_DOCUMENTS_I18N\nlabel := t.full_num\n\nwhere := type IN (10) AND id IN (SELECT value FROM documents_cstm WHERE model_id = \'$model_id\' AND var_id = \'3305\')\norder by := t.id ASC\n\nview_mode := link\nview_mode_url := index.php?launch=documents&documents=view&view=\n'
WHERE `model` = 'Document'
  AND `model_type` = 4
  AND `name` = 'article_second_code'
  AND `gt2` = 1;

# Updated settings of 'byfar_request_management' report
UPDATE `reports`
SET `settings` = CONCAT(`settings`, '\r\n# articles and quantities that exceed Production Requests\r\n# are saved as delivered for this customer and no contract\r\nsurplus_customer := 768\r\n')
WHERE `type` = 'byfar_request_management' AND `settings` NOT LIKE '%surplus_customer%';

########################################################################
# 2019-08-30 - Added placeholder for generating IFI file

# Added placeholder for generating IFI file
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'leather_main', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Процент кожа', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Percent leather', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'rubber_main', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Процентгума/полиестер', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Percent rubber/plastic', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'textile_main', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Процент текстил', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Percent textile', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'leather_sole', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Кожена подметка', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Leather sole', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'rubber_sole', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Гумена подметка', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Rubber sole', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'other_sole', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подметка от друг материал', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Sole of other material', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'sf_y', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Повече от 50% кожа (да)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'More than 50% leather (yes)', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'sf_n', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Повече от 50% кожа (не)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'More than 50% leather (no)', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'up_mat_leath', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част от кожа', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper part from leather', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'up_mat_tex', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част от текстил', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper part from textile', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'up_mat_none', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част от друг материал', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper part from other material', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'liner_common_name', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подплата име', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Liner common name', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'liner_science_name', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подплата научно име', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Liner science name', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'liner_type', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подплата тип', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Liner type', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'liner_country', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подплата произход', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Liner country of origin', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'liner_source', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подплата източник', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Liner source', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'upper_common_name', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част име', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper common name', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'upper_science_name', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част научно име', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper science name', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'upper_type', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част тип', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper type', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'upper_country', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част произход', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper country of origin', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'upper_source', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Горна част източник', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Upper source', NULL, 'en');

########################################################################
# 2019-09-02 - Added additional settings for generating IFI file automation

# Added additional settings for generating IFI file automation
UPDATE `automations` SET `settings`=REPLACE(settings, '\r\n\r\noption_ankle_height_ankle_or_higher :=', '\r\nbasic_material := basic_material_id\r\nsecondary_material := second_material_id\r\nsole_material := hs_sole_material\r\nleather_surface := hs_question_nineteen\r\nshoe_liner := hs_liner_leather_lining\r\n\r\noption_ankle_height_ankle_or_higher :=') WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%secondary_material%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nleather_type := 17\r\nrubber_plastic_type := 44\r\nmaterial_textile := 18\r\n\r\nleather_surface_yes := 1\r\nleather_surface_no := 2\r\n\r\nshoe_liner_y := 1\r\n\r\nsole_material_leather := 1\r\nsole_material_rubber_plastic := 2\r\nsole_material_other := 3') WHERE method like '%createShoesFile%' AND settings NOT LIKE '%leather_type%';

########################################################################
# 2019-09-03 - Added pattern plugin for invoices

# Added pattern plugin for labels
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(89, 'Finance_Incomes_Reason', 1, 'byfar', 'prepareFinance', '', '', now(), now());

INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(89, 'Подготовка за печат на финансови документи', '', 'bg', NOW()),
(89, 'Finance document preparation', '', 'en', NOW());

# Set plugin to the Label print pattern
UPDATE patterns SET plugin=89 WHERE id IN (14, 15, 21, 22);

# Add dummy patterns for the settings of the different types of articles
INSERT IGNORE INTO `patterns` (`id`, `model`, `model_type`, `section`, `list`, `for_printform`, `company`, `position`, `header`, `footer`, `background_image`, `background_image_position`, `landscape`, `prefix`, `format`, `force_generate`, `not_regenerate_finished_record`, `plugin`, `handover_direction`, `is_portal`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
(24, 'Finance_Incomes_Reason', '1', 0, 0, 1, 0, 10, 0, 0, NULL, 'left_top', 0, '[full_num]_[added]_[customer_name]', 'pdf', 0, 0, NULL, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(25, 'Finance_Incomes_Reason', '1', 0, 0, 1, 0, 11, 0, 0, NULL, 'left_top', 0, '[full_num]_[added]_[customer_name]', 'pdf', 0, 0, NULL, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(26, 'Finance_Incomes_Reason', '1', 0, 0, 1, 0, 12, 0, 0, NULL, 'left_top', 0, '[full_num]_[added]_[customer_name]', 'pdf', 0, 0, NULL, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(27, 'Finance_Incomes_Reason', '1', 0, 0, 1, 0, 13, 0, 0, NULL, 'left_top', 0, '[full_num]_[added]_[customer_name]', 'pdf', 0, 0, NULL, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
INSERT IGNORE INTO `patterns_i18n` (`parent_id`, `name`, `description`, `content`, `lang`, `translated`) VALUES
(24, 'Advance invoice (print settings)', '', ' ', 'en', NOW()),
(24, 'Авансова фактура (настройки за печат)', '', ' ', 'bg', NOW()),
(25, 'Invoice Shoes (print settings)', '', ' ', 'en', NOW()),
(25, 'Фактура обувки (настройки за печат)', '', ' ', 'bg', NOW()),
(26, 'Invoice Bags (print settings)', '', ' ', 'en', NOW()),
(26, 'Фактура чанти (настройки за печат)', '', ' ', 'bg', NOW()),
(27, 'Invoice Fragrances (print settings)', '', ' ', 'en', NOW()),
(27, 'Фактура парфюми (настройки за печат)', '', ' ', 'bg', NOW());

# Set example content of the Invoice pattern
UPDATE `patterns_i18n`
SET content='<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n	<tbody>\r\n		<tr>\r\n			<td style="vertical-align:top; width:2%">&nbsp;</td>\r\n			<td style="width:98%"><img alt="" src="http://byfar.n-zoom.com/resources/uploads/image/logo_2.png" style="float:left; height:77px; width:250px" /></td>\r\n		</tr>\r\n		<tr>\r\n			<td style="width:2%">&nbsp;</td>\r\n			<td style="width:98%">&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>\r\n			<table border="1" cellpadding="0" cellspacing="0" style="width:932px">\r\n				<tbody>\r\n					<tr>\r\n						<td colspan="4" style="height:20px; width:475px">Invoice TO</td>\r\n						<td colspan="4" style="width:463px">Order Details</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">Bill TO</td>\r\n						<td colspan="2" style="width:241px">Ship To</td>\r\n						<td colspan="4" style="width:463px">Date:&nbsp;[draft_order_date|date_format:%d.%m.%Y]</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" rowspan="2" style="height:40px; vertical-align:top; width:233px">​[en_customer_company_name]​</td>\r\n						<td colspan="2" rowspan="2" style="vertical-align:top; width:241px">[draft_order_shipment_office_name]</td>\r\n						<td colspan="4" style="width:463px">Order submission deadline:&nbsp;[draft_order_deadline|date_format:%d.%m.%Y]</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="4" style="height:20px; width:463px">Deadline by Customer:[draft_order_shipping_date|date_format:%d.%m.%Y]</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">Country:&nbsp;​[en_customer_country]​</td>\r\n						<td colspan="2" rowspan="5" style="vertical-align:top; width:241px">[draft_order_shipping_address]</td>\r\n						<td colspan="4" style="width:463px">Sales Terms:&nbsp;[draft_order_sales_terms]</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">Address:&nbsp;[en_customer_registration_address]</td>\r\n						<td colspan="4" style="vertical-align:top; width:463px">Shipping terms:&nbsp;[draft_order_delivery_terms]</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">Representative:&nbsp;[en_customer_mol]</td>\r\n						<td colspan="4" style="width:463px">Delivery Period:&nbsp;</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">VAT: [customer_in_dds]</td>\r\n						<td colspan="4" style="width:463px">From date:&nbsp;[contract_date_start|date_format:%d.%m.%Y]</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">&nbsp;</td>\r\n						<td colspan="4" style="width:463px">To date: [contract_date_validity|date_format:%d.%m.%Y]&nbsp;</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">Email:&nbsp;[customer_email]</td>\r\n						<td colspan="2" style="width:241px">Email:&nbsp;[draft_order_use_email]</td>\r\n						<td colspan="4" style="width:463px">Note:&nbsp;​[draft_order_notes]​</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">Phone:&nbsp;[customer_phone]</td>\r\n						<td colspan="2" style="width:241px">Phone:&nbsp;[draft_order_number_phone]</td>\r\n						<td colspan="4" style="width:463px">Notify:&nbsp;​[draft_order_description]​</td>\r\n					</tr>\r\n					<tr>\r\n						<td colspan="2" style="height:20px; width:233px">Mobile:&nbsp;[customer_gsm]</td>\r\n						<td colspan="2" style="width:241px">Mobile:&nbsp;[draft_order_gsm_number]</td>\r\n						<td colspan="4" style="width:463px">&nbsp;</td>\r\n					</tr>\r\n				</tbody>\r\n			</table>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td style="text-align:center">&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>[en_grouping_table_2]</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n			<td>[en_totals]</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n'
WHERE parent_id=14 AND lang='en';

# Plugin placeholders
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_date', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Date', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Date', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_shipment_office_name', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Shipment Office', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Shipment Office', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_deadline', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Deadline', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Deadline', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_shipping_date', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Shipping Date', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Shipping Date', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_shipping_address', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Shipping Address', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Shipping Address', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_sales_terms', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Sales Terms', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Sales Terms', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_delivery_terms', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Delivery Terms', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Delivery Terms', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_use_email', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Email', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Email', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_number_phone', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Phone', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Phone', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_gsm_number', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Mobile', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Mobile', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_notes', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Notes', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Notes', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'draft_order_description', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Draft Order Description', NULL, 'bg'),
(LAST_INSERT_ID(), 'Draft Order Description', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_date_start', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Contract Start Date', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract Start Date', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_date_validity', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Contract Validity Date', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract Validity Date', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'totals', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Totals table', NULL, 'bg'),
(LAST_INSERT_ID(), 'Totals table', NULL, 'en');

# Print settings for GT2
INSERT IGNORE INTO `_fields_template_settings` VALUES
('Pattern', 24, 100064, 0, 'left', '1000px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 0, ''),
('Pattern', 24, 100065, 0, 'left', '400px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 2, ''),
('Pattern', 24, 100066, 0, 'right', '30px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 4, ''),
('Pattern', 24, 100067, 0, 'center', '30px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 5, ''),
('Pattern', 24, 100068, 1, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 6, ''),
('Pattern', 24, 100069, 1, 'right', '40px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 7, ''),
('Pattern', 24, 100070, 0, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 8, ''),
('Pattern', 24, 100071, 1, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 9, ''),
('Pattern', 24, 100072, 0, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 10, ''),
('Pattern', 24, 100073, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 11, ''),
('Pattern', 24, 100074, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 12, ''),
('Pattern', 24, 100075, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 13, ''),
('Pattern', 24, 100076, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 14, ''),
('Pattern', 24, 100077, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 15, ''),
('Pattern', 24, 100078, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 16, ''),
('Pattern', 24, 100079, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 44, ''),
('Pattern', 24, 100080, 1, 'left', '140px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 45, ''),
('Pattern', 24, 100083, 1, 'left', '90px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 46, ''),
('Pattern', 24, 100084, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 47, ''),
('Pattern', 24, 100085, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 48, ''),
('Pattern', 24, 100086, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 49, ''),
('Pattern', 24, 100087, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 50, ''),
('Pattern', 24, 100088, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 51, ''),
('Pattern', 24, 100089, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 52, ''),
('Pattern', 24, 100090, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 53, ''),
('Pattern', 24, 100091, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 54, ''),
('Pattern', 24, 100092, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 55, ''),
('Pattern', 24, 100093, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 56, ''),
('Pattern', 24, 100094, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 57, ''),
('Pattern', 24, 100095, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 58, ''),
('Pattern', 24, 100096, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 17, ''),
('Pattern', 24, 100097, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 18, ''),
('Pattern', 24, 100098, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 19, ''),
('Pattern', 24, 100099, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 20, ''),
('Pattern', 24, 100100, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 21, ''),
('Pattern', 24, 100101, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 22, ''),
('Pattern', 24, 100102, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 23, ''),
('Pattern', 24, 100103, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 24, ''),
('Pattern', 24, 100104, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 25, ''),
('Pattern', 24, 100105, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 26, ''),
('Pattern', 24, 100106, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 27, ''),
('Pattern', 24, 100107, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 28, ''),
('Pattern', 24, 100108, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 29, ''),
('Pattern', 24, 100109, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 30, ''),
('Pattern', 24, 100110, 1, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 31, ''),
('Pattern', 24, 100111, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 32, ''),
('Pattern', 24, 100112, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 33, ''),
('Pattern', 24, 100113, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 34, ''),
('Pattern', 24, 100114, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 3, ''),
('Pattern', 24, 100115, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 35, ''),
('Pattern', 24, 100116, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 36, ''),
('Pattern', 24, 100117, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 37, ''),
('Pattern', 24, 100118, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 38, ''),
('Pattern', 24, 100119, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 39, ''),
('Pattern', 24, 100120, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 40, ''),
('Pattern', 24, 100121, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 41, ''),
('Pattern', 24, 100122, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 42, ''),
('Pattern', 24, 100123, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 43, ''),
('Pattern', 24, 100124, 1, 'left', '100px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 59, ''),
('Pattern', 24, 100125, 1, 'left', '100px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 60, ''),
('Pattern', 24, 102111, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 1, ''),
('Pattern', 25, 100064, 0, 'left', '900px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 0, ''),
('Pattern', 25, 100065, 0, 'left', '500px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 7, ''),
('Pattern', 25, 100066, 0, 'right', '30px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 10, ''),
('Pattern', 25, 100067, 1, 'left', '30px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 15, ''),
('Pattern', 25, 100068, 0, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 11, ''),
('Pattern', 25, 100069, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 16, ''),
('Pattern', 25, 100070, 1, 'right', '40px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 17, ''),
('Pattern', 25, 100071, 1, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 18, ''),
('Pattern', 25, 100072, 0, 'right', '40px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 13, ''),
('Pattern', 25, 100073, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 20, ''),
('Pattern', 25, 100074, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 19, ''),
('Pattern', 25, 100075, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 21, ''),
('Pattern', 25, 100076, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 22, ''),
('Pattern', 25, 100077, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 23, ''),
('Pattern', 25, 100078, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 24, ''),
('Pattern', 25, 100079, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 45, ''),
('Pattern', 25, 100080, 1, 'left', '140px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 46, ''),
('Pattern', 25, 100083, 1, 'left', '90px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 47, ''),
('Pattern', 25, 100084, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 48, ''),
('Pattern', 25, 100085, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 49, ''),
('Pattern', 25, 100086, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 50, ''),
('Pattern', 25, 100087, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 51, ''),
('Pattern', 25, 100088, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 52, ''),
('Pattern', 25, 100089, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 53, ''),
('Pattern', 25, 100090, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 54, ''),
('Pattern', 25, 100091, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 55, ''),
('Pattern', 25, 100092, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 56, ''),
('Pattern', 25, 100093, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 57, ''),
('Pattern', 25, 100094, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 58, ''),
('Pattern', 25, 100095, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 6, ''),
('Pattern', 25, 100096, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 25, ''),
('Pattern', 25, 100097, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 26, ''),
('Pattern', 25, 100098, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 27, ''),
('Pattern', 25, 100099, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 28, ''),
('Pattern', 25, 100100, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 29, ''),
('Pattern', 25, 100101, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 30, ''),
('Pattern', 25, 100102, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 31, ''),
('Pattern', 25, 100103, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 32, ''),
('Pattern', 25, 100104, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 33, ''),
('Pattern', 25, 100105, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 34, ''),
('Pattern', 25, 100106, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 35, ''),
('Pattern', 25, 100107, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 36, ''),
('Pattern', 25, 100108, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 12, ''),
('Pattern', 25, 100109, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 37, ''),
('Pattern', 25, 100110, 1, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 38, ''),
('Pattern', 25, 100111, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 39, ''),
('Pattern', 25, 100112, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 40, ''),
('Pattern', 25, 100113, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 41, ''),
('Pattern', 25, 100114, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 1, ''),
('Pattern', 25, 100115, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 4, ''),
('Pattern', 25, 100116, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 5, ''),
('Pattern', 25, 100117, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 2, ''),
('Pattern', 25, 100118, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 3, ''),
('Pattern', 25, 100119, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 42, ''),
('Pattern', 25, 100120, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 8, ''),
('Pattern', 25, 100121, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 43, ''),
('Pattern', 25, 100122, 0, 'left', '15px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 9, ''),
('Pattern', 25, 100123, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 44, ''),
('Pattern', 25, 100124, 1, 'left', '100px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 59, ''),
('Pattern', 25, 100125, 1, 'left', '100px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 60, ''),
('Pattern', 25, 102111, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 14, ''),
('Pattern', 26, 100064, 0, 'left', '900px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 0, ''),
('Pattern', 26, 100065, 0, 'left', '500px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 8, ''),
('Pattern', 26, 100066, 0, 'right', '30px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 11, ''),
('Pattern', 26, 100067, 1, 'left', '30px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 16, ''),
('Pattern', 26, 100068, 0, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 12, ''),
('Pattern', 26, 100069, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 17, ''),
('Pattern', 26, 100070, 1, 'right', '40px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 18, ''),
('Pattern', 26, 100071, 1, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 19, ''),
('Pattern', 26, 100072, 0, 'right', '40px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 14, ''),
('Pattern', 26, 100073, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 20, ''),
('Pattern', 26, 100074, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 21, ''),
('Pattern', 26, 100075, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 22, ''),
('Pattern', 26, 100076, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 23, ''),
('Pattern', 26, 100077, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 24, ''),
('Pattern', 26, 100078, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 25, ''),
('Pattern', 26, 100079, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 46, ''),
('Pattern', 26, 100080, 1, 'left', '140px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 47, ''),
('Pattern', 26, 100083, 0, 'left', '90px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 7, ''),
('Pattern', 26, 100084, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 48, ''),
('Pattern', 26, 100085, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 49, ''),
('Pattern', 26, 100086, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 50, ''),
('Pattern', 26, 100087, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 51, ''),
('Pattern', 26, 100088, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 52, ''),
('Pattern', 26, 100089, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 53, ''),
('Pattern', 26, 100090, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 54, ''),
('Pattern', 26, 100091, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 55, ''),
('Pattern', 26, 100092, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 56, ''),
('Pattern', 26, 100093, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 57, ''),
('Pattern', 26, 100094, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 58, ''),
('Pattern', 26, 100095, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 6, ''),
('Pattern', 26, 100096, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 26, ''),
('Pattern', 26, 100097, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 27, ''),
('Pattern', 26, 100098, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 28, ''),
('Pattern', 26, 100099, 1, 'right', '50px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 29, ''),
('Pattern', 26, 100100, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 30, ''),
('Pattern', 26, 100101, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 31, ''),
('Pattern', 26, 100102, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 32, ''),
('Pattern', 26, 100103, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 33, ''),
('Pattern', 26, 100104, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 34, ''),
('Pattern', 26, 100105, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 35, ''),
('Pattern', 26, 100106, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 36, ''),
('Pattern', 26, 100107, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 37, ''),
('Pattern', 26, 100108, 0, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 13, ''),
('Pattern', 26, 100109, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 38, ''),
('Pattern', 26, 100110, 1, 'right', '70px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 39, ''),
('Pattern', 26, 100111, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 40, ''),
('Pattern', 26, 100112, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 41, ''),
('Pattern', 26, 100113, 1, 'right', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 42, ''),
('Pattern', 26, 100114, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 3, ''),
('Pattern', 26, 100115, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 2, ''),
('Pattern', 26, 100116, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 5, ''),
('Pattern', 26, 100117, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 4, ''),
('Pattern', 26, 100118, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 1, ''),
('Pattern', 26, 100119, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 43, ''),
('Pattern', 26, 100120, 0, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 9, ''),
('Pattern', 26, 100121, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 44, ''),
('Pattern', 26, 100122, 0, 'left', '15px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 10, ''),
('Pattern', 26, 100123, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 45, ''),
('Pattern', 26, 100124, 1, 'left', '100px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 59, ''),
('Pattern', 26, 100125, 1, 'left', '100px', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 60, ''),
('Pattern', 26, 102111, 1, 'left', '', 'Arial,sans-serif', '', '#000000', 'normal', 'normal', 15, '');

INSERT IGNORE INTO `_fields_template_settings_i18n` VALUES 
('Pattern', 24, 100065, 'Артикул', 'bg'),
('Pattern', 24, 100065, 'Item', 'en'),
('Pattern', 24, 100066, 'К-во', 'bg'),
('Pattern', 24, 100066, 'Quantity', 'en'),
('Pattern', 24, 100067, 'Мярка', 'bg'),
('Pattern', 24, 100067, 'Measure', 'en'),
('Pattern', 24, 100068, 'Ед. цена(без ДДС)', 'bg'),
('Pattern', 24, 100068, 'Unit price(w/o VAT)', 'en'),
('Pattern', 24, 100069, 'T.O.(%)', 'bg'),
('Pattern', 24, 100069, 'Discount(%)', 'en'),
('Pattern', 24, 100070, 'Ед. цена(след Т.О., без ДДС)', 'bg'),
('Pattern', 24, 100070, 'Unit price', 'en'),
('Pattern', 24, 100071, 'Стойност', 'bg'),
('Pattern', 24, 100071, 'Subtotal', 'en'),
('Pattern', 24, 100072, 'Стойност(след Т.О.)', 'bg'),
('Pattern', 24, 100072, 'Subtotal', 'en'),
('Pattern', 24, 100073, 'Общо', 'bg'),
('Pattern', 24, 100073, 'Total', 'en'),
('Pattern', 24, 100074, 'ДДС ставка', 'bg'),
('Pattern', 24, 100074, 'VAT rate', 'en'),
('Pattern', 24, 100075, 'Размер ДДС', 'bg'),
('Pattern', 24, 100075, 'VAT Total', 'en'),
('Pattern', 24, 100076, 'Общо с ДДС', 'bg'),
('Pattern', 24, 100076, 'Total with VAT', 'en'),
('Pattern', 24, 100077, 'Основание за неначисляване на ДДС', 'bg'),
('Pattern', 24, 100077, 'No VAT reason', 'en'),
('Pattern', 24, 100078, 'Валута', 'bg'),
('Pattern', 24, 100078, 'Currency', 'en'),
('Pattern', 24, 100079, 'Доставчик(ID)', 'bg'),
('Pattern', 24, 100079, 'Deliverer(ID)', 'en'),
('Pattern', 24, 100080, 'Доставчик', 'bg'),
('Pattern', 24, 100080, 'Deliverer', 'en'),
('Pattern', 24, 100083, 'Код на артикул', 'bg'),
('Pattern', 24, 100083, 'Article code', 'en'),
('Pattern', 24, 100084, 'Артикул(ID)', 'bg'),
('Pattern', 24, 100084, 'Article(ID)', 'en'),
('Pattern', 24, 100085, 'Надценка(%)', 'bg'),
('Pattern', 24, 100085, 'Surplus charge(%)', 'en'),
('Pattern', 24, 100086, 'Общо без отстъпка / надценка', 'bg'),
('Pattern', 24, 100086, 'Total without discount / mark-up', 'en'),
('Pattern', 24, 100087, 'Обща Т.О. (%)', 'bg'),
('Pattern', 24, 100087, 'Total Discount (%)', 'en'),
('Pattern', 24, 100088, 'Обща Т.О.', 'bg'),
('Pattern', 24, 100088, 'Total Discount', 'en'),
('Pattern', 24, 100089, 'Обща надценка (%)', 'bg'),
('Pattern', 24, 100089, 'Total Mark-up (%)', 'en'),
('Pattern', 24, 100090, 'Обща надценка', 'bg'),
('Pattern', 24, 100090, 'Total Mark-up', 'en'),
('Pattern', 24, 100091, 'Основание за неначисляване на ДДС (ID)', 'bg'),
('Pattern', 24, 100091, 'No VAT reason (ID)', 'en'),
('Pattern', 24, 100092, 'Търговска марка', 'bg'),
('Pattern', 24, 100092, 'Trademark', 'en'),
('Pattern', 24, 100093, 'Алтернативен доставчик(ID)', 'bg'),
('Pattern', 24, 100093, 'Alternative deliverer(ID)', 'en'),
('Pattern', 24, 100094, 'Алтернативен доставчик', 'bg'),
('Pattern', 24, 100094, 'Alternative deliverer', 'en'),
('Pattern', 24, 100095, 'Код от доставчик', 'bg'),
('Pattern', 24, 100095, 'Delivery code', 'en'),
('Pattern', 24, 100096, 'Допълнитен код', 'bg'),
('Pattern', 24, 100096, 'Second code', 'en'),
('Pattern', 24, 100097, 'Описание на артикул', 'bg'),
('Pattern', 24, 100097, 'Article description', 'en'),
('Pattern', 24, 100098, 'Т.О.(Стойност)', 'bg'),
('Pattern', 24, 100098, 'Discount value', 'en'),
('Pattern', 24, 100099, 'Надценка(Стойност)', 'bg'),
('Pattern', 24, 100099, 'Surplus charge', 'en'),
('Pattern', 24, 100100, 'Т.О.(обща ст-ст)', 'bg'),
('Pattern', 24, 100100, 'Subtotal discount', 'en'),
('Pattern', 24, 100101, 'ДДС (за артикула)', 'bg'),
('Pattern', 24, 100101, 'VAT for the article', 'en'),
('Pattern', 24, 100102, 'Стойност(с ДДС)', 'bg'),
('Pattern', 24, 100102, 'Subtotal width VAT', 'en'),
('Pattern', 24, 100103, 'Стойност(след Т.О. с ДДС)', 'bg'),
('Pattern', 24, 100103, 'Subtotal width discount with VAT', 'en'),
('Pattern', 24, 100104, 'Налично', 'bg'),
('Pattern', 24, 100104, 'Available quantity', 'en'),
('Pattern', 24, 100105, 'Баркод', 'bg'),
('Pattern', 24, 100105, 'Barcode', 'en'),
('Pattern', 24, 100106, 'Височина', 'bg'),
('Pattern', 24, 100106, 'Height', 'en'),
('Pattern', 24, 100107, 'Широчина', 'bg'),
('Pattern', 24, 100107, 'Width', 'en'),
('Pattern', 24, 100108, 'Тегло', 'bg'),
('Pattern', 24, 100108, 'Weight', 'en'),
('Pattern', 24, 100109, 'Обем', 'bg'),
('Pattern', 24, 100109, 'Volume', 'en'),
('Pattern', 24, 100110, 'Последна доставна цена', 'bg'),
('Pattern', 24, 100110, 'Last delivery price', 'en'),
('Pattern', 24, 100111, 'Печалба без Т.О.', 'bg'),
('Pattern', 24, 100111, 'Profit w/o discount', 'en'),
('Pattern', 24, 100112, 'Общо печалба без Т.О.', 'bg'),
('Pattern', 24, 100112, 'Subtotal profit w/o discount', 'en'),
('Pattern', 24, 100113, 'Средно претеглена покупна цена', 'bg'),
('Pattern', 24, 100113, 'Average calculated price', 'en'),
('Pattern', 24, 100114, 'Свободно текстово поле 1', 'bg'),
('Pattern', 24, 100114, 'Free text field 1', 'en'),
('Pattern', 24, 100115, 'Свободно текстово поле 2', 'bg'),
('Pattern', 24, 100115, 'Free text field 2', 'en'),
('Pattern', 24, 100116, 'Свободно текстово поле 3', 'bg'),
('Pattern', 24, 100116, 'Free text field 3', 'en'),
('Pattern', 24, 100117, 'Свободно текстово поле 4', 'bg'),
('Pattern', 24, 100117, 'Free text field 4', 'en'),
('Pattern', 24, 100118, 'Свободно текстово поле 5', 'bg'),
('Pattern', 24, 100118, 'Free text field 5', 'en'),
('Pattern', 24, 100119, 'Свободно поле 1', 'bg'),
('Pattern', 24, 100119, 'Free field 1', 'en'),
('Pattern', 24, 100120, 'Свободно поле 2', 'bg'),
('Pattern', 24, 100120, 'Free field 2', 'en'),
('Pattern', 24, 100121, 'Свободно поле 3', 'bg'),
('Pattern', 24, 100121, 'Free field 3', 'en'),
('Pattern', 24, 100122, 'Свободно поле 4', 'bg'),
('Pattern', 24, 100122, 'Free field 4', 'en'),
('Pattern', 24, 100123, 'Свободно поле 5', 'bg'),
('Pattern', 24, 100123, 'Free field 5', 'en'),
('Pattern', 24, 100124, 'Поле за отстъпка/надценка', 'bg'),
('Pattern', 24, 100124, 'Discount/surplus field', 'en'),
('Pattern', 24, 100125, 'Поле за обща отстъпка/надценка', 'bg'),
('Pattern', 24, 100125, 'Total discount/surplus field', 'en'),
('Pattern', 24, 102111, 'Тип фактуриране', 'bg'),
('Pattern', 24, 102111, 'Invoicing type', 'en'),
('Pattern', 25, 100065, 'Name', 'bg'),
('Pattern', 25, 100065, 'Name', 'en'),
('Pattern', 25, 100066, 'К-во', 'bg'),
('Pattern', 25, 100066, 'Total Units', 'en'),
('Pattern', 25, 100067, 'Мярка', 'bg'),
('Pattern', 25, 100067, 'Measure', 'en'),
('Pattern', 25, 100068, 'Wholesale Price', 'bg'),
('Pattern', 25, 100068, 'Wholesale Price', 'en'),
('Pattern', 25, 100069, 'T.O.(%)', 'bg'),
('Pattern', 25, 100069, 'Discount(%)', 'en'),
('Pattern', 25, 100070, 'Ед. цена(след Т.О., без ДДС)', 'bg'),
('Pattern', 25, 100070, 'Unit price', 'en'),
('Pattern', 25, 100071, 'Стойност', 'bg'),
('Pattern', 25, 100071, 'Subtotal', 'en'),
('Pattern', 25, 100072, 'Стойност(след Т.О.)', 'bg'),
('Pattern', 25, 100072, 'Subtotal', 'en'),
('Pattern', 25, 100073, 'Общо', 'bg'),
('Pattern', 25, 100073, 'Due amount', 'en'),
('Pattern', 25, 100074, 'ДДС ставка', 'bg'),
('Pattern', 25, 100074, 'VAT rate', 'en'),
('Pattern', 25, 100075, 'Размер ДДС', 'bg'),
('Pattern', 25, 100075, 'VAT Total', 'en'),
('Pattern', 25, 100076, 'Общо с ДДС', 'bg'),
('Pattern', 25, 100076, 'Due amount with VAT', 'en'),
('Pattern', 25, 100077, 'Основание за неначисляване на ДДС', 'bg'),
('Pattern', 25, 100077, 'No VAT reason', 'en'),
('Pattern', 25, 100078, 'Валута', 'bg'),
('Pattern', 25, 100078, 'Currency', 'en'),
('Pattern', 25, 100079, 'Доставчик(ID)', 'bg'),
('Pattern', 25, 100079, 'Deliverer(ID)', 'en'),
('Pattern', 25, 100080, 'Доставчик', 'bg'),
('Pattern', 25, 100080, 'Deliverer', 'en'),
('Pattern', 25, 100083, 'Код на артикул', 'bg'),
('Pattern', 25, 100083, 'Article code', 'en'),
('Pattern', 25, 100084, 'Артикул(ID)', 'bg'),
('Pattern', 25, 100084, 'Article(ID)', 'en'),
('Pattern', 25, 100085, 'Надценка(%)', 'bg'),
('Pattern', 25, 100085, 'Surplus charge(%)', 'en'),
('Pattern', 25, 100086, 'Общо без отстъпка / надценка', 'bg'),
('Pattern', 25, 100086, 'Total without discount / mark-up', 'en'),
('Pattern', 25, 100087, 'Обща Т.О. (%)', 'bg'),
('Pattern', 25, 100087, 'Total Discount (%)', 'en'),
('Pattern', 25, 100088, 'Обща Т.О.', 'bg'),
('Pattern', 25, 100088, 'Total Discount', 'en'),
('Pattern', 25, 100089, 'Обща надценка (%)', 'bg'),
('Pattern', 25, 100089, 'Total Mark-up (%)', 'en'),
('Pattern', 25, 100090, 'Обща надценка', 'bg'),
('Pattern', 25, 100090, 'Total Mark-up', 'en'),
('Pattern', 25, 100091, 'Основание за неначисляване на ДДС (ID)', 'bg'),
('Pattern', 25, 100091, 'No VAT reason (ID)', 'en'),
('Pattern', 25, 100092, 'Търговска марка', 'bg'),
('Pattern', 25, 100092, 'Trademark', 'en'),
('Pattern', 25, 100093, 'Алтернативен доставчик(ID)', 'bg'),
('Pattern', 25, 100093, 'Alternative deliverer(ID)', 'en'),
('Pattern', 25, 100094, 'Алтернативен доставчик', 'bg'),
('Pattern', 25, 100094, 'Alternative deliverer', 'en'),
('Pattern', 25, 100095, 'SKU code', 'bg'),
('Pattern', 25, 100095, 'SKU code', 'en'),
('Pattern', 25, 100096, 'Допълнитен код', 'bg'),
('Pattern', 25, 100096, 'Second code', 'en'),
('Pattern', 25, 100097, 'Описание на артикул', 'bg'),
('Pattern', 25, 100097, 'Article description', 'en'),
('Pattern', 25, 100098, 'Т.О.(Стойност)', 'bg'),
('Pattern', 25, 100098, 'Discount value', 'en'),
('Pattern', 25, 100099, 'Надценка(Стойност)', 'bg'),
('Pattern', 25, 100099, 'Surplus charge', 'en'),
('Pattern', 25, 100100, 'Т.О.(обща ст-ст)', 'bg'),
('Pattern', 25, 100100, 'Subtotal discount', 'en'),
('Pattern', 25, 100101, 'ДДС (за артикула)', 'bg'),
('Pattern', 25, 100101, 'VAT for the article', 'en'),
('Pattern', 25, 100102, 'Стойност(с ДДС)', 'bg'),
('Pattern', 25, 100102, 'Subtotal width VAT', 'en'),
('Pattern', 25, 100103, 'Стойност(след Т.О. с ДДС)', 'bg'),
('Pattern', 25, 100103, 'Subtotal width discount with VAT', 'en'),
('Pattern', 25, 100104, 'Налично', 'bg'),
('Pattern', 25, 100104, 'Available quantity', 'en'),
('Pattern', 25, 100105, 'Баркод', 'bg'),
('Pattern', 25, 100105, 'Barcode', 'en'),
('Pattern', 25, 100106, 'Височина', 'bg'),
('Pattern', 25, 100106, 'Height', 'en'),
('Pattern', 25, 100107, 'Широчина', 'bg'),
('Pattern', 25, 100107, 'Width', 'en'),
('Pattern', 25, 100108, 'Retail Price', 'bg'),
('Pattern', 25, 100108, 'Retail Price', 'en'),
('Pattern', 25, 100109, 'Обем', 'bg'),
('Pattern', 25, 100109, 'Volume', 'en'),
('Pattern', 25, 100110, 'Последна доставна цена', 'bg'),
('Pattern', 25, 100110, 'Last delivery price', 'en'),
('Pattern', 25, 100111, 'Печалба без Т.О.', 'bg'),
('Pattern', 25, 100111, 'Profit w/o discount', 'en'),
('Pattern', 25, 100112, 'Общо печалба без Т.О.', 'bg'),
('Pattern', 25, 100112, 'Subtotal profit w/o discount', 'en'),
('Pattern', 25, 100113, 'Средно претеглена покупна цена', 'bg'),
('Pattern', 25, 100113, 'Average calculated price', 'en'),
('Pattern', 25, 100114, 'Composition', 'bg'),
('Pattern', 25, 100114, 'Composition', 'en'),
('Pattern', 25, 100115, 'Категория', 'bg'),
('Pattern', 25, 100115, 'Category', 'en'),
('Pattern', 25, 100116, 'HS code', 'bg'),
('Pattern', 25, 100116, 'HS code', 'en'),
('Pattern', 25, 100117, 'Manufacturer', 'bg'),
('Pattern', 25, 100117, 'Manufacturer', 'en'),
('Pattern', 25, 100118, 'Manufacturer', 'bg'),
('Pattern', 25, 100118, 'Manufacturer\'s Address', 'en'),
('Pattern', 25, 100119, 'Свободно поле 1', 'bg'),
('Pattern', 25, 100119, 'Free field 1', 'en'),
('Pattern', 25, 100120, 'Color', 'bg'),
('Pattern', 25, 100120, 'Color', 'en'),
('Pattern', 25, 100121, 'Свободно поле 3', 'bg'),
('Pattern', 25, 100121, 'Free field 3', 'en'),
('Pattern', 25, 100122, 'Size', 'bg'),
('Pattern', 25, 100122, 'Size', 'en'),
('Pattern', 25, 100123, 'Свободно поле 5', 'bg'),
('Pattern', 25, 100123, 'Free field 5', 'en'),
('Pattern', 25, 100124, 'Поле за отстъпка/надценка', 'bg'),
('Pattern', 25, 100124, 'Discount/surplus field', 'en'),
('Pattern', 25, 100125, 'Поле за обща отстъпка/надценка', 'bg'),
('Pattern', 25, 100125, 'Total discount/surplus field', 'en'),
('Pattern', 25, 102111, 'Тип фактуриране', 'bg'),
('Pattern', 25, 102111, 'Invoicing type', 'en'),
('Pattern', 26, 100065, 'Name', 'bg'),
('Pattern', 26, 100065, 'Name', 'en'),
('Pattern', 26, 100066, 'К-во', 'bg'),
('Pattern', 26, 100066, 'Quantity', 'en'),
('Pattern', 26, 100067, 'Мярка', 'bg'),
('Pattern', 26, 100067, 'Measure', 'en'),
('Pattern', 26, 100068, 'Discounted Price', 'bg'),
('Pattern', 26, 100068, 'Discounted Price', 'en'),
('Pattern', 26, 100069, 'T.O.(%)', 'bg'),
('Pattern', 26, 100069, 'Discount(%)', 'en'),
('Pattern', 26, 100070, 'Ед. цена(след Т.О., без ДДС)', 'bg'),
('Pattern', 26, 100070, 'Unit price', 'en'),
('Pattern', 26, 100071, 'Стойност', 'bg'),
('Pattern', 26, 100071, 'Subtotal', 'en'),
('Pattern', 26, 100072, 'Стойност(след Т.О.)', 'bg'),
('Pattern', 26, 100072, 'Subtotal(inc. discount)', 'en'),
('Pattern', 26, 100073, 'Общо', 'bg'),
('Pattern', 26, 100073, 'Due amount', 'en'),
('Pattern', 26, 100074, 'ДДС ставка', 'bg'),
('Pattern', 26, 100074, 'VAT rate', 'en'),
('Pattern', 26, 100075, 'Размер ДДС', 'bg'),
('Pattern', 26, 100075, 'VAT Total', 'en'),
('Pattern', 26, 100076, 'Общо с ДДС', 'bg'),
('Pattern', 26, 100076, 'Due amount with VAT', 'en'),
('Pattern', 26, 100077, 'Основание за неначисляване на ДДС', 'bg'),
('Pattern', 26, 100077, 'No VAT reason', 'en'),
('Pattern', 26, 100078, 'Валута', 'bg'),
('Pattern', 26, 100078, 'Currency', 'en'),
('Pattern', 26, 100079, 'Доставчик(ID)', 'bg'),
('Pattern', 26, 100079, 'Deliverer(ID)', 'en'),
('Pattern', 26, 100080, 'Доставчик', 'bg'),
('Pattern', 26, 100080, 'Deliverer', 'en'),
('Pattern', 26, 100083, 'Код на артикул', 'bg'),
('Pattern', 26, 100083, 'SKU code', 'en'),
('Pattern', 26, 100084, 'Артикул(ID)', 'bg'),
('Pattern', 26, 100084, 'Article(ID)', 'en'),
('Pattern', 26, 100085, 'Надценка(%)', 'bg'),
('Pattern', 26, 100085, 'Surplus charge(%)', 'en'),
('Pattern', 26, 100086, 'Общо без отстъпка / надценка', 'bg'),
('Pattern', 26, 100086, 'Total without discount / mark-up', 'en'),
('Pattern', 26, 100087, 'Обща Т.О. (%)', 'bg'),
('Pattern', 26, 100087, 'Total Discount (%)', 'en'),
('Pattern', 26, 100088, 'Обща Т.О.', 'bg'),
('Pattern', 26, 100088, 'Total Discount', 'en'),
('Pattern', 26, 100089, 'Обща надценка (%)', 'bg'),
('Pattern', 26, 100089, 'Total Mark-up (%)', 'en'),
('Pattern', 26, 100090, 'Обща надценка', 'bg'),
('Pattern', 26, 100090, 'Total Mark-up', 'en'),
('Pattern', 26, 100091, 'Основание за неначисляване на ДДС (ID)', 'bg'),
('Pattern', 26, 100091, 'No VAT reason (ID)', 'en'),
('Pattern', 26, 100092, 'Търговска марка', 'bg'),
('Pattern', 26, 100092, 'Trademark', 'en'),
('Pattern', 26, 100093, 'Алтернативен доставчик(ID)', 'bg'),
('Pattern', 26, 100093, 'Alternative deliverer(ID)', 'en'),
('Pattern', 26, 100094, 'Алтернативен доставчик', 'bg'),
('Pattern', 26, 100094, 'Alternative deliverer', 'en'),
('Pattern', 26, 100095, 'SKU code', 'bg'),
('Pattern', 26, 100095, 'SKU code', 'en'),
('Pattern', 26, 100096, 'Допълнитен код', 'bg'),
('Pattern', 26, 100096, 'Second code', 'en'),
('Pattern', 26, 100097, 'Описание на артикул', 'bg'),
('Pattern', 26, 100097, 'Article description', 'en'),
('Pattern', 26, 100098, 'Т.О.(Стойност)', 'bg'),
('Pattern', 26, 100098, 'Discount value', 'en'),
('Pattern', 26, 100099, 'Надценка(Стойност)', 'bg'),
('Pattern', 26, 100099, 'Surplus charge', 'en'),
('Pattern', 26, 100100, 'Т.О.(обща ст-ст)', 'bg'),
('Pattern', 26, 100100, 'Subtotal discount', 'en'),
('Pattern', 26, 100101, 'ДДС (за артикула)', 'bg'),
('Pattern', 26, 100101, 'VAT for the article', 'en'),
('Pattern', 26, 100102, 'Стойност(с ДДС)', 'bg'),
('Pattern', 26, 100102, 'Subtotal width VAT', 'en'),
('Pattern', 26, 100103, 'Стойност(след Т.О. с ДДС)', 'bg'),
('Pattern', 26, 100103, 'Subtotal width discount with VAT', 'en'),
('Pattern', 26, 100104, 'Налично', 'bg'),
('Pattern', 26, 100104, 'Available quantity', 'en'),
('Pattern', 26, 100105, 'Баркод', 'bg'),
('Pattern', 26, 100105, 'Barcode', 'en'),
('Pattern', 26, 100106, 'Височина', 'bg'),
('Pattern', 26, 100106, 'Height', 'en'),
('Pattern', 26, 100107, 'Широчина', 'bg'),
('Pattern', 26, 100107, 'Width', 'en'),
('Pattern', 26, 100108, 'Retail Price', 'bg'),
('Pattern', 26, 100108, 'Retail Price', 'en'),
('Pattern', 26, 100109, 'Обем', 'bg'),
('Pattern', 26, 100109, 'Volume', 'en'),
('Pattern', 26, 100110, 'Последна доставна цена', 'bg'),
('Pattern', 26, 100110, 'Last delivery price', 'en'),
('Pattern', 26, 100111, 'Печалба без Т.О.', 'bg'),
('Pattern', 26, 100111, 'Profit w/o discount', 'en'),
('Pattern', 26, 100112, 'Общо печалба без Т.О.', 'bg'),
('Pattern', 26, 100112, 'Subtotal profit w/o discount', 'en'),
('Pattern', 26, 100113, 'Средно претеглена покупна цена', 'bg'),
('Pattern', 26, 100113, 'Average calculated price', 'en'),
('Pattern', 26, 100114, 'Composition', 'bg'),
('Pattern', 26, 100114, 'Composition', 'en'),
('Pattern', 26, 100115, 'Категория', 'bg'),
('Pattern', 26, 100115, 'Category', 'en'),
('Pattern', 26, 100116, 'HS code', 'bg'),
('Pattern', 26, 100116, 'HS code', 'en'),
('Pattern', 26, 100117, 'Manufacturer', 'bg'),
('Pattern', 26, 100117, 'Manufacturer', 'en'),
('Pattern', 26, 100118, 'Manufacturer', 'bg'),
('Pattern', 26, 100118, 'Manufacturer\'s Address', 'en'),
('Pattern', 26, 100119, 'Свободно поле 1', 'bg'),
('Pattern', 26, 100119, 'Free field 1', 'en'),
('Pattern', 26, 100120, 'Color', 'bg'),
('Pattern', 26, 100120, 'Color', 'en'),
('Pattern', 26, 100121, 'Свободно поле 3', 'bg'),
('Pattern', 26, 100121, 'Free field 3', 'en'),
('Pattern', 26, 100122, 'Size', 'bg'),
('Pattern', 26, 100122, 'Size', 'en'),
('Pattern', 26, 100123, 'Свободно поле 5', 'bg'),
('Pattern', 26, 100123, 'Free field 5', 'en'),
('Pattern', 26, 100124, 'Поле за отстъпка/надценка', 'bg'),
('Pattern', 26, 100124, 'Discount/surplus field', 'en'),
('Pattern', 26, 100125, 'Поле за обща отстъпка/надценка', 'bg'),
('Pattern', 26, 100125, 'Total discount/surplus field', 'en'),
('Pattern', 26, 102111, 'Тип фактуриране', 'bg'),
('Pattern', 26, 102111, 'Invoicing type', 'en');

########################################################################
# 2019-09-04 - Added automations for nomenclatures sync to Enola

# Added automations for nomenclatures sync to Enola
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'nZoom Shoes to Enola Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '8', '#SANDBOX\r\nenola_api_url := http://**************:9001\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\n#enola_api_url := \r\n#enola_api_token := \r\nsend_to_email := <EMAIL>', 'condition := 1', 'method := nzoom2EnolaNomSync\r\nplugin := byfar', NULL, 99, 0, 1),
(NULL, 'nZoom Bag to Enola Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '32', '#SANDBOX\r\nenola_api_url := http://**************:9001\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\n#enola_api_url := \r\n#enola_api_token := \r\nsend_to_email := <EMAIL>', 'condition := 1', 'method := nzoom2EnolaNomSync\r\nplugin := byfar', NULL, 99, 0, 1);

########################################################################
# 2019-09-05 - Added automations for production requests sync to Enola, added test mode to all Enola sync automations

# Added automation for document production request sync to Enola
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'nZoom Production Request to Enola DeliveryDocument Sync', 0, NULL, 1, 'documents', NULL, 'crontab', '3', '#SANDBOX\r\nenola_api_url := http://**************:9001\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\n#enola_api_url := \r\n#enola_api_token := \r\nsend_to_email := <EMAIL>\r\n\r\ntest_mode := ', 'condition := 1', 'method := nzoom2EnolaProductionRequestSync\r\nplugin := byfar', NULL, 101, 0, 1);

# Added test_mode to nzoom2EnolaNomSync
UPDATE automations
SET settings=CONCAT(settings, '\n\ntest_mode := ')
WHERE method like '%nzoom2EnolaNomSync%' AND settings NOT LIKE '%test_mode%';

# Update position of sync of bags
UPDATE automations SET position=100 WHERE method like '%nzoom2EnolaNomSync%' AND start_model_type=32;

# Insert value for enola_docid for the production request documents with substatus 2
INSERT INTO documents_cstm
  SELECT id AS model_id, (SELECT id FROM _fields_meta WHERE name='enola_docid') AS var_id, 1 AS num, id AS VALUE, NOW(), 1, NOW(), 1, '' FROM documents WHERE STATUS='closed' AND TYPE=3 AND substatus=2
ON DUPLICATE KEY
  UPDATE value=VALUES(`value`);

########################################################################
# 2019-09-03 - Added pattern plugin for invoices, debi/credit notes

# Set plugin to the Label print pattern
UPDATE patterns_plugins SET model_type=0 WHERE method='prepareFinance';

########################################################################
# 2019-09-18 - Updated automations for Fulfillment
#            - Added automation to change template of issued invoices on annex actions
#            - Updated template of issued invoices to current template of contract, where different
#            - Added "Invoiced" substatus for Fulfillment

# Updated automations for Fulfillment
UPDATE `automations`
SET `name` = 'Creates (interim) Invoice from Fulfillment',
    `depend` = 0, 
    `automation_type` = 'before_action',
    `position` = 2,
    `settings` = 'invoice_email_template := 1002\nproforma_invoice_email_template := 1001\ninvoice_pattern := 14\nproforma_invoice_pattern := 15\n\r\ncon_type_order_handover := 1',
    `conditions` = 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'closed_4\' && \'[prev_b_substatus]\' != \'4\' && \'[a_warehouse]\' > 0\r\n',
    `after_action` = 'cancel_action_on_fail := 1'
WHERE `module` = 'documents' AND `start_model_type` = 5 AND`method` LIKE '%method := issueInterimInvoice%';

UPDATE `automations`
SET `name` = 'Creates outgoing Handover from Fulfillment',
    `automation_type` = 'before_action',
    `position` = 3,
    `settings` = 'warehouse_var := warehouse\r\n\r\ncon_type_order_handover := 1\r\nfulfillment_substatus_invoiced := 4',
    `conditions` = 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'closed_1\' && \'[prev_b_substatus]\' != \'1\' && \'[a_warehouse]\' > 0\r\n',
    `after_action` = 'cancel_action_on_fail := 1'
WHERE `module` = 'documents' AND `start_model_type` = 5 AND `method` LIKE '%method := addOutgoingHandover%';

# Added automation to change template of issued invoices on annex actions
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Updates all issued finance from contract to be related to its new invoicing template when agreement enters into force or exits from force', 0, NULL, 1, 'contracts', NULL, 'action', '1', '', 'condition := \'[b_status]\' == \'closed\' && \'[b_subtype]\' == \'annex\' && (\'[action]\' == \'setstatus\' && \'[prev_b_status]\' != \'closed\' && \'[b_date_start_subtype]\' <= date(\'Y-m-d\') || \'[action]\' == \'annulmentsubtype\' && \'[b_subtype_status]\' == \'executed\')\r\n', 'plugin := byfar\r\nmethod := updateInvoiceTemplateRelations', NULL, 4, 0, 1);

# Updated template of issued invoices to current template of contract, where different
UPDATE `fin_invoices_templates_info` fiti
JOIN `fin_invoices_templates` fit_old
  ON fiti.`parent_id` = fit_old.`id` AND fit_old.`recurrent` = 0 AND fit_old.`type` = 15 AND fit_old.`deleted_by` != 0
JOIN `fin_invoices_templates` fit
  ON fit.`contract_id` = fit_old.`contract_id` AND fit.`recurrent` = 0 and fit.`type` = 15 and fit.`deleted_by` = 0
SET fiti.`parent_id` = fit.`id`;

# Added "Invoiced" substatus for Fulfillment
INSERT IGNORE INTO `documents_statuses` (`id`, `doc_type`, `name`, `description`, `status`, `sequence`, `requires_comment`, `active`, `group`, `icon_name`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`, `lang`) VALUES
(4, 5, 'Invoiced', '', 'closed', 1, 'without_comment', 1, 1, NULL, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0, 'en');

INSERT IGNORE INTO `status_settings` (`id`, `model`, `model_type`, `from_status`, `to_status`, `allow`, `users`, `assignments`, `groups`) VALUES
(1, 'Document', 5, '4', '1', 1, '', '', '1'),
(2, 'Document', 5, 'opened', '1', 0, '', '', '1'),
(3, 'Document', 5, 'locked', '1', 0, '', '', '1'),
(4, 'Document', 5, 'closed', '1', 0, '', '', '1');

########################################################################
# 2019-10-02 - Added automation to receive information of Delivery Documents from Enola
#            - Duplicated automation createDeliveryGt2 to be started as action automation
#            - Fixed the conditions of composeBoxNumber automation to not require POST

# Added automation to receive information of Delivery Documents from Enola
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Enola to nZoom DeliveryDocument Sync', 0, NULL, 1, 'documents', NULL, 'crontab', '10', '#SANDBOX\r\nenola_api_url := http://**************:9001\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\n#enola_api_url := \r\n#enola_api_token := \r\nsend_to_email := <EMAIL>\r\n\r\ntest_mode := 1\r\ntest_delivery_request := {"Document":{"DocumentDate":"10.9.2019 \\u0433.","DocumentNumber":"PR.0136CST.000043.10\\/09\\/2019","DocumentStatus":"Registered","DocumentStatusName":"\\u0440\\u0435\\u0433\\u0438\\u0441\\u0442\\u0440\\u0438\\u0440\\u0430\\u043d\\u0430","DocumentType":"\\u043f\\u043e\\u0440\\u044a\\u0447\\u043a\\u0430 \\u0437\\u0430 \\u0434\\u043e\\u0441\\u0442\\u0430\\u0432\\u043a\\u0430","Items":[{"Barcode":"BOX-001929-01\\/10\\/2019-SHOE00914","ArticleBarcode":null,"ArticleCode":"20CRAANSBLL360","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRAANSBLL370","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRAANSBLL380","ArticleMu":null,"ArticleName":null,"Quantity":2,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRAANSBLL390","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRAANSBLL400","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRMGLSCEST360","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRMGLSCEST370","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRMGLSCEST380","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRMGLSCEST390","ArticleMu":null,"ArticleName":null,"Quantity":2,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRMGLSCEST400","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRGRTWRDL360","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRGRTWRDL370","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRGRTWRDL380","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRGRTWRDL390","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0},{"ArticleBarcode":null,"ArticleCode":"20CRGRTWRDL400","ArticleMu":null,"ArticleName":null,"Quantity":1,"QuantityReceived":0}],"SupplierName":"Talisma","Usr1":"","Usr2":"","Usr3":"","Usr4":"","Usr5":"d3431","WarehouseCode":"BASE"},"Log":{"ProcedureName":"GetDeliveryDocumentInfo","BegDate":"2019-09-25T15:22:01.6629541+03:00","EndDate":"2019-09-25T15:22:01.8660788+03:00","Duration":0.2031247,"WarningCount":0,"ErrorCount":0,"Events":[]}}\r\n', 'condition := 1', 'method := enola2NzoomDeliveryRequestSync\r\nplugin := byfar', NULL, 101, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%enola2NzoomDeliveryRequestSync%');

# Duplicated automation createDeliveryGt2 to be started as action automation
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Creates GT2 table of Delivery Verification Report from data in its GT and indirectly related Production Requests (started by automation enola2NzoomDeliveryRequestSync)', 0, NULL, 1, 'documents', NULL, 'action', '4', 'doc_type_request := 3\r\ndoc_type_packing_label := 10\r\n\r\nrequest_substatus_supplied := 2\r\n\r\ndelivery_packing_label_id_var := packing_label_id\r\n\r\nproducer_currency_var := delivery_currency\r\n\r\ncontract_var := article_alternative_deliverer\r\ncustomer_var := article_deliverer\r\ndelivery_date_var := free_field3\r\nsize_var =: free_field4\r\nundelivered_quantity_var := article_volume\r\nparcel_var := article_second_code\r\n\r\n# articles and quantities that exceed Production Requests\r\n# are saved as delivered for this customer and no contract\r\nsurplus_customer := 768\r\n', 'condition :=  \'[action]\' == \'automation_setstatus\' && \'[prev_b_status]\' != \'locked\' && \'[b_status]\' == \'locked\'', 'plugin := byfar\r\nmethod := createDeliveryGt2', '', 2, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%createDeliveryGt2%' AND automation_type='action');

# Fixed the conditions of composeBoxNumber automation to not require POST (to be executed from another automation)
UPDATE automations SET conditions=REPLACE(conditions, "'[request_is_post]' && ", '') WHERE method LIKE '%composeBoxNumber%' AND conditions LIKE '%request_is_post%';

########################################################################
# 2019-10-23 - Added enola delivery document id to prevent re-importing
#            - Changed the name of the automation

# Added enola delivery document id to prevent re-importing
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(3907, 'Document', 10, 'enola_delivery_doc_id', 'text', '', 0, 1, '', '', 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4280, 30, '', '', '');
INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(3907, 'label', 'Enola Devery Document ID', 'bg'),
(3907, 'label', 'Enola Devery Document ID', 'en');

# Changed the name of the automation
UPDATE automations SET settings=CONCAT('start_time := 01:00\r\nstart_before := 03:00\r\n\r\n', REPLACE(settings, 'test_delivery_request', 'test_data')), method=REPLACE(method, 'enola2NzoomDeliveryRequestSync', 'enola2NzoomDeliveriesSync') WHERE method like '%enola2NzoomDeliveryRequestSync%';

########################################################################
# 2019-11-13 - Added enola picking document sync

# Added enola picking document sync
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Send Fulfillment document to Enola Picking document', 0, NULL, 1, 'documents', NULL, 'before_action', '5', '#SANDBOX\r\nenola_api_url := http://**************:9001\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\n#enola_api_url := http://**************:9000\r\n#enola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\nsend_to_email := <EMAIL>\r\n\r\ncon_type_order_handover := 1\r\ninvoice_pattern_id := 14\r\n\r\ntest_mode := ', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'closed_5\' && \'[prev_b_substatus]\' != \'5\'', 'plugin := byfar\r\nmethod := nzoom2EnolaPickingDocument', 'cancel_action_on_fail := 1', 10, 1, 1
WHERE NOT EXISTS (
  SELECT 1 FROM automations
  WHERE method LIKE '%nzoom2EnolaPickingDocument%'
);

########################################################################
# 2019-11-21 - Added export

# Added pattern plugin for labels
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(90, 'Document', 3, 'byfar', 'prepareProductionRequest', '', '', NOW(), NOW());

INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(90, 'Подготовка за печат на заявка за производство', '', 'bg', NOW()),
(90, 'Print production request', '', 'en', NOW());

# Set plugin to the Label print pattern
UPDATE patterns SET plugin=90, format='xlsx' WHERE id = '16';

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'en_finance_company', 'Document', 'basic', 'patterns', ',90,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Име на собствена фирма', NULL, 'bg'),
(LAST_INSERT_ID(), 'Own company', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'en_finance_company_registration_address', 'Document', 'basic', 'patterns', ',90,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Адрес на собствена фирма', NULL, 'bg'),
(LAST_INSERT_ID(), 'Own company address', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'finance_company_vat_number', 'Document', 'basic', 'patterns', ',90,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'ДДС номер на собствена фирма', NULL, 'bg'),
(LAST_INSERT_ID(), 'Own company VAT number', NULL, 'en');

########################################################################
# 2019-12-11 - Change the addOutgoingHandover conditions to work with status Picked (closed_6) instead of Transferred to courier (closed_1) as it was previously.
#            - Added new automation attachPackingListToFulfillment (attaches packing list PDF file to the closed document) for status Picked (closed_6).
#            - Added new automation changeInvoiceDateOfPayment (previously part of addOutgoingHandover) for status Transferred to courier (closed_1).
#            - Added standard automation setAdditionalVar (sets current date to courier_transfer_date) for status Transferred to courier (closed_1).
#            - Added REST settings used by the ByFarAPI

# PRE-DEPLOYED # Change the addOutgoingHandover conditions to work with status Picked (closed_6) instead of Transferred to courier (closed_1). Usually triggered by ByFarAPI but also available by the standard nZoom interface.
#UPDATE `automations`
#SET `conditions`='condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'closed_6\' && \'[prev_b_substatus]\' != \'6\' && \'[a_warehouse]\' > 0 \r\n'
#WHERE method like '%addOutgoingHandover%' AND automation_type='before_action';

# PRE-DEPLOYED # Added new automation attachPackingListToFulfillment (attaches packing list PDF file to the closed document) for status Picked (closed_6). Triggered ONLY by ByFarAPI
#INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
#SELECT 'Attach Packing List to Fulfillment (only by ByFarAPI)', 0, NULL, 1, 'documents', NULL, 'before_action', '5', '', 'condition := \'[request_is_post]\' && \'[action]\' == \'setstatus\' && $registry->get(\'request\')->get(\'substatus\') == \'closed_6\' && \'[prev_b_substatus]\' != \'6\'\r\n', 'plugin := byfar\r\nmethod := attachPackingListToFulfillment', 'cancel_action_on_fail := 1', 4, 1, 1
#WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%attachPackingListToFulfillment%');

# PRE-DEPLOYED # Added new automation changeInvoiceDateOfPayment (previously part of addOutgoingHandover) for status Transferred to courier (closed_1). Usually triggered by ByFarAPI but also available by the standard nZoom interface.
#INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
#SELECT 'Change date of payment in related invoice', 0, NULL, 1, 'documents', NULL, 'before_action', '5', 'con_type_order_handover := 1\r\nfulfillment_substatus_invoiced := 4', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'closed_1\' && \'[prev_b_substatus]\' == \'6\'\r\n', 'plugin := byfar\r\nmethod := changeInvoiceDateOfPayment', 'cancel_action_on_fail := 1', 3, 1, 1
#WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%changeInvoiceDateOfPayment%');

# PRE-DEPLOYED # Added standard automation setAdditionalVar (sets current date to courier_transfer_date) for status Transferred to courier (closed_1). Usually triggered by ByFarAPI but also available by the standard nZoom interface.
#INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
#SELECT 'Change date transfer to courier when the status is shipped', 0, NULL, 1, 'documents', NULL, 'action', '5', '', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\'))\r\ncondition := "[prev_b_substatus]" == "6"\r\ncondition := "[b_substatus]" == "1"', 'method := setAdditionalVar\r\nvar_name := courier_transfer_date\r\nvar_value := php(date(\'Y-m-d H:i:00\'))', '', 4, 1, 1
#WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%setAdditionalVar%' AND method LIKE '%courier_transfer_date%');

# PRE-DEPLOYED # Added REST settings used by the ByFarAPI
#UPDATE settings SET VALUE=concat(value, ', ByFarAPI') WHERE section='rest' AND name='allowed_rest_user_agents' AND value NOT LIKE '%ByFarAPI%';
#INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES ('rest', 'filter_vars_documents_5', 'all');

########################################################################
# 2020-01-08 - Changed the automation that issues advance invoice from contracts of type 1
#            - Fix the incorrect invoice templates

# Changed the automation that issues advance invoice from contracts of type 1
UPDATE automations
SET
    method=REPLACE(method, 'createAdvanceInvoicesTemplate', 'issueAdvanceInvoiceFromContract'),
    settings=CONCAT(settings, '\nshipping_costs_nom_id := 3435\ndate_of_payment_count := 5\ndate_of_payment_period_type := calendar\ndate_of_payment_period := day\ndate_of_payment_direction := after\ndate_of_payment_point := issue'),
    conditions='condition := ''[action]'' == ''setstatus'' &&  ''[b_status]'' == ''closed'' && ''[b_substatus]'' == ''1'' &&  ''[b_subtype]'' != ''annex''',
    name='Издаване авансова фактура от договор',
    after_action='',
    automation_type='action',
    nums=1,
    position=10
WHERE method LIKE '%createAdvanceInvoicesTemplate%' AND settings NOT LIKE '%shipping_costs_nom_id%';

# Fix the incorrect invoice templates
UPDATE fin_invoices_templates SET deleted=NOW(), deleted_by=1 where id IN (158, 160, 492, 494, 524, 526, 2519, 2574, 2576, 2814);
UPDATE fin_invoices_templates_info SET parent_id=493 where contract_id=296 AND parent_id=492;
UPDATE fin_invoices_templates_info SET parent_id=2813 where contract_id=1497 AND parent_id=2519;
UPDATE fin_invoices_templates_info SET parent_id=2575 where contract_id=1540 AND parent_id=2574;

########################################################################
# 2020-01-30 - Added new automation to update the shipping cost of an order when adding annex

# Added new automation to update the shipping cost of an order when adding annex
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Update order shipping cost', 0, NULL, 1, 'contracts', NULL, 'action', '1', 'shipping_cost_var := shipping_cost\r\nshipping_cost_nom := 3435\r\n', 'condition := \'[action]\' == \'addannex\' && \'[request_is_post]\' == \'1\'', 'plugin := byfar\r\nmethod := addEditShippingCost', NULL, 0, 0, 1);

########################################################################
# 2020-02-03 - Added automation to insert joor ids into nomenclatures (should be started ONCE only)
#            - Added tag section ids to the nzoom2JoorNomSync
#            - Added tags and tag section ids to the nzoom2JoorCustomerSync, as well as conditions to start sundays

# Added automation to insert joor ids into nomenclatures (should be started ONCE only)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'nZoom JOOR shoes, boots and bags to nZoom Sync Ids', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', '#SANDBOX\r\n#joor_api_url := https://apisandbox.jooraccess.com/v2\r\n#joor_api_token := ********************************\r\n#PRODUCTION\r\njoor_api_url := https://api.jooraccess.com/v2\r\njoor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43\r\nsend_to_email := <EMAIL>', 'condition := 1', 'method := joor2NzoomNomSyncIds\r\nplugin := byfar', NULL, 100, 1, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%joor2NzoomNomSyncIds%');

# Added tag section ids to the nzoom2JoorNomSync
UPDATE automations
SET settings="#SANDBOX
#joor_api_url := https://apisandbox.jooraccess.com/v2
#joor_api_token := ********************************
#PRODUCTION
joor_api_url := https://api.jooraccess.com/v2
joor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43
tags :=
tag_sections := 2
send_to_email := <EMAIL>"
WHERE  method LIKE '%nzoom2JoorNomSync%' AND
       settings NOT LIKE '%tag_sections%';

# Added tags and tag section ids to the nzoom2JoorCustomerSync, as well as conditions to start sundays
UPDATE automations
SET settings="#start sundays
start_week_day := 7
start_time := 01:00
start_before := 03:00
#SANDBOX
#joor_api_url := https://apisandbox.jooraccess.com/v2
#joor_api_token := ********************************
#PRODUCTION
joor_api_url := https://api.jooraccess.com/v2
joor_api_token := 7fbbf9e4fe0045f6b19402ec52eb8f43
tags := 66
tag_sections :=
send_to_email := <EMAIL>"
WHERE  method LIKE '%nzoom2JoorCustomerSync%' AND
        settings NOT LIKE '%tag_sections%';

########################################################################
# 2020-02-04 - Fixed shipping measure in the B2C sales (orders) to be unit (1)
#            - Changed conditions of addEditShippingCost to start when when adding/editing contract or agreement

# Fixed shipping measure in the B2C sales (orders) to be unit (1)
UPDATE fin_incomes_reasons fir
    JOIN gt2_details gt2
    ON gt2.model_id=fir.id AND gt2.model='Finance_Incomes_Reason'  AND fir.type=101
    JOIN gt2_details_i18n gt2i18n
    ON gt2.id=gt2i18n.parent_id AND
       gt2.article_id=3435 AND gt2i18n.article_measure_name=''
SET gt2i18n.article_measure_name=1;

# Changed conditions of addEditShippingCost to start when when adding/editing contract or agreement
UPDATE automations
SET conditions='#start this automation when adding/editing contract or agreement\ncondition := preg_match(''#(add|edit).*#'', ''[action]'')  && ''[request_is_post]'' == ''1'''
WHERE method LIKE '%addEditShippingCost%' AND conditions NOT LIKE '%condition := preg_match%';

########################################################################
# 2020-02-25 - Added default sales terms id

# Added default sales terms id
UPDATE automations
SET settings=CONCAT(settings, '\ndefault_sales_terms_id := 224')
WHERE method LIKE '%joor2NzoomOrderSync%' AND settings NOT LIKE '%default_sales_terms_id%';

########################################################################
# 2020-03-24 - Allow issueAdvanceInvoiceFromContract to run for annexes

# Allow issueAdvanceInvoiceFromContract to run for annexes
UPDATE `automations` SET `conditions`='condition := \'[action]\' == \'setstatus\' &&  \'[b_status]\' == \'closed\' &&  \'[prev_b_status]\' != \'closed\' && \'[b_substatus]\' == \'1\'\r\n' WHERE  method like '%issueAdvanceInvoiceFromContract%';

########################################################################
# 2020-04-16 - Added byfar_products_sent report

# Added byfar_products_sent report
SET @report_id := 413;
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`) VALUES
(@report_id, 'byfar_products_sent', 'document_types := 5\r\ndocument_substatuses := 1\r\ncustomer_types := 3\r\nnomenclature_types := 5, 32\r\nnomenclature_exlude_ids := 1, 3435');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
(@report_id, 'Изпратени стоки по клиенти', 'bg'),
(@report_id, 'Products sent', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
('reports', 'generate_report', @report_id, '1'),
('reports', 'export', @report_id, '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module`     = 'reports'
  AND `action`     IN ('generate_report', 'export')
  AND `model_type` = @report_id;

########################################################################
# 2020-04-27 - Change the status of Fulfillment substatuses: Invoiced (4), Data sent to Alog (5), Picked (6) to be opened.
#            - Removed the automation attachPackingListToFulfillment it is replaced by the ByFAR API
#            - Made packing_list_file auditable
#            - Added automation to sync the nzoom warehouse transfers to Enola (enola2NzoomDeliveryRequestSync)

# Change the status of Fulfillment substatuses: Invoiced (4), Data sent to Alog (5), Picked (6) to be opened.
# Transferred to courier (1) stays closed
UPDATE documents_statuses SET status="opened" WHERE doc_type=5 AND id!=1;
UPDATE documents SET status="opened" WHERE type=5 and status="closed" AND substatus IN (4, 5, 6);
UPDATE automations SET conditions=REPLACE(conditions, 'closed_', 'opened_') WHERE start_model_type=5 AND (method like '%issueInterimInvoice%' OR method like '%addOutgoingHandover%' OR method like '%nzoom2EnolaPickingDocument%');
# Removed the automation attachPackingListToFulfillment it is replaced by the ByFAR API
DELETE FROM automations WHERE method like '%attachPackingListToFulfillment%';
# Made packing_list_file auditable
UPDATE _fields_meta SET auditable=1 WHERE name='packing_list_file';

# Added automation to sync the nzoom warehouse transfers to Enola (enola2NzoomDeliveryRequestSync)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Sync warehouse transfers to Enola', 0, NULL, 1, 'finance', 'warehouses_documents', 'action', '8', '#SANDBOX\r\n#enola_api_url := http://**************:9001\r\n#enola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\nenola_api_url := http://**************:9000\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45', 'condition := \'[b_status]\' == \'finished\'\r\ncondition := \'[prev_b_status]\' != \'finished\'', 'plugin := byfar\r\nmethod := nzoom2EnolaWarehouseTransfersSync', NULL, 1, 1, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2EnolaWarehouseTransfersSync%');

########################################################################
# 2020-05-08 - Added hats (49), belts (50) and scarves (51) to the settings of byfar_request_management report
#            - Fixed the spelling of "scarves"
#            - Added automations to create variants for hats (49), belts (50) and scarves (51)

# Added hats (49), belts (50) and scarves to the settings of byfar_request_management report
UPDATE reports SET settings=REPLACE(settings, 'nom_type_article := 5,32\r\n', 'nom_type_article := 5,32,48,49,50,51\r\n') WHERE type='byfar_request_management';
UPDATE reports SET settings=REPLACE(settings, 'nom_type_variant := 8\r\n', 'nom_type_variant := 8,52,53,54\r\n') WHERE type='byfar_request_management';

# Fixed the spelling of "scarves"
UPDATE nom_types_i18n SET name_plural='Scarves' WHERE parent_id=51 AND lang='en';
UPDATE nom_types_i18n SET name_plural='Variant Scarves' WHERE parent_id=54 AND lang='en';
UPDATE nom_sections SET name='Scarves' WHERE id=9 AND lang='en';
UPDATE nom_counters_i18n SET name='Scarves' WHERE parent_id=44 AND lang='en';
UPDATE nom_counters_i18n SET name='Variant Scarves' WHERE parent_id=47 AND lang='en';

# Added automations to create variants for hats (49), belts (50) and scarves (51)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Create variants (sizes) of hats', 0, NULL, 1, 'nomenclatures', NULL, 'action', '49', 'nom_variant_type := 53\r\nnom_variant_var_barcode := info_barcode\r\nnom_variant_var_size := info_size\r\n\r\nbarcode_prefix := 3800977\r\nbarcode_leading_zeros := 5\r\nbarcode_first_code := 1677\r\n\r\n', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'', 'method := createVariants\r\nplugin := byfar', NULL, 5, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%createVariants%' AND start_model_type=49);
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Create variants (sizes) of belts', 0, NULL, 1, 'nomenclatures', NULL, 'action', '50', 'nom_variant_type := 52\r\nnom_variant_var_barcode := info_barcode\r\nnom_variant_var_size := info_size\r\n\r\nbarcode_prefix := 3800977\r\nbarcode_leading_zeros := 5\r\nbarcode_first_code := 1677\r\n\r\n', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'', 'method := createVariants\r\nplugin := byfar', NULL, 5, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%createVariants%' AND start_model_type=50);
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Create variants (sizes) of scarves', 0, NULL, 1, 'nomenclatures', NULL, 'action', '51', 'nom_variant_type := 54\r\nnom_variant_var_barcode := info_barcode\r\nnom_variant_var_size := info_size\r\n\r\nbarcode_prefix := 3800977\r\nbarcode_leading_zeros := 5\r\nbarcode_first_code := 1677\r\n\r\n', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'', 'method := createVariants\r\nplugin := byfar', NULL, 5, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%createVariants%' AND start_model_type=51);

########################################################################
# 2020-05-19 - Changed the name of byfar_products_sent report

# Added byfar_products_sent report
SET @report_id := 413;
UPDATE `reports_i18n` SET name='Изпратени стоки по дата' WHERE parent_id=@report_id AND lang='bg';
UPDATE `reports_i18n` SET name='Shipped products by date' WHERE parent_id=@report_id AND lang='en';

########################################################################
# 2020-06-17 - Added automation that send delivery document to Enola when fulfillment is cancelled (7) and previously picked (6)
#            - Added autionation to sync Belts to Enola
#            - Added autionation to sync Scarves to Enola

# Added automation that send delivery document to Enola when fulfillment is cancelled (7) and previously picked (6)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Send cancelled Fulfillment document to Enola delivery document', 0, NULL, 1, 'documents', NULL, 'before_action', '5', '#SANDBOX\r\n#enola_api_url := http://**************:9001\r\n#enola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\nenola_api_url := http://**************:9000\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\nsend_to_email := <EMAIL>\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'closed_7\' && \'[prev_b_substatus]\' == \'6\'', 'plugin := byfar\r\nmethod := nzoom2EnolaCancelFulfillment', 'cancel_action_on_fail := 1', 11, 1, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2EnolaCancelFulfillment%' AND start_model_type=5);

# Added autionation to sync Belts to Enola
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'nZoom Belts to Enola Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '52', '#SANDBOX\r\n#enola_api_url := http://**************:9001\r\n#enola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\nenola_api_url := http://**************:9000\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\nsend_to_email := <EMAIL>\r\n\r\ntest_mode := ', 'condition := 1', 'method := nzoom2EnolaNomSync\r\nplugin := byfar', NULL, 99, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2EnolaNomSync%' AND start_model_type=52);

# Added autionation to sync Scarves to Enola
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'nZoom Scarves to Enola Sync', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '54', '#SANDBOX\r\n#enola_api_url := http://**************:9001\r\n#enola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\nenola_api_url := http://**************:9000\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\nsend_to_email := <EMAIL>\r\n\r\ntest_mode := ', 'condition := 1', 'method := nzoom2EnolaNomSync\r\nplugin := byfar', NULL, 99, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%nzoom2EnolaNomSync%' AND start_model_type=54);

########################################################################
# 2020-07-15 - Added placeholders invoices

# Added placeholders invoices
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'draft_delivery_date_from', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Draft Order Delivery Date From', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Draft Order Delivery Date From', NULL, 'en');
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'draft_delivery_date_to', 'Finance_Incomes_Reason', 'basic', 'patterns', ',89,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Draft Order Delivery Date To', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Draft Order Delivery Date То', NULL, 'en');

# Replace contract_date_start with draft_delivery_date_from and contract_date_validity with draft_delivery_date_to
UPDATE patterns_i18n SET content=REPLACE(content, 'contract_date_start', 'draft_delivery_date_from') WHERE content LIKE '%contract_date_start%';
UPDATE patterns_i18n SET content=REPLACE(content, 'contract_date_validity', 'draft_delivery_date_to') WHERE content LIKE '%contract_date_validity%';

# rename draft_delivery_date_from to contract_delivery_date_from and draft_delivery_date_to to contract_delivery_date_to
UPDATE patterns_i18n SET content=REPLACE(content, 'draft_delivery_date_from', 'contract_delivery_date_from') WHERE content LIKE '%draft_delivery_date_from%';
UPDATE patterns_i18n SET content=REPLACE(content, 'draft_delivery_date_to', 'contract_delivery_date_to') WHERE content LIKE '%draft_delivery_date_to%';

UPDATE placeholders SET varname='contract_delivery_date_from' WHERE varname='draft_delivery_date_from';
UPDATE placeholders SET varname='contract_delivery_date_to' WHERE varname='draft_delivery_date_to';
UPDATE placeholders_i18n SET name=REPLACE(name, 'Draft Order Delivery Date', 'Contract Delivery Date') WHERE name LIKE 'Draft Order Delivery Date%';

########################################################################
# 2020-07-29 - Added values for product_id and product_name in variants

# Added values for product_id and product_name in variants
INSERT IGNORE INTO nom_cstm
SELECT nc1.VALUE AS variant_id, fm.id, 1, n.id as product_id, NOW(), -1, NOW(), -1, ''
FROM nom_cstm nc1
         JOIN nom n
              ON nc1.var_id IN (SELECT id FROM _fields_meta WHERE NAME="sku_code_id") AND n.id=nc1.model_id
         JOIN nom n2
              ON n2.id=nc1.value
         JOIN _fields_meta fm
              ON n2.type=fm.model_type AND fm.model="Nomenclature" AND fm.NAME="product_id";

INSERT IGNORE INTO nom_cstm
SELECT nc1.VALUE AS variant_id, fm.id, 1, ni.name as product_name, NOW(), -1, NOW(), -1, 'en'
FROM nom_cstm nc1
         JOIN nom n
              ON nc1.var_id IN (SELECT id FROM _fields_meta WHERE NAME="sku_code_id") AND n.id=nc1.model_id
         JOIN nom_i18n ni
              ON n.id=ni.parent_id AND ni.lang='en'
         JOIN nom n2
              ON n2.id=nc1.value
         JOIN _fields_meta fm
              ON n2.type=fm.model_type AND fm.model="Nomenclature" AND fm.NAME="product_name";

########################################################################
# 2020-08-06 - Added values for invoice_num/invoice_num_id

# Added values for invoice_num/invoice_num_id
DELETE FROM documents_cstm
WHERE var_id IN (SELECT id FROM _fields_meta WHERE NAME IN ('invoice_num', 'invoice_num_id'));

SELECT @invoice_num_id:=id FROM _fields_meta WHERE NAME='invoice_num_id';
SELECT @invoice_num:=id FROM _fields_meta WHERE NAME='invoice_num';
INSERT IGNORE INTO documents_cstm
SELECT d.id, @invoice_num_id, 1, fir.id, NOW(), -1, NOW(), -1, ''
FROM documents_relatives dr
         JOIN documents d
              ON d.TYPE=5 AND d.id=dr.link_to AND dr.parent_model_name="Finance_Incomes_Reason" AND dr.link_to_model_name="Document"
         JOIN fin_incomes_reasons fir
              ON fir.id=dr.parent_id;
INSERT IGNORE INTO documents_cstm
SELECT d.id, @invoice_num, 1, fir.num, NOW(), -1, NOW(), -1, ''
FROM documents_relatives dr
         JOIN documents d
              ON d.TYPE=5 AND d.id=dr.link_to AND dr.parent_model_name="Finance_Incomes_Reason" AND dr.link_to_model_name="Document"
         JOIN fin_incomes_reasons fir
              ON fir.id=dr.parent_id;

########################################################################
# 2020-08-07 - Fixed the values of product_id/product_name

# Fixed the values of product_id/product_name
DELETE FROM nom_cstm
WHERE var_id IN (SELECT id FROM _fields_meta WHERE NAME IN ('product_id', 'product_name'));

# Added values for product_id and product_name in variants
INSERT IGNORE INTO nom_cstm
SELECT nc1.VALUE AS variant_id, fm.id, 1, n.id as product_id, NOW(), -1, NOW(), -1, ''
FROM nom_cstm nc1
         JOIN nom n
              ON nc1.var_id IN (SELECT id FROM _fields_meta WHERE NAME="sku_code_id") AND n.id=nc1.model_id
         JOIN nom n2
              ON n2.id=nc1.value
         JOIN _fields_meta fm
              ON n2.type=fm.model_type AND fm.model="Nomenclature" AND fm.NAME="product_id";

INSERT IGNORE INTO nom_cstm
SELECT nc1.VALUE AS variant_id, fm.id, 1, ni.name as product_name, NOW(), -1, NOW(), -1, 'en'
FROM nom_cstm nc1
         JOIN nom n
              ON nc1.var_id IN (SELECT id FROM _fields_meta WHERE NAME="sku_code_id") AND n.id=nc1.model_id
         JOIN nom_i18n ni
              ON n.id=ni.parent_id AND ni.lang='en'
         JOIN nom n2
              ON n2.id=nc1.value
         JOIN _fields_meta fm
              ON n2.type=fm.model_type AND fm.model="Nomenclature" AND fm.NAME="product_name";

########################################################################
# 2020-08-20 - Set enola2NzoomDeliveriesSync to start regularly instead of once a day

# Set enola2NzoomDeliveriesSync to start regularly instead of once a day
UPDATE automations SET settings=REPLACE(REPLACE(settings, 'start_time', '#start_time'), 'start_before', '#start_before') WHERE method LIKE '%enola2NzoomDeliveriesSync%';

########################################################################
# 2020-11-19 - Add param log_requests and log_stats

UPDATE automations
SET settings=CONCAT(settings, '\r\nlog_requests := 1')
WHERE settings NOT LIKE '%log_requests%' AND
    (method LIKE '%shopify%' AND method NOT LIKE '%validate%' || method LIKE '%nzoom2JoorCustomerSync%');
UPDATE automations
SET settings=CONCAT(settings, '\r\nlog_requests :=')
WHERE settings NOT LIKE '%log_requests%' AND
      (method LIKE '%enola%' OR method LIKE '%joor%');
UPDATE automations
SET settings=CONCAT(settings, '\r\nlog_stats :=')
WHERE settings NOT LIKE '%log_stats%' AND
    (method LIKE '%joor2NzoomOrderSync%');


########################################################################
# 2020-11-27 - Added settings to patterns plugins

UPDATE `patterns_plugins`
SET settings='advance_id := 1\r\nshipping_id := 3435\r\nsizes_type_id := 26\r\n#pattern settings advance\r\nnom_1_pattern_settings := 24\r\n#pattern settings shoes\r\nnom_8_pattern_settings := 25\r\n#pattern settings bags\r\nnom_32_pattern_settings := 26\r\n#pattern settings hats\r\nnom_53_pattern_settings := 39\r\n#pattern settings belts\r\nnom_52_pattern_settings := 38\r\n#pattern settings scarves\r\nnom_54_pattern_settings := 40\r\n'
WHERE method='prepareFinance' AND settings NOT LIKE '%advance_id%';

UPDATE `patterns_plugins`
SET settings='sizes_type_id := 26\r\n'
WHERE method='prepareProductionRequest' AND settings NOT LIKE '%sizes_type_id%';

########################################################################
# 2020-11-30 - Added settings for product_type in the nzoom2ShopifySync automations

# Added settings for product_type in the nzoom2ShopifySync automations
UPDATE automations
SET settings=CONCAT(settings, '\r\nproduct_type_5 := SHOES\r\nproduct_type_32 := BAGS\r\nproduct_type_49 := HATS\r\nproduct_type_50 := BELTS\r\nproduct_type_51 := SCARVES\r\nproduct_type_55 := ACCESSORIES\r\n')
WHERE method LIKE '%nzoom2ShopifySync%' AND settings NOT LIKE '%product_type%';

# Renamed validateShoes to validateProductWithSizes as to become usable for other types of products with sizes/variants
UPDATE automations SET method=REPLACE(method, 'validateShoes', 'validateProductWithSizes') WHERE method like '%validateShoes%';

# Modified the conditions of setContractIntoGt2 to be able to execute for annexes
UPDATE `automations` SET `conditions`='condition := in_array(\'[b_type]\', array(1, 2, 3))\r\ncondition := \'[request_is_post]\'\r\ncondition := in_array(\'[action]\', array(\'add\', \'addannex\', \'edit\', \'edittopic\', \'setstatus\'))\r\n'
WHERE  method LIKE '%setContractIntoGt2%';

########################################################################
# 2020-12-01 - Added additional placeholder for generating IFI file

# Added additional placeholder for generating IFI file
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'sole_common_name', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подметка име', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Sole common name', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'sole_science_name', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подметка научно име', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Liner science name', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'sole_type', 'Document', 'basic', 'patterns', ',3,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Подметка тип', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Sole type', NULL, 'en');

########################################################################
# 2020-12-17 - Added pattern plugin for contracts

# Added pattern plugin for contracts
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(92, 'Contract', 0, 'byfar', 'prepareContract', 'extend_article_vars := producer_name, hs_name, composition, lining, upper, curr_jpy__retail, curr_eur__retail, curr_usd__retail, curr_gbp__retail, curr_aud__retail, curr_cny__retail, curr_cad__retail', '', NOW(), NOW());

INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(92, 'Подготовка за печат на договори', '', 'bg', NOW()),
(92, 'Print contracts', '', 'en', NOW());

########################################################################
# 2021-01-21 - Expand the automation that sync the orders from JOOR to work with 4 types of draft orders: shoes (1), bags (7), hats, belts, scarves (15) and accessories (17)
#            - Added new autocompleter plugin for BYFAR (getProductDetails)
#            - Enabled group table refresh buttons
#            - Added customer settings from Document type 1 to Document type 15
#            - Added customer settings from Document type 1 to Document type 16
#            - Copy the customer settings of Document type 1 to Document type 7
#            - Deactivate the automations that set additional vars for season (the functionality is done by joor2NzoomOrderSync)
#            - Copy automations from Draft order Shoes (1) to Draft Order HBS (15) and Draft Order Accessories (16)

# Expand the automation that sync the orders from JOOR to work with 4 types of draft orders: shoes (1), bags (7), hats, belts, scarves (15) and accessories (17)
UPDATE `automations` SET `settings`=REPLACE(settings, 'draft_order_bags_type := 7', 'draft_order_bags_type := 7\r\ndraft_order_hbs_type := 15\r\ndraft_order_accessories_type := 16') WHERE  method like '%joor2NzoomOrderSync%' AND settings NOT LIKE '%draft_order_hbs_type%';

# Added new autocompleter plugin for BYFAR (getProductDetails)
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := [<code>] <name> \r\nautocomplete_filter := type => 5\r\n\r\nautocomplete_fill_options := $shoe_id => <id>\r\nautocomplete_fill_options := $shoe_name => <name>\r\nautocomplete_fill_options := $shoe_code => <code>\r\nautocomplete_fill_options := $shoe_ref_code => <ref>\r\nautocomplete_fill_options := $shoe_cat => <cat>\r\nautocomplete_fill_options := $shoe_color => <color>\r\nautocomplete_fill_options := $hs_code_region => <hscode>\r\nautocomplete_fill_options := $rrp_priceshoe => <retail>\r\nautocomplete_fill_options := $wholesale_price => <wholesale>\r\nautocomplete_fill_options := $b2b_priceshoe => <wholesale_with_discount>\r\nautocomplete_fill_options := $landed_priceshoe_nodisc => <landed>\r\nautocomplete_fill_options := $landed_priceshoe => <landed_with_discount>\r\nautocomplete_fill_options := $marginshoe => <marg>\r\nautocomplete_fill_options := $markupshoe => <mark>\r\nautocomplete_fill_options := $shoe_hiddenpic => <picfile>\r\n\r\nautocomplete_plugin := byfar\r\nautocomplete_on_select := getProductDetails\r\nautocomplete_plugin_param_pl := $pricelist\r\nautocomplete_plugin_param_currency := $order_currency\r\nautocomplete_plugin_param_maxvat := $max_vat\r\nautocomplete_plugin_param_eucheck := $ship_eucheck\r\n\r\nautocomplete_clear := 1\r\nautocomplete_refresh := 1\r\nautocomplete_button_menu := 1\r\nautocomplete_view_mode := link\r\n' WHERE  `id`=3009;
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := [<code>] <name> \r\nautocomplete_filter := type => 32\r\n\r\nautocomplete_fill_options := $bag_id => <id>\r\nautocomplete_fill_options := $bag_name => <name>\r\nautocomplete_fill_options := $bag_code => <code>\r\nautocomplete_fill_options := $bag_ref_code => <ref>\r\nautocomplete_fill_options := $bag_cat => <cat>\r\nautocomplete_fill_options := $bag_color => <color>\r\nautocomplete_fill_options := $size_bag => <size_id>\r\nautocomplete_fill_options := $rrp_pricebag => <retail>\r\nautocomplete_fill_options := $hs_code_bag_region => <hscode>\r\nautocomplete_fill_options := $wholesale_pricebag => <wholesale>\r\nautocomplete_fill_options := $b2b_pricebag => <wholesale_with_discount>\r\nautocomplete_fill_options := $landed_pricebag_nodisc => <landed>\r\nautocomplete_fill_options := $landed_pricebag => <landed_with_discount>\r\nautocomplete_fill_options := $marginbag => <marg>\r\nautocomplete_fill_options := $markupbag => <mark>\r\nautocomplete_fill_options := $bag_hiddenpic => <picfile>\r\n\r\nautocomplete_plugin := byfar\r\nautocomplete_on_select := getProductDetails\r\nautocomplete_plugin_param_pl := $pricelist\r\nautocomplete_plugin_param_currency := $order_currency\r\nautocomplete_plugin_param_maxvat := $max_vat\r\nautocomplete_plugin_param_eucheck := $ship_eucheck\r\n\r\n\r\nautocomplete_clear := 1\r\nautocomplete_refresh := 1\r\nautocomplete_button_menu := 1\r\nautocomplete_view_mode := link\r\n' WHERE  `id`=3042;
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := [<code>] <name> \r\nautocomplete_filter := type => 49,50,51\r\n\r\nautocomplete_fill_options := $hbs_id => <id>\r\nautocomplete_fill_options := $hbs_name => <name>\r\nautocomplete_fill_options := $hbs_code => <code>\r\nautocomplete_fill_options := $hbs_ref_code => <ref>\r\nautocomplete_fill_options := $hbs_cat => <cat>\r\nautocomplete_fill_options := $hbs_color => <color>\r\nautocomplete_fill_options := $hs_code_region => <hscode>\r\nautocomplete_fill_options := $rrp_pricehbs => <retail>\r\nautocomplete_fill_options := $wholesale_price => <wholesale>\r\nautocomplete_fill_options := $b2b_pricehbs => <wholesale_with_discount>\r\nautocomplete_fill_options := $landed_pricehbs_nodisc => <landed>\r\nautocomplete_fill_options := $landed_pricehbs => <landed_with_discount>\r\nautocomplete_fill_options := $marginhbs => <marg>\r\nautocomplete_fill_options := $markuphbs => <mark>\r\nautocomplete_fill_options := $hbs_hiddenpic => <picfile>\r\n\r\nautocomplete_plugin := byfar\r\nautocomplete_on_select := getProductDetails\r\nautocomplete_plugin_param_pl := $pricelist\r\nautocomplete_plugin_param_currency := $order_currency\r\nautocomplete_plugin_param_maxvat := $max_vat\r\nautocomplete_plugin_param_eucheck := $ship_eucheck\r\n\r\nautocomplete_clear := 1\r\nautocomplete_refresh := 1\r\nautocomplete_button_menu := 1\r\nautocomplete_view_mode := link\r\n' WHERE  `id`=4408;
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := [<code>] <name> \r\nautocomplete_filter := type => 55\r\n\r\nautocomplete_fill_options := $accessories_id => <id>\r\nautocomplete_fill_options := $accessories_name => <name>\r\nautocomplete_fill_options := $accessories_code => <code>\r\nautocomplete_fill_options := $accessories_ref_code => <ref>\r\nautocomplete_fill_options := $accessories_cat => <cat>\r\nautocomplete_fill_options := $accessories_color => <color>\r\nautocomplete_fill_options := $size_accessories => <size_id>\r\nautocomplete_fill_options := $rrp_priceaccessories => <retail>\r\nautocomplete_fill_options := $hs_code_accessories_region => <hscode>\r\nautocomplete_fill_options := $wholesale_priceaccessories => <wholesale>\r\nautocomplete_fill_options := $b2b_priceaccessories => <wholesale_with_discount>\r\nautocomplete_fill_options := $landed_priceaccessories_nodisc => <landed>\r\nautocomplete_fill_options := $landed_priceaccessories => <landed_with_discount>\r\nautocomplete_fill_options := $marginaccessories => <marg>\r\nautocomplete_fill_options := $markupaccessories => <mark>\r\nautocomplete_fill_options := $accessories_hiddenpic => <picfile>\r\n\r\nautocomplete_plugin := byfar\r\nautocomplete_on_select := getProductDetails\r\nautocomplete_plugin_param_pl := $pricelist\r\nautocomplete_plugin_param_currency := $order_currency\r\nautocomplete_plugin_param_maxvat := $max_vat\r\nautocomplete_plugin_param_eucheck := $ship_eucheck\r\n\r\nautocomplete_clear := 1\r\nautocomplete_refresh := 1\r\nautocomplete_button_menu := 1\r\nautocomplete_view_mode := link\r\n' WHERE  `id`=4503;
UPDATE `_fields_meta` SET `source`='autocomplete := nomenclatures\r\nautocomplete_suggestions := [<code>] <name> \r\nautocomplete_filter := type => 8,32,51,52,53,54,55\r\n\r\nautocomplete_fill_options := $article_id => <id>\r\nautocomplete_fill_options := $article_name => <name>\r\nautocomplete_fill_options := $article_code => <code>\r\nautocomplete_fill_options := $article_barcode => <a_info_barcode>\r\nautocomplete_fill_options := $article_delivery_code => <ref>\r\nautocomplete_fill_options := $free_field2 => <color>\r\nautocomplete_fill_options := $free_field4 => <size_id>\r\nautocomplete_fill_options := $price => <wholesale>\r\nautocomplete_fill_options := $article_height => <wholesale>\r\nautocomplete_fill_options := $article_width => <landed>\r\nautocomplete_fill_options := $article_weight => <retail>\r\nautocomplete_fill_options := $last_delivery_price => <margin>\r\nautocomplete_fill_options := $article_volume => <markup>\r\nautocomplete_fill_options := $discount_percentage => <discount>\r\n#IMPORTANT: cannot set value of file_upload\r\n#autocomplete_fill_options := $free_field1 => <picfile>\r\n\r\nautocomplete_plugin := byfar\r\nautocomplete_on_select := getProductDetails\r\n\r\nautocomplete_plugin_param_pl := $pricelist\r\nautocomplete_plugin_param_currency := $currency\r\nautocomplete_plugin_param_maxvat := $max_vat\r\nautocomplete_plugin_param_eucheck := $ship_eucheck\r\n\r\nautocomplete_clear := 1\r\nautocomplete_refresh := 1\r\nautocomplete_button_menu := 1\r\nautocomplete_view_mode := link\r\n\r\ntext_align := left\r\npermissions_edit := 1\r\npermissions_view := 1\r\n' WHERE  `id`=103772;

# Enabled group table refresh buttons
UPDATE `_fields_meta` SET `source`=CONCAT(source, '\r\nshow_refresh_buttons := nomenclatures') WHERE name IN ('group_shoes', 'group_bags', 'group_hbs', 'group_accessories') AND model="Document" AND model_type IN (1,7,15,16) AND source NOT LIKE '%show_refresh_buttons%';
UPDATE `_fields_meta` SET `source`=CONCAT(source, '\r\nshow_refresh_buttons := nomenclatures') WHERE name='group_table_2' AND model="Contract" AND model_type=3 AND source NOT LIKE '%show_refresh_buttons%';

# Added customer settings from Document type 1 to Document type 15
INSERT IGNORE INTO _fields_meta
SELECT NULL, `model`, 15, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, (SELECT layout_id FROM layouts WHERE `keyname`='customer' AND model='Document' AND model_type=15), `position`, `width`, `width_print`, `height`
FROM _fields_meta WHERE name="Customer" AND model="Document" AND model_type=1;

# Added customer settings from Document type 1 to Document type 16
INSERT IGNORE INTO _fields_meta
SELECT NULL, `model`, 16, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, (SELECT layout_id FROM layouts WHERE `keyname`='customer' AND model='Document' AND model_type=16), `position`, `width`, `width_print`, `height`
FROM _fields_meta WHERE name="Customer" AND model="Document" AND model_type=1;

# Copy the customer settings of Document type 1 to Document type 7
SELECT `source` INTO @cus_source FROM _fields_meta WHERE name="Customer" AND model="Document" AND model_type=1;
UPDATE _fields_meta SET source=@cus_source
WHERE name="Customer" AND model="Document" AND model_type=7;

# Deactivate the automations that set additional vars for season (the functionality is done by joor2NzoomOrderSync)
UPDATE automations SET active=0 WHERE method LIKE '%var_name := nom_type; season_name_id; season_name; year_on_sale%';

# Copy automations from Draft order Shoes (1) to Draft Order HBS (15) and Draft Order Accessories (16)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT NULL, 'Copy values from group var to other group var from the same document', 0, NULL, 1, 'documents', NULL, 'action', '15', '# field_копирано поле := поле приемащо стойността\r\nfield_hbs_hiddenpic := hbs_picture', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')\r\ncondition := \'[request_is_post]\'', 'plugin := byfar\r\nmethod := copyGroupVars', NULL, 0, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%copyGroupVars%' AND `start_model_type`=15);

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT NULL, 'Copy values from group var to other group var from the same document', 0, NULL, 1, 'documents', NULL, 'action', '16', '# field_копирано поле := поле приемащо стойността\r\nfield_accessories_hiddenpic := accessories_picture', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')\r\ncondition := \'[request_is_post]\'', 'plugin := byfar\r\nmethod := copyGroupVars', NULL, 0, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%copyGroupVars%' AND `start_model_type`=16);

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT NULL, 'Creates GT2 table of model from data in its GTs', 0, NULL, 1, 'documents', NULL, 'action', '15', 'currency_var := order_currency\r\n\r\n# keywords of GT fields data will be taken from\r\nkeywords := hbs\r\n\r\n# specific variables for variants\r\nhbs_id_var := hbs_id\r\nhbs_size_var_prefix := size_\r\nhbs_size_var := info_size\r\n\r\n# hbs var mapping\r\ngt2_hbs_article_id := s_a_sku_code_id\r\ngt2_hbs_quantity := \r\ngt2_hbs_price := \r\ngt2_hbs_article_name := d_b_name\r\ngt2_hbs_article_code := d_b_code\r\ngt2_hbs_article_barcode := d_a_info_barcode\r\ngt2_hbs_article_delivery_code := hbs_ref_code\r\ngt2_hbs_article_trademark := s_a_producer_color_id\r\ngt2_hbs_article_measure_name := 1\r\ngt2_hbs_article_height := b2b_pricehbs\r\ngt2_hbs_article_width := landed_pricehbs\r\ngt2_hbs_article_weight := rrp_pricehbs\r\ngt2_hbs_article_volume := markuphbs\r\ngt2_hbs_last_delivery_price := marginhbs\r\ngt2_hbs_free_field1 := hbs_picture\r\ngt2_hbs_free_field2 := hbs_color\r\ngt2_hbs_free_field4 := d_a_info_size\r\n\r\n# price depends on conditions\r\ndelivery_terms_var := delivery_terms\r\ndelivery_terms_222 := b2b_price[keyword]\r\ndelivery_terms_223 := landed_price[keyword]\r\ndelivery_terms_3436 := b2b_price[keyword]\r\ndelivery_terms_4605 := b2b_price[keyword]\r\n\r\nshipping_costs_var := shipping_total\r\nshipping_costs_article_id := 3435\r\n', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')\r\ncondition := \'[request_is_post]\'', 'plugin := byfar\r\nmethod := createOrderGt2', NULL, 2, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%createOrderGt2%' AND `start_model_type`=15);

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT NULL, 'Creates GT2 table of model from data in its GTs', 0, NULL, 1, 'documents', NULL, 'action', '16', 'currency_var := order_currency\r\n\r\n# keywords of GT fields data will be taken from\r\nkeywords := accessories\r\n\r\naccessories_id_var := accessories_id\r\n\r\n# accessories var mapping\r\ngt2_accessories_article_id := accessories_id\r\ngt2_accessories_quantity := accessories_qty\r\ngt2_accessories_price := \r\ngt2_accessories_article_name := accessories_name\r\ngt2_accessories_article_code := accessories_code\r\ngt2_accessories_article_barcode := s_a_info_barcode\r\ngt2_accessories_article_delivery_code := accessories_ref_code\r\ngt2_accessories_article_trademark := s_a_producer_color_id\r\ngt2_accessories_article_measure_name := 1\r\ngt2_accessories_article_height := b2b_priceaccessories\r\ngt2_accessories_article_width := landed_priceaccessories\r\ngt2_accessories_article_weight := rrp_priceaccessories\r\ngt2_accessories_article_volume := markupaccessories\r\ngt2_accessories_last_delivery_price := marginaccessories\r\ngt2_accessories_free_field1 := accessories_picture\r\ngt2_accessories_free_field2 := accessories_color\r\ngt2_accessories_free_field4 := size_accessories\r\n\r\n# price depends on conditions\r\ndelivery_terms_var := delivery_terms\r\ndelivery_terms_222 := b2b_price[keyword]\r\ndelivery_terms_223 := landed_price[keyword]\r\ndelivery_terms_3436 := b2b_price[keyword]\r\ndelivery_terms_4605 := b2b_price[keyword]\r\n\r\nshipping_costs_var := shipping_total\r\nshipping_costs_article_id := 3435\r\n', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')\r\ncondition := \'[request_is_post]\'', 'plugin := byfar\r\nmethod := createOrderGt2', NULL, 2, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%createOrderGt2%' AND `start_model_type`=16);

########################################################################
# 2021-02-11 - Added the tags to be in the simple search of nomenclatures

# Added the tags to be in the simple search of nomenclatures
UPDATE `_search_defs` SET `key`='1' WHERE var_name='tags.tag_id' AND module='nomenclatures';

########################################################################
# 2021-02-12 - Added new automations for synchronizing nZoom incomes reasons with AJUR

# Added new automations for synchronizing nZoom incomes reasons with AJUR
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'nZoom to AJUR Sync (crontab)', 0, NULL, 1, 'finance', 'incomes_reasons', 'crontab', '0', 'start_time := 23:00\r\nstart_before := 23:59\r\n\r\napi_url := http://10.7.214.14:1080//AjurRestAPI.dll/datasnap/rest/TRestMethods/\r\napi_key := 987\r\napi_pass := ajur\r\n\r\ntag_ajur_exported :=\r\nnom_advance := 1\r\ndefault_warehouse := 1\r\n\r\ncontract_var_warehouse := warehouse\r\n\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := byfar\r\nmethod := nzoom2AJURSync', NULL, 111, 0, 0
    WHERE NOT EXISTS(SELECT id FROM automations WHERE start_model_type = 0 AND method LIKE '%nzoom2AJURSync%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'nZoom to AJUR Sync (фактура)', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '1', 'api_url := http://10.7.214.14:1080//AjurRestAPI.dll/datasnap/rest/TRestMethods/\r\napi_key := 987\r\napi_pass := ajur\r\n\r\ntag_ajur_exported :=\r\nnom_advance := 1\r\ndefault_warehouse := 1\r\n\r\ncontract_var_warehouse := warehouse', 'condition := \'[prev_b_status]\' != \'finished\' && \'[b_status]\' == \'finished\'', 'plugin := byfar\r\nmethod := nzoom2AJURSync', NULL, 1, 0, 0
    WHERE NOT EXISTS(SELECT id FROM automations WHERE start_model_type = 1 AND method LIKE '%nzoom2AJURSync%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'nZoom to AJUR Sync (кредитни известия)', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '3', 'api_url := http://10.7.214.14:1080//AjurRestAPI.dll/datasnap/rest/TRestMethods/\r\napi_key := 987\r\napi_pass := ajur\r\n\r\ntag_ajur_exported :=\r\nnom_advance := 1\r\ndefault_warehouse := 1\r\n\r\ncontract_var_warehouse := warehouse', 'condition := \'[prev_b_status]\' != \'finished\' && \'[b_status]\' == \'finished\'', 'plugin := byfar\r\nmethod := nzoom2AJURSync', NULL, 1, 0, 0
    WHERE NOT EXISTS(SELECT id FROM automations WHERE start_model_type = 3 AND method LIKE '%nzoom2AJURSync%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'nZoom to AJUR Sync (дебитни известия)', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '4', 'api_url := http://10.7.214.14:1080//AjurRestAPI.dll/datasnap/rest/TRestMethods/\r\napi_key := 987\r\napi_pass := ajur\r\n\r\ntag_ajur_exported :=\r\nnom_advance := 1\r\ndefault_warehouse := 1\r\n\r\ncontract_var_warehouse := warehouse', 'condition := \'[prev_b_status]\' != \'finished\' && \'[b_status]\' == \'finished\'', 'plugin := byfar\r\nmethod := nzoom2AJURSync', NULL, 1, 0, 0
    WHERE NOT EXISTS(SELECT id FROM automations WHERE start_model_type = 4 AND method LIKE '%nzoom2AJURSync%');

########################################################################
# 2021-02-15 - Added requests log, test period and default ajur EIK for nzoom2AJURSync automations

# Added requests log, test period and default ajur EIK for nzoom2AJURSync automations
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\ncontract_var_warehouse :=', '\r\najur_default_eik := 999999999999999\r\n\r\ntest_date_from :=\r\ntest_date_to :=\r\n\r\ncontract_var_warehouse :=') WHERE `method` LIKE '%nzoom2AJURSync%' AND `settings` NOT LIKE '%ajur_default_eik%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'contract_var_warehouse := warehouse', 'contract_var_warehouse := warehouse\r\n\r\nlog_requests := 1') WHERE `method` LIKE '%nzoom2AJURSync%' AND `settings` NOT LIKE '%log_requests%';

########################################################################
# 2021-02-24 - Renamed shoe_serial_num, belt_serial_num, hat_serial_num, accessories_serial_num, scarf_serial_num to just serial_num

# Renamed shoe_serial_num, belt_serial_num, hat_serial_num, accessories_serial_num, scarf_serial_num to just serial_num
UPDATE _fields_meta SET NAME='serial_num' WHERE name LIKE '%serial_num';

########################################################################
# 2021-02-26 - Added settings default_sales_terms_id, default_delivery_terms_id to joor2NzoomCustomerSync

# Added settings default_sales_terms_id, default_delivery_terms_id to joor2NzoomCustomerSync
DELETE FROM automations where id=48;
UPDATE automations
SET settings=REPLACE(settings, "default_sales_terms_id := 224", "default_sales_terms_id := 224\r\ndefault_delivery_terms_id := 3436")
WHERE method like '%joor2NzoomCustomerSync%' AND settings NOT LIKE '%default_delivery_terms_id%';

########################################################################
# 2021-03-09 - Fixed default ajur EIK for nzoom2AJURSync automations

# Fixed default ajur EIK for nzoom2AJURSync automations
UPDATE `automations` SET `settings`=REPLACE(`settings`, '999999999999999', '999999999') WHERE `settings` LIKE '%999999999999999%' AND `method` LIKE '%nzoom2AJURSync%';

########################################################################
# 2021-03-10 - Added producer_address as column for the contract pattern plugin

# Added producer_address as column for the contract pattern plugin
UPDATE `patterns_plugins`
SET `settings`='extend_article_vars := producer_name, producer_address, hs_name, composition, lining, upper, sole, curr_jpy__retail, curr_eur__retail, curr_usd__retail, curr_gbp__retail, curr_aud__retail, curr_cny__retail, curr_cad__retail'
WHERE `method`='prepareContract';

########################################################################
# 2021-04-08 - Added automation to sync the metafield "colors" in Shopify defining the relations in models
#            - Added crontab automation to fix the metafield "colors" in Shopify for the existing products (for one time use only, you can change the start_mdoel_type to fix different type of nomenclatures)

# Added automation to sync the metafield "colors" in Shopify defining the relations in models
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT REPLACE(atm.name, 'nZoom to Shopify Sync', 'nZoom to Shopify Metafield Colors Sync'), 0, NULL, 0, 'nomenclatures', NULL, 'action', atm.start_model_type, '', 'condition := \'[action]\' == \'add\' || \'[action]\' == \'edit\'', 'method := nzoom2ShopifyRelativesSync\r\nplugin := byfar', NULL, 101, 0, atm.active FROM automations atm WHERE method LIKE '%nzoom2shopify%' AND automation_type='action'
    AND NOT EXISTS(SELECT id FROM automations WHERE start_model_type = atm.start_model_type AND method LIKE '%nzoom2ShopifyRelativesSync%' AND automation_type='action');

# Added crontab automation to fix the metafield "colors" in Shopify for the existing products (for one time use only, you can change the start_mdoel_type to fix different type of nomenclatures)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'nZoom to Shopify Metafield Colors Sync (fixing colors, for one type sync only)', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', '', 'where := n.id IN (SELECT tmp.model_id FROM (SELECT nc1.model_id,  nc1.value FROM nom_cstm nc1 JOIN _fields_meta fm1 ON nc1.var_id=fm1.id AND fm1.name="model_name_id" AND fm1.model="Nomenclature" AND nc1.value!="" JOIN _fields_meta fm2 ON fm2.name="sh_nz_website_id" AND fm2.model="Nomenclature" AND fm1.model_type=fm2.model_type JOIN nom_cstm nc2 ON nc2.var_id=fm2.id AND nc1.model_id=nc2.model_id AND nc2.VALUE!="" GROUP BY nc1.value) as tmp)\r\n', 'method := nzoom2ShopifyRelativesSync\r\nplugin := byfar', NULL, 101, 1, 0
      WHERE NOT EXISTS(SELECT id FROM automations WHERE  method LIKE '%nzoom2ShopifyRelativesSync%' AND automation_type='crontab');

########################################################################
# 2021-04-14 - Added no vat reason for all the documents, contracts, revenue documents and invoice templates

# Added no vat reason for all the documents, contracts, revenue documents and invoice templates
UPDATE
    contracts co
        JOIN customers_cstm cc
        ON co.customer=cc.model_id AND cc.var_id=8449 AND cc.lang='' AND cc.num=1 AND cc.VALUE!=''
        JOIN contracts_cstm coc
        ON coc.model_id=co.id AND coc.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_vat_rate' AND model='Contract') AND coc.lang='' AND coc.num=1 AND coc.VALUE=0
        JOIN contracts_cstm coc2
        ON coc2.model_id=co.id AND coc2.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_no_vat_reason' AND model='Contract') AND coc2.lang='' AND coc2.num=1 AND coc2.VALUE=''
SET coc2.VALUE=IF(cc.VALUE=219,20455,20454);

UPDATE
    contracts co
        JOIN customers_cstm cc
        ON co.customer=cc.model_id AND cc.var_id=8449 AND cc.lang='' AND cc.num=1 AND cc.VALUE!=''
        JOIN contracts_cstm coc
        ON coc.model_id=co.id AND coc.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_vat_rate' AND model='Contract') AND coc.lang='' AND coc.num=1 AND coc.VALUE=0
        JOIN contracts_cstm coc2
        ON coc2.model_id=co.id AND coc2.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_no_vat_reason_text' AND model='Contract') AND coc2.lang='en' AND coc2.num=1  AND coc2.VALUE=''
SET coc2.VALUE=IF(cc.VALUE=219,(SELECT name FROM nom_i18n where parent_id=20455 AND lang='en'),(SELECT name FROM nom_i18n where parent_id=20454 AND lang='en'));

UPDATE
    documents d
        JOIN customers_cstm cc
        ON d.customer=cc.model_id AND d.TYPE IN (1, 5, 7, 15, 16) AND cc.var_id=8449 AND cc.lang='' AND cc.num=1 AND cc.VALUE!=''
        JOIN documents_cstm dc
        ON dc.model_id=d.id AND dc.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_vat_rate' AND model='Document') AND dc.lang='' AND dc.num=1 AND dc.VALUE=0
        JOIN documents_cstm dc2
        ON dc2.model_id=d.id AND dc2.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_no_vat_reason' AND model='Document') AND dc2.lang='' AND dc2.num=1 AND dc2.VALUE=''
SET dc2.VALUE=IF(cc.VALUE=219, 20455, 20454);

UPDATE
    documents d
        JOIN customers_cstm cc
        ON d.customer=cc.model_id AND d.TYPE IN (1, 5, 7, 15, 16) AND cc.var_id=8449 AND cc.lang='' AND cc.num=1 AND cc.VALUE!=''
        JOIN documents_cstm dc
        ON dc.model_id=d.id AND dc.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_vat_rate' AND model='Document') AND dc.lang='' AND dc.num=1 AND dc.VALUE=0
        JOIN documents_cstm dc2
        ON dc2.model_id=d.id AND dc2.var_id IN (SELECT id FROM _fields_meta WHERE NAME='total_no_vat_reason_text' AND model='Document') AND dc2.lang='en' AND dc2.num=1 AND dc2.VALUE=''
SET dc2.VALUE=IF(cc.VALUE=219,(SELECT name FROM nom_i18n where parent_id=20455 AND lang='en'),(SELECT name FROM nom_i18n where parent_id=20454 AND lang='en'));

UPDATE
    fin_invoices_templates fit
        JOIN customers_cstm cc
        ON fit.customer=cc.model_id AND cc.var_id=8449 AND cc.lang='' AND cc.num=1 AND cc.VALUE!=''
        JOIN fin_invoices_templates_i18n fiti
        ON fiti.parent_id=fit.id AND fiti.lang='en'
SET fit.total_no_vat_reason=IF(cc.VALUE=219,20455,20454),
    fiti.total_no_vat_reason_text=IF(cc.VALUE=219,(SELECT name FROM nom_i18n where parent_id=20455 AND lang='en'),(SELECT name FROM nom_i18n where parent_id=20454 AND lang='en'))
WHERE fit.total_vat_rate=0;

UPDATE
    fin_incomes_reasons fir
        JOIN customers_cstm cc
        ON fir.customer=cc.model_id AND cc.var_id=8449 AND cc.lang='' AND cc.num=1 AND cc.VALUE!=''
        JOIN fin_incomes_reasons_i18n firi
        ON firi.parent_id=fir.id AND firi.lang='en'
        JOIN fin_incomes_reasons_i18n firi2
        ON firi2.parent_id=fir.id AND firi2.lang='bg'
SET fir.total_no_vat_reason=IF(cc.VALUE=219,20455,20454),
    firi.total_no_vat_reason_text=IF(cc.VALUE=219,(SELECT name FROM nom_i18n where parent_id=20455 AND lang='en'),(SELECT name FROM nom_i18n where parent_id=20454 AND lang='en')),
    firi2.total_no_vat_reason_text=IF(cc.VALUE=219,(SELECT name FROM nom_i18n where parent_id=20455 AND lang='bg'),(SELECT name FROM nom_i18n where parent_id=20454 AND lang='bg'))
WHERE fir.total_vat_rate=0;

########################################################################
# 2021-06-08 - Added new automation for notfifying for upcoming deadlines of invoices 5 days before the deadline date
#            - Added additional placeholder for the new automation

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Изпращане на известия за предстоящ падеж на фактури', 0, NULL, 1, 'finance_incomes_reasons', '', 'crontab', '1', 'start_time := 01:00\r\nstart_before := 20:00\r\n\r\ninvoice_interval_days := 5\r\nincluded_noms := 16937,16935,11042,4639,4638,4637,3438,230,229,228\r\ncontract_sale_var := sales_terms\r\n\r\nnotification_template_id := 1003\r\n\r\nsend_to_address := <EMAIL>\r\nsend_to_name :=\r\n\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := byfar\r\nmethod := sendInvoiceNotifications', NULL, 1, 0, 1
      WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%sendInvoiceNotifications%');

# Added no vat reason for all the documents, contracts, revenue documents and invoice templates
INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang)
  SELECT 'upcoming_invoice_deadlines_list', 'Finance_Incomes_Reason', 'send', 'emails', ',1003,', '', 0
      WHERE NOT EXISTS(SELECT id FROM placeholders WHERE varname LIKE '%upcoming_invoice_deadlines_list%' AND model='Finance_Incomes_Reason' AND `pattern_id` LIKE '%,1003,%');
SET @id := (SELECT id FROM placeholders WHERE varname LIKE '%upcoming_invoice_deadlines_list%' AND model='Finance_Incomes_Reason' AND `pattern_id` LIKE '%,1003,%');
INSERT IGNORE INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (@id, 'Списък с фактури с предстоящ падеж', NULL, 'bg'),
  (@id, 'List with invoices with upcoming payment dates', NULL, 'en');

########################################################################
# 2021-06-24 - Replaced the before_action generateBarcode with action setBarcode to have more control on the barcode generation

# Replaced the before_action generateBarcode with action setBarcode to have more control on the barcode generation
UPDATE automations
SET method=REPLACE(method, 'generateBarcode', 'setBarcode'),
    conditions="condition := in_array('[action]', array('add', 'edit')) && '[a_info_barcode]' == ''",
    automation_type='action'
WHERE method like '%generateBarcode%';

########################################################################
# 2021-07-13 - Added REST settings for Accessories, Belts, Eyewear, Fragrances, Hats and Scarves

# Added REST settings for Accessories, Belts, Eyewear, Fragrances, Hats and Scarves
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_nomenclatures_49', 'all'),
 ('rest', 'filter_vars_nomenclatures_50', 'all'),
 ('rest', 'filter_vars_nomenclatures_51', 'all'),
 ('rest', 'filter_vars_nomenclatures_55', 'all'),
 ('rest', 'filter_vars_nomenclatures_61', 'all'),
 ('rest', 'filter_vars_nomenclatures_64', 'all');


########################################################################
# 2021-08-03 - Expand the automation that sync the orders from JOOR with fragrances

# Expand the automation that sync the orders from JOOR with fragrances
UPDATE `automations` SET `settings`=REPLACE(settings, 'draft_order_accessories_type := 16', 'draft_order_accessories_type := 16\r\ndraft_order_fragrances_type := 17')
WHERE  method like '%joor2NzoomOrderSync%' AND settings NOT LIKE '%draft_order_fragrances_type%';
UPDATE `automations` SET `settings`=REPLACE(settings, 'draft_order_hbs_type := 15\r\ndraft_order_accessories_type := 16', 'draft_order_accessories_type := 15')
WHERE  method like '%joor2NzoomOrderSync%' AND settings LIKE '%draft_order_hbs_type%';

########################################################################
# 2021-09-05 - Added new automation updateRelatedNomenclatures to update related nomenclatures from certain types

# Added new automation updateRelatedNomenclatures to update related nomenclatures from certain types
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Обновяване на данни в свързани номенклатури', 0, NULL, 1, 'nomenclatures', NULL, 'action', '0', 'nom_types_to_change := 5,32,49,51,55,61,64\r\n\r\nuse_updated_model := 1\r\n\r\nsource_nom_product_id := lth_prod_id\r\nsource_nom_product_name := lth_prod\r\n\r\ntarget_nom_material_id := material_id\r\ntarget_nom_material_name := material_name\r\ntarget_nom_product_id := prod_name_id\r\ntarget_nom_product_name := prod_name\r\ntarget_nom_price := prod_price', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[request_is_post]\' == \'1\'\r\ncondition := \'[prev_a_lth_prod_id]\' != \'[a_lth_prod_id]\' || \'[prev_b_name]\' != \'[b_name]\' || \'[prev_b_sell_price]\' != \'[b_sell_price]\'\r\ncondition := in_array(\'[b_type]\', array(\'19\',\'16\',\'20\'))', 'plugin := byfar\r\nmethod := updateRelatedNomenclatures', NULL, 10, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%updateRelatedNomenclatures%' AND `method` LIKE '%byfar%');

########################################################################
# 2021-09-13 - Update settings for createShoesFile automation

# Update settings for createShoesFile automation
UPDATE `automations` SET `settings`=REPLACE(settings, '\r\n\r\nleather_surface_yes :=', '\r\nleather := 19\r\n\r\nleather_surface_yes :=')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\r\nleather :=%';
UPDATE `automations` SET `settings`=REPLACE(settings, '\n\nleather_surface_yes :=', '\nleather := 19\n\nleather_surface_yes :=')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\nleather :=%';

UPDATE `automations` SET `settings`=CONCAT(settings, '\r\n\r\nleather_scientific_name := scientific_name\r\nleather_upper_type := raw_type\r\nleather_country := country_origin')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\r\nleather_upper_type :=%' AND `settings` LIKE '%\r\n%';
UPDATE `automations` SET `settings`=CONCAT(settings, '\n\nleather_scientific_name := scientific_name\nleather_upper_type := raw_type\nleather_country := country_origin')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\nleather_upper_type :=%' AND `settings` NOT LIKE '%\r\n%';

########################################################################
# 2021-09-30 - Added barcode generator settings for ByFar labels

# Added barcode generator settings for ByFar labels
UPDATE `patterns_plugins` SET `settings`='# Tec-IT barcode generator\r\n#barcode_generator_url := https://barcode.tec-it.com/barcode.ashx?data=%s&code=EAN13&multiplebarcodes=false&translate-esc=false&unit=Mm&dpi=220&imagetype=Png&rotation=0&qunit=Mm&quiet=0&modulewidth=0.382\r\n\r\n# BGS barcode generator\r\nbarcode_generator_url := https://bg.n-zoom.com/generate?data=%s&type=EAN_13&height=150&width=2&format=png&includetext=' WHERE `folder`='byfar' AND `method`='prepareLabels' AND (`settings` IS NULL OR `settings` NOT LIKE '%barcode%');
UPDATE `patterns_plugins` SET `settings`='# Tec-IT barcode generator\r\n# barcode_generator_url := https://barcode.tec-it.com/barcode.ashx?data=%s&code=EAN13&multiplebarcodes=false&translate-esc=false&unit=Mm&dpi=220&imagetype=Png&rotation=0&qunit=Mm&quiet=0&modulewidth=0.382\r\n\r\n# BGS barcode generator\r\nbarcode_generator_url := https://bg.n-zoom.com/generate?data=%s&type=EAN_13&height=150&width=2&format=png&includetext=' WHERE `folder`='byfar' AND `method`='prepareLabelsA4' AND (`settings` IS NULL OR `settings` NOT LIKE '%barcode%');
UPDATE `patterns_plugins` SET `settings`='# Tec-IT barcode generator\nbarcode_generator_url := https://barcode.tec-it.com/barcode.ashx?data=%s&code=Code128&multiplebarcodes=false&translate-esc=false&unit=Mm&dpi=96&imagetype=Png&rotation=0&qunit=Mm&quiet=0\n\n# BGS barcode generator\n# barcode_generator_url := https://bg.n-zoom.com/generate?data=%s&type=CODE_128&height=150&width=2&format=png&includetext=' WHERE `folder`='byfar' AND `method`='preparePackingLabels' AND (`settings` IS NULL OR `settings` NOT LIKE '%barcode%');

########################################################################
# 2021-10-07 - Added details_tab, weight_total as columns for the contract pattern plugin

# Added details_tab, weight_total as columns for the contract pattern plugin
UPDATE `patterns_plugins`
SET `settings`='extend_article_vars := producer_name, producer_address, hs_name, composition, lining, upper, sole, curr_jpy__retail, curr_eur__retail, curr_usd__retail, curr_gbp__retail, curr_aud__retail, curr_cny__retail, curr_cad__retail,details_tab,weight_total'
WHERE `method`='prepareContract';

########################################################################
# 2021-10-12 - Update settings for createShoesFile automation

# Update settings for createShoesFile automation
UPDATE `automations` SET `settings`=REPLACE(settings, '\r\n\r\noption_ankle_height_ankle_or_higher :=', '\r\nshoe_material := material_id\r\n\r\noption_ankle_height_ankle_or_higher :=')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\r\nshoe_material :=%';
UPDATE `automations` SET `settings`=REPLACE(settings, '\n\noption_ankle_height_ankle_or_higher :=', '\nshoe_material := material_id\n\noption_ankle_height_ankle_or_higher :=')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\nshoe_material :=%';

########################################################################
# 2021-10-15 - Added settings for manual filters by date: filter_date_start, filter_date_end

# Expand the automation that sync the orders from JOOR with fragrances
UPDATE `automations` SET `settings`=CONCAT(settings, '\r\n# joor filter default is the last sync date, format: YY-mm-dd HH:mm:ss\r\r#filter_date_start := \r\n# default is current datetime, same format\r\r#filter_date_end :=\r\n')
WHERE  method like '%joor2NzoomOrderSync%' AND settings NOT LIKE '%filter_date_start%';

########################################################################
# 2021-11-02 - Added automation that returns commodities for credit not with certain tag

# Added automation that returns commodities for credit not with certain tag
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Приемателен протокол за връщане на стоки от кредитно известие', 0, NULL, 1, 'finance', 'incomes_reasons', 'before_action', '3', '', 'condition := in_array(\'[action]\', array(\'tag\'))\r\ncondition := in_array(93, $request->get(\'tags\'))', 'method := addCreditNoteHandover\r\nplugin := byfar\r\n', 'cancel_action_on_fail := 1', 0, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%addCreditNoteHandover%');

########################################################################
# 2021-11-19 - Added log_stats option in some of the crontab automations
#            - Added new settings for createShoesFile automation
#            - Added placeholder for sole country
#            - Added placeholder for sole source

# Added log_stats option in some of the crontab automations
UPDATE automations SET settings=CONCAT(settings, '\nlog_stats := \n')
WHERE (
    method like '%shopify2NzoomMetaFieldsSync%' OR
    method like '%nzoom2JoorNomSync%' OR
    method like '%joor2NzoomCustomerSync%' OR
    method like '%joor2NzoomOrderSync%'
    )
  AND settings NOT LIKE '%log_stats%';

# Added new settings for createShoesFile automation
UPDATE `automations` SET `settings`=REPLACE(settings, '\r\n\r\noption_ankle_height_ankle_or_higher :=', '\r\nshoe_component := shoe_components\r\nshoe_component_upper := Upper\r\nshoe_component_linear := Lining\r\nshoe_material_calfskin := 14551\r\n\r\noption_ankle_height_ankle_or_higher :=')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\nshoe\_material\_gulfskin :=%';
UPDATE `automations` SET `settings`=REPLACE(settings, '\n\noption_ankle_height_ankle_or_higher :=', '\nshoe_component := shoe_components\nshoe_component_upper := Upper\nshoe_component_linear := Lining\nshoe_material_calfskin := 14551\n\noption_ankle_height_ankle_or_higher :=')
WHERE  method like '%createShoesFile%' AND settings NOT LIKE '%\nshoe\_material\_gulfskin :=%';

# Added placeholder for sole country
INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang)
SELECT 'sole_country', 'Document', 'send', 'patterns', ',3,', '', 0
    WHERE NOT EXISTS(SELECT id FROM placeholders WHERE varname LIKE '%sole_country%' AND model='Document' AND `pattern_id` LIKE '%,3,%');
SET @id := (SELECT id FROM placeholders WHERE varname LIKE '%sole_country%' AND model='Document' AND `pattern_id` LIKE '%,3,%');
INSERT IGNORE INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (@id, 'Подметка произход', NULL, 'bg'),
  (@id, 'Sole origin country', NULL, 'en');

# Added placeholder for sole source
INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang)
SELECT 'sole_source', 'Document', 'send', 'patterns', ',3,', '', 0
    WHERE NOT EXISTS(SELECT id FROM placeholders WHERE varname LIKE '%sole_source%' AND model='Document' AND `pattern_id` LIKE '%,3,%');
SET @id := (SELECT id FROM placeholders WHERE varname LIKE '%sole_source%' AND model='Document' AND `pattern_id` LIKE '%,3,%');
INSERT IGNORE INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (@id, 'Подметка източник', NULL, 'bg'),
  (@id, 'Sole source', NULL, 'en');

########################################################################
# 2022-01-11 - Added new 'byfar_sales_status_report' report

# Added new 'byfar_sales_status_report' report
SET @id := 435;

INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(@id, 'byfar_sales_status_report', 'cust_type_customer := 3\r\nnom_type_article := 32,50,52,64,61,49,53,51,54,55,60,5,8\r\nnom_type_season := 27\r\ncon_type_order := 1\r\ndoc_type_fulfillment := 5 \r\n\r\ncon_order_type := order_type\r\ncon_po_number := po_number\r\ncon_shipping_address := shipping_address\r\ncon_account_manager := account_manager\r\ncon_warehouse := warehouse\r\ncon_season := season_name_id\r\ncon_year := year_on_sale\r\ncon_delivery_date := delivery_date__to\r\n\r\nnom_var_color := client_color_id\r\nnom_var_model := model_name_id\r\nnom_var_sku_category := sku_category_id\r\nnom_var_material := basic_material_name', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(@id, 'Справка по продажби и поръчки', NULL, NULL, 'bg'),
(@id, 'Sales report/Order status report', NULL, NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', @id, 0, 1),
(NULL, 'reports', '', 'export', @id, 0, 2);

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = @id;

######################################################################################
# 2022-01-12 - Added new setting for parent product in 'byfar_sales_status_report' report

# Added new setting for parent product in 'byfar_sales_status_report' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\nnom_var_parent_product := product_id')
WHERE `type` = 'byfar_sales_status_report' AND `settings` NOT LIKE '%nom\_var\_parent\_product%';

######################################################################################
# 2022-02-04 - Added new setting for customer tag for account manager in 'byfar_sales_status_report' report
#            - Added new settings for fullfilment status in 'byfar_sales_status_report' report

# Added new setting for customer tag for account manager in 'byfar_sales_status_report' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\nnom_type_article :=', '\r\ncust_tag_account_manager := 101\r\nnom_type_article :=')
WHERE `type` = 'byfar_sales_status_report' AND `settings` NOT LIKE '%cust\_tag\_account\_manager%';

# Added new settings for fullfilment status in 'byfar_sales_status_report' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ndoc_substatus_transffered := 1\r\ndoc_substatus_not_shipped := 6,5,4')
WHERE `type` = 'byfar_sales_status_report' AND `settings` NOT LIKE '%doc\_substatus\_transffered%';

######################################################################################
# 2022-02-25 - Fix contracts, invoices, templates and documents

DROP VIEW IF EXISTS tmp_gt2;
CREATE VIEW tmp_gt2 AS
SELECT g.id
     , g2.id                                  AS id2
     , g.model
     , g.model_id
     , g.article_alternative_deliverer
     , g.article_deliverer
     , g2.article_alternative_deliverer       AS article_alternative_deliverer2
     , g2.article_deliverer                   AS article_deliverer2
     , gibg.article_alternative_deliverer_name as article_alternative_deliverer_name_bg
     , gibg.article_deliverer_name as article_deliverer_name_bg
     , gien.article_alternative_deliverer_name as article_alternative_deliverer_name_en
     , gien.article_deliverer_name as article_deliverer_name_en
     , gi2bg.article_alternative_deliverer_name AS article_alternative_deliverer_name2_bg
     , gi2bg.article_deliverer_name             AS article_deliverer_name2_bg
     , gi2en.article_alternative_deliverer_name AS article_alternative_deliverer_name2_en
     , gi2en.article_deliverer_name             AS article_deliverer_name2_en
FROM gt2_details g
JOIN gt2_details g2
  ON g.model_id = g2.model_id AND g.model = g2.model AND g2.article_alternative_deliverer != 0 AND
     g.article_alternative_deliverer = 0 and g.article_deliverer = 0 AND g.id != g2.id
LEFT JOIN gt2_details_i18n gibg
  ON gibg.parent_id = g.id AND gibg.lang='bg'
LEFT JOIN gt2_details_i18n gien
  ON gien.parent_id = g.id AND gien.lang='en'
LEFT JOIN gt2_details_i18n gi2bg
     ON gi2bg.parent_id = g2.id AND gi2bg.lang='bg'
LEFT JOIN gt2_details_i18n gi2en
     ON gi2en.parent_id = g2.id AND gi2en.lang='en'
GROUP BY g.id;

-- SELECT * FROM tmp_gt2;

UPDATE
    gt2_details g
JOIN tmp_gt2 t
    ON g.id=t.id
LEFT JOIN gt2_details_i18n gibg
    ON g.id=gibg.parent_id AND gibg.lang='bg'
LEFT JOIN gt2_details_i18n gien
    ON g.id=gien.parent_id AND gien.lang='en'
SET g.article_alternative_deliverer=t.article_alternative_deliverer2, g.article_deliverer=t.article_deliverer2,
    gibg.article_alternative_deliverer_name=t.article_alternative_deliverer_name2_bg, gibg.article_deliverer_name=t.article_deliverer_name2_bg,
    gien.article_alternative_deliverer_name=t.article_alternative_deliverer_name2_en, gien.article_deliverer_name=t.article_deliverer_name2_en;

DROP VIEW IF EXISTS tmp_gt2;

######################################################################################
# 2022-02-28 - Fixed the autocompleter article_alternative_deliverer_name for invoices

# Fixed the autocompleter article_alternative_deliverer_name for invoices
UPDATE gt2_details g
JOIN gt2_details_i18n gi
  ON g.model='Finance_Incomes_Reason' AND g.id=gi.parent_id AND gi.lang='bg'
JOIN gt2_details_i18n gi2
  ON g.id=gi2.parent_id AND gi2.lang='en'
JOIN fin_incomes_reasons fir
  ON fir.id=g.model_id AND fir.`type` IN (1,2,3,4)
JOIN contracts co
  ON co.id=g.article_alternative_deliverer
SET gi.article_alternative_deliverer_name=co.num
WHERE gi.article_alternative_deliverer_name!='' AND gi2.article_alternative_deliverer_name!='';

UPDATE _fields_meta
SET source='text_align := left
permissions_edit := 1
permissions_view := 1
autocomplete := contracts
autocomplete_search := <num>, <customer>
autocomplete_suggestions := [<code>] <name> <lastname>
autocomplete_fill_options := $article_alternative_deliverer => <id>
autocomplete_fill_options := $article_alternative_deliverer_name => <num>'
WHERE name='article_alternative_deliverer_name' AND
        model='Finance_Incomes_Reason' AND model_type IN (1,2,3,4);

########################################################################
# 2022-05-24 - Added new automation which will create handovers from expesnese reasons based on DVRs

# Added automation to receive information of Delivery Documents from Enola
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Add incoming handover from delivery invoice', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', '20', 'document_dvr_type_id := 4\r\ndocument_dvr_warehouse := warehouse', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[prev_b_status]\' != \'finished\' && \'[b_status]\' == \'finished\'', 'plugin := byfar\r\nmethod := addIncomingHandoverFromInvoice', NULL, 1, 1, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%addIncomingHandoverFromInvoice%');

########################################################################
# 2022-05-25 - Fixed conditions for execution of addIncomingHandoverFromInvoice automation

# Fixed conditions for execution of addIncomingHandoverFromInvoice automation
UPDATE `automations` SET `conditions` = 'condition := \'[request_is_post]\' && \'[prev_b_status]\' != \'finished\' && \'[b_status]\' == \'finished\'' WHERE method LIKE '%addIncomingHandoverFromInvoice%' AND method LIKE '%plugin := byfar%' AND `conditions` LIKE '%multistatus%';

######################################################################################
# 2022-06-03 - Added new automation for issuing of reservation protocols from fulfillments
#            - Added fulfillment cancelation automations

# Added new automation for issuing of reservation protocols from fulfillments
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Creates Commodity reservation when invoicing the Fulfillment', 20, NULL, 1, 'documents', NULL, 'before_action', '5', 'warehouse_var := warehouse\nfound_defects_var := free_field5\ncontract_var := article_alternative_deliverer\n\nreservation_period := 1 month\n\ncon_type_order_reserve := 1\ndoc_type_request := 3\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'opened_4\' \r\n#&& \'[prev_b_substatus]\' != \'4\' && \'[a_warehouse]\' > 0\r\n', 'plugin := byfar\nmethod := addCommoditiesReservation', 'cancel_action_on_fail := 1', 10, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%addCommoditiesReservation%' AND `start_model_type`=5);

# Add fulfillment cancelation automations
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Cancel Fulfillment (release reserved commodities, credit invoice, return commodities)', 0, NULL, 1, 'documents', NULL, 'before_action', '5', '#SANDBOX\r\n#enola_api_url := http://**************:9001\r\n#enola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\n#PRODUCTION\r\nenola_api_url := http://**************:9000\r\nenola_api_token := DFEF30FA-B34B-4569-8D80-A8D8095BAF45\r\nsend_to_email := <EMAIL>\r\nsend_to_notifications := errors, warnings\r\n\r\nlog_requests :=\r\n\r\n#test mode should be set on sandbox/test environments\r\n#test_mode := 1', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $registry->get(\'request\')->get(\'substatus\') == \'closed_7\' && \'[prev_b_substatus]\' != \'closed_7\'', 'plugin := byfar\r\nmethod := cancelFulfillment', 'cancel_action_on_fail := 1', 11, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%cancelFulfillment%' AND `start_model_type`=5);

# removed the nzoom2EnolaCancelFulfillment (it is already part of cancelFulfillment)
DELETE FROM automations WHERE method like '%nzoom2EnolaCancelFulfillment%';

# deactivated automations that reserve commodities from DVR, contract (Confirmed B2BOrder) and expense reasons
UPDATE automations SET active=0 WHERE method like '%addCommoditiesReservation%' AND `start_model_type`!=5;

UPDATE automations SET settings=REPLACE(settings, 'con_type_order_handover', 'con_type_order') WHERE settings like '%con_type_order_handover%';
UPDATE automations SET settings=REPLACE(settings, 'con_type_order_reserve', 'con_type_order') WHERE settings like '%con_type_order_reserve%';

######################################################################################
# 2022-06-03 - Changed the name of the method that creates Commodity Reservation of Fulfillment

# Changed the name of the method that creates Commodity Reservation of Fulfillment to addCommoditiesReservationFromFulfillment (ex addCommoditiesReservation)
UPDATE automations SET method=REPLACE(method, 'addCommoditiesReservation', 'addCommoditiesReservationFromFulfillment') WHERE method like '%addCommoditiesReservation' and start_model_type=5 and module='documents';

######################################################################################
# 2022-07-01 - Added direct_invoice variable and automation to set it

# Added direct_invoice variable and automation to set it
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
  (77, 'Contract', 1, 'direct_invoice', 'checkbox_group', '', 0, 1, NULL, NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2953, 26, '220', '', '');
INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
  (77, 'label', 'Издай директно фактура', 'bg'),
  (77, 'label', 'Direct invoice', 'en');
INSERT IGNORE INTO `_fields_options` (`id`, `parent_name`, `label`, `option_value`, `child_name`, `extended_value`, `position`, `active_option`, `lang`) VALUES
(221, 'direct_invoice', '', '1', '', '', 1, 1, 'bg'),
(222, 'direct_invoice', '', '1', '', '', 1, 1, 'en');
INSERT IGNORE INTO `contracts_cstm` (`model_id`, `var_id`, `num`, `value`, `formula`, `index`, `index_date`, `index_formula`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
  SELECT id, 77, 1, '0', NULL, NULL, NULL, NULL, added, added_by, modified, modified_by, '' FROM contracts where type=1;

 INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
   SELECT 'Задаване на вид фактура/проформа в договор', 0, NULL, 1, 'contracts', NULL, 'action', '1', '', 'condition := in_array(\'[action]\', array(\'add\', \'addannex\', \'edit\', \'edittopic\', \'setstatus\'))\r\n', 'method := setAdditionalVar\r\nvar_name := direct_invoice\r\nvar_value := php(Calculator::calc_sql($registry, \'SELECT IF((SELECT 1 AS proforma FROM tags_models WHERE model="Customer" AND tag_id=105 AND model_id="[b_customer]") IS NULL, 1, 0)\'))', NULL, 5, 0, 1
     WHERE NOT EXISTS (SELECT id FROM automations WHERE module='contracts' AND `method` LIKE '%setAdditionalVar%' AND `start_model_type`=1 AND method like '%direct_invoice%');

######################################################################################
# 2022-07-18 - Set value of direct_invoice to be 1 for all the contracts

# Set value of direct_invoice to be 1 for all the contracts
INSERT IGNORE INTO `contracts_cstm` (`model_id`, `var_id`, `num`, `value`, `formula`, `index`, `index_date`, `index_formula`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
  SELECT id, 77, 1, '1', NULL, NULL, NULL, NULL, added, added_by, modified, modified_by, '' FROM contracts where type=1;
UPDATE contracts_cstm SET VALUE=1 WHERE var_id=77 AND VALUE=0;
# Set position of automation setting direct_invoice
UPDATE automations SET position=1 WHERE module='contracts' AND `method` LIKE '%setAdditionalVar%' AND `start_model_type`=1 AND method like '%direct_invoice%';

######################################################################################
# 2022-09-20 - Added automation to generate fragrance sheet file

# Added automation to generate fragrance sheet file
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Create Sheet for Fragrances', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', '61', 'generate_pattern := 53\r\nfile_var := file_sheet\r\n', 'condition := $request->get(\'create_sheet\')', 'plugin := byfar\r\nmethod := createFragranceFile', NULL, 8, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE module='nomenclatures' AND `method` LIKE '%createFragranceFile%' AND `start_model_type`=61);

######################################################################################
# 2022-10-11 - Added new setting for customer region in 'byfar_sales_status_report' report

# Added new setting for customer region in 'byfar_sales_status_report' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\nnom_var_color :=', '\r\n\r\ncust_region := region_info\r\nnom_var_color :=')
WHERE `type` = 'byfar_sales_status_report' AND `settings` NOT LIKE '%cust\_region%';

######################################################################################
# 2022-11-15 - Added new setting for return warehouse in addCreditNoteHandover automation

# Added new setting for return warehouse in addCreditNoteHandover automation
UPDATE automations SET settings="return_warehouse_id := 17" WHERE `method` LIKE '%addCreditNoteHandover%' AND `settings` NOT LIKE '%return_warehouse_id%';

######################################################################################
# 2022-12-02 - Added automation completeFullTextRecipe to generate text recipe in nomenclatures
#            - Update the buttons to make them not perform overwriting of the recipe text

# Added automation completeFullTextRecipe to generate text recipe in nomenclatures
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Генриране на текст на рецепта', 0, NULL, 1, 'nomenclatures', NULL, 'action', '0', 'group_table_content := reception_group\r\nsearched_class := product_list\r\n\r\ncomplete_field := product_all\r\nseparator := ,\r\n', 'condition := in_array(\'[action]\', array(\'add\', \'edit\'))\r\ncondition := in_array(\'[b_type]\', array(\'5\',\'32\',\'48\',\'49\',\'50\',\'51\',\'55\',\'56\',\'61\',\'64\'))', 'plugin := byfar\r\nmethod := completeFullTextRecipe', NULL, 200, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE module='nomenclatures' AND `method` LIKE '%completeFullTextRecipe%' AND `start_model_type`=0);

# Update the buttons to make them not perform overwriting of the recipe text
UPDATE `_fields_meta` SET `source`=CONCAT('#', `source`) WHERE `model`='Nomenclature' AND `name`='button_product' AND `type`='button' AND `source` LIKE '%prod.getValue%';

######################################################################################
# 2022-12-29 - Added new settings for SMTP oauth2

# Added new settings for SMTP oauth2
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
   ('smtp', 'oauth2_provider', 'Azure'),
   ('smtp', 'oauth2_client_id', 'd58e9af7-9182-46ac-a8b8-0c45192d631d'),
   ('smtp', 'oauth2_client_secret', '****************************************'),
   ('smtp', 'oauth2_tenant_id', 'bae42f6f-61bd-4aa8-9c81-c5c24ab201a9'),
   ('smtp', 'oauth2_refresh_token', '*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')
 ON DUPLICATE KEY UPDATE
   `value` = VALUES(`value`);

######################################################################################
# 2023-01-03 - Fixed the conditions and the composion of name in the autonmation that set the title of a product

# Fixed the conditions and the composion of name in the autonmation that set the title of a product
UPDATE automations SET conditions=REPLACE(conditions, "&& '[b_name]' == '[a_model_name]'", "&& trim('[b_name]') == trim('[a_model_name]')")
WHERE conditions LIKE "%&& '[b_name]' == '[a_model_name]'%";

UPDATE automations
SET method=REPLACE(method, 'SELECT CONCAT(nc1.value," ",nc2.value," ",nc3.value)', 'SELECT TRIM(CONCAT(nc1.value," ",nc2.value," ",nc3.value))')
WHERE method LIKE '%SELECT CONCAT(nc1.value," ",nc2.value," ",nc3.value)%';

UPDATE automations
SET method=REPLACE(method, 'SELECT CONCAT(nc1.value," ",nc2.value)', 'SELECT TRIM(CONCAT(nc1.value," ",nc2.value))')
WHERE method LIKE '%SELECT CONCAT(nc1.value," ",nc2.value)%';

######################################################################################
# 2023-01-17 - Corrected position of the completeFullTextRecipe automation

# Corrected position of the completeFullTextRecipe automation
UPDATE automations SET `position`=90
WHERE `method` LIKE '%completeFullTextRecipe%' AND `position`=200;
