########################################################################################
### SQL nZoom Specific Updates - Биотрейд България (http://nzoom.biotrade.bg/) ###
########################################################################################

########################################################################
# 2012-07-20 - Added dashlet plugin for quick adding of expenses reports documents for Biotrade Bulgaria installation (1792)

# Added dashlet plugin for quick adding of expenses reports documents for Biotrade Bulgaria installation (1792)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'biotrade_add_expenses_report', 'document_type := 6\r\n\r\nexpenses_fuel  := 6\r\nexpenses_tickets := 7\r\nexpenses_fees := 8\r\nexpenses_parking := 9\r\nexpenses_trip_expenses := 10\r\nexpenses_hotel := 11\r\nexpenses_food := 12\r\nexpenses_other := 13\r\n', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дневен отчет', 'Инфо панел, предназначен за бързо добавяне на дневен отчет за разходи', 'bg'),
(LAST_INSERT_ID(), 'Daily Report', 'Dashlet for quick adding of daily expenses reports', 'en');

########################################################################
# 2012-09-28 - Added automation for updating the "assigned" field of all customers when editing their region
#            - Added automation for updating the "assigned" field of the current customer when changing his region

# Added automation for updating the "assigned" field of all customers when editing their region
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'nomenclatures', NULL, 'action', 8, 'customer_types := 2,3', 'condition := 1', 'plugin := biotrade\r\nmethod := updateRegionAgent', NULL, 1, 0);

# Added automation for updating the "assigned" field of the current customer when changing his region
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'customers', NULL, 'action', 2, '', 'condition := 1', 'plugin := biotrade\r\nmethod := updateCustomerAssigned', NULL, 1, 0);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'customers', NULL, 'action', 3, '', 'condition := 1', 'plugin := biotrade\r\nmethod := updateCustomerAssigned', NULL, 1, 0);

######################################################################################
# 2012-10-11 - Added new report 'biotrade_finance_timesheet' for the Biotrade installation (1792)
#            - Added permissions for generate and export the report 'biotrade_finance_timesheet' for the Biotrade installation (1792)

# Added new report 'biotrade_finance_timesheet' for the Biotrade installation (1792)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(238, 'biotrade_finance_timesheet', 'document_expenses_timesheet := 6\r\ndocument_advance := 5\r\n\r\ndocument_starting_km := starting_miles\r\ndocument_ending_km := final_miles\r\ndocument_personal_km := personal_miles\r\n\r\nnom_fuels_id := 6\r\nnom_tickets_id := 7\r\nnom_taxes_id := 8\r\nnom_parcking_id := 9\r\nnom_travels_id := 10\r\nnom_hotels_id := 11\r\nnom_foods_id := 12\r\nnom_others_id := 13\r\n\r\namortization_multiplier := 0.09', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(238, 'Финансов отчет', NULL, NULL, 'bg'),
(238, 'Finance timesheet', NULL, NULL, 'en');

# Added permissions for generate and export the report 'biotrade_finance_timesheet' for the Biotrade installation (1792)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 238, 0, 1),
('reports', 'export', 238, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=238;

######################################################################################
# 2013-04-16 - Added automation to validate production cards (document type 8)

# Added automation to validate production cards (document type 8)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Валидация на производствена карта (допуска се една ПК за една рецепта)', 0, NULL, 1, 'documents', '', 'before_action', 8, '', 'condition := 1', 'plugin := biotrade\r\nmethod := validateProductionCard\r\n', 'cancel_action_on_fail := 1', 0, 0);

######################################################################################
# 2013-04-17 - Added automation to validate recipes (nomenclature type 5)
#            - Added automation to validate lots (nomenclature type 15)

# Added automation to validate recipes (nomenclature type 5)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Валидация на рецепта (не допуска редакция на заключена ПК)', 0, NULL, 1, 'nomenclatures', '', 'before_action', 5, '', 'condition := 1', 'plugin := biotrade\r\nmethod := validateRecipe\r\n', 'cancel_action_on_fail := 1', 0, 0);

# Added automation to validate lots (nomenclature type 15)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Валидация на партида (допуска се една партида за една рецепта)', 0, NULL, 1, 'nomenclatures', '', 'before_action', 15, '', 'condition := 1', 'plugin := biotrade\r\nmethod := validateLot\r\n', 'cancel_action_on_fail := 1', 0, 0);

######################################################################################
# 2013-04-19 - Added automation to create production requests recursively

# Added automation to create production requests recursively
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Създава заявки за производство рекурсивно', 0, NULL, 1, 'documents', '', 'before_action', 10, '', 'condition := 1', 'plugin := biotrade\r\nmethod := createProductionRequests\r\n', 'cancel_action_on_fail := 1', 0, 0);

######################################################################################
# 2013-04-22 - Added production dashlet

# Added production dashlet
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'biotrade_production', 'doc_type_production_card := 8\r\ndoc_type_production_record := 9\r\ndoc_type_production_request := 10\r\nnom_type_product := 5\r\nnom_type_material := 12\r\nnom_type_package := 13\r\nnom_type_consumable := 14\r\nnom_type_lot := 15\r\n\r\nwarehouse_main := 1\r\nwarehouse_production := 2\r\nrecord_status_finished_no_warehouse_entry := 1\r\nrecord_status_finished_warehouse_entry := 2\r\ncust_own_company := 8\r\ncompany := 1\r\noffice_main := 1\r\noffice_production := 2\r\ncurrency := BGN\r\nproduct_subtype_recipe := 3\r\ndepartment_all := 1\r\n\r\n# Product (nom 5)\r\nproduct_targetrecipe_var := product_targetrecipe\r\nproduct_measure_var := product_measure\r\nproduct_subtype_var := subtype_product\r\nproduct_article_name_var := product_name\r\nproduct_article_id_var := product_id\r\nproduct_article_measure_var := material_measure\r\nproduct_article_num_var := product_num\r\n\r\n# Material/Package/Consumable (nom 12,13,14)\r\nmaterial_measure_var := product_measure\r\npackage_measure_var := product_measure\r\nconsumable_measure_var := product_measure\r\n\r\n# Lot (nom 15)\r\nlot_recipe_id_var := recipe_id\r\n\r\n# Production card (doc 8)\r\ncard_product_name_var := prod_recipe_name\r\ncard_product_id_var := prod_recipe_id\r\ncard_step_var := Step\r\ncard_article_name_var := article_name\r\ncard_article_id_var := article_id\r\ncard_article_measure_var := article_me\r\ncard_article_quantity_var := quantity\r\ncard_article_loss_var := loss\r\ncard_time_var := Time\r\ncard_timeout_var := Timeout\r\ncard_description_var := Description\r\n\r\n# Production record (doc 9)\r\nrecord_started_var := started_on\r\nrecord_finished_var := finished_on\r\nrecord_product_name_var := recipe\r\nrecord_product_id_var := recipe_id\r\nrecord_quantity_var := quantity\r\nrecord_produced_var := produced\r\nrecord_loss_var := loss\r\nrecord_in_warehouse_var := in_warehouse\r\nrecord_measure_var := measure\r\nrecord_lot_var := lot\r\nrecord_lot_id_var := lot_id\r\nrecord_expiry_date_var := expiry_date\r\nrecord_note_var := note\r\nrecord_step_var := step\r\nrecord_article_name_var := article\r\nrecord_article_id_var := article_id\r\nrecord_article_lot_var := article_lot\r\nrecord_article_measure_var := article_me\r\nrecord_article_quantity_var := article_quantity\r\nrecord_article_in_var := article_in\r\nrecord_article_loss_var := article_loss\r\nrecord_description_var := description\r\nrecord_history_var := history\r\nrecord_step_start_var := step_start_datetime\r\nrecord_step_end_var := step_end_datetime\r\nrecord_step_user_var := step_user\r\n\r\n# Production request (doc 10)\r\nrequest_product_name_var := article\r\nrequest_product_id_var := article_id\r\nrequest_quantity_var := article_quantity\r\nrequest_measure_var := article_me\r\nrequest_record_id_var := protocol_id\r\nrequest_record_name_var := protocol_name\r\nrequest_record_date_var := date_protocol\r\nrequest_record_quantity_planned_var := quantity_planned\r\nrequest_record_quantity_produced_var := quantity_produced\r\n', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Производство', '', 'bg'),
(LAST_INSERT_ID(), 'Production', '', 'en');

######################################################################################
# 2013-05-09 - Modified settings of production dashlet

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'warehouse_production := 2', 'warehouse_production := 2\r\nrequest_status_locked_for_production := 4')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%request_status_locked_for_production%';
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'nom_type_lot := 15', 'nom_type_lot := 15\r\nfer_type_production := 101\r\nfir_type_production := 102')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%fir_type_production%' AND `settings` NOT LIKE '%fer_type_production%';
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'office_production := 2', 'office_production := 2\r\ncashbox_production := 1')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%cashbox_production%';

######################################################################################
# 2013-05-16 - Modified settings of production dashlet
#            - Removed relations (if any) between finished/cancelled production records and production cards
#            - Added dashlet plugin for week schedule in Biotrade installation (1792)

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'product_subtype_recipe', 'measure_num := 1\r\nproduct_subtype_product := 1\r\nproduct_subtype_recipe')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%product_subtype_product%';
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'warehouse_main := 1', 'max_inactive_time := 30\r\nwarehouse_main := 1')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%max_inactive_time%';

# Removed relations (if any) between finished/cancelled production records and production cards
UPDATE `documents` AS `d1`
JOIN `documents_relatives` AS `dr`
  ON `dr`.`link_to_model_name`='Document' AND `dr`.`link_to`=`d1`.`id` AND `d1`.`type`=8
JOIN `documents` AS `d2`
  ON `dr`.`parent_model_name`='Document' AND `dr`.`parent_id`=`d2`.`id` AND `d2`.`type`=9
SET `d2`.`transform_num`=`d1`.`id`
WHERE `dr`.`origin`='transformed' AND `d2`.`status`='closed' AND (`d2`.`active`=0 OR `d2`.`substatus`=2);

DELETE `dr`.*
FROM `documents` AS `d1`
JOIN `documents_relatives` AS `dr`
  ON `dr`.`link_to_model_name`='Document' AND `dr`.`link_to`=`d1`.`id` AND `d1`.`type`=8
JOIN `documents` AS `d2`
  ON `dr`.`parent_model_name`='Document' AND `dr`.`parent_id`=`d2`.`id` AND `d2`.`type`=9
WHERE `dr`.`origin`='transformed' AND `d2`.`status`='closed' AND (`d2`.`active`=0 OR `d2`.`substatus`=2);

# Added dashlet plugin for week schedule in Biotrade installation (1792)
INSERT INTO `dashlets_plugins` (`type`, `settings`, `is_portal`, `visible`) VALUES
('biotrade_week_schedule', 'daily_plan_type_id := 3\r\ndaily_plan_ray := ray_id\r\n\r\ncustomer_pharmacy := 2\r\ncustomer_pharmacy_address := job_address\r\ncustomer_pharmacy_positioned_var := pharamacy_positioned\r\ncustomer_pharmacy_positioned := 1\r\ncustomer_pharmacy_not_positioned := 0\r\ncustomer_medic := 3\r\ncustomer_medic_ray := ray_id\r\ncustomer_medic_address := job_address\r\n\r\ndaily_plan_meeting_marked := 2\r\ndaily_plan_meeting_failed := 3\r\n\r\nsell_type := 103\r\nvisit_type := 104\r\nrequest_type := 1', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Седмичен график', 'График за посещения', 'bg'),
(LAST_INSERT_ID(), 'Week schedule', 'Visits schedule', 'en');

######################################################################################
# 2013-05-22 - Added new report - 'warehouse_availabilities'

# Added new report - 'warehouse_availabilities'
INSERT INTO `reports` (`id` ,`type` ,`settings` , `position`, `is_portal` ,`visible`) VALUES
  ('112', 'warehouse_availabilities', 'min_stock_var := min_stock\r\narticle_measure := measure', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  ('112', 'Наличности',     NULL, 'bg'),
  ('112', 'Availabilities', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '112', '0', '1'),
  ('reports', '', 'export',          '112', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '112';

######################################################################################
# 2013-05-29 - Added new report - 'goods_movement' for Biotrade installation (1792)
#            - Added some settings for 'goods_movement' report

# Added new report - 'goods_movement' for Biotrade installation (1792)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (261, 'goods_movement', 'sale_types := \r\ndelivery_types := \r\nofficial_marking_types := \r\nnomenclature_types := \r\ncustomer_types := \r\nwarehouses := \r\nofficial_marking_label := \r\nofficial_marking_label_bg := \r\nofficial_marking_label_en := \r\n', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (261, 'Движение на стоки', NULL, NULL, 'bg'),
  (261, 'Goods movement', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '261', '0', '1'),
  ('reports', '', 'export',          '261', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '261';

# Added some settings for 'goods_movement' report
UPDATE `reports`
SET `settings`='sale_types := 103,104,105,107,108,109\r\ndelivery_types := 20,22,23,106\r\nofficial_marking_types := 101,102\r\nnomenclature_types := 5,12,13\r\ncustomer_types := \r\nwarehouses := \r\nofficial_marking_label := \r\nofficial_marking_label_bg := производство\r\nofficial_marking_label_en := production\r\n'
WHERE `type`='goods_movement';

######################################################################################
# 2013-05-30 - Added pattern plugin to modify the GT2 (precision of some of the fields is changed, some rowspan is added as well)

# Added pattern plugin to modify the GT2 (precision of some of the fields is changed, some rowspan is added as well)
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(48, 'Finance_Incomes_Reason', 0, 'biotrade', 'prepareModifiedGT2', '', '', NOW(), NOW()),
(49, 'Document', 0, 'biotrade', 'prepareModifiedGT2', '', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(48, 'Подготовка на ГТ2 данни', 'Подготвят се данни за печат на модифицирана GT2', 'bg', NOW()),
(48, 'Preparation of GT2', 'Prepare data for printing of modified GT2', 'en', NOW()),
(49, 'Подготовка на ГТ2 данни', 'Подготвят се данни за печат на модифицирана GT2', 'bg', NOW()),
(49, 'Preparation of GT2', 'Prepare data for printing of modified GT2', 'en', NOW());

######################################################################################
# 2013-05-31 - Added new report - 'biotrade_stock_planning' for Biotrade installation (1792)

# Added new report - 'biotrade_stock_planning' for Biotrade installation (1792)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (262, 'biotrade_stock_planning', 'nomenclature_type_products := 5\r\nnomenclature_categories := 2,3\r\nnomenclature_type_materials := 5,12,13\r\nwarehouses := 1\r\nmeasure_num := 1\r\n\r\nmeasure_var := product_measure\r\nmin_stock_var := min_stock\r\ndelivery_term_var := deliveryterm\r\n\r\nproduct_article_id_var := product_id\r\nproduct_article_num_var := product_num\r\nproduct_targetrecipe_var := product_targetrecipe', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (262, 'Планиране на наличности', NULL, NULL, 'bg'),
  (262, 'Stock planning', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '262', '0', '1'),
  ('reports', '', 'export',          '262', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '262';

######################################################################################
# 2013-06-03 - Added new settings to the week schedule dashlet

# Added new settings to the week schedule dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'request_type := 1', 'request_type := 1\r\ndepartment_all := 1')
WHERE `type`='biotrade_week_schedule' AND `settings` NOT LIKE '%department_all%';

######################################################################################
# 2013-06-04 - Added pattern plugin to modify the GT2 (precision of some of the fields is changed, some rowspan is added as well)
#            - Added report - 'due_or_recoverable_vat'

# Added pattern plugin to modify the GT2 (precision of some of the fields is changed, some rowspan is added as well)
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(50, 'Finance_Warehouses_Document', 0, 'biotrade', 'prepareModifiedGT2', '', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(50, 'Подготовка на ГТ2 данни', 'Подготвят се данни за печат на модифицирана GT2', 'bg', NOW()),
(50, 'Preparation of GT2', 'Prepare data for printing of modified GT2', 'en', NOW());

# Added report - 'due_or_recoverable_vat'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (220, 'due_or_recoverable_vat', 'allows_files_generation := 1\r\nfile_origin := self\r\nhide_export_default := 1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (220, 'Дължимо/възстановимо ДДС', '01. Финанси', NULL, 'bg'),
  (220, 'Due/recoverable VAT', '01. Financial', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '220', '0', '1'),
  ('reports', 'export', '220', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '220';

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'invoice_expenses_and_incomes', 'Report', 'send', 'all', ',220,', 'invoice_expenses_and_incomes', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Разходи/приходи по фактури (таблица)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Expenses/revenues by invoices (table)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'due_or_recoverable_vat', 'Report', 'send', 'all', ',220,', 'due_or_recoverable_vat', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС (таблица)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Due/recoverable VAT (table)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'due_or_recoverable_vat_documents', 'Report', 'send', 'all', ',220,', 'due_or_recoverable_vat_documents', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Документи, генериращи ДДС за внасяне/възстановяване (таблица)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Documents generating due/recoverable VAT (table)', NULL, 'en');

INSERT INTO `patterns` (`id`, `model`, `model_type`, `section`, `list`, `for_printform`, `company`, `position`, `header`, `footer`, `background_image`, `background_image_position`, `landscape`, `prefix`, `format`, `force_generate`, `not_regenerate_finished_record`, `plugin`, `handover_direction`, `is_portal`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
  (NULL, 'Report', '220', 0, 0, 0, 0, 1, 0, 0, NULL, 'left_top', 0, '[current_date]_[name]', 'pdf', 0, 0, 0, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
INSERT INTO `patterns_i18n` (`parent_id`, `name`, `description`, `content`, `lang`, `translated`) VALUES
  (LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС', '', '<p>[invoice_expenses_and_incomes]</p>\r\n<p>[due_or_recoverable_vat_documents]</p>\r\n<p>[due_or_recoverable_vat]</p>\r\n', 'bg', NOW()),
  (LAST_INSERT_ID(), 'Due/recoverable VAT', '', '<p>[invoice_expenses_and_incomes]</p>\r\n<p>[due_or_recoverable_vat_documents]</p>\r\n<p>[due_or_recoverable_vat]</p>\r\n', 'en', NOW());

######################################################################################
# 2013-06-05 - Modified settings of production dashlet

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'product_subtype_recipe := 3\r\n', 'product_subtype_recipe := 3\r\nproduct_subtype_package := 4\r\n')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%product_subtype_package%';

######################################################################################
# 2013-06-21 - Modified settings of production dashlet
#            - Added before action automation to validate if the entered quantity (when measure number is selected) is a whole number for Biotrade installation (1792)

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'warehouse_main := 1\r\n', '')
WHERE `type`='biotrade_production' AND `settings` LIKE '%warehouse_main := 1\r\n%';

# Added before action automation to validate if the entered quantity (when measure number is selected) is a whole number for Biotrade installation (1792)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
('Валидация за брой (да се допускат единствено цели числа)', 0, NULL, 1, 'documents', '', 'before_action', 10, 'quantity_field := article_quantity\r\nmeasure_field := article_me\r\n\r\nmeasure_unit := 1', 'condition := 1', 'plugin := biotrade\r\nmethod := validateCountNumber\r\n', 'cancel_action_on_fail := 1', 0, 0);

######################################################################################
# 2013-06-27 - Modified settings of production dashlet

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'office_main := 1\r\n', '')
WHERE `type`='biotrade_production' AND `settings` LIKE '%office_main := 1\r\n%';

######################################################################################
# 2013-07-17 - Added currency as param for the pricelist AC plugin

# Added currency as param for the pricelist AC plugin
UPDATE _fields_meta SET source = replace( source, "autocomplete_on_select := pushPricelist", "autocomplete_on_select := pushPricelist\nautocomplete_plugin_param_currency := $currency") WHERE source NOT LIKE '%autocomplete_plugin_param_currency := $currency%';

######################################################################################
# 2013-07-30 - Modified settings of Weekly schedule dashlet

# Modified settings of Weekly schedule dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'customer_pharmacy_address := job_address\r\ncustomer_pharmacy_positioned_var := pharamacy_positioned\r\ncustomer_pharmacy_positioned := 1\r\ncustomer_pharmacy_not_positioned := 0', 'customer_pharmacy_address := job_address\r\ncustomer_pharmacy_tags_ids := 7, 8, 9, 10')
WHERE `type`='biotrade_week_schedule' AND `settings` NOT LIKE '%customer_pharmacy_tags_ids%';

######################################################################################
# 2013-08-01 - Added Visit planning report
#            - Changed settings for report 'warehouse_availabilities'

# Added Visit planning report
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(265, 'biotrade_visit_planning', 'nom_type_ray := 6\r\nnom_type_region := 8\r\ncust_type_pharmacy := 2\r\ncust_type_doctor := 3\r\ndoc_type_daily_schedule := 3\r\ndoc_type_target_visit := 12\r\ndoc_type_target_campaign := 13\r\nfir_type_visit := 104\r\n\r\nray_region_id_var := region_id\r\n\r\nregion_agent_id_var := agent_id\r\nregion_agent_name_var := agent_name\r\n\r\npharmacy_address_var := job_address\r\npharmacy_ray_id_var := ray_id\r\npharmacy_workdays_var := pharmacy_workdays\r\npharmacy_worktime_week_var := pharamacy_worktime_week\r\npharmacy_worktime_week__to_var := pharamacy_worktime_week__to\r\npharmacy_worktime_sat_var := pharmacy_worktime_sat\r\npharmacy_worktime_sat__to_var := pharmacy_worktime_sat__to\r\npharmacy_worktime_sun_var := pharmacy_worktime_sun\r\npharmacy_worktime_sun__to_var := pharmacy_worktime_sun__to\r\n\r\ndoctor_place_var := job_place\r\ndoctor_city_var := city_name\r\ndoctor_address_var := job_address\r\ndoctor_ray_id_var := ray_id\r\ndoctor_p1_t1_var := work_time_one\r\ndoctor_p1_t1_from_var := work_time_one__from\r\ndoctor_p1_t1_to_var := work_time_one__to\r\ndoctor_p1_t2_var := work_time_two\r\ndoctor_p1_t2_from_var := work_time_two__from\r\ndoctor_p1_t2_to_var := work_time_two__to\r\ndoctor_p1_t3_var := work_time_three\r\ndoctor_p1_t3_from_var := work_time_three__from\r\ndoctor_p1_t3_to_var := work_time_three__to\r\ndoctor_p2_t1_var := work_time_four\r\ndoctor_p2_t1_from_var := work_time_four__from\r\ndoctor_p2_t1_to_var := work_time_four__to\r\ndoctor_p2_t2_var := work_time_five\r\ndoctor_p2_t2_from_var := work_time_five__from\r\ndoctor_p2_t2_to_var := work_time_five__to\r\ndoctor_p2_t3_var := work_time_six\r\ndoctor_p2_t3_from_var := work_time_six__from\r\ndoctor_p2_t3_to_var := work_time_six__to\r\ndoctor_p3_t1_var := work_time_seven\r\ndoctor_p3_t1_from_var := work_time_seven__from\r\ndoctor_p3_t1_to_var := work_time_seven__to\r\ndoctor_p3_t2_var := work_time_eight\r\ndoctor_p3_t2_from_var := work_time_eight__from\r\ndoctor_p3_t2_to_var := work_time_eight__to\r\ndoctor_p3_t3_var := work_time_nine\r\ndoctor_p3_t3_from_var := work_time_nine__from\r\ndoctor_p3_t3_to_var := work_time_nine__to\r\n\r\ntarget_visit_from_date_var := from_date\r\ntarget_visit_months_num_var := months_num\r\ntarget_visit_meetings_ctag_var := meetings_ctag\r\ntarget_visit_meetings_ctype_var := meetings_ctype\r\ntarget_visit_meetings_num_var := meetings_num\r\n\r\ntarget_campaign_start_period_var := start_period\r\ntarget_campaign_finish_period_var := finish_period\r\ntarget_campaign_meetings_ctag_var := meetings_ctag\r\ntarget_campaign_meetings_ctype_var := meetings_ctype\r\ntarget_campaign_meetings_num_var := meetings_num\r\n\r\ndaily_schedule_ray_id_var := ray_id\r\ndaily_schedule_ray_name_var := ray_name\r\n\r\nrole_sales := 3\r\nclass_tags := 7,8,9,10\r\nwork_time_even := 8\r\nwork_time_odd := 9\r\n', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(265, 'Планиране на посещения', NULL, NULL, 'bg'),
(265, 'Visit planning', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '265', '0', '1'),
  ('reports', '', 'export',          '265', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '265';

# Changed settings for report 'warehouse_availabilities'
UPDATE `reports`
  SET `settings` = 'min_stock_var := min_stock\r\narticle_measure := product_measure\r\nshow_depleted_articles_filter := 1\r\nsearch_type_nomenclature := 5,12,13\r\nshow_include_zero_availabilities_filter := '
  WHERE `type` = 'warehouse_availabilities';

######################################################################################
# 2013-08-02 - Added some new settings to the Visit planning report

# Added some new settings to the Visit planning report
UPDATE reports SET settings=REPLACE(settings, 'pharmacy_address_var := job_address', 'pharmacy_city_var := city_name\r\npharmacy_address_var := job_address') WHERE type='biotrade_visit_planning' AND settings NOT LIKE '%pharmacy_city_var%';
UPDATE reports SET settings=REPLACE(settings, 'daily_schedule_ray_name_var := ray_name', 'daily_schedule_ray_name_var := ray_name\r\ndaily_schedule_status_planned := 1') WHERE type='biotrade_visit_planning' AND settings NOT LIKE '%daily_schedule_status_planned%';

######################################################################################
# 2013-08-05 - Removed export right for Visit planning report
#            - Modified settings of Weekly schedule dashlet

# Removed export right for Visit planning report
DELETE rd.*, rp.* FROM roles_definitions rd, roles_permissions rp
WHERE rd.id=rp.definition_id AND rd.module='reports' AND rd.model_type='265' AND rd.action='export';

# Modified settings of Weekly schedule dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'customer_pharmacy := 2\r\ncustomer_pharmacy_address := job_address\r\ncustomer_pharmacy_tags_ids := 7, 8, 9, 10\r\ncustomer_medic := 3\r\ncustomer_medic_ray := ray_id\r\ncustomer_medic_address := job_address', 'customer_pharmacy := 2\r\ncustomer_pharmacy_tags_ids := 7, 8, 9, 10\r\ncustomer_medic := 3')
WHERE `type`='biotrade_week_schedule' AND `settings` LIKE '%customer_pharmacy_address%';

######################################################################################
# 2013-08-28 - Added automation to remove "Initial supply" tag from Pharmacies (customer of type 2)

# Added automation to remove "Initial supply" tag from Pharmacies (customer of type 2)
INSERT INTO `automations` VALUES
(NULL, 'Премахване на таг "Първоначално зареждане" от Аптеки', 0, NULL, 1, 'customers', NULL, 'crontab', 2, 'start_time := 01:00\r\nstart_before := 03:00\r\n\r\ntag_id := 12\r\nfir_type_sale := 105', 'condition := 1', 'plugin := biotrade\r\nmethod := removeInitialSupplyTag', NULL, 0, 0);

######################################################################################
# 2013-10-03 - Changed settings for dashlet plugin 'biotrade_add_expenses_report'

# Changed settings for dashlet plugin 'biotrade_add_expenses_report'
UPDATE `dashlets_plugins`
  SET `settings` = 'document_type := 6\r\n\r\nexpenses_fuel := 6\r\nexpenses_fuel_with_card := 787\r\nexpenses_tickets := 7\r\nexpenses_fees := 8\r\nexpenses_parking := 9\r\nexpenses_trip_expenses := 10\r\nexpenses_hotel := 11\r\nexpenses_food := 12\r\nexpenses_other := 13\r\n'
  WHERE `type` = 'biotrade_add_expenses_report';

######################################################################################
# 2013-10-09 - Added report for sales of representatives
#            - Updated settings for the report 'biotrade_finance_timesheet' to include the nomenclature for fuel with card

# Added report for sales of representatives
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(272, 'biotrade_representatives_sales', 'nom_type_ray := 6\r\ncust_type_pharmacy := 2\r\ncust_type_doctor := 3\r\ndoc_type_daily_schedule := 3\r\nfir_type_sale := 103\r\nfir_type_visit := 104\r\n\r\ndaily_schedule_ray_id_var := ray_id\r\nvisit_giveaway_type_var := giveaway_type\r\nrole_sales := 3', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(272, 'Продажби (ТП)', NULL, NULL, 'bg'),
(272, 'Sales (Sales Reps)', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '272', '0', '1'),
  ('reports', '', 'export',          '272', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '272';

# Updated settings for the report 'biotrade_finance_timesheet' to include the nomenclature for fuel with card
UPDATE reports SET settings=REPLACE(settings, 'nom_others_id := 13', 'nom_others_id := 13\r\nnom_fuel_with_card_id := 787') WHERE type='biotrade_finance_timesheet' AND settings NOT LIKE '%nom_fuel_with_card_id%';

######################################################################################
# 2013-10-21 - Updated settings of 'biotrade_visit_planning' report to include finance incomes reason type for 'Sale'

# Updated settings of 'biotrade_visit_planning' report to include finance incomes reason type for 'Sale'
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'fir_type_visit := 104', 'fir_type_sale := 103\r\nfir_type_visit := 104') WHERE `type`='biotrade_visit_planning' AND `settings` NOT LIKE '%fir_type_sale%';

######################################################################################
# 2013-10-25 - Extended pattern plugin that modifies the GT2 (adding three total vars in euro)

# Extended pattern plugin that modifies the GT2 (adding three total vars in euro)
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'total_eur', 'Document', 'basic', 'pattern_plugins', ',48,49,50,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Данъчна основа (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Total (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'total_vat_eur', 'Document', 'basic', 'pattern_plugins', ',48,49,50,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Размер на данъка (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'VAT (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'total_with_vat_eur', 'Document', 'basic', 'pattern_plugins', ',48,49,50,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Обща стойност (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Total Including VAT (EUR)', NULL, 'en');

######################################################################################
# 2013-12-02 - Added report for invoices

# Added report for invoices
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(278, 'biotrade_invoices', '', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(278, 'Фактури', NULL, NULL, 'bg'),
(278, 'Invoices', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '278', '0', '1'),
  ('reports', '', 'export',          '278', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '278';

######################################################################################
# 2013-12-05 - Modified settings of production dashlet
#            - Changed variable name
#            - Fixed (automatically formed) name and audit of commodities transfers, handovers and waste records created from production dashlet
#            - Modified settings of button that opens "Warehouse availabilities" report from "Production request" document

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'request_status_locked_for_production := 4\r\n', 'warehouse_samples := 3\r\nquantity_samples := 2\r\n')
WHERE `type`='biotrade_production' AND `settings` LIKE '%request_status_locked_for_production%';

UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'lot_recipe_id_var := recipe_id\r\n', 'lot_recipe_id_var := recipe_id\r\nlot_batch_num_var := batch_num\r\n')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%lot_batch_num_var%';

# Changed variable name
UPDATE `_fields_meta` SET `name`='batch_num' WHERE `name`='bactch_num';

# Fixed (automatically formed) name and audit of commodities transfers, handovers and waste records created from production dashlet
UPDATE `fin_warehouses_documents_i18n` SET `name`='Междускладов трансфер на стоки' WHERE `name` LIKE 'Междускладов трансфер на стоки за %';
UPDATE `fin_warehouses_documents_i18n` SET `name`='Приемо-предавателен протокол за Междускладов трансфер на стоки' WHERE `name` LIKE 'Приемо-предавателен протокол за Междускладов трансфер на стоки за %';
UPDATE `fin_warehouses_documents_i18n` SET `name`='Протокол за брак' WHERE `name` LIKE 'Протокол за брак за %';

UPDATE `fin_audit` SET `field_value`='Междускладов трансфер на стоки' WHERE `field_name`='name' AND `field_value` LIKE 'Междускладов трансфер на стоки за %';
UPDATE `fin_audit` SET `field_value`='Приемо-предавателен протокол за Междускладов трансфер на стоки' WHERE `field_name`='name' AND `field_value` LIKE 'Приемо-предавателен протокол за Междускладов трансфер на стоки за %';
UPDATE `fin_audit` SET `field_value`='Протокол за брак' WHERE `field_name`='name' AND `field_value` LIKE 'Протокол за брак за %';

# Modified settings of button that opens "Warehouse availabilities" report from "Production request" document
UPDATE `_fields_meta` SET `source`='href := reports&reports=generate_report\r\nreport_type := warehouse_availabilities\r\ntarget := _blank\r\nnomenclature := a_article_id\r\nnomenclature_autocomplete := a_article\r\nreport_based_on_article := article_checked'
WHERE `id`=1011 AND `source` LIKE 'href := reports&reports=generate_report\r\nreport_type := warehouse_availabilities\r\n%';

######################################################################################
# 2013-12-12 - Added report for visit report analysis

# Added report for visit report analysis
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(282, 'biotrade_visit_analysis', 'nom_type_ray := 6\r\nnom_type_region := 8\r\ncust_type_pharmacy := 2\r\ncust_type_doctor := 3\r\ndoc_type_daily_schedule := 3\r\ndoc_type_target_visit := 12\r\ndoc_type_target_campaign := 13\r\nfir_type_sale := 103\r\nfir_type_visit := 104\r\n\r\nray_region_id_var := region_id\r\n\r\nregion_agent_id_var := agent_id\r\nregion_agent_name_var := agent_name\r\n\r\npharmacy_city_var := city_name\r\npharmacy_address_var := job_address\r\npharmacy_ray_id_var := ray_id\r\npharmacy_workdays_var := pharmacy_workdays\r\npharmacy_worktime_week_var := pharamacy_worktime_week\r\npharmacy_worktime_week__to_var := pharamacy_worktime_week__to\r\npharmacy_worktime_sat_var := pharmacy_worktime_sat\r\npharmacy_worktime_sat__to_var := pharmacy_worktime_sat__to\r\npharmacy_worktime_sun_var := pharmacy_worktime_sun\r\npharmacy_worktime_sun__to_var := pharmacy_worktime_sun__to\r\n\r\ndoctor_place_var := job_place\r\ndoctor_city_var := city_name\r\ndoctor_address_var := job_address\r\ndoctor_ray_id_var := ray_id\r\ndoctor_p1_t1_var := work_time_one\r\ndoctor_p1_t1_from_var := work_time_one__from\r\ndoctor_p1_t1_to_var := work_time_one__to\r\ndoctor_p1_t2_var := work_time_two\r\ndoctor_p1_t2_from_var := work_time_two__from\r\ndoctor_p1_t2_to_var := work_time_two__to\r\ndoctor_p1_t3_var := work_time_three\r\ndoctor_p1_t3_from_var := work_time_three__from\r\ndoctor_p1_t3_to_var := work_time_three__to\r\ndoctor_p2_t1_var := work_time_four\r\ndoctor_p2_t1_from_var := work_time_four__from\r\ndoctor_p2_t1_to_var := work_time_four__to\r\ndoctor_p2_t2_var := work_time_five\r\ndoctor_p2_t2_from_var := work_time_five__from\r\ndoctor_p2_t2_to_var := work_time_five__to\r\ndoctor_p2_t3_var := work_time_six\r\ndoctor_p2_t3_from_var := work_time_six__from\r\ndoctor_p2_t3_to_var := work_time_six__to\r\ndoctor_p3_t1_var := work_time_seven\r\ndoctor_p3_t1_from_var := work_time_seven__from\r\ndoctor_p3_t1_to_var := work_time_seven__to\r\ndoctor_p3_t2_var := work_time_eight\r\ndoctor_p3_t2_from_var := work_time_eight__from\r\ndoctor_p3_t2_to_var := work_time_eight__to\r\ndoctor_p3_t3_var := work_time_nine\r\ndoctor_p3_t3_from_var := work_time_nine__from\r\ndoctor_p3_t3_to_var := work_time_nine__to\r\n\r\ntarget_visit_from_date_var := from_date\r\ntarget_visit_months_num_var := months_num\r\ntarget_visit_meetings_ctag_var := meetings_ctag\r\ntarget_visit_meetings_ctype_var := meetings_ctype\r\ntarget_visit_meetings_num_var := meetings_num\r\n\r\ntarget_campaign_start_period_var := start_period\r\ntarget_campaign_finish_period_var := finish_period\r\ntarget_campaign_meetings_ctag_var := meetings_ctag\r\ntarget_campaign_meetings_ctype_var := meetings_ctype\r\ntarget_campaign_meetings_num_var := meetings_num\r\n\r\nrole_sales := 3\r\nclass_tags := 7,8,9,10\r\nwork_time_even := 8\r\nwork_time_odd := 9\r\n', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(282, 'Анализ на отчети за посещения', NULL, NULL, 'bg'),
(282, 'Visit report analysis', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', '', 'generate_report', '282', '0', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report')
      AND `model_type` = '282';

######################################################################################
# 2013-12-14 - Modified automation for lot validation
#            - Modified settings of production dashlet
#            - Modified some settings of additional variables

# Modified automation for lot validation
UPDATE `automations`
SET `settings`='nom_type_product := 5\r\n\r\nproduct_subtype_var := subtype_product\r\nproduct_article_id_var := product_id\r\nproduct_subtype_product := 1\r\nproduct_subtype_recipe := 3\r\n\r\nlot_recipe_var := recipe\r\nlot_recipe_id_var := recipe_id\r\nlot_product_var := product\r\nlot_product_id_var := product_id'
WHERE `method` LIKE '%method := validateLot%';

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'lot_batch_num_var := batch_num\r\n', 'lot_batch_num_var := batch_num\r\nlot_product_id_var := product_id\r\n')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%lot_product_id_var%';

# Modified some settings of additional variables
UPDATE `_fields_meta` SET `hidden`=1 WHERE `model`='Document' and `model_type`=9 and `name` IN ('step_end_datetime', 'step_start_datetime', 'step_user');
UPDATE `_fields_meta` SET `width`=200 WHERE `model`='Nomenclature' AND `model_type`=15 AND `name`='product' AND (`width` IS NULL OR `width`='');
UPDATE `_fields_meta` SET `searchable`='autocompleter' WHERE `model`='Document' AND `model_type`=8 AND `name`='prod_recipe_name';

######################################################################################
# 2013-12-21 - Added new automation to finish sells and visits

# Added new automation to finish sells and visits
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
    (NULL, 'Приключване на продажби и посещения', '0', NULL, '1', 'finance', 'incomes_reasons', 'crontab', '0', 'start_week_day := 1\nstart_time := 01:00\nstart_before := 03:00', 'condition := 1', 'plugin := biotrade\nmethod := finishVisitsAndSells', NULL, '0', '0');

INSERT INTO `emails` (`id`, `model`, `model_type`, `name`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
    (1001, 'Finance_Incomes_Reason', 0, 'finish_sell_visit_errors', 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
INSERT INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
    (1001, 'Грешки при приключване на документи', '<table border="0" cellpadding="0" cellspacing="0" width="100%">\r\n  <tbody>\r\n     <tr>\r\n            <td>\r\n                Здравейте, [user_name],</td>\r\n        </tr>\r\n       <tr>\r\n            <td>\r\n                &nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>\r\n                Възникнаха грешки при автоматично приключване на срещи и/или продажби на [system_date|date_format:%d.%m.%Y]:</td>\r\n        </tr>\r\n       <tr>\r\n            <td>\r\n                &nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>\r\n                [errors_for_sells_visits]</td>\r\n      </tr>\r\n       <tr>\r\n            <td>\r\n                &nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>\r\n                <strong>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на <strong>nZoom</strong>.</td>\r\n        </tr>\r\n       <tr>\r\n            <td>\r\n                <em>При възникнали проблеми можете да <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">изпратите e-mail</a> на екипа за поддръжка на <strong>nZoom</strong>.</em></td>\r\n       </tr>\r\n       <tr>\r\n            <td>\r\n                &nbsp;</td>\r\n     </tr>\r\n   </tbody>\r\n</table>\r\n<br />\r\n', '', 'bg', '2013-12-21 15:09:57');

######################################################################################
# 2014-01-29 - Modified settings of production dashlet

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'record_description_var := description\r\n', 'record_description_var := description_protocol\r\n')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%record_description_var := description_protocol%';
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'card_description_var := Description\r\n', 'card_description_var := Description_production\r\n')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%card_description_var := Description_production%';

######################################################################################
# 2014-02-17 - Added new report: 'biotrade_sales'

# Added new report: 'biotrade_sales'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('286', 'biotrade_sales', 'fir_types := 105,103,109,108,107\r\nfir_statuses := finished\r\nnom_type_product_receipe := 5', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('286', 'Продажби', NULL, NULL, 'bg'),
  ('286', 'Sales',    NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '286', '0', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     = 'generate_report'
      AND `model_type` = '286';

######################################################################################
# 2014-02-25 - Added setting for roles with restricted permissions to warehouses for 'goods_movement' report

# Added setting for roles with restricted permissions to warehouses for 'goods_movement' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'official_marking_label := ', 'default_warehouse_roles := 3\r\nofficial_marking_label := ')
WHERE `type`='goods_movement' AND `settings` NOT LIKE '%default_warehouse_roles%';

######################################################################################
# 2014-03-06 - Added new automation for articles retest

# Added new automation for articles retest
UPDATE `_fields_meta` SET `source` = 'autocomplete := autocompleters\nautocomplete_plugin_search := customQuery\nautocomplete_plugin_param_sql := select fb.id, fb.code, wq.expire_date from fin_warehouses_quantities wq join fin_batches fb on wq.batch_id = fb.id and fb.code like "%<search_string_parts>%" where wq.quantity > 0 and wq.nomenclature_id = <current_article>\nautocomplete_plugin_param_current_article := $material_name_id\nautocomplete_fill_options := $batch_name_id => <id>\nautocomplete_fill_options := $batch_name => <code>\nautocomplete_fill_options := $expired_date => <expire_date>\nautocomplete_suggestions := <code> [<expire_date>]' WHERE `id` = 1712;
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, 'Ре-тест на артикули', '0', NULL, '1', 'documents', '', 'before_action', '18', '', 'condition := 1', 'plugin := biotrade\nmethod := articlesRetest', 'cancel_action_on_fail := 1', '0', '0');
UPDATE `_fields_meta` SET `source` = 'dont_copy_values := 1' WHERE `id` = 1715;

######################################################################################
# 2014-03-13 - Fix warehouses quantities problem for specific nomenclature (i.e. fixes the problem from Bug 3875 comment 17)

# PRE-DEPLOYED # Fix warehouses quantities problem for specific nomenclature (i.e. fixes the problem from Bug 3875 comment 17)
#DELETE FROM `fin_warehouses_quantities` WHERE `parent_id` = '1' AND `nomenclature_id` = '920';
#DELETE FROM `gt2_batches_data` WHERE `batch_id` = '1077';

######################################################################################
# 2014-04-04 - Added GT2 placeholder in EUR for the prepareModifiedGT2 pattern plugin

# Added GT2 placeholder in EUR for the prepareModifiedGT2 pattern plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'grouping_table_2_EUR', 'Document', 'basic', 'pattern_plugins', ',48,49,50,', '', 1);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'ГТ2 (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'GT2 (EUR)', NULL, 'en');

######################################################################################
# 2014-04-08 - Changed settings for report 'warehouse_availabilities'

# Changed settings for report 'warehouse_availabilities'
UPDATE `reports`
  SET `settings` = CONCAT(`settings`, '\r\nnom_cats_order := 2,3,11,7,6,12')
  WHERE `type` = 'warehouse_availabilities'
    AND `settings` NOT LIKE '%nom_cats_order%';

######################################################################################
# 2014-04-22 - Added position (`free_field5` field) for all GT2 rows of 'Daily schedule' documents (type 3)
#            - Changed settings of `free_field5` field of 'Daily schedule' document (type 3)

# Added position (`free_field5` field) for all GT2 rows of 'Daily schedule' documents (type 3)
DROP TABLE IF EXISTS `gt2_position`;
CREATE TABLE `gt2_position` (
  `id` int(11) NOT NULL,
  `position` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

INSERT INTO `gt2_position` (`id`, `position`)
SELECT g.`id`, FIND_IN_SET(g.`id`, (SELECT GROUP_CONCAT(g2.`id`) FROM `gt2_details` g2 WHERE g2.`model`='Document' AND g2.`model_id`=g.`model_id` ORDER BY g2.`id` ASC))
FROM `documents` d, `gt2_details` g
WHERE d.`id`=g.`model_id` AND g.`model`='Document' AND d.`type`=3;

UPDATE `gt2_details` g, `gt2_position` gp
SET g.`free_field5`=gp.`position`
WHERE g.`id`=gp.`id`;

DROP TABLE `gt2_position`;

# Changed settings of `free_field5` field of 'Daily schedule' document (type 3)
UPDATE `_fields_meta` SET `type`='text', `source`=REPLACE(`source`, 'text_align := left', 'text_align := right'), `validate`='js_filter := insertOnlyPositiveIntegers' WHERE `id`=102600;

######################################################################################
# 2014-05-09 - Changed settings for report 'biotrade_visit_analysis'

# Changed settings for report 'biotrade_visit_analysis'
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nevent_free_days := 6\r\nevent_sickness := 7\r\nevent_conferences := 8\r\nevent_working_lunch := 9')
WHERE `type` = 'biotrade_visit_analysis' AND `settings` NOT LIKE '%event_working_lunch%';

######################################################################################
# 2014-05-23 - Fixed batches quantities and prices

# Fixed batches quantities and prices
DROP TABLE IF EXISTS quantities_tmp;
CREATE TABLE quantities_tmp
SELECT q2.*
FROM fin_warehouses_quantities q1
JOIN fin_warehouses_quantities q2
  ON q1.nomenclature_id = q2.nomenclature_id AND q1.batch_id = q2.batch_id AND q2.delivery_price = 0
WHERE q1.delivery_price > 0
GROUP BY q2.parent_id, q2.nomenclature_id, q2.batch_id;

DELETE q1.*
FROM fin_warehouses_quantities q1
JOIN quantities_tmp q2
  ON q1.parent_id = q2.parent_id AND q1.nomenclature_id = q2.nomenclature_id AND q1.batch_id = q2.batch_id
WHERE q1.delivery_price = 0;

UPDATE fin_warehouses_quantities q1
JOIN quantities_tmp q2
  ON q1.parent_id = q2.parent_id AND q1.nomenclature_id = q2.nomenclature_id AND q1.batch_id = q2.batch_id
SET q1.quantity = q1.quantity + q2.quantity;

DELETE q2.*
FROM fin_warehouses_quantities q1
JOIN quantities_tmp q2
  ON q1.parent_id = q2.parent_id AND q1.nomenclature_id = q2.nomenclature_id AND q1.batch_id = q2.batch_id
WHERE q1.delivery_price > 0;

UPDATE quantities_tmp q1
JOIN fin_warehouses_quantities q2
  ON q1.nomenclature_id = q2.nomenclature_id AND q1.batch_id = q2.batch_id
SET q1.delivery_price = q2.delivery_price;

INSERT INTO fin_warehouses_quantities
SELECT * FROM quantities_tmp;

DROP TABLE quantities_tmp;

######################################################################################
# 2014-07-04 - Added new report: 'biotrade_made_failed_visits'

# Added new report: 'biotrade_made_failed_visits'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('291', 'biotrade_made_failed_visits', 'daily_plan_type_id := 3\r\ndaily_plan_ray := ray_id\r\n\r\ndaily_plan_meeting_marked := 2\r\ndaily_plan_meeting_failed := 3\r\n\r\nrole_traders := 3\r\n\r\nnom_type_ray := 6\r\nfield_nom_ray_region_id := region_id\r\n\r\nnom_type_region := 8\r\nfield_nom_region_agent_id := agent_id', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('291', 'Направени/Пропаднали посещения',    NULL, NULL, 'bg'),
  ('291', 'Analysis of procedures', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '291', '0', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     = 'generate_report'
      AND `model_type` = '291';

######################################################################################
# 2014-08-26 - Added new report: 'biotrade_expiration_date'

# Added new report: 'biotrade_expiration_date'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('298', 'biotrade_expiration_date', 'nom_type_material := 12\r\nwarehouses := 1,2,3,4,5\r\nbatch_days_limit := 50\r\nbatch_color_expired := #FF9999\r\nbatch_color_expires_soon := #FFFF66\r\nbatch_color_shelf_life := #8CFFB1', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('298', 'Срок на годност', NULL, NULL, 'bg'),
  ('298', 'Expire date',     NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '298', '0', '1'),
  ('reports', 'export', '298', '0', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '298';

######################################################################################
# 2014-10-20 - Added report - 'globaltest_calibration_plan' - to the BIOTRADE installation
#            - Added report - 'globaltest_available_tsii_list' - to the BIOTRADE installation
#            - Added report - 'globaltest_tsii_file' - to the BIOTRADE installation
#            - Added automation for updating the date fields (Bug 3487)

# PRE-DEPLOYED # Added report - 'globaltest_calibration_plan' - to the BIOTRADE installation
#INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#  ('197', 'globaltest_calibration_plan', 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_technicle_means := 21\r\nnomenclature_category := 1\r\ndocument_type_calibration_certificate := 23\r\nnext_calibration_date_color_past := DB0416\r\nnext_calibration_date_color_present := FF9C2A\r\nnext_calibration_date_max_days_left := 10\r\ndocument_type_repair_protocol := 21', '0', '0', '1');
#INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#  ('197', 'План за калибриране', NULL, NULL, 'bg'),
#  ('197', 'Calibration plan',    NULL, NULL, 'en');
#INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#  ('reports', 'generate_report', '197', '0', '1'),
#  ('reports', 'export',          '197', '0', '2');
#INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#  SELECT '1', `id`, 'all'
#    FROM `roles_definitions`
#    WHERE `module` = 'reports'
#      AND (`action` = 'generate_report'
#        OR `action` = 'export')
#      AND `model_type` = '197';

#INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  (NULL, 'calibration_plan_table', 'Report', 'send', 'all', ',197,', 'calibration_plan_table', 0);
#INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'План за калибриране (таблица)', NULL, 'bg'),
#  (LAST_INSERT_ID(), 'Calibration plan (table)', NULL, 'en');

# PRE-DEPLOYED # Added report - 'globaltest_available_tsii_list' - to the BIOTRADE installation
#INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#  ('221', 'globaltest_available_tsii_list', 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_tsii := 21\r\ndocument_type_certificate := 23\r\ndocument_type_protocol := 22', '0', '0', '1');
#INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#  ('221', 'Списък на наличните ТСИИ', NULL, NULL, 'bg'),
#  ('221', 'List of available TMMT',   NULL, NULL, 'en');
#INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#  ('reports', 'generate_report', '221', '0', '1'),
#  ('reports', 'export',          '221', '0', '2');
#INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#  SELECT '1', `id`, 'all'
#  FROM `roles_definitions`
#  WHERE `module` = 'reports'
#    AND `action` IN ('generate_report', 'export')
#    AND `model_type` = '221';

# PRE-DEPLOYED # Add placeholders for 'globaltest_available_tsii_list' report templates
#INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  (NULL, 'available_tsii_list_tables', 'Report', 'send', 'all', ',221,', 'available_tsii_list_tables', 0);
#INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Списък на наличните ТСИИ (таблици)', NULL, 'bg'),
#  (LAST_INSERT_ID(), 'List of available TMMT (tables)',    NULL, 'en');

# PRE-DEPLOYED # Added report - 'globaltest_tsii_file' - to the BIOTRADE installation
#INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#  ('206', 'globaltest_tsii_file', 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_tsii := 21\r\ndocument_certificate := 23\r\ndocument_protocol := 21\r\ndocument_check_protocol := 22', '0', '0', '1');
#INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#  ('206', 'Досие на ТСИИ', NULL, NULL, 'bg'),
#  ('206', 'ТММТ file',     NULL, NULL, 'en');
#INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#  ('reports', 'generate_report', '206', '0', '1'),
#  ('reports', 'export',          '206', '0', '2');
#INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#  SELECT '1', `id`, 'all'
#  FROM `roles_definitions`
#    WHERE `module` = 'reports'
#      AND (`action` = 'generate_report'
#        OR `action` = 'export')
#      AND `model_type` = '206';

# PRE-DEPLOYED # Add placeholders for 'globaltest_tsii_file' report templates
#INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  (NULL, 'tsii_table', 'Report', 'send', 'all', ',206,', 'tsii_table', 0);
#INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'ТСИИ (таблица)', NULL, 'bg'),
#  (LAST_INSERT_ID(), 'TMMT (table)',   NULL, 'en');
#INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  (NULL, 'tsii_certificates_table', 'Report', 'send', 'all', ',206,', 'tsii_certificates_table', 0);
#INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Сертификати от калибриране (таблица)', NULL, 'bg'),
#  (LAST_INSERT_ID(), 'Calibration certificates (table)',     NULL, 'en');
#INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  (NULL, 'tsii_protocols_table', 'Report', 'send', 'all', ',206,', 'tsii_protocols_table', 0);
#INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Протоколи от калибриране (таблица)', NULL, 'bg'),
#  (LAST_INSERT_ID(), 'Calibration protocols (table)',      NULL, 'en');
#INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#  (NULL, 'tsii_check_protocols_table', 'Report', 'send', 'all', ',206,', 'tsii_check_protocols_table', 0);
#INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#  (LAST_INSERT_ID(), 'Протоколи за проверка на техническо средство (таблица)', NULL, 'bg'),
#  (LAST_INSERT_ID(), 'Protocols for checking of a technical mean (table)',     NULL, 'en');

# PRE-DEPLOYED # Added automation for updating the date fields (Bug 3487)
#INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#(NULL, 'Изчисляване на следващата дата за "Сертификат от калибриране"', 0, NULL, 1, 'documents', NULL, 'action', '23', 'validation_protocol := \r\nverification_protocol := \r\ncalibration_certificate := 23\r\nrepair_protocol := 21', 'condition := \'[a_article_id]\' != \'\'\r\ncondition := \'[a_article_name]\' != \'\'', 'plugin := globaltest\r\nmethod := updateNomenclatureNextDate', NULL, 0, 0, 1),
#(NULL, 'Изчисляване на следващата дата за "Протокол от проверка на ТСНИ"', 0, NULL, 1, 'documents', NULL, 'action', '21', 'validation_protocol := \r\nverification_protocol := \r\ncalibration_certificate := 23\r\nrepair_protocol := 21\r\nfield_tsii_id := \r\nnomenclature_type_test_method := \r\nrepair_protocol_add_days := 15', 'condition := \'[a_article_id]\' != \'\'\r\ncondition := \'[a_article_name]\' != \'\'\r\ncondition := \'[a_repair_calibration]\' == \'calibration_yes\'', 'plugin := globaltest\r\nmethod := updateNomenclatureNextDate', NULL, 0, 0, 1);

######################################################################################
# 2014-10-22 - Added settings to 'biotrade_stock_planning' report

# Added settings to 'biotrade_stock_planning' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ndoc_type_production_request := 10\r\nrequest_status_planning := opened\r\nrequest_substatus_planning := 14\r\nrequest_product_id_var := article_id\r\nrequest_quantity_var := article_quantity')
WHERE `type`='biotrade_stock_planning' AND `settings` NOT LIKE '%doc_type_production_request%';

######################################################################################
# 2014-11-10 - Modified settings of production dashlet

# Modified settings of production dashlet
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, 'department_all := 1', 'department_all := 1\r\nbatch_days_limit := 50')
WHERE `type`='biotrade_production' AND `settings` NOT LIKE '%batch_days_limit%';

######################################################################################
# 2014-11-18 - Changed settings for report 'biotrade_sales'
#            - Fix articles without measure

# Changed settings for report 'biotrade_sales'
UPDATE reports
  SET settings = REPLACE(settings, 'fir_types := 105,103,109,108,107', 'fir_types := 105,103,109,108,107,1')
  WHERE `type` = 'biotrade_sales'
    AND settings NOT LIKE '%fir_types := 105,103,109,108,107,1%';
# Fix articles without measure
UPDATE fin_incomes_reasons AS fir
  JOIN gt2_details AS gd
    ON (gd.model = 'Finance_Incomes_Reason'
      AND gd.model_id = fir.id)
  JOIN gt2_details_i18n AS gdi
    ON (gdi.parent_id = gd.id
      AND gdi.article_measure_name = '')
  SET gdi.article_measure_name = '1'
  WHERE fir.type IN (105,103,109,108,107,1);
UPDATE gt2_details_i18n
  SET article_measure_name = '4'
  WHERE parent_id IN (13246, 13250);
UPDATE gt2_details_i18n
  SET article_measure_name = '5'
  WHERE parent_id IN (13248, 13252);

######################################################################################
# 2015-03-27 - Add setting for report 'biotrade_sales'

# Add setting for report 'biotrade_sales'
UPDATE reports
  SET settings = CONCAT(settings, '\r\nincompatible_fir_types := 105,1')
  WHERE `type` = 'biotrade_sales'
    AND settings NOT LIKE '%incompatible_fir_types%';

######################################################################################
# 2015-06-10 - Fixed search by some additional variables - copied source from visible autocompleter field into hidden one

# Fixed search by some additional variables - copied source from visible autocompleter field into hidden one
SELECT @source := `source` FROM `_fields_meta` WHERE `id` = 302;
UPDATE `_fields_meta` SET `source` = @source WHERE `id` = 301;
SELECT @source := `source` FROM `_fields_meta` WHERE `id` = 903;
UPDATE `_fields_meta` SET `source` = @source WHERE `id` = 904;

######################################################################################
# 2015-12-21 - Added report for sending emails for debt collection

# Added report for sending emails for debt collection
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(334, 'bgs_debt_collection_email_sender', '# settings for report filters\r\nfin_documents_types := 1,2,3,105,107\r\ncustomers_types := 2,3,5,6,7,8,9,10,11,12\r\nupcoming_num_days := 2\r\n\r\n# created document types per company\r\ndocument_type1 := 28\r\ndocument_type2 := 28\r\ndocument_type3 := \r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(334, 'Писма за дължими суми', '01. Финанси', NULL, 'bg'),
(334, 'Debt collection emails', '01. Financial', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 334, 0, 1);

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` = 'generate_report' AND `model_type` = 334;

######################################################################################
# 2016-03-29 - Updated GT2 data in a "Visit" incomes reason with filled in article_name and no article_id

# Updated GT2 data in a "Visit" incomes reason with filled in article_name and no article_id
UPDATE `gt2_details`
SET `article_id` = 479, `article_code` = 'PR0035', `average_weighted_delivery_price` = 0.190000, `last_delivery_price` = 0.270000, `price` = 11.500000, `price_with_discount` = 11.500000, `vat_value` = 2.300000, `profit_no_final_discount` = 11.310000, `subtotal_profit_no_final_discount` = 11.310000, `subtotal` = 11.500000, `subtotal_with_discount` = 11.500000, `subtotal_with_vat` = 13.800000, `subtotal_with_vat_with_discount` = 13.800000
WHERE `id` = 41628;

UPDATE `gt2_details_i18n`
SET `article_name` = 'СЕБОМАКС ШАМПОАН СЕНЗИТИВ', `article_measure_name` = '1'
WHERE `parent_id` = 41628 AND `lang` = 'bg';

UPDATE `fin_incomes_reasons`
SET `total_without_discount` = 11.500000, `total` = 11.500000, `total_vat` = 2.300000, `total_with_vat` = 13.800000
WHERE `id` = 7093;

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 20 DAY) <= NOW()' WHERE `id` = 8;

######################################################################################
# 2018-03-09 - Updated the names of few of the settings in the biotrade_week_schedule dashlet

# Updated the names of few of the settings in the biotrade_week_schedule dashlet
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'customer_pharmacy :=', 'customer_types_show_tags :=') WHERE `type`='biotrade_week_schedule' AND `settings` LIKE '%customer_pharmacy :=%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'customer_pharmacy_tags_ids :=', 'customer_show_tags_ids :=') WHERE `type`='biotrade_week_schedule' AND `settings` LIKE '%customer_pharmacy_tags_ids :=%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\ncustomer_medic :=', '\r\ncustomer_show_tags_groups :=\r\ncustomer_medic :=') WHERE `type`='biotrade_week_schedule' AND `settings` NOT LIKE '%customer_show_tags_groups :=%';

######################################################################################
# 2018-03-13 - Updated settings to delete customer_medic setting and change the value of the setting customer_types_show_tags

# Updated settings to delete customer_medic setting and change the value of the setting customer_types_show_tags
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\ncustomer_medic := 3', '')  WHERE `type`='biotrade_week_schedule' AND `settings` LIKE '%customer_medic := 3%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\ncustomer_types_show_tags := 2', '\r\ncustomer_types_show_tags := 2,3')  WHERE `type`='biotrade_week_schedule' AND `settings` NOT LIKE '%customer_types_show_tags := 2,3%';

######################################################################################
# 2018-03-26 - Updated setting of 'goods_movement' general report

# Updated setting of 'goods_movement' general report
UPDATE `reports`
SET `settings` = REPLACE(`settings`, 'include_waste := ', 'include_waste := 1')
WHERE `type` = 'goods_movement' AND `settings` NOT LIKE '%include_waste := 1%';

######################################################################################
# 2018-07-27 - Update for table _fields_meta

# Updating source of the field to inclode javascript option
UPDATE `_fields_meta`
SET `source`= CONCAT(`source`, '\r\njavascript := document.addEventListener("DOMContentLoaded",function(e){$$("tr[id^=var_group]").forEach(function(e){if(1!=e.select("select option:selected")[0].value){e.select("input, textarea, select").forEach(function(e){e.offsetWidth>0&&e.offsetHeight>0&&toggleReadonly(e,!0)}),e.select("img.hide_row")[0].addClassName("hidden")}})});')
WHERE  `model_type` = 3 AND `model` = 'Document' AND `name` = 'group_table_2' AND `source` NOT LIKE '%javascript%';

######################################################################################
# 2018-07-30 - Update for table _fields_meta

# Updating the javascript option
UPDATE `_fields_meta` SET `source`='totals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total_discount_surplus_field, total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\ntext_align := left\npermissions_edit := 1\npermissions_view := 1\njavascript := document.addEventListener("DOMContentLoaded", function (e) {addField(\\\'var_group_0\\\');$$("tr[id^=var_group]").forEach(function (row) {if (1 != row.select("select.free_field1")[0].value && row.select("select.free_field1")[0].value != ""){row.select("input, textarea,select").forEach(function (input) {if(input.offsetWidth > 0 && input.offsetHeight > 0) {toggleReadonly(input, true);}});row.select("img.hide_row")[0].addClassName("hidden")}})});'
WHERE  `model_type` = 3 AND `model` = 'Document' AND `name` = 'group_table_2';

######################################################################################
# 2018-08-03 - Update for table _fields_meta

# Updating the javascript option
UPDATE `_fields_meta` SET `source`='totals_texts_colspan := 3\ntotals_texts_rowspan := 5\nuse_as_plain := total_discount_surplus_field, total, total_vat_rate, total_vat, total_no_vat_reason, total_no_vat_reason_text, total_with_vat, currency, total_discount_value, total_discount_percentage, total_surplus_value, total_surplus_percentage, total_without_discount\ntext_align := left\npermissions_edit := 1\npermissions_view := 1\njavascript := document.addEventListener("DOMContentLoaded", function (e) {addField(\'var_group_0\');$$("tr[id^=var_group]").forEach(function (row) {if (1 != row.select("select.free_field1")[0].value && row.select("select.free_field1")[0].value != ""){row.select("input, textarea,select").forEach(function (input) {if(input.offsetWidth > 0 && input.offsetHeight > 0) {toggleReadonly(input, true);}});row.select("img.hide_row")[0].addClassName("hidden")}})});'
WHERE  `model_type` = 3 AND `model` = 'Document' AND `name` = 'group_table_2';

######################################################################################
# 2018-12-03 - Update the 'biotrade_week_schedule' dashlet settings so they will contain required tags groups

# Update the 'biotrade_week_schedule' dashlet settings so they will contain required tags groups
UPDATE `dashlets_plugins`
SET `settings`=REPLACE(`settings`, '\r\n\r\ndaily_plan_meeting_marked :=', '\r\nrequired_tags_groups :=\r\n\r\ndaily_plan_meeting_marked :=')
WHERE `type`='biotrade_week_schedule' AND `settings` NOT LIKE '%required_tags_groups%';

######################################################################################
# 2020-01-31 - Updated 'biotrade_finance_timesheet' report with new settings

# Updated 'biotrade_finance_timesheet' report with new settings
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\nnom_fuels_id :=', '\r\ndocument_route_from := traveled_route_from\r\ndocument_route_to := traveled_route_to\r\n\r\nnom_fuels_id :=') WHERE `type` = 'biotrade_finance_timesheet' AND `settings` NOT LIKE '%document_route_from%';

######################################################################################
# 2020-06-25 - MySQL queries to fix the warehouse quantity and price data mismatch

# Update the precision options
UPDATE `settings` SET `value`='4' WHERE `section`='precision' AND `name` IN ('gt2_quantity', 'gt2_rows', 'gt2_total', 'gt2_total_vat', 'gt2_total_with_vat', 'nom_last_delivery_price') AND `value`='2';

UPDATE `fin_warehouses_quantities` fwq, `fin_warehouses_quantities` fwq2
SET fwq.quantity=IF((fwq.quantity+fwq2.quantity)>0, fwq.quantity+fwq2.quantity, 0.000000)
WHERE fwq.parent_id=fwq2.parent_id AND fwq.nomenclature_id=fwq2.nomenclature_id AND fwq.batch_id=fwq2.batch_id AND fwq.`delivery_price`!=fwq2.`delivery_price` AND fwq.`updated`<fwq2.`updated`;

# Set flag (use serial_number) to mark which are the records which have to be deleted
UPDATE `fin_warehouses_quantities` fwq, `fin_warehouses_quantities` fwq2
SET fwq2.serial_number=1
WHERE fwq.parent_id=fwq2.parent_id AND
      fwq.nomenclature_id=fwq2.nomenclature_id AND
	  fwq.batch_id=fwq2.batch_id AND fwq.`delivery_price`!=fwq2.`delivery_price`  AND fwq.`updated`<fwq2.`updated`;

# Set flag to mark which are the records we will use to update other tables
UPDATE `fin_warehouses_quantities` fwq, `fin_warehouses_quantities` fwq2
SET fwq.serial_number=2
WHERE fwq.parent_id=fwq2.parent_id AND
      fwq.nomenclature_id=fwq2.nomenclature_id AND
	  fwq.batch_id=fwq2.batch_id AND fwq.`delivery_price`!=fwq2.`delivery_price`  AND fwq.`updated`<fwq2.`updated`;

# Update delivery price in the gt2_batches_data table
UPDATE gt2_batches_data bd, gt2_details g, fin_warehouses_documents fwd, fin_warehouses_quantities as fwq
SET bd.delivery_price=fwq.`delivery_price`
WHERE bd.parent_id=g.id AND g.added>'2020-06-10 12:00:00' AND g.model_id=fwd.id AND fwq.serial_number=2 AND
      fwq.batch_id=bd.batch_id AND (fwd.warehouse=fwq.parent_id OR fwd.to_warehouse=fwq.parent_id) AND
      fwq.`delivery_price`!=bd.delivery_price AND fwq.`nomenclature_id`=g.article_id;

# Delete the rows with new info that we will not need anymore from fin_warehouses_quantities
DELETE FROM `fin_warehouses_quantities` WHERE `serial_number`='1';

# Reset the temporary flag
UPDATE `fin_warehouses_quantities` SET `serial_number`='' WHERE `serial_number`='2';
