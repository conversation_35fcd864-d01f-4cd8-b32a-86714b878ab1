#####################################################################################################
### SQL nZoom Specific Updates - Фасилити Оптимум България АД (http://foptimum.n-zoom.com/) ###
#####################################################################################################

########################################################################
# 2012-07-12 - Added dashlet plugin for quick adding of request documents for Facility Optimum installation (1791)

# Added dashlet plugin for quick adding of request documents for Facility Optimum installation (1791)
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'fo_add_requests', 'document_type_request := 1\r\nnom_type_object := 1\r\nnom_type_service := 5\r\ncustomer_type_responsible := 2\r\n\r\ndocument_default_department := 1\r\n\r\n# Responsible customer variables\r\nresponsible_object_var := object_id\r\nresponsible_consumer_var := consumer_id\r\n\r\n# Employee customer variables\r\nemployee_city_id_var := city_id\r\nemployee_service_name_var := service_name\r\n\r\n# Request document variables\r\nrequest_date_var := date_request\r\nrequest_name_var := name_request\r\nrequest_name_id_var := name_request_id\r\nrequest_object_id_var := object_id\r\nrequest_object_name_var := object_name\r\nrequest_object_type_var := object_type\r\nrequest_city_id_var := city_id\r\nrequest_city_name_var := city_name\r\nrequest_object_address_var := object_address\r\nrequest_object_mail_var := object_mail\r\nrequest_object_phone_var := object_phone\r\nrequest_service_name_var := service_name\r\nrequest_description_var := description_service\r\nrequest_priority_var := priority_service\r\n\r\n# fill options\r\nobject_fill_options := object_type => object_type, city_id => city_id, city_name => city_name, object_address => client_address, object_phone => client_phone|client_mobile, object_mail => client_mail', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Пускане на заявки за извършване на услуги', 'Инфо панел, предназначен за бързо добавяне на заявки за извършване на услуги', 'bg'),
(LAST_INSERT_ID(), 'Add requests for provision of services', 'Dashlet for quick adding of requests for provision of services', 'en');

########################################################################
# 2012-12-14 - Updated settings of dashlet plugin for quick adding of request documents for Facility Optimum installation (1791)

# Updated settings of dashlet plugin for quick adding of request documents for Facility Optimum installation (1791)
UPDATE `dashlets_plugins`
SET `settings` = '# Types\r\ndocument_type_request := 1\r\ndocument_type_agreement := 2\r\nnom_type_object := 1\r\ncustomer_type_responsible := 2\r\ncustomer_type_client := 3\r\n\r\ndocument_default_department := 1\r\ndocument_status := opened\r\ndocument_substatus := 3\r\n\r\n# Responsible customer variables\r\nresponsible_consumer_var := consumer_id\r\n\r\n# Client customer variables\r\nclient_service_var := service_id\r\nclient_response_var := response_id \r\n\r\n# Agreement document variables\r\nagreement_service_var := service_name\r\nagreement_priority_var := priority_service\r\n\r\n# Selected radio for request type\r\ntype_value := 5\r\n\r\n# Request document variables\r\nrequest_date_var := date_request\r\nrequest_name_var := name_request\r\nrequest_name_id_var := name_request_id\r\nrequest_type_request_var := type_request\r\nrequest_object_id_var := object_id\r\nrequest_object_name_var := object_name\r\nrequest_object_type_var := object_type\r\nrequest_region_id_var := region_id\r\nrequest_region_name_var := region_name\r\nrequest_city_id_var := city_id\r\nrequest_city_name_var := city_name\r\nrequest_object_address_var := object_address\r\nrequest_object_mail_var := object_mail\r\nrequest_object_phone_var := object_phone\r\nrequest_service_id_var := service_id\r\nrequest_service_name_var := service_name\r\nrequest_description_var := description_service\r\nrequest_priority_var := priority_service'
WHERE `type` = 'fo_add_requests';

########################################################################
# 2012-12-17 - Added automations to manage request documents on status change

# Added automations to manage request documents on status change
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 1, '# Types\r\ndocument_type_request := 1\r\ndocument_type_agreement := 2\r\nnomenclature_type_object := 1\r\n\r\n# Employee vars\r\nemployee_region := region_id\r\nemployee_service := service_id\r\nemployee_client := client_id\r\n\r\n# Object vars\r\nobject_object_service := object_type_service\r\n\r\n# Request vars\r\nrequest_approval_date := approval_date\r\nrequest_service := service_id\r\nrequest_region := region_id\r\nrequest_priority := priority_service\r\nrequest_object := object_id\r\n\r\n# Agreement vars\r\nagreement_service := service_name\r\nagreement_object_type := object_type_service\r\nagreement_request_type := type_request_service\r\nagreement_priority := priority_service\r\nagreement_max_term := max_term\r\nagreement_unit_time := unit_time\r\n\r\n# Dropdown values\r\nrq_working_hours := 1\r\nrq_outside_working_hours := 2\r\nrq_anytime := 3\r\nobj_type_any := без значение\r\npriority_any := 55\r\nunit_time_hour := 1\r\nunit_time_day := 2\r\n\r\n# Other settings\r\nnum_assignees := 2\r\nworking_hours_start := 09:00\r\nworking_hours_end := 18:00', 'condition := (''[prev_b_substatus]'' != ''4'' && ''[b_substatus]'' == ''4'')', 'plugin := fo\r\nmethod := manageRequestOnApprove', NULL, 1, 1),
(NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 1, 'request_new_status := locked\r\nrequest_new_substatus := 5', 'condition := (''[prev_b_substatus]'' != ''1'' && ''[b_substatus]'' == ''1'')', 'plugin := fo\r\nmethod := manageRequestOnAccept', NULL, 2, 1);

######################################################################################
# 2013-01-17 - Add new report - 'fo_activity_analysis' - for the Facility Optimum installation (1791)

# Add new report - 'fo_activity_analysis' - for the Facility Optimum installation (1791)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('254', 'fo_activity_analysis', 'document_type_request := 1\r\nnomenclature_type_region := 10\r\nnomenclature_type_service := 5\r\nnomenclature_type_subservice := 8\r\ncustomer_type_client := 3\r\nnomenclature_no_subservice := 367\r\n\r\nfield_date_request := date_request\r\nfield_region_id := region_id\r\nfield_service_id := service_id\r\nfield_subservice_id := subservice_id\r\nfield_object_id := object_id\r\nfield_report_description := report_description\r\nfield_to_service := to_service', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('254', 'Анализ на дейността', NULL, NULL, 'bg'),
  ('254', 'Activity analysis',   NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '254', '0', '1'),
  ('reports', 'export',          '254', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '254';

######################################################################################
# 2013-01-31 - Add new report - 'fo_orders_history' - for the Facility Optimum installation (1791)

# Add new report - 'fo_orders_history' - for the Facility Optimum installation (1791)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('256', 'fo_orders_history', 'document_type_request := 1\r\ncustomer_type_client := 3\r\nnomenclature_type_object := 1\r\n\r\nfield_date_request := date_request\r\nfield_approval_date := approval_date\r\nfield_object_id := object_id\r\nfield_service_id := service_id\r\n\r\nworking_time_starts := 09:00\r\nworking_time_ends := 18:00', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('256', 'Хронология на заявки', NULL, NULL, 'bg'),
  ('256', 'History of orders',    NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '256', '0', '1'),
  ('reports', 'export',          '256', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '256';

########################################################################
# 2013-04-15 - Updated settings of dashlet plugin for quick adding of request documents for Facility Optimum installation (1791)

# Updated settings of dashlet plugin for quick adding of request documents for Facility Optimum installation (1791)
UPDATE `dashlets_plugins`
SET `settings`='# Types\r\ndocument_type_request := 1\r\ndocument_type_agreement := 2\r\ndocument_type_visit := 3\r\nnom_type_object := 1\r\ncustomer_type_responsible := 2\r\ncustomer_type_client := 3\r\n\r\ndocument_default_department := 1\r\ndocument_status := opened\r\ndocument_substatus_awaiting_approval := 3\r\ndocument_substatus_approved := 4\r\n\r\n# Responsible customer variables\r\nresponsible_consumer_var := consumer_id\r\n\r\n# Client customer variables\r\nclient_service_var := service_id\r\nclient_response_var := response_id \r\n\r\n# Agreement document variables\r\nagreement_service_var := service_name\r\nagreement_priority_var := priority_service\r\n\r\n# Visit document variables\r\nvisit_object_id_var := object_id\r\nvisit_performed_date_var := performed_date\r\nvisit_planned_date_var := planned_date\r\n\r\n# Selected radio for request type\r\ntype_value := 5\r\n\r\n# Request document variables\r\nrequest_date_var := date_request\r\nrequest_name_var := name_request\r\nrequest_name_id_var := name_request_id\r\nrequest_type_request_var := type_request\r\nrequest_object_id_var := object_id\r\nrequest_object_name_var := object_name\r\nrequest_object_type_var := object_type\r\nrequest_region_id_var := region_id\r\nrequest_region_name_var := region_name\r\nrequest_city_id_var := city_id\r\nrequest_city_name_var := city_name\r\nrequest_object_address_var := object_address\r\nrequest_object_mail_var := object_mail\r\nrequest_object_phone_var := object_phone\r\nrequest_service_id_var := service_id\r\nrequest_service_name_var := service_name\r\nrequest_description_var := description_service\r\nrequest_priority_var := priority_service'
WHERE `type`='fo_add_requests';

########################################################################
# 2013-04-22 - Changed conditions, settings and number of executions for automation 'manageRequestOnApprove'

# Changed conditions, settings and number of executions for automation 'manageRequestOnApprove'
UPDATE `automations`
  SET `conditions` = 'condition := (''[prev_b_substatus]'' != ''4'' && ''[b_substatus]'' == ''4'') || (''[prev_b_substatus]'' != ''11'' && ''[b_substatus]'' == ''11'')',
    `settings` = CONCAT(`settings`, '\r\nsubstatus_skip_deadline := 11'),
    `nums` = '2'
  WHERE `method` LIKE '%manageRequestOnApprove%'
    AND `settings` NOT LIKE '%substatus_skip_deadline%';

########################################################################
# 2013-07-24 - Changed settings for report 'fo_activity_analysis'

# Changed settings for report 'fo_activity_analysis'
UPDATE `reports`
  SET `settings` = 'nomenclature_type_object := 1\r\nnomenclature_type_region := 10\r\nnomenclature_type_service := 5\r\nnomenclature_type_subservice := 8\r\ncustomer_type_client := 3\r\nnomenclature_no_subservice := 367\r\nfield_nom_subservice_to_service := to_service\r\nfield_nom_object_region_id := region_id\r\n\r\ndocument_type_request := 1\r\nfield_doc_request_completed_on := completed_on\r\nfield_doc_request_date_request := date_request\r\nfield_doc_request_region_id := region_id\r\nfield_doc_request_service_id := service_id\r\nfield_doc_request_subservice_id := subservice_id\r\nfield_doc_request_object_id := object_id\r\nfield_doc_request_report_description := report_description\r\n\r\ndocument_type_planned_visit := 3\r\nfield_doc_planned_visit_performed_date := performed_date\r\nfield_doc_planned_visit_date_request := date_request\r\nfield_doc_planned_visit_service_id := service_id\r\nfield_doc_planned_visit_subservice_id := subservice_id\r\nfield_doc_planned_visit_object_id := object_id\r\nfield_doc_planned_visit_report_work := report_work'
  WHERE `type` = 'fo_activity_analysis'
    AND `settings` NOT LIKE '%document_type_planned_visit%';

########################################################################
# 2014-01-28 - Changed settings for report 'fo_activity_analysis'

# Changed settings for report 'fo_activity_analysis'
UPDATE `reports`
  SET `settings` = 'nomenclature_type_object := 1\r\nnomenclature_type_region := 10\r\nnomenclature_type_service := 5\r\nnomenclature_type_subservice := 8\r\ncustomer_type_client := 3\r\nnomenclature_no_subservice := 367\r\nfield_nom_subservice_to_service := to_service\r\nfield_nom_object_region_id := region_id\r\n\r\ndocument_type_request := 1\r\nfield_doc_request_completed_on := completed_on\r\nfield_doc_request_date_request := date_request\r\nfield_doc_request_region_id := region_id\r\nfield_doc_request_service_id := service_id\r\nfield_doc_request_subservice_id := subservice_id\r\nfield_doc_request_object_id := object_id\r\nfield_doc_request_report_description := report_description\r\nfield_doc_request_type_request := type_request\r\ndoc_request_type_request_reclamation := 6\r\n\r\ndocument_type_planned_visit := 3\r\nfield_doc_planned_visit_performed_date := performed_date\r\nfield_doc_planned_visit_date_request := date_request\r\nfield_doc_planned_visit_service_id := service_id\r\nfield_doc_planned_visit_subservice_id := subservice_id\r\nfield_doc_planned_visit_object_id := object_id\r\nfield_doc_planned_visit_report_work := report_work'
  WHERE `type` = 'fo_activity_analysis'
    AND `settings` NOT LIKE '%doc_request_type_request_reclamation%';

########################################################################
# 2014-03-17 - Changed settings for report 'fo_activity_analysis'

# PRE-DEPLOYED # Changed settings for report 'fo_activity_analysis'
#UPDATE `reports`
#  SET `settings` = 'nomenclature_type_object := 1\r\nnomenclature_type_region := 10\r\nnomenclature_type_service := 5\r\nnomenclature_type_subservice := 8\r\ncustomer_type_client := 3\r\nnomenclature_no_subservice := 367\r\nfield_nom_subservice_to_service := to_service\r\nfield_nom_object_region_id := region_id\r\n\r\ndocument_type_request := 1\r\nfield_doc_request_completed_on := completed_on\r\nfield_doc_request_date_request := date_request\r\nfield_doc_request_region_id := region_id\r\nfield_doc_request_service_id := service_id\r\nfield_doc_request_subservice_id := subservice_id\r\nfield_doc_request_object_id := object_id\r\nfield_doc_request_type_request := type_request\r\ndoc_request_type_request_reclamation := 6\r\n\r\ndocument_type_planned_visit := 3\r\nfield_doc_planned_visit_performed_date := performed_date\r\nfield_doc_planned_visit_date_request := date_request\r\nfield_doc_planned_visit_service_id := service_id\r\nfield_doc_planned_visit_subservice_id := subservice_id\r\nfield_doc_planned_visit_object_id := object_id\r\n\r\ndocument_type_protocol := 4\r\nfield_doc_protocol_parent_number_id := parent_number_id\r\nfield_doc_protocol_service_id := service_id\r\nfield_doc_protocol_subservice_id := subservice_id\r\nfield_doc_protocol_report_description := report_description\r\nfield_doc_protocol_completed_on := completed_on\r\nfield_doc_protocol_completed_start := completed_start\r\nfield_doc_protocol_swept_area := swept_area'
#  WHERE `type` = 'fo_activity_analysis'
#    AND `settings` NOT LIKE '%document_type_protocol%';

########################################################################
# 2014-04-04 - Changed settings for report 'fo_activity_analysis'

# PRE-DEPLOYED # Changed settings for report 'fo_activity_analysis'
#UPDATE `reports`
#  SET `settings` = 'nomenclature_type_object := 1\r\nnomenclature_type_region := 10\r\nnomenclature_type_service := 5\r\nnomenclature_type_subservice := 8\r\ncustomer_type_client := 3\r\nfield_nom_subservice_to_service := to_service\r\nfield_nom_object_region_id := region_id\r\n\r\ndocument_type_request := 1\r\nfield_doc_request_date_request := date_request\r\nfield_doc_request_region_id := region_id\r\nfield_doc_request_service_id := service_id\r\nfield_doc_request_subservice_id := subservice_id\r\nfield_doc_request_object_id := object_id\r\nfield_doc_request_type_request := type_request\r\ndoc_request_type_request_reclamation := 6\r\n\r\ndocument_type_planned_visit := 3\r\nfield_doc_planned_visit_service_id := service_id\r\nfield_doc_planned_visit_subservice_id := subservice_id\r\nfield_doc_planned_visit_object_id := object_id\r\n\r\ndocument_type_protocol := 4\r\nfield_doc_protocol_parent_number_id := parent_number_id\r\nfield_doc_protocol_service_id := service_id\r\nfield_doc_protocol_subservice_id := subservice_id\r\nfield_doc_protocol_report_description := report_description\r\nfield_doc_protocol_completed_start := completed_start\r\nfield_doc_protocol_swept_area := swept_area\r\nfield_doc_protocol_technician_id := technician_id\r\nfield_doc_protocol_technician_name := technician_name\r\nfield_doc_protocol_report_minutes := report_minutes'
#  WHERE `type` = 'fo_activity_analysis'
#    AND `settings` NOT LIKE '%field_doc_protocol_technician_id%';

########################################################################
# 2014-04-22 - Changed settings for report 'fo_activity_analysis'

# Changed settings for report 'fo_activity_analysis'
UPDATE `reports`
  SET `settings` = REPLACE(`settings`, 'customer_type_client := 3', 'customer_type_client := 3\r\ncustomer_type_employee := 1')
  WHERE `type` = 'fo_activity_analysis'
    AND `settings` NOT LIKE '%customer_type_employee%';

########################################################################
# 2014-06-03 - Changed settings for report 'fo_activity_analysis'

# Changed settings for report 'fo_activity_analysis'
UPDATE reports
  SET settings = REPLACE(
    settings,
    'document_type_protocol := 4',
    'document_type_protocol := 4\r\ndoc_protocol_statuses := closed')
  WHERE type = 'fo_activity_analysis'
    AND settings NOT LIKE '%doc_protocol_statuses%';

########################################################################
# 2014-06-05 - Add automation for validating documents of type "Protocol"

# Add automation for validating documents of type "Protocol"
INSERT INTO automations (module, automation_type, start_model_type, settings, conditions, method, after_action, nums) VALUES
  ('documents', 'before_action', '4', 'field_technician_id := technician_id\r\nfield_technician_name := technician_name\r\nfield_service_id := service_id\r\nfield_subservice_id := subservice_id\r\nfield_subservice_other_id := subservice_other_id\r\nfield_swept_area := swept_area\r\nservices := 8\r\nsubservices := 66,70', 'condition := [request_is_post]', 'plugin := fo\r\nmethod := validateProtocol', 'cancel_action_on_fail := 1', 0);

########################################################################
# 2014-06-19 - Updated settings of dashlet plugin for quick adding of request documents

# Updated settings of dashlet plugin for quick adding of request documents
UPDATE `dashlets_plugins`
SET `settings` = CONCAT(`settings`, '\r\n\r\n# Object nomenclature variables\r\nobject_response_id := response_id\r\n\r\n# Fixed assignments as Responsible\r\n#assignments_responsible_196 := 301,302,303,304\r\n')
WHERE `type` = 'fo_add_requests' AND `settings` NOT LIKE '%object_response_id%';

########################################################################
# 2014-07-04 - Add new setting for report 'fo_activity_analysis'
#            - Added automations to set deadline of requests under specific conditions

# Add new setting for report 'fo_activity_analysis'
UPDATE reports
  SET settings = REPLACE(settings, 'field_nom_object_region_id := region_id', 'field_nom_object_region_id := region_id\r\nfield_nom_object_client_id := client_id')
  WHERE `type` = 'fo_activity_analysis'
    AND settings NOT LIKE '%field_nom_object_client_id := client_id%';

# Added automations to set deadline of requests under specific conditions
UPDATE `automations` SET `name`='Задава "Одобрена на", Отговорници и "Срок" за Заявка при избор на състояние "Одобрена от ЦУ" или "За оферта"' WHERE `method` LIKE '%manageRequestOnApprove%';
UPDATE `automations` SET `name`='Задава Изпълнител и състояние "В изпълнение" за Заявка при избор на състояние "Приета"' WHERE `method` LIKE '%manageRequestOnAccept%';

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Обновявава "Срок" на Заявка при промяна на Приоритет или Услуга', 0, NULL, 1, 'documents', NULL, 'action', '1', '# Types\r\ndocument_type_agreement := 2\r\nnomenclature_type_object := 1\r\n\r\n# Object vars\r\nobject_object_service := object_type_service\r\n\r\n# Request vars\r\nrequest_approval_date := approval_date\r\nrequest_service := service_id\r\nrequest_priority := priority_service\r\nrequest_object := object_id\r\n\r\n# Agreement vars\r\nagreement_service := service_name\r\nagreement_object_type := object_type_service\r\nagreement_request_type := type_request_service\r\nagreement_priority := priority_service\r\nagreement_max_term := max_term\r\nagreement_unit_time := unit_time\r\nagreement_final_max_term := final_max_term\r\nagreement_final_unit_time := final_unit_time\r\n\r\n# Dropdown values\r\nrq_working_hours := 1\r\nrq_outside_working_hours := 2\r\nrq_anytime := 3\r\nobj_type_any := без значение\r\npriority_any := 55\r\nunit_time_hour := 1\r\nunit_time_day := 2\r\n\r\n# Other settings\r\nworking_hours_start := 09:00\r\nworking_hours_end := 18:00\r\nsubstatus_visited := 15', 'condition := ''[prev_a_priority_service]'' != ''[a_priority_service]'' || ''[prev_a_service_id]'' != ''[a_service_id]''', 'plugin := fo\r\nmethod := setDeadline', NULL, 3, 0),
(NULL, 'Обновявава "Срок" на Заявка при задаване на състояние "Посетен обект"', 0, NULL, 1, 'documents', NULL, 'action', '1', '# Types\r\ndocument_type_agreement := 2\r\nnomenclature_type_object := 1\r\n\r\n# Object vars\r\nobject_object_service := object_type_service\r\n\r\n# Request vars\r\nrequest_approval_date := approval_date\r\nrequest_service := service_id\r\nrequest_priority := priority_service\r\nrequest_object := object_id\r\n\r\n# Agreement vars\r\nagreement_service := service_name\r\nagreement_object_type := object_type_service\r\nagreement_request_type := type_request_service\r\nagreement_priority := priority_service\r\nagreement_final_max_term := final_max_term\r\nagreement_final_unit_time := final_unit_time\r\n\r\n# Dropdown values\r\nrq_working_hours := 1\r\nrq_outside_working_hours := 2\r\nrq_anytime := 3\r\nobj_type_any := без значение\r\npriority_any := 55\r\nunit_time_hour := 1\r\nunit_time_day := 2\r\n\r\n# Other settings\r\nworking_hours_start := 09:00\r\nworking_hours_end := 18:00', 'condition := ''[prev_b_substatus]'' != ''15'' && ''[b_substatus]'' == ''15''', 'plugin := fo\r\nmethod := setDeadline', NULL, 4, 0);

########################################################################
# 2014-07-07 - Changed settings for automation 'validateProtocol'

# Changed settings for automation 'validateProtocol'
UPDATE automations
  SET settings = CONCAT(settings, '\r\nfield_subservice_name := subservice_name\r\nfield_completed_start := completed_start'),
    name = 'Валидация на протокол'
  WHERE module = 'documents'
    AND start_model_type = 4
    AND method LIKE 'plugin := fo%method := validateProtocol'
    AND settings NOT LIKE '%field_subservice_name%';

########################################################################
# 2014-07-15 - Updated automation names

# Updated automation names
UPDATE `automations` SET `name`=REPLACE(`name`, 'Обновявава', 'Обновява') WHERE `name` LIKE 'Обновявава%';

########################################################################
# 2015-01-05 - Added planned visits import

# PRE-DEPLOYED # Added planned visits import
#INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
#(15, 'foptimum_visits', 'document_type_visit := 3\r\nnomenclature_type_object := 1\r\nnomenclature_type_service := 5', 1);

#INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#(15, 'Импортиране на планирани посещения', NULL, 'bg'),
#(15, 'Import of planned visits', NULL, 'en');

########################################################################
# 2015-02-17 - Added calendar days option to the setDeadline automation

# Added calendar days option to the setDeadline automation
UPDATE automations SET settings=REPLACE(settings, 'agreement_unit_time := unit_time', 'agreement_unit_time := unit_time\r\nagreement_days_type := days_type') WHERE (method LIKE '%setDeadline%' OR method LIKE '%manageRequestOnApprove%') AND settings NOT LIKE '%agreement_days_type := days_type%';
UPDATE automations SET settings=REPLACE(settings, 'unit_time_day := 2', 'unit_time_day := 2\r\ndays_working := 1\r\ndays_calendar := 2') WHERE (method LIKE '%setDeadline%' OR method LIKE '%manageRequestOnApprove%') AND settings NOT LIKE '%days_working := 1%';

########################################################################
# 2015-04-02 - Add settings for report "fo_orders_history"

# Add settings for report "fo_orders_history"
UPDATE reports
  SET settings = REPLACE(
    CONCAT(settings, '\r\n\r\nshow_nonportal_comments_to_portal_users := 0'),
    'field_service_id := service_id',
    'field_service_id := service_id\r\nfield_description_service := description_service')
  WHERE `type` = 'fo_orders_history'
    AND settings NOT LIKE '%field_description_service%';

######################################################################################
# 2017-05-25 - Updated not calculated values

# Updated not calculated values

-- type 4

-- Document 140018
UPDATE `documents_cstm` SET `value` = '105' WHERE `model_id` = '140018' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '105' WHERE `model_id` = '140018' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143208
UPDATE `documents_cstm` SET `value` = '175' WHERE `model_id` = '143208' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '175' WHERE `model_id` = '143208' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143239
UPDATE `documents_cstm` SET `value` = '25' WHERE `model_id` = '143239' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '25' WHERE `model_id` = '143239' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143316
UPDATE `documents_cstm` SET `value` = '110' WHERE `model_id` = '143316' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '110' WHERE `model_id` = '143316' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143400
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143400' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143400' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143402
UPDATE `documents_cstm` SET `value` = '75' WHERE `model_id` = '143402' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '75' WHERE `model_id` = '143402' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143404
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143404' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143404' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143406
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143406' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143406' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143433
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143433' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143433' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143438
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143438' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143438' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143442
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143442' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143442' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143444
UPDATE `documents_cstm` SET `value` = '70' WHERE `model_id` = '143444' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '70' WHERE `model_id` = '143444' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143446
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '143446' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '143446' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143449
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143449' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143449' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143463
UPDATE `documents_cstm` SET `value` = '110' WHERE `model_id` = '143463' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '110' WHERE `model_id` = '143463' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143471
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '143471' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '143471' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143506
UPDATE `documents_cstm` SET `value` = '720' WHERE `model_id` = '143506' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143506' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '420' WHERE `model_id` = '143506' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '143506' AND `var_id` = '431' AND `num` = '3' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143508
UPDATE `documents_cstm` SET `value` = '630' WHERE `model_id` = '143508' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '450' WHERE `model_id` = '143508' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '143508' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143511
UPDATE `documents_cstm` SET `value` = '270' WHERE `model_id` = '143511' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '270' WHERE `model_id` = '143511' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143561
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143561' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143561' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143571
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143571' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143571' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143575
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143575' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143575' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143612
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143612' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143612' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143614
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143614' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143614' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143634
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143634' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143638
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '143638' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '143638' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143640
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '143640' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '143640' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143695
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '143695' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143695' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143695' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143697
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143697' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143697' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143701
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '143701' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '143701' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143705
UPDATE `documents_cstm` SET `value` = '255' WHERE `model_id` = '143705' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '75' WHERE `model_id` = '143705' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '143705' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143780
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '143780' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '143780' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143785
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143785' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143785' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143788
UPDATE `documents_cstm` SET `value` = '15' WHERE `model_id` = '143788' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '15' WHERE `model_id` = '143788' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143807
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143807' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143807' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143807' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143840
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '143840' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '143840' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143840' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143848
UPDATE `documents_cstm` SET `value` = '300' WHERE `model_id` = '143848' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '143848' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143848' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143860
UPDATE `documents_cstm` SET `value` = '105' WHERE `model_id` = '143860' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '105' WHERE `model_id` = '143860' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143862
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143862' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '143862' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143872
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '143872' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '143872' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143876
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '143876' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '143876' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143878
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143878' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143961
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '143961' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '143961' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143969
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143969' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143969' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143975
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143975' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143987
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '143987' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143989
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143989' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '143989' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 143991
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '143991' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144001
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '144001' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144013
UPDATE `documents_cstm` SET `value` = '45' WHERE `model_id` = '144013' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '45' WHERE `model_id` = '144013' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144030
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144030' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144030' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144033
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '144033' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '144033' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144132
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '144132' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144140
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '144140' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144161
UPDATE `documents_cstm` SET `value` = '100' WHERE `model_id` = '144161' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144192
UPDATE `documents_cstm` SET `value` = '300' WHERE `model_id` = '144192' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '300' WHERE `model_id` = '144192' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144200
UPDATE `documents_cstm` SET `value` = '100' WHERE `model_id` = '144200' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '100' WHERE `model_id` = '144200' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144204
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '144204' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '144204' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144208
UPDATE `documents_cstm` SET `value` = '170' WHERE `model_id` = '144208' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '170' WHERE `model_id` = '144208' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144211
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144211' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144211' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144216
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144216' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144216' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144301
UPDATE `documents_cstm` SET `value` = '200' WHERE `model_id` = '144301' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '200' WHERE `model_id` = '144301' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144334
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144334' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144334' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144335
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144335' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144335' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144338
UPDATE `documents_cstm` SET `value` = '20' WHERE `model_id` = '144338' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '20' WHERE `model_id` = '144338' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144340
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '144340' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144346
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144346' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144358
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '144358' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144427
UPDATE `documents_cstm` SET `value` = '65' WHERE `model_id` = '144427' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '65' WHERE `model_id` = '144427' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144432
UPDATE `documents_cstm` SET `value` = '75' WHERE `model_id` = '144432' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '75' WHERE `model_id` = '144432' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144437
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '144437' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '144437' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144440
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '144440' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '144440' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144443
UPDATE `documents_cstm` SET `value` = '45' WHERE `model_id` = '144443' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '45' WHERE `model_id` = '144443' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144461
UPDATE `documents_cstm` SET `value` = '720' WHERE `model_id` = '144461' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '720' WHERE `model_id` = '144461' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144480
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '144480' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '144480' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144665
UPDATE `documents_cstm` SET `value` = '300' WHERE `model_id` = '144665' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144665' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '144665' AND `var_id` = '431' AND `num` = '2' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144668
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144668' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144668' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144676
UPDATE `documents_cstm` SET `value` = '20' WHERE `model_id` = '144676' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '20' WHERE `model_id` = '144676' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144712
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '144712' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '144712' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144717
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '144717' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '144717' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144730
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '144730' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '50' WHERE `model_id` = '144730' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144733
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '144733' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '144733' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144737
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144737' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144737' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144766
UPDATE `documents_cstm` SET `value` = '20' WHERE `model_id` = '144766' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '20' WHERE `model_id` = '144766' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144773
UPDATE `documents_cstm` SET `value` = '420' WHERE `model_id` = '144773' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '420' WHERE `model_id` = '144773' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144774
UPDATE `documents_cstm` SET `value` = '25' WHERE `model_id` = '144774' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '25' WHERE `model_id` = '144774' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144776
UPDATE `documents_cstm` SET `value` = '45' WHERE `model_id` = '144776' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '45' WHERE `model_id` = '144776' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144788
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '144788' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '144788' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144791
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '144791' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144797
UPDATE `documents_cstm` SET `value` = '420' WHERE `model_id` = '144797' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144806
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144806' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144809
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144809' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144809' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144815
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '144815' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '144815' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144822
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '144822' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '144822' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144827
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '144827' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '90' WHERE `model_id` = '144827' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144829
UPDATE `documents_cstm` SET `value` = '75' WHERE `model_id` = '144829' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '75' WHERE `model_id` = '144829' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144833
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '144833' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '144833' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144881
UPDATE `documents_cstm` SET `value` = '420' WHERE `model_id` = '144881' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144886
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144886' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '144886' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144907
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144907' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144907' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144915
UPDATE `documents_cstm` SET `value` = '270' WHERE `model_id` = '144915' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '270' WHERE `model_id` = '144915' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144949
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144949' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144949' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 144957
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144957' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '144957' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145051
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145051' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145051' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145053
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145053' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145053' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145054
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145054' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145054' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145055
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '145055' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '145055' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145058
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '145058' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '145058' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145059
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '145059' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '145059' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145062
UPDATE `documents_cstm` SET `value` = '85' WHERE `model_id` = '145062' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '85' WHERE `model_id` = '145062' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145083
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145083' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145092
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145092' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145092' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145096
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145096' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145096' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145098
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '145098' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '40' WHERE `model_id` = '145098' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145099
UPDATE `documents_cstm` SET `value` = '135' WHERE `model_id` = '145099' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '135' WHERE `model_id` = '145099' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145116
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '145116' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '150' WHERE `model_id` = '145116' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145118
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '145118' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '210' WHERE `model_id` = '145118' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145138
UPDATE `documents_cstm` SET `value` = '270' WHERE `model_id` = '145138' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '270' WHERE `model_id` = '145138' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145147
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '145147' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '180' WHERE `model_id` = '145147' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145150
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '145150' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '145150' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145151
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145151' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145151' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145153
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145153' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145153' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145164
UPDATE `documents_cstm` SET `value` = '130' WHERE `model_id` = '145164' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '130' WHERE `model_id` = '145164' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145172
UPDATE `documents_cstm` SET `value` = '85' WHERE `model_id` = '145172' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '85' WHERE `model_id` = '145172' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145173
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145173' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145173' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145174
UPDATE `documents_cstm` SET `value` = '105' WHERE `model_id` = '145174' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '105' WHERE `model_id` = '145174' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145214
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '145214' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '30' WHERE `model_id` = '145214' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145233
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145233' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145233' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145239
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145239' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145246
UPDATE `documents_cstm` SET `value` = '100' WHERE `model_id` = '145246' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '100' WHERE `model_id` = '145246' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145275
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145275' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145275' AND `var_id` = '431' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145387
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '145387' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145420
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145420' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145662
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145662' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145737
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '145737' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 145739
UPDATE `documents_cstm` SET `value` = '240' WHERE `model_id` = '145739' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 146026
UPDATE `documents_cstm` SET `value` = '60' WHERE `model_id` = '146026' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';

-- Document 146040
UPDATE `documents_cstm` SET `value` = '120' WHERE `model_id` = '146040' AND `var_id` = '433' AND `num` = '1' AND `lang` = '' AND `value` = '' AND `modified` < '2017-05-22 00:00:00';
