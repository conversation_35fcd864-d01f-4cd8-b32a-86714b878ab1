###################################################################################
### SQL nZoom Specific Updates ИЦ Глобалтест (http://87.120.213.51:8082/) ###
###################################################################################

######################################################################################
# 2012-03-08 - Added new report - 'globaltest_calibration_plan' - for the Globaltest installation (1742)

# Added new report - 'globaltest_calibration_plan' - for the Globaltest installation (1742)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('197', 'globaltest_calibration_plan', 'nomenclature_type_technicle_means := 6\r\nnomenclature_category := 1\r\ndocument_type_calibration_certificate := 13\r\ndocument_type_repair_protocol := 14\r\nnext_calibration_date_color_past := DB0416\r\nnext_calibration_date_color_present := FF9C2A\r\nnext_calibration_date_max_days_left := 10', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('197', 'План за калибриране', NULL, NULL, 'bg'),
  ('197', 'Calibration plan',    NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '197', '0', '1'),
  ('reports', 'export',          '197', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND (`action` = 'generate_report'
        OR `action` = 'export')
      AND `model_type` = '197';

######################################################################################
# 2012-03-09 - Added automation for updating the deadline of reclamation type documents based on the document date

# Added automation for updating the deadline of reclamation type documents based on the document date
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 5, 'add_working_days := 7\r\ndeadline_hour := 18:00:00', 'condition := \'[prev_b_date]\' != \'[b_date]\'', 'plugin := globaltest\r\nmethod := updateDeadline', NULL, 0, 0);

######################################################################################
# 2012-03-15 - Added automation for updating a QMS type nomenclature related to a document

# Added automation for updating a QMS type nomenclature related to a document
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 24, 'nomenclature_type := 18\r\nstatus := closed\r\nsubstatus := 42', 'condition := \'[a_qms_doc_id]\' > 0', 'plugin := globaltest\r\nmethod := updateNomenclatureQMS', NULL, 0, 0);

######################################################################################
# 2012-03-19 - Added automation for updating the additional var date_approval of the current documetn
#            - Added automation for setting the document deadline

# Added automation for updating the additional var date_approval of the current documetn
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 11, 'var_id := 1101', 'condition := [prev_b_substatus] != 24\r\ncondition := [b_substatus] == 24', 'plugin := globaltest\r\nmethod := updateDateApproval', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 15, 'var_id := 1505', 'condition := [prev_b_substatus] != 58\r\ncondition := [b_substatus] == 58', 'plugin := globaltest\r\nmethod := updateDateApproval', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 16, 'var_id := 1604', 'condition := [prev_b_substatus] != 63\r\ncondition := [b_substatus] == 63', 'plugin := globaltest\r\nmethod := updateDateApproval', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 17, 'var_id := 1716', 'condition := [prev_b_substatus] != 68\r\ncondition := [b_substatus] == 68', 'plugin := globaltest\r\nmethod := updateDateApproval', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 20, 'var_id := 2021', 'condition := [prev_b_substatus] != 37\r\ncondition := [b_substatus] == 37', 'plugin := globaltest\r\nmethod := updateDateApproval', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 24, 'var_id := 2410', 'condition := [prev_b_substatus] != 42\r\ncondition := [b_substatus] == 42', 'plugin := globaltest\r\nmethod := updateDateApproval', NULL, 0, 0);

# Added automation for setting the document deadline
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 1, 'hours := 4', 'condition := 1', 'plugin := globaltest\r\nmethod := setDocumentDeadline', NULL, 0, 0);

######################################################################################
# 2012-03-27 - Update settings of report 'globaltest_calibration_plan' to have an option for exporting with a template

# Update settings of report 'globaltest_calibration_plan' to have an option for exporting with a template
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_technicle_means := 6\r\nnomenclature_category := 1\r\ndocument_type_calibration_certificate := 13\r\ndocument_type_repair_protocol := 14\r\nnext_calibration_date_color_past := DB0416\r\nnext_calibration_date_color_present := FF9C2A\r\nnext_calibration_date_max_days_left := 10'
  WHERE `id` = '197';
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'calibration_plan_table', 'Report', 'send', 'all', ',197,', 'calibration_plan_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'План за калибриране (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Calibration plan (table)', NULL, 'en');

######################################################################################
# 2012-04-02 - Added automation for updating the date fields of "Test method" type nomenclature
#            - Change method and settings of the automation for the "Test method" type nomenclature
#            - Add automation for updating dates in "Test method" and "TSII" type nomenclatures

# Added automation for updating the date fields of "Test method" nomenclature
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 22, 'validation_protocol := 22\r\nverification_protocol := 23', 'condition := \'[a_method_id]\' != \'\'\r\ncondition := \'[a_method_name]\' != \'\'', 'plugin := globaltest\r\nmethod := updateNomenclatureTestMethod', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 23, 'validation_protocol := 22\r\nverification_protocol := 23', 'condition := \'[a_method_id]\' != \'\'\r\ncondition := \'[a_method_name]\' != \'\'', 'plugin := globaltest\r\nmethod := updateNomenclatureTestMethod', NULL, 0, 0);

# Change method and settings of the automation for the "Test method" nomenclature
UPDATE `automations`
  SET `method` = 'plugin := globaltest\r\nmethod := updateNomenclatureNextDate',
    `settings` = 'validation_protocol := 22\r\nverification_protocol := 23\r\ncalibration_certificate := 13\r\nrepair_protocol := 14'
  WHERE `method` LIKE '%updateNomenclatureTestMethod'
  AND `start_model_type` = '22';
UPDATE `automations`
  SET `method` = 'plugin := globaltest\r\nmethod := updateNomenclatureNextDate',
    `settings` = 'validation_protocol := 22\r\nverification_protocol := 23\r\ncalibration_certificate := 13\r\nrepair_protocol := 14'
  WHERE `method` LIKE '%updateNomenclatureTestMethod'
    AND `start_model_type` = '23';

# Add automation for updating dates in "Test method" and "TSII" type nomenclatures
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 13, 'validation_protocol := 22\r\nverification_protocol := 23\r\ncalibration_certificate := 13\r\nrepair_protocol := 14', 'condition := \'[a_article_id]\' != \'\'\r\ncondition := \'[a_article_name]\' != \'\'', 'plugin := globaltest\r\nmethod := updateNomenclatureNextDate', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 14, 'validation_protocol := 22\r\nverification_protocol := 23\r\ncalibration_certificate := 13\r\nrepair_protocol := 14\r\nfield_tsii_id := 50810\r\nnomenclature_type_test_method := 8', 'condition := \'[a_article_id]\' != \'\'\r\ncondition := \'[a_article_name]\' != \'\'', 'plugin := globaltest\r\nmethod := updateNomenclatureNextDate', NULL, 0, 0);

######################################################################################
# 2012-04-04 - Added new report - 'globaltest_warehouse_availabilities' - for the Globaltest installation (1742)
#            - Added header plugin
#            - Added placeholders for header plugin

# Added new report - 'globaltest_warehouse_availabilities' - for the Globaltest installation (1742)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('200', 'globaltest_warehouse_availabilities', 'cartridge := 9\r\nchemical := 10\r\nglassware := 11\r\nssm := 15\r\nssv := 16', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('200', 'Наличности',        NULL, NULL, 'bg'),
  ('200', 'Availabilities',    NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '200', '0', '1'),
  ('reports', 'export',          '200', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND (`action` = 'generate_report'
        OR `action` = 'export')
      AND `model_type` = '200';

#Added header plugin
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(27, '', 0, 'globaltest', 'prepareHeader', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(27, 'Подготовка на хедър', 'Подготвят се данни за "Идентификация", "Текуща редакция", "Дата на текуща редакция" и пр.', 'bg', NOW()),
(27, 'Header Preparation', 'Data prepared for header content including Code, Current Version, Current Date etc.', 'en', NOW());

#Added placeholders for header plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'header_code', 'Document', 'basic', 'pattern_plugins', ',27,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Идентификация/Код', NULL, 'bg'),
(LAST_INSERT_ID(), 'Code', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'header_current_version', 'Document', 'basic', 'pattern_plugins', ',27,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Текуща редакция', NULL, 'bg'),
(LAST_INSERT_ID(), 'Current Version', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'header_date_current', 'Document', 'basic', 'pattern_plugins', ',27,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на текуща редакция', NULL, 'bg'),
(LAST_INSERT_ID(), 'Current Date', NULL, 'en');

######################################################################################
# 2012-04-06 - Added offer pattern plugin

#Added offer pattern plugin
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(28, 'Document', 2, 'globaltest', 'prepareOffer', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(28, 'Подготовка на оферта', 'Подготвят се данни за "Измерван показател", "Пробоподготовка", "Сравнително вещество" и пр.', 'bg', NOW()),
(28, 'Offer Preparation', 'Tables and totals fetched from GT2', 'en', NOW());

#Added placeholders for offer plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind1_type1', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за измервани показатели (Изглед 1)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Measured parameters table (Type 1)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind1_type2', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за измервани показатели (Изглед 2)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Measured parameters table (Type 2)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind1_total', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Сума от всички измервани показатели', NULL, 'bg'),
(LAST_INSERT_ID(), 'Measured parameters table total', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind2_type1', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за пробоподготовката (Изглед 1)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table containing data of all samples (Type 1)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind2_type2', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за пробоподготовката (Изглед 2)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table containing data of all samples (Type 2)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind2_total', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Сума за пробоподготовката', NULL, 'bg'),
(LAST_INSERT_ID(), 'All samples total', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind3_type1', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за сравнителните вещества (Изглед 1)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table containing all of the reference substances (Type 1)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind3_type2', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за сравнителните вещества (Изглед 2)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table containing all of the reference substances (Type 2)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind3_total', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Сума за сравнителните вещества', NULL, 'bg'),
(LAST_INSERT_ID(), 'Reference substances total', NULL, 'en');

######################################################################################
# 2012-04-10 - Update settings of report 'globaltest_warehouse_availabilities' to have an option for exporting with a template

# Update settings of report 'globaltest_warehouse_availabilities' to have an option for exporting with a template
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\ncartridge := 9\r\nchemical := 10\r\nglassware := 11\r\nssm := 15\r\nssv := 16'
  WHERE `id` = '200';
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'availabilities_table', 'Report', 'send', 'all', ',200,', 'availabilities_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Наличности (таблица)',   NULL, 'bg'),
  (LAST_INSERT_ID(), 'Availabilities (table)', NULL, 'en');

######################################################################################
# 2012-04-18 - Add new report - 'globaltest_goods_movement' - for the Globaltest installation (1742)
#                             - Add placeholders for 'globaltest_goods_movement' report templates

# Add new report - 'globaltest_goods_movement' - for the Globaltest installation (1742)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('203', 'globaltest_goods_movement', 'allows_files_generation := 1\r\nfiles_origin := self\r\ncartridge := 9\r\nchemical := 10\r\nglassware := 11\r\nssm := 15\r\nssv := 16\r\nrecord_of_handover := 7\r\nwaste_certificate := 11\r\nrecord_of_missing_stock := 13\r\nsurplus_record := 19', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('203', 'Движение на стока',  NULL, NULL, 'bg'),
  ('203', 'Movement of a good', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '203', '0', '1'),
  ('reports', 'export',          '203', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND (`action` = 'generate_report'
        OR `action` = 'export')
      AND `model_type` = '203';

# Add placeholders for 'globaltest_goods_movement' report templates
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'good_availability_table', 'Report', 'send', 'all', ',203,', 'good_availability_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Наличност на стоката (таблица)',   NULL, 'bg'),
  (LAST_INSERT_ID(), 'Availability of the good (table)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'good_documents_table', 'Report', 'send', 'all', ',203,', 'good_documents_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Документи съдържащи стоката (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Documents containing the good (table)', NULL, 'en');

######################################################################################
# 2012-04-21 - Added new automation that copies regulation document name to testing methods

#Added new automation that copies regulation document name to testing methods
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Копиране на нормативния документ в данните на методите за изпитание', 0, NULL, 1, 'nomenclatures', NULL, 'before_action', 17, '', 'condition := 1', 'plugin := globaltest\r\nmethod := copyRegulationDocumentToTestingMethods', 'cancel_action_on_fail := 1', 0, 0);

#Added regulation data for the already existing methods
DELETE ncstm1.* FROM nom_cstm as ncstm1,_fields_meta as fm
WHERE  fm.name IN ("regulation_id", "regulation_name") AND fm.model="Nomenclature" AND fm.model_type=8 AND fm.id=ncstm1.var_id;
SELECT @regulation_id := id FROM _fields_meta WHERE name='regulation_id' AND model_type=8 AND model='Nomenclature';
SELECT @regulation_name := id FROM _fields_meta WHERE name='regulation_name' AND model_type=8 AND model='Nomenclature';
INSERT INTO nom_cstm
SELECT ncstm1.value, @regulation_id, 1, ncstm1.model_id, now(), -1, NOW(), -1, ''
FROM nom_cstm as ncstm1,_fields_meta as fm
WHERE  fm.name="method_id" AND fm.model="Nomenclature" AND fm.model_type=17 AND fm.id=ncstm1.var_id AND ncstm1.value!='';
INSERT INTO nom_cstm
SELECT ncstm1.model_id, @regulation_name, 1, n.name, now(), -1, NOW(), -1, ''
FROM nom_cstm as ncstm1,_fields_meta as fm, nom_i18n as n
WHERE  fm.name="regulation_id" AND fm.model="Nomenclature" AND fm.model_type=8 AND fm.id=ncstm1.var_id AND ncstm1.value=n.parent_id AND n.lang='bg' AND ncstm1.value!='';

######################################################################################
# 2012-04-23 - Add new report - 'globaltest_tsii_file' - for the Globaltest installation (1742)
#            - Add placeholders for 'globaltest_tsii_file' report templates

# Add new report - 'globaltest_tsii_file' - for the Globaltest installation (1742)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('206', 'globaltest_tsii_file', 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_tsii := 6\r\ndocument_certificate := 13\r\ndocument_protocol := 14', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('206', 'Досие на ТСИИ', NULL, NULL, 'bg'),
  ('206', 'ТММТ file',     NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '206', '0', '1'),
  ('reports', 'export',          '206', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND (`action` = 'generate_report'
        OR `action` = 'export')
      AND `model_type` = '206';

# Add placeholders for 'globaltest_tsii_file' report templates
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'tsii_table', 'Report', 'send', 'all', ',206,', 'tsii_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'ТСИИ (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'TMMT (table)',   NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'tsii_certificates_table', 'Report', 'send', 'all', ',206,', 'tsii_certificates_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Сертификати от калибриране (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Calibration certificates (table)',     NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'tsii_protocols_table', 'Report', 'send', 'all', ',206,', 'tsii_protocols_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Протоколи от калибриране (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Calibration protocols (table)',      NULL, 'en');

########################################################################
# 2012-04-24 - Add new report - 'qms_management' for the Globaltest installation (1742)

# Add new report - 'qms_management' for the Globaltest installation (1742)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('205', 'qms_management', 'nomenclature_type_id := 18\r\ndefault_bg_color := ECEDEE\r\ndoc_type_color := FFA500\r\nnomenclature_color := 3FCC59\r\ndoc_type_width := 40\r\ncode_name_width := 635\r\ncurrent_version_min_width := 10\r\ndate_current_width := 70', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('205', 'Управление на СУК', NULL, NULL, 'bg'),
  ('205', 'Management of QMS', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '205', '0', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` = 'generate_report'
      AND `model_type` = '205';

########################################################################
# 2012-04-27 - Add automation for adding a handover when finishing an incomes reasons document

# Add automation for adding a handover when finishing an incomes reasons document
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, 0, NULL, 1, 'finance', 'incomes_reasons', 'before_action', 103, 'warehouse_id := 1',                      'condition := \'[b_status]\' != \'finished\'',                                                     'plugin := globaltest\r\nmethod := verifyBeforeAddHandover', NULL, 0, 0),
  (NULL, NULL, 0, NULL, 1, 'finance', 'incomes_reasons', 'action',        103, 'warehouse_id := 1\r\nlocation := София', 'condition := \'[prev_b_status]\' != \'finished\'\r\ncondition := \'[b_status]\' == \'finished\'', 'plugin := globaltest\r\nmethod := addHandover',             NULL, 0, 0);

######################################################################################
# 2012-05-04 - Add new report - 'globaltest_employee_cardboard' - for the Globaltest installation (1742)
#            - Add placeholders for 'globaltest_employee_cardboard' report templates

# Add new report - 'globaltest_employee_cardboard' - for the Globaltest installation (1742)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('209', 'globaltest_employee_cardboard', 'allows_files_generation := 1\r\nfiles_origin := self\r\ncustomer_type_employee := 1\r\ndocument_type_work_contract := 34\r\ndocument_type_education_protocol := 21', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('209', 'Картон на служител', NULL, NULL, 'bg'),
  ('209', 'Employee cardboard', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '209', '0', '1'),
  ('reports', 'export',          '209', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND (`action` = 'generate_report'
        OR `action` = 'export')
      AND `model_type` = '209';

# Add placeholders for 'globaltest_employee_cardboard' report templates
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'employee_education_and_experience_table', 'Report', 'send', 'all', ',209,', 'employee_education_and_experience_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Данни за служителя, образованието и стажа му (таблица)',            NULL, 'bg'),
  (LAST_INSERT_ID(), 'Data about the employee, his education and work experience(table)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'trainings_table', 'Report', 'send', 'all', ',209,', 'trainings_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Квалификация (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Training (table)',       NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'employee_name', 'Report', 'send', 'all', ',209,', 'employee_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Име, Презиме, Фамилия',  NULL, 'bg'),
  (LAST_INSERT_ID(), 'Name, Surename, Family', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'address_by_personal_id', 'Report', 'send', 'all', ',209,', 'address_by_personal_id', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Адрес по лична карта', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Address by ID card',   NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'secondary_education_year', 'Report', 'send', 'all', ',209,', 'secondary_education_year', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Средно образование (година)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Secondary education (year)',  NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'secondary_education_school_name', 'Report', 'send', 'all', ',209,', 'secondary_education_school_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Учебно заведение, където е придобито средното образование, град', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Institution where the secondary education is acquired, city',     NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'higher_education_year', 'Report', 'send', 'all', ',209,', 'higher_education_year', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Висше образование (година)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Higher education (year)',    NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'higher_education_school_name', 'Report', 'send', 'all', ',209,', 'higher_education_school_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Учебно заведение, където е придобито висшето образование, град', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Institution where the higher education is acquired, city',       NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'school_kind', 'Report', 'send', 'all', ',209,', 'school_kind', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Специалност (по диплома на последно завършено образование)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Specialty (by diploma of last graduated education)',         NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'total_work_experience', 'Report', 'send', 'all', ',209,', 'total_work_experience', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Общ трудов стаж',       NULL, 'bg'),
  (LAST_INSERT_ID(), 'Total work experience', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'total_work_experience_by_specialty', 'Report', 'send', 'all', ',209,', 'total_work_experience_by_specialty', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Трудов стаж по специалността',       NULL, 'bg'),
  (LAST_INSERT_ID(), 'Total work experience by specialty', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'work_contract_date', 'Report', 'send', 'all', ',209,', 'work_contract_date', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Дата на назначаване във фирмата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Work contract date',              NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'cv_old_work', 'Report', 'send', 'all', ',209,', 'cv_old_work', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Къде е работил/а преди това и каква длъжност е заемал/а',           NULL, 'bg'),
  (LAST_INSERT_ID(), 'What has he/she worked before and what position he/she has helded', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'cv_old_project', 'Report', 'send', 'all', ',209,', 'cv_old_project', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Участия в проекти и разработки',             NULL, 'bg'),
  (LAST_INSERT_ID(), 'Participation in projects and developments', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'cv_publications', 'Report', 'send', 'all', ',209,', 'cv_publications', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Научни публикации',       NULL, 'bg'),
  (LAST_INSERT_ID(), 'Scientific publications', NULL, 'en');

######################################################################################
# 2012-05-14 - Changed the automation for updating a QMS type nomenclature related to a document

# Changed the automation for updating a QMS type nomenclature related to a document
UPDATE `automations`
  SET `conditions` = 'condition := \'[a_qms_doc_id]\' > 0\r\ncondition := \'[b_status]\' == \'closed\'\r\ncondition := \'[b_substatus]\' == \'42\'',
    `nums` = '1'
  WHERE `method` LIKE 'plugin := globaltest%method := updateNomenclatureQMS';

######################################################################################
# 2012-05-15 - Change the conditions and the settings of the automation for updating the next date of a nomenclatures

# Change the conditions and the settings of the automation for updating the next date of a nomenclatures
UPDATE `automations`
  SET `conditions` = 'condition := \'[a_article_id]\' != \'\'\r\ncondition := \'[a_article_name]\' != \'\'\r\ncondition := \'[a_repair_calibration]\' == \'calibration_yes\'',
    `settings` = 'validation_protocol := 22\r\nverification_protocol := 23\r\ncalibration_certificate := 13\r\nrepair_protocol := 14\r\nfield_tsii_id := 50810\r\nnomenclature_type_test_method := 8\r\nrepair_protocol_add_days := 15'
  WHERE `method` LIKE 'plugin := globaltest%method := updateNomenclatureNextDate'
    AND `start_model_type` = '14';

######################################################################################
# 2012-05-16 - Add history for a version changes from 120 (this is the count at this moment) documents to 104 (this is the count at this moment) nomenclatures (the entire list of ids can be found into the bug as an attachment)

# Add history for a version changes from 120 (this is the count at this moment) documents to 104 (this is the count at this moment) nomenclatures (the entire list of ids can be found into the bug as an attachment)
SET @current_version := '';
SET @prev_nomenclature := '';

SET @current_version := (IF (@prev_nomenclature != '1162', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1162', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1162', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1162', '51809', '1', '', NOW(), '-1', NOW(), '-1', ''),
  ('1162', '51808', '1', 'QP-000014', NOW(), '-1', NOW(), '-1', ''),
  ('1162', '51807', '1', '193', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1162';

SET @current_version := (IF (@prev_nomenclature != '1163', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1163', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1163', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1163', '51809', '1', '377', NOW(), '-1', NOW(), '-1', ''),
  ('1163', '51808', '1', 'QP-000015', NOW(), '-1', NOW(), '-1', ''),
  ('1163', '51807', '1', '194', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1163';

SET @current_version := (IF (@prev_nomenclature != '1164', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1164', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1164', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1164', '51809', '1', '378', NOW(), '-1', NOW(), '-1', ''),
  ('1164', '51808', '1', 'QP-000016', NOW(), '-1', NOW(), '-1', ''),
  ('1164', '51807', '1', '195', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1164';

SET @current_version := (IF (@prev_nomenclature != '1165', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1165', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51806', '2', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51809', '2', '521', NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51808', '2', 'QP-000037', NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51807', '2', '271', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1165';

SET @current_version := (IF (@prev_nomenclature != '1165', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1165', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51809', '1', '', NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51808', '1', 'QP-000017', NOW(), '-1', NOW(), '-1', ''),
  ('1165', '51807', '1', '196', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1165';

SET @current_version := (IF (@prev_nomenclature != '1166', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1166', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51806', '2', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51809', '2', '520', NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51808', '2', 'QP-000036', NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51807', '2', '270', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1166';

SET @current_version := (IF (@prev_nomenclature != '1166', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1166', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51809', '1', '', NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51808', '1', 'QP-000018', NOW(), '-1', NOW(), '-1', ''),
  ('1166', '51807', '1', '197', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1166';

SET @current_version := (IF (@prev_nomenclature != '1168', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1168', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1168', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1168', '51809', '1', '', NOW(), '-1', NOW(), '-1', ''),
  ('1168', '51808', '1', 'QP-000019', NOW(), '-1', NOW(), '-1', ''),
  ('1168', '51807', '1', '198', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1168';

SET @current_version := (IF (@prev_nomenclature != '1426', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1426', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1426', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1426', '51809', '1', '508', NOW(), '-1', NOW(), '-1', ''),
  ('1426', '51808', '1', 'QP-000031', NOW(), '-1', NOW(), '-1', ''),
  ('1426', '51807', '1', '263', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1426';

SET @current_version := (IF (@prev_nomenclature != '1427', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1427', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1427', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1427', '51809', '1', '506', NOW(), '-1', NOW(), '-1', ''),
  ('1427', '51808', '1', 'QP-000030', NOW(), '-1', NOW(), '-1', ''),
  ('1427', '51807', '1', '262', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1427';

SET @current_version := (IF (@prev_nomenclature != '1428', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1428', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1428', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1428', '51809', '1', '499', NOW(), '-1', NOW(), '-1', ''),
  ('1428', '51808', '1', 'QP-000028', NOW(), '-1', NOW(), '-1', ''),
  ('1428', '51807', '1', '259', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1428';

SET @current_version := (IF (@prev_nomenclature != '1429', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1429', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1429', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1429', '51809', '1', '497', NOW(), '-1', NOW(), '-1', ''),
  ('1429', '51808', '1', 'QP-000027', NOW(), '-1', NOW(), '-1', ''),
  ('1429', '51807', '1', '258', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1429';

SET @current_version := (IF (@prev_nomenclature != '1430', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1430', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1430', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1430', '51809', '1', '495', NOW(), '-1', NOW(), '-1', ''),
  ('1430', '51808', '1', 'QP-000026', NOW(), '-1', NOW(), '-1', ''),
  ('1430', '51807', '1', '257', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1430';

SET @current_version := (IF (@prev_nomenclature != '1431', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1431', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1431', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1431', '51809', '1', '492', NOW(), '-1', NOW(), '-1', ''),
  ('1431', '51808', '1', 'QP-000025', NOW(), '-1', NOW(), '-1', ''),
  ('1431', '51807', '1', '256', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1431';

SET @current_version := (IF (@prev_nomenclature != '1651', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1651', '51805', '3', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51806', '3', '2011-03-15', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51809', '3', '657', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51808', '3', 'QP-000109', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51807', '3', '376', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1651';

SET @current_version := (IF (@prev_nomenclature != '1651', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1651', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51806', '2', '2011-03-15', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51809', '2', '', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51808', '2', 'QP-000108', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51807', '2', '375', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1651';

SET @current_version := (IF (@prev_nomenclature != '1651', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1651', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51806', '1', '2011-03-15', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51809', '1', '462', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51808', '1', 'QP-000021', NOW(), '-1', NOW(), '-1', ''),
  ('1651', '51807', '1', '238', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1651';

SET @current_version := (IF (@prev_nomenclature != '1656', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1656', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51806', '2', '2009-09-21', NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51809', '2', '683', NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51808', '2', 'QP-000113', NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51807', '2', '386', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1656';

SET @current_version := (IF (@prev_nomenclature != '1656', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1656', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51806', '1', '2008-09-21', NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51809', '1', '485', NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51808', '1', 'QP-000022', NOW(), '-1', NOW(), '-1', ''),
  ('1656', '51807', '1', '239', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1656';

SET @current_version := (IF (@prev_nomenclature != '1657', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1657', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1657', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1657', '51809', '1', '', NOW(), '-1', NOW(), '-1', ''),
  ('1657', '51808', '1', 'QP-000023', NOW(), '-1', NOW(), '-1', ''),
  ('1657', '51807', '1', '254', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1657';

SET @current_version := (IF (@prev_nomenclature != '1658', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1658', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1658', '51806', '1', '2009-09-21', NOW(), '-1', NOW(), '-1', ''),
  ('1658', '51809', '1', '490', NOW(), '-1', NOW(), '-1', ''),
  ('1658', '51808', '1', 'QP-000024', NOW(), '-1', NOW(), '-1', ''),
  ('1658', '51807', '1', '255', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1658';

SET @current_version := (IF (@prev_nomenclature != '1712', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1712', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1712', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1712', '51809', '1', '524', NOW(), '-1', NOW(), '-1', ''),
  ('1712', '51808', '1', 'QP-000040', NOW(), '-1', NOW(), '-1', ''),
  ('1712', '51807', '1', '276', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1712';

SET @current_version := (IF (@prev_nomenclature != '1713', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1713', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1713', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1713', '51809', '1', '525', NOW(), '-1', NOW(), '-1', ''),
  ('1713', '51808', '1', 'QP-000041', NOW(), '-1', NOW(), '-1', ''),
  ('1713', '51807', '1', '277', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1713';

SET @current_version := (IF (@prev_nomenclature != '1719', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1719', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1719', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1719', '51809', '1', '528', NOW(), '-1', NOW(), '-1', ''),
  ('1719', '51808', '1', 'QP-000043', NOW(), '-1', NOW(), '-1', ''),
  ('1719', '51807', '1', '280', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1719';

SET @current_version := (IF (@prev_nomenclature != '1720', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1720', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1720', '51806', '1', '2011-01-01', NOW(), '-1', NOW(), '-1', ''),
  ('1720', '51809', '1', '529', NOW(), '-1', NOW(), '-1', ''),
  ('1720', '51808', '1', 'QP-000044', NOW(), '-1', NOW(), '-1', ''),
  ('1720', '51807', '1', '281', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1720';

SET @current_version := (IF (@prev_nomenclature != '1721', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1721', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51806', '2', '2012-05-10', NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51809', '2', '770', NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51808', '2', 'QP-000139', NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51807', '2', '429', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1721';

SET @current_version := (IF (@prev_nomenclature != '1721', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1721', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51806', '1', '2010-03-01', NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51809', '1', '530', NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51808', '1', 'QP-000045', NOW(), '-1', NOW(), '-1', ''),
  ('1721', '51807', '1', '282', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1721';

SET @current_version := (IF (@prev_nomenclature != '1722', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1722', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1722', '51806', '1', '2012-01-01', NOW(), '-1', NOW(), '-1', ''),
  ('1722', '51809', '1', '532', NOW(), '-1', NOW(), '-1', ''),
  ('1722', '51808', '1', 'QP-000047', NOW(), '-1', NOW(), '-1', ''),
  ('1722', '51807', '1', '286', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1722';

SET @current_version := (IF (@prev_nomenclature != '1741', '6', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1741', '51805', '4', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51806', '4', '2012-06-30', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51809', '4', '818', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51808', '4', 'QP-000146', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51807', '4', '457', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1741';

SET @current_version := (IF (@prev_nomenclature != '1741', '6', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1741', '51805', '3', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51806', '3', '2012-04-26', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51809', '3', '583', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51808', '3', 'QP-000070', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51807', '3', '328', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1741';

SET @current_version := (IF (@prev_nomenclature != '1741', '6', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1741', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51806', '2', '2010-06-03', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51809', '2', '578', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51808', '2', 'QP-000069', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51807', '2', '324', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1741';

SET @current_version := (IF (@prev_nomenclature != '1741', '6', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1741', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51806', '1', '2010-06-03', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51809', '1', '577', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51808', '1', 'QP-000068', NOW(), '-1', NOW(), '-1', ''),
  ('1741', '51807', '1', '323', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1741';

SET @current_version := (IF (@prev_nomenclature != '1743', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1743', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1743', '51806', '1', '2009-09-20', NOW(), '-1', NOW(), '-1', ''),
  ('1743', '51809', '1', '575', NOW(), '-1', NOW(), '-1', ''),
  ('1743', '51808', '1', 'QP-000066', NOW(), '-1', NOW(), '-1', ''),
  ('1743', '51807', '1', '321', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1743';

SET @current_version := (IF (@prev_nomenclature != '1744', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1744', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1744', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1744', '51809', '1', '574', NOW(), '-1', NOW(), '-1', ''),
  ('1744', '51808', '1', 'QP-000065', NOW(), '-1', NOW(), '-1', ''),
  ('1744', '51807', '1', '320', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1744';

SET @current_version := (IF (@prev_nomenclature != '1745', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1745', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1745', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1745', '51809', '1', '573', NOW(), '-1', NOW(), '-1', ''),
  ('1745', '51808', '1', 'QP-000064', NOW(), '-1', NOW(), '-1', ''),
  ('1745', '51807', '1', '319', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1745';

SET @current_version := (IF (@prev_nomenclature != '1746', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1746', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1746', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1746', '51809', '1', '576', NOW(), '-1', NOW(), '-1', ''),
  ('1746', '51808', '1', 'QP-000067', NOW(), '-1', NOW(), '-1', ''),
  ('1746', '51807', '1', '322', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1746';

SET @current_version := (IF (@prev_nomenclature != '1747', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1747', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1747', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1747', '51809', '1', '584', NOW(), '-1', NOW(), '-1', ''),
  ('1747', '51808', '1', 'QP-000071', NOW(), '-1', NOW(), '-1', ''),
  ('1747', '51807', '1', '329', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1747';

SET @current_version := (IF (@prev_nomenclature != '1748', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1748', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1748', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1748', '51809', '1', '585', NOW(), '-1', NOW(), '-1', ''),
  ('1748', '51808', '1', 'QP-000072', NOW(), '-1', NOW(), '-1', ''),
  ('1748', '51807', '1', '330', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1748';

SET @current_version := (IF (@prev_nomenclature != '1749', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1749', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1749', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1749', '51809', '1', '586', NOW(), '-1', NOW(), '-1', ''),
  ('1749', '51808', '1', 'QP-000073', NOW(), '-1', NOW(), '-1', ''),
  ('1749', '51807', '1', '331', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1749';

SET @current_version := (IF (@prev_nomenclature != '1750', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1750', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1750', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1750', '51809', '1', '587', NOW(), '-1', NOW(), '-1', ''),
  ('1750', '51808', '1', 'QP-000074', NOW(), '-1', NOW(), '-1', ''),
  ('1750', '51807', '1', '332', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1750';

SET @current_version := (IF (@prev_nomenclature != '1751', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1751', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1751', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1751', '51809', '1', '588', NOW(), '-1', NOW(), '-1', ''),
  ('1751', '51808', '1', 'QP-000075', NOW(), '-1', NOW(), '-1', ''),
  ('1751', '51807', '1', '333', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1751';

SET @current_version := (IF (@prev_nomenclature != '1752', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1752', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1752', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1752', '51809', '1', '589', NOW(), '-1', NOW(), '-1', ''),
  ('1752', '51808', '1', 'QP-000076', NOW(), '-1', NOW(), '-1', ''),
  ('1752', '51807', '1', '334', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1752';

SET @current_version := (IF (@prev_nomenclature != '1753', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1753', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1753', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1753', '51809', '1', '590', NOW(), '-1', NOW(), '-1', ''),
  ('1753', '51808', '1', 'QP-000077', NOW(), '-1', NOW(), '-1', ''),
  ('1753', '51807', '1', '335', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1753';

SET @current_version := (IF (@prev_nomenclature != '1754', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1754', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1754', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1754', '51809', '1', '591', NOW(), '-1', NOW(), '-1', ''),
  ('1754', '51808', '1', 'QP-000078', NOW(), '-1', NOW(), '-1', ''),
  ('1754', '51807', '1', '336', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1754';

SET @current_version := (IF (@prev_nomenclature != '1755', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1755', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1755', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1755', '51809', '1', '592', NOW(), '-1', NOW(), '-1', ''),
  ('1755', '51808', '1', 'QP-000079', NOW(), '-1', NOW(), '-1', ''),
  ('1755', '51807', '1', '337', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1755';

SET @current_version := (IF (@prev_nomenclature != '1756', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1756', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1756', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1756', '51809', '1', '593', NOW(), '-1', NOW(), '-1', ''),
  ('1756', '51808', '1', 'QP-000080', NOW(), '-1', NOW(), '-1', ''),
  ('1756', '51807', '1', '338', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1756';

SET @current_version := (IF (@prev_nomenclature != '1758', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1758', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1758', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1758', '51809', '1', '599', NOW(), '-1', NOW(), '-1', ''),
  ('1758', '51808', '1', 'QP-000081', NOW(), '-1', NOW(), '-1', ''),
  ('1758', '51807', '1', '341', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1758';

SET @current_version := (IF (@prev_nomenclature != '1759', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1759', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1759', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1759', '51809', '1', '602', NOW(), '-1', NOW(), '-1', ''),
  ('1759', '51808', '1', 'QP-000082', NOW(), '-1', NOW(), '-1', ''),
  ('1759', '51807', '1', '342', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1759';

SET @current_version := (IF (@prev_nomenclature != '1760', '05', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1760', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1760', '51806', '1', '2011-01-10', NOW(), '-1', NOW(), '-1', ''),
  ('1760', '51809', '1', '603', NOW(), '-1', NOW(), '-1', ''),
  ('1760', '51808', '1', 'QP-000083', NOW(), '-1', NOW(), '-1', ''),
  ('1760', '51807', '1', '343', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1760';

SET @current_version := (IF (@prev_nomenclature != '1761', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1761', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1761', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1761', '51809', '1', '604', NOW(), '-1', NOW(), '-1', ''),
  ('1761', '51808', '1', 'QP-000084', NOW(), '-1', NOW(), '-1', ''),
  ('1761', '51807', '1', '344', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1761';

SET @current_version := (IF (@prev_nomenclature != '1762', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1762', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1762', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1762', '51809', '1', '605', NOW(), '-1', NOW(), '-1', ''),
  ('1762', '51808', '1', 'QP-000085', NOW(), '-1', NOW(), '-1', ''),
  ('1762', '51807', '1', '345', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1762';

SET @current_version := (IF (@prev_nomenclature != '1763', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1763', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1763', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1763', '51809', '1', '607', NOW(), '-1', NOW(), '-1', ''),
  ('1763', '51808', '1', 'QP-000087', NOW(), '-1', NOW(), '-1', ''),
  ('1763', '51807', '1', '348', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1763';

SET @current_version := (IF (@prev_nomenclature != '1764', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1764', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1764', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1764', '51809', '1', '608', NOW(), '-1', NOW(), '-1', ''),
  ('1764', '51808', '1', 'QP-000088', NOW(), '-1', NOW(), '-1', ''),
  ('1764', '51807', '1', '349', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1764';

SET @current_version := (IF (@prev_nomenclature != '1765', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1765', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1765', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1765', '51809', '1', '609', NOW(), '-1', NOW(), '-1', ''),
  ('1765', '51808', '1', 'QP-000089', NOW(), '-1', NOW(), '-1', ''),
  ('1765', '51807', '1', '350', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1765';

SET @current_version := (IF (@prev_nomenclature != '1766', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1766', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1766', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1766', '51809', '1', '610', NOW(), '-1', NOW(), '-1', ''),
  ('1766', '51808', '1', 'QP-000090', NOW(), '-1', NOW(), '-1', ''),
  ('1766', '51807', '1', '351', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1766';

SET @current_version := (IF (@prev_nomenclature != '1767', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1767', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1767', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1767', '51809', '1', '611', NOW(), '-1', NOW(), '-1', ''),
  ('1767', '51808', '1', 'QP-000091', NOW(), '-1', NOW(), '-1', ''),
  ('1767', '51807', '1', '352', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1767';

SET @current_version := (IF (@prev_nomenclature != '1768', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1768', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51806', '2', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51809', '2', '676', NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51808', '2', 'QP-000111', NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51807', '2', '383', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1768';

SET @current_version := (IF (@prev_nomenclature != '1768', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1768', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51809', '1', '673', NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51808', '1', 'QP-000110', NOW(), '-1', NOW(), '-1', ''),
  ('1768', '51807', '1', '381', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1768';

SET @current_version := (IF (@prev_nomenclature != '1769', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1769', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51806', '2', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51809', '2', '677', NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51808', '2', 'QP-000112', NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51807', '2', '384', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1769';

SET @current_version := (IF (@prev_nomenclature != '1769', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1769', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51809', '1', '632', NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51808', '1', 'QP-000098', NOW(), '-1', NOW(), '-1', ''),
  ('1769', '51807', '1', '363', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1769';

SET @current_version := (IF (@prev_nomenclature != '1770', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1770', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1770', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1770', '51809', '1', '689', NOW(), '-1', NOW(), '-1', ''),
  ('1770', '51808', '1', 'QP-000119', NOW(), '-1', NOW(), '-1', ''),
  ('1770', '51807', '1', '392', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1770';

SET @current_version := (IF (@prev_nomenclature != '1773', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1773', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1773', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1773', '51809', '1', '695', NOW(), '-1', NOW(), '-1', ''),
  ('1773', '51808', '1', 'QP-000120', NOW(), '-1', NOW(), '-1', ''),
  ('1773', '51807', '1', '394', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1773';

SET @current_version := (IF (@prev_nomenclature != '1774', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1774', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1774', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1774', '51809', '1', '627', NOW(), '-1', NOW(), '-1', ''),
  ('1774', '51808', '1', 'QP-000096', NOW(), '-1', NOW(), '-1', ''),
  ('1774', '51807', '1', '361', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1774';

SET @current_version := (IF (@prev_nomenclature != '1775', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1775', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1775', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1775', '51809', '1', '624', NOW(), '-1', NOW(), '-1', ''),
  ('1775', '51808', '1', 'QP-000093', NOW(), '-1', NOW(), '-1', ''),
  ('1775', '51807', '1', '358', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1775';

SET @current_version := (IF (@prev_nomenclature != '1776', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1776', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1776', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1776', '51809', '1', '612', NOW(), '-1', NOW(), '-1', ''),
  ('1776', '51808', '1', 'QP-000092', NOW(), '-1', NOW(), '-1', ''),
  ('1776', '51807', '1', '353', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1776';

SET @current_version := (IF (@prev_nomenclature != '1777', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1777', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1777', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1777', '51809', '1', '628', NOW(), '-1', NOW(), '-1', ''),
  ('1777', '51808', '1', 'QP-000097', NOW(), '-1', NOW(), '-1', ''),
  ('1777', '51807', '1', '362', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1777';

SET @current_version := (IF (@prev_nomenclature != '1778', '05', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1778', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1778', '51806', '1', '2011-12-12', NOW(), '-1', NOW(), '-1', ''),
  ('1778', '51809', '1', '567', NOW(), '-1', NOW(), '-1', ''),
  ('1778', '51808', '1', 'QP-000062', NOW(), '-1', NOW(), '-1', ''),
  ('1778', '51807', '1', '317', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1778';

SET @current_version := (IF (@prev_nomenclature != '1779', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1779', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1779', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1779', '51809', '1', '625', NOW(), '-1', NOW(), '-1', ''),
  ('1779', '51808', '1', 'QP-000094', NOW(), '-1', NOW(), '-1', ''),
  ('1779', '51807', '1', '359', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1779';

SET @current_version := (IF (@prev_nomenclature != '1780', '4', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1780', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51806', '2', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51809', '2', '762', NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51808', '2', 'QP-000134', NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51807', '2', '422', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1780';

SET @current_version := (IF (@prev_nomenclature != '1780', '4', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1780', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51806', '1', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51809', '1', '760', NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51808', '1', 'QP-000133', NOW(), '-1', NOW(), '-1', ''),
  ('1780', '51807', '1', '421', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1780';

SET @current_version := (IF (@prev_nomenclature != '1784', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1784', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1784', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1784', '51809', '1', '653', NOW(), '-1', NOW(), '-1', ''),
  ('1784', '51808', '1', 'QP-000106', NOW(), '-1', NOW(), '-1', ''),
  ('1784', '51807', '1', '373', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1784';

SET @current_version := (IF (@prev_nomenclature != '1788', '05', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1788', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51806', '2', '2011-06-11', NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51809', '2', '698', NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51808', '2', 'QP-000123', NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51807', '2', '397', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1788';

SET @current_version := (IF (@prev_nomenclature != '1788', '05', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1788', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51806', '1', '2011-06-11', NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51809', '1', '696', NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51808', '1', 'QP-000121', NOW(), '-1', NOW(), '-1', ''),
  ('1788', '51807', '1', '395', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1788';

SET @current_version := (IF (@prev_nomenclature != '1789', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1789', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1789', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1789', '51809', '1', '626', NOW(), '-1', NOW(), '-1', ''),
  ('1789', '51808', '1', 'QP-000095', NOW(), '-1', NOW(), '-1', ''),
  ('1789', '51807', '1', '360', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1789';

SET @current_version := (IF (@prev_nomenclature != '1790', '5', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1790', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1790', '51806', '1', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1790', '51809', '1', '759', NOW(), '-1', NOW(), '-1', ''),
  ('1790', '51808', '1', 'QP-000132', NOW(), '-1', NOW(), '-1', ''),
  ('1790', '51807', '1', '420', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1790';

SET @current_version := (IF (@prev_nomenclature != '1791', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1791', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1791', '51806', '1', '2012-06-11', NOW(), '-1', NOW(), '-1', ''),
  ('1791', '51809', '1', '776', NOW(), '-1', NOW(), '-1', ''),
  ('1791', '51808', '1', 'QP-000143', NOW(), '-1', NOW(), '-1', ''),
  ('1791', '51807', '1', '438', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1791';

SET @current_version := (IF (@prev_nomenclature != '1793', '5', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1793', '51805', '3', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51806', '3', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51809', '3', '771', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51808', '3', 'QP-000141', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51807', '3', '431', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1793';

SET @current_version := (IF (@prev_nomenclature != '1793', '5', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1793', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51806', '2', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51809', '2', '749', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51808', '2', 'QP-000130', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51807', '2', '414', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1793';

SET @current_version := (IF (@prev_nomenclature != '1793', '5', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1793', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51809', '1', '746', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51808', '1', 'QP-000129', NOW(), '-1', NOW(), '-1', ''),
  ('1793', '51807', '1', '413', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1793';

SET @current_version := (IF (@prev_nomenclature != '1795', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1795', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1795', '51806', '1', '2010-02-20', NOW(), '-1', NOW(), '-1', ''),
  ('1795', '51809', '1', '652', NOW(), '-1', NOW(), '-1', ''),
  ('1795', '51808', '1', 'QP-000105', NOW(), '-1', NOW(), '-1', ''),
  ('1795', '51807', '1', '372', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1795';

SET @current_version := (IF (@prev_nomenclature != '1796', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1796', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1796', '51806', '1', '2011-01-01', NOW(), '-1', NOW(), '-1', ''),
  ('1796', '51809', '1', '637', NOW(), '-1', NOW(), '-1', ''),
  ('1796', '51808', '1', 'QP-000101', NOW(), '-1', NOW(), '-1', ''),
  ('1796', '51807', '1', '366', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1796';

SET @current_version := (IF (@prev_nomenclature != '1804', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1804', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1804', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1804', '51809', '1', '606', NOW(), '-1', NOW(), '-1', ''),
  ('1804', '51808', '1', 'QP-000086', NOW(), '-1', NOW(), '-1', ''),
  ('1804', '51807', '1', '346', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1804';

SET @current_version := (IF (@prev_nomenclature != '1805', '5', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1805', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1805', '51806', '1', '2012-01-25', NOW(), '-1', NOW(), '-1', ''),
  ('1805', '51809', '1', '566', NOW(), '-1', NOW(), '-1', ''),
  ('1805', '51808', '1', 'QP-000060', NOW(), '-1', NOW(), '-1', ''),
  ('1805', '51807', '1', '314', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1805';

SET @current_version := (IF (@prev_nomenclature != '1806', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1806', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1806', '51806', '1', '2011-11-25', NOW(), '-1', NOW(), '-1', ''),
  ('1806', '51809', '1', '562', NOW(), '-1', NOW(), '-1', ''),
  ('1806', '51808', '1', 'QP-000057', NOW(), '-1', NOW(), '-1', ''),
  ('1806', '51807', '1', '311', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1806';

SET @current_version := (IF (@prev_nomenclature != '1807', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1807', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1807', '51806', '1', '2011-12-12', NOW(), '-1', NOW(), '-1', ''),
  ('1807', '51809', '1', '563', NOW(), '-1', NOW(), '-1', ''),
  ('1807', '51808', '1', 'QP-000058', NOW(), '-1', NOW(), '-1', ''),
  ('1807', '51807', '1', '312', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1807';

SET @current_version := (IF (@prev_nomenclature != '1809', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1809', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1809', '51806', '1', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1809', '51809', '1', '768', NOW(), '-1', NOW(), '-1', ''),
  ('1809', '51808', '1', 'QP-000137', NOW(), '-1', NOW(), '-1', ''),
  ('1809', '51807', '1', '427', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1809';

SET @current_version := (IF (@prev_nomenclature != '1812', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1812', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1812', '51806', '1', '2011-03-15', NOW(), '-1', NOW(), '-1', ''),
  ('1812', '51809', '1', '656', NOW(), '-1', NOW(), '-1', ''),
  ('1812', '51808', '1', 'QP-000107', NOW(), '-1', NOW(), '-1', ''),
  ('1812', '51807', '1', '374', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1812';

SET @current_version := (IF (@prev_nomenclature != '1813', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1813', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1813', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1813', '51809', '1', '750', NOW(), '-1', NOW(), '-1', ''),
  ('1813', '51808', '1', 'QP-000131', NOW(), '-1', NOW(), '-1', ''),
  ('1813', '51807', '1', '416', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1813';

SET @current_version := (IF (@prev_nomenclature != '1816', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1816', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1816', '51806', '1', '2012-04-25', NOW(), '-1', NOW(), '-1', ''),
  ('1816', '51809', '1', '550', NOW(), '-1', NOW(), '-1', ''),
  ('1816', '51808', '1', 'QP-000048', NOW(), '-1', NOW(), '-1', ''),
  ('1816', '51807', '1', '300', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1816';

SET @current_version := (IF (@prev_nomenclature != '1817', '2', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1817', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1817', '51806', '1', '2012-04-25', NOW(), '-1', NOW(), '-1', ''),
  ('1817', '51809', '1', '635', NOW(), '-1', NOW(), '-1', ''),
  ('1817', '51808', '1', 'QP-000099', NOW(), '-1', NOW(), '-1', ''),
  ('1817', '51807', '1', '364', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1817';

SET @current_version := (IF (@prev_nomenclature != '1820', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1820', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1820', '51806', '1', '2012-04-25', NOW(), '-1', NOW(), '-1', ''),
  ('1820', '51809', '1', '636', NOW(), '-1', NOW(), '-1', ''),
  ('1820', '51808', '1', 'QP-000100', NOW(), '-1', NOW(), '-1', ''),
  ('1820', '51807', '1', '365', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1820';

SET @current_version := (IF (@prev_nomenclature != '1826', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1826', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1826', '51806', '1', '2012-04-25', NOW(), '-1', NOW(), '-1', ''),
  ('1826', '51809', '1', '551', NOW(), '-1', NOW(), '-1', ''),
  ('1826', '51808', '1', 'QP-000049', NOW(), '-1', NOW(), '-1', ''),
  ('1826', '51807', '1', '301', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1826';

SET @current_version := (IF (@prev_nomenclature != '1837', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1837', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1837', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1837', '51809', '1', '697', NOW(), '-1', NOW(), '-1', ''),
  ('1837', '51808', '1', 'QP-000122', NOW(), '-1', NOW(), '-1', ''),
  ('1837', '51807', '1', '396', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1837';

SET @current_version := (IF (@prev_nomenclature != '1839', '5', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1839', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1839', '51806', '1', '2012-06-02', NOW(), '-1', NOW(), '-1', ''),
  ('1839', '51809', '1', '649', NOW(), '-1', NOW(), '-1', ''),
  ('1839', '51808', '1', 'QP-000102', NOW(), '-1', NOW(), '-1', ''),
  ('1839', '51807', '1', '369', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1839';

SET @current_version := (IF (@prev_nomenclature != '1841', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1841', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1841', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1841', '51809', '1', '650', NOW(), '-1', NOW(), '-1', ''),
  ('1841', '51808', '1', 'QP-000103', NOW(), '-1', NOW(), '-1', ''),
  ('1841', '51807', '1', '370', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1841';

SET @current_version := (IF (@prev_nomenclature != '1843', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1843', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1843', '51806', '1', '2009-08-20', NOW(), '-1', NOW(), '-1', ''),
  ('1843', '51809', '1', '651', NOW(), '-1', NOW(), '-1', ''),
  ('1843', '51808', '1', 'QP-000104', NOW(), '-1', NOW(), '-1', ''),
  ('1843', '51807', '1', '371', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1843';

SET @current_version := (IF (@prev_nomenclature != '1850', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1850', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1850', '51806', '1', '2010-09-01', NOW(), '-1', NOW(), '-1', ''),
  ('1850', '51809', '1', '571', NOW(), '-1', NOW(), '-1', ''),
  ('1850', '51808', '1', 'QP-000063', NOW(), '-1', NOW(), '-1', ''),
  ('1850', '51807', '1', '318', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1850';

SET @current_version := (IF (@prev_nomenclature != '1851', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1851', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1851', '51806', '1', '2010-09-01', NOW(), '-1', NOW(), '-1', ''),
  ('1851', '51809', '1', '572', NOW(), '-1', NOW(), '-1', ''),
  ('1851', '51808', '1', 'QP-000061', NOW(), '-1', NOW(), '-1', ''),
  ('1851', '51807', '1', '316', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1851';

SET @current_version := (IF (@prev_nomenclature != '1852', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1852', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1852', '51806', '1', '2012-03-25', NOW(), '-1', NOW(), '-1', ''),
  ('1852', '51809', '1', '564', NOW(), '-1', NOW(), '-1', ''),
  ('1852', '51808', '1', 'QP-000059', NOW(), '-1', NOW(), '-1', ''),
  ('1852', '51807', '1', '313', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1852';

SET @current_version := (IF (@prev_nomenclature != '1860', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1860', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1860', '51806', '1', '2012-04-25', NOW(), '-1', NOW(), '-1', ''),
  ('1860', '51809', '1', '553', NOW(), '-1', NOW(), '-1', ''),
  ('1860', '51808', '1', 'QP-000051', NOW(), '-1', NOW(), '-1', ''),
  ('1860', '51807', '1', '303', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1860';

SET @current_version := (IF (@prev_nomenclature != '1861', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1861', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1861', '51806', '1', '2012-04-25', NOW(), '-1', NOW(), '-1', ''),
  ('1861', '51809', '1', '554', NOW(), '-1', NOW(), '-1', ''),
  ('1861', '51808', '1', 'QP-000050', NOW(), '-1', NOW(), '-1', ''),
  ('1861', '51807', '1', '302', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1861';

SET @current_version := (IF (@prev_nomenclature != '1862', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1862', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1862', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1862', '51809', '1', '555', NOW(), '-1', NOW(), '-1', ''),
  ('1862', '51808', '1', 'QP-000052', NOW(), '-1', NOW(), '-1', ''),
  ('1862', '51807', '1', '304', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1862';

SET @current_version := (IF (@prev_nomenclature != '1864', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1864', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1864', '51806', '1', '2009-09-21', NOW(), '-1', NOW(), '-1', ''),
  ('1864', '51809', '1', '556', NOW(), '-1', NOW(), '-1', ''),
  ('1864', '51808', '1', 'QP-000053', NOW(), '-1', NOW(), '-1', ''),
  ('1864', '51807', '1', '305', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1864';

SET @current_version := (IF (@prev_nomenclature != '1866', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1866', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1866', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1866', '51809', '1', '557', NOW(), '-1', NOW(), '-1', ''),
  ('1866', '51808', '1', 'QP-000054', NOW(), '-1', NOW(), '-1', ''),
  ('1866', '51807', '1', '306', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1866';

SET @current_version := (IF (@prev_nomenclature != '1867', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1867', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1867', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1867', '51809', '1', '558', NOW(), '-1', NOW(), '-1', ''),
  ('1867', '51808', '1', 'QP-000055', NOW(), '-1', NOW(), '-1', ''),
  ('1867', '51807', '1', '307', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1867';

SET @current_version := (IF (@prev_nomenclature != '1870', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1870', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1870', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1870', '51809', '1', '559', NOW(), '-1', NOW(), '-1', ''),
  ('1870', '51808', '1', 'QP-000056', NOW(), '-1', NOW(), '-1', ''),
  ('1870', '51807', '1', '308', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1870';

SET @current_version := (IF (@prev_nomenclature != '1949', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1949', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1949', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1949', '51809', '1', '684', NOW(), '-1', NOW(), '-1', ''),
  ('1949', '51808', '1', 'QP-000114', NOW(), '-1', NOW(), '-1', ''),
  ('1949', '51807', '1', '387', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1949';

SET @current_version := (IF (@prev_nomenclature != '1950', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1950', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1950', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1950', '51809', '1', '685', NOW(), '-1', NOW(), '-1', ''),
  ('1950', '51808', '1', 'QP-000115', NOW(), '-1', NOW(), '-1', ''),
  ('1950', '51807', '1', '388', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1950';

SET @current_version := (IF (@prev_nomenclature != '1951', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1951', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1951', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1951', '51809', '1', '686', NOW(), '-1', NOW(), '-1', ''),
  ('1951', '51808', '1', 'QP-000116', NOW(), '-1', NOW(), '-1', ''),
  ('1951', '51807', '1', '389', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1951';

SET @current_version := (IF (@prev_nomenclature != '1952', '04', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1952', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1952', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1952', '51809', '1', '687', NOW(), '-1', NOW(), '-1', ''),
  ('1952', '51808', '1', 'QP-000117', NOW(), '-1', NOW(), '-1', ''),
  ('1952', '51807', '1', '390', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1952';

SET @current_version := (IF (@prev_nomenclature != '1953', '03', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1953', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1953', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1953', '51809', '1', '688', NOW(), '-1', NOW(), '-1', ''),
  ('1953', '51808', '1', 'QP-000118', NOW(), '-1', NOW(), '-1', ''),
  ('1953', '51807', '1', '391', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1953';

SET @current_version := (IF (@prev_nomenclature != '1954', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1954', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1954', '51806', '1', '2009-09-21', NOW(), '-1', NOW(), '-1', ''),
  ('1954', '51809', '1', '702', NOW(), '-1', NOW(), '-1', ''),
  ('1954', '51808', '1', 'QP-000124', NOW(), '-1', NOW(), '-1', ''),
  ('1954', '51807', '1', '398', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1954';

SET @current_version := (IF (@prev_nomenclature != '1955', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1955', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1955', '51806', '1', '2009-09-21', NOW(), '-1', NOW(), '-1', ''),
  ('1955', '51809', '1', '703', NOW(), '-1', NOW(), '-1', ''),
  ('1955', '51808', '1', 'QP-000125', NOW(), '-1', NOW(), '-1', ''),
  ('1955', '51807', '1', '399', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1955';

SET @current_version := (IF (@prev_nomenclature != '1956', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1956', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1956', '51806', '1', '2011-06-21', NOW(), '-1', NOW(), '-1', ''),
  ('1956', '51809', '1', '704', NOW(), '-1', NOW(), '-1', ''),
  ('1956', '51808', '1', 'QP-000126', NOW(), '-1', NOW(), '-1', ''),
  ('1956', '51807', '1', '400', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1956';

SET @current_version := (IF (@prev_nomenclature != '1957', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1957', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1957', '51806', '1', '2009-08-21', NOW(), '-1', NOW(), '-1', ''),
  ('1957', '51809', '1', '705', NOW(), '-1', NOW(), '-1', ''),
  ('1957', '51808', '1', 'QP-000127', NOW(), '-1', NOW(), '-1', ''),
  ('1957', '51807', '1', '401', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1957';

SET @current_version := (IF (@prev_nomenclature != '1959', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1959', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1959', '51806', '1', '2012-02-01', NOW(), '-1', NOW(), '-1', ''),
  ('1959', '51809', '1', '721', NOW(), '-1', NOW(), '-1', ''),
  ('1959', '51808', '1', 'QP-000128', NOW(), '-1', NOW(), '-1', ''),
  ('1959', '51807', '1', '404', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1959';

SET @current_version := (IF (@prev_nomenclature != '1987', '01', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1987', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1987', '51806', '1', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1987', '51809', '1', '764', NOW(), '-1', NOW(), '-1', ''),
  ('1987', '51808', '1', 'QP-000135', NOW(), '-1', NOW(), '-1', ''),
  ('1987', '51807', '1', '423', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1987';

SET @current_version := (IF (@prev_nomenclature != '1988', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1988', '51805', '2', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51806', '2', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51809', '2', '792', NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51808', '2', 'QP-000145', NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51807', '2', '442', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1988';

SET @current_version := (IF (@prev_nomenclature != '1988', '02', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('1988', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51806', '1', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51809', '1', '765', NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51808', '1', 'QP-000136', NOW(), '-1', NOW(), '-1', ''),
  ('1988', '51807', '1', '424', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('2' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '1988';

SET @current_version := (IF (@prev_nomenclature != '2013', '2', @current_version));
INSERT INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
  ('2013', '51805', '1', @current_version, NOW(), '-1', NOW(), '-1', ''),
  ('2013', '51806', '1', '2012-06-01', NOW(), '-1', NOW(), '-1', ''),
  ('2013', '51809', '1', '790', NOW(), '-1', NOW(), '-1', ''),
  ('2013', '51808', '1', 'QP-000144', NOW(), '-1', NOW(), '-1', ''),
  ('2013', '51807', '1', '441', NOW(), '-1', NOW(), '-1', '');
SET @current_version := (IF ('1' = '1', @current_version - 1, @current_version));
SET @prev_nomenclature := '2013';

######################################################################################
# 2012-05-17 - Change the settings of the 'globaltest_calibration_plan' report
#            - Change the format of the number into the 'current_version' and 'history_version' fields of all nomenclatures
#            - Change the settings of the 'globaltest_tsii_file' report
#            - Add placeholders for the check protocols result table of the 'globaltest_tsii_file' report

# Change the settings of the 'globaltest_calibration_plan' report
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_technicle_means := 6\r\nnomenclature_category := 1\r\ndocument_type_calibration_certificate := 13\r\nnext_calibration_date_color_past := DB0416\r\nnext_calibration_date_color_present := FF9C2A\r\nnext_calibration_date_max_days_left := 10'
  WHERE `id` = '197';

# Change the format of the number into the 'current_version' and 'history_version' fields of all nomenclatures.
UPDATE `nom_cstm`
  SET `value` = CONCAT('0', `value`)
  WHERE `var_id` IN ('51802', '51805')
    AND CHAR_LENGTH(TRIM(`value`)) = '1';

# Change the settings of the 'globaltest_tsii_file' report
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_tsii := 6\r\ndocument_certificate := 13\r\ndocument_protocol := 14\r\ndocument_check_protocol := 35'
  WHERE `id` = '206';

# Add placeholders for the check protocols result table of the 'globaltest_tsii_file' report
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'tsii_check_protocols_table', 'Report', 'send', 'all', ',206,', 'tsii_check_protocols_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Протоколи за проверка на техническо средство (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Protocols for checking of a technical mean (table)',     NULL, 'en');

######################################################################################
# 2012-05-22 - Add new report - 'globaltest_verifying_methods' - for the Globaltest installation (remote)

# Add new report - 'globaltest_verifying_methods' - for the Globaltest installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (210, 'globaltest_verifying_methods', 'verifying_method_id := 8\r\nvalidating_protocol_id := 22\r\nverifying_protocol_id := 23\r\nprotocol_method_id := method_id\r\nprotocol_method_code := method_code\r\nprotocol_method_name := method_name\r\ntesting_method_type_id := 8\r\nnom_next_date := next_date\r\nnom_calibration_interval := tech_interval\r\n', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (210, 'Методи за верифициране', NULL, NULL, 'bg'),
  (210, 'Verifying methods', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 210, '0', '1'),
  ('reports', 'export', 210, '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 210;

######################################################################################
# 2012-05-29 - Added new report - 'globaltest_claims_from_customers' - for Globaltest installation (remote)

# Added new report - 'globaltest_claims_from_customers' - for Globaltest installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('212', 'globaltest_claims_from_customers', NULL, '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('212', 'Вземания от клиенти',   NULL, NULL, 'bg'),
  ('212', 'Claims from customers', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '212', '0', '1'),
  ('reports', 'export',          '212', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports'
    AND `action` IN ('generate_report', 'export')
    AND `model_type` = '212';

######################################################################################
# 2012-06-15 - Added one more table for offer pattern plugin

#Added placeholders for offer plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind1_type3', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за измервани показатели (Изглед 3)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Measured parameters table (Type 3)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_offer_kind1_type4', 'Document', 'basic', 'pattern_plugins', ',28,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица за измервани показатели (Изглед 4)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Measured parameters table (Type 4)', NULL, 'en');

######################################################################################
# 2012-06-20 - Added automation to copy basic "name" variable to additional "description" variable for nomenclaures of type Testing method (id=8) when values differ
#            - Added queries to copy basic "name" variable to additional "description" variable for nomenclaures of type Testing method (id=8)

# Added automation to copy basic "name" variable to additional "description" variable for nomenclaures of type Testing method (id=8) when values differ
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Метод за изпитване: Описание', 0, NULL, 1, 'nomenclatures', NULL, 'action', 8, '', 'condition := ''[b_name]'' != ''[a_description]''', 'method := setAdditionalVar\r\nvar_name := description\r\nvar_value := [b_name]', NULL, 1, 0);

# Added queries to copy basic "name" variable to additional "description" variable for nomenclaures of type Testing method (id=8)
SELECT @aid := id FROM `_fields_meta` WHERE `model_type`=8 AND model='Nomenclature' AND name='description';

# Update "description" field to be multilang
UPDATE `_fields_meta` SET multilang=1 WHERE id=@aid;

# Update existing values
UPDATE `nom` n, `nom_cstm` ncstm, `nom_i18n` ni18n
SET ncstm.value=ni18n.name, ncstm.modified_by=-1, ncstm.modified=NOW(), ncstm.lang=ni18n.lang
WHERE n.type=8 AND n.id=ni18n.parent_id AND ni18n.lang='bg' AND n.id=ncstm.model_id AND ncstm.var_id=@aid AND ncstm.value!=ni18n.name;

# Add new values
INSERT IGNORE INTO `nom_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
SELECT n.id, @aid, 1, ni18n.name, NOW(), -1, NOW(), -1, ni18n.lang
FROM `nom` n, `nom_i18n` ni18n
WHERE n.type=8 AND n.id=ni18n.parent_id AND ni18n.lang='bg';

######################################################################################
# 2012-07-05 - Added new report - 'globaltest_qms_documents_changelog' - for Globaltest installation (remote)
#            - Add placeholders for 'globaltest_qms_documents_changelog' report templates

# Added new report - 'globaltest_qms_documents_changelog' - for Globaltest installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('217', 'globaltest_qms_documents_changelog', 'allows_files_generation := 1\r\nfiles_origin := self\r\ndocument_type_id := 24\r\nnomenclature_type_id := 18\r\npattern_id := 0', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('217', 'Списък на измененията на документите от СУК', NULL, NULL, 'bg'),
  ('217', 'QMS documents changelog',                     NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '217', '0', '1'),
  ('reports', 'export',          '217', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports'
    AND `action` IN ('generate_report', 'export')
    AND `model_type` = '217';

# Add placeholders for 'globaltest_qms_documents_changelog' report templates
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'qms_documents_changelog_table', 'Report', 'send', 'all', ',217,', 'qms_documents_changelog_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Списък на измененията на документите от СУК (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'QMS documents changelog (table)',                       NULL, 'en');

######################################################################################
# 2012-07-06 - Added new report - 'globaltest_qms_documents_common_list' - for Globaltest installation (remote)
#            - Add placeholders for 'globaltest_qms_documents_common_list' report templates

# Added new report - 'globaltest_qms_documents_common_list' - for Globaltest installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('219', 'globaltest_qms_documents_common_list', 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_id := 18\r\npattern_id := 0', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('219', 'Общ списък на документите от СУК', NULL, NULL, 'bg'),
  ('219', 'QMS documents common list',        NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '219', '0', '1'),
  ('reports', 'export',          '219', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports'
    AND `action` IN ('generate_report', 'export')
    AND `model_type` = '219';

# Add placeholders for 'globaltest_qms_documents_common_list' report templates
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'qms_documents_common_list_table', 'Report', 'send', 'all', ',219,', 'qms_documents_common_list_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Общ списък на документите от СУК (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'QMS documents common list (table)',          NULL, 'en');

########################################################################
# 2012-07-09 - Added new report - 'due_or_recoverable_vat' for Globaltest installation (remote)
#            - Added new report - 'globaltest_available_tsii_list' - for Globaltest installation (remote)
#            - Add placeholders for 'globaltest_available_tsii_list' report templates

# Added new report - 'due_or_recoverable_vat' for Globaltest installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (220, 'due_or_recoverable_vat', 'allows_files_generation := 1\r\nfile_origin := self\r\nhide_export_default := 1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (220, 'Дължимо/възстановимо ДДС', '01. Финанси', NULL, 'bg'),
  (220, 'Due/recoverable VAT', '01. Financial', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '220', '0', '1'),
  ('reports', 'export', '220', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '220';

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'period_expenses_and_incomes', 'Report', 'send', 'all', ',220,', 'period_expenses_and_incomes', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разходи/приходи за периода (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Period expenses/revenues (table)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'due_or_recoverable_vat', 'Report', 'send', 'all', ',220,', 'due_or_recoverable_vat', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Due/recoverable VAT (table)', NULL, 'en');

INSERT INTO `patterns` (`id`, `model`, `model_type`, `section`, `list`, `for_printform`, `company`, `position`, `header`, `footer`, `background_image`, `background_image_position`, `landscape`, `prefix`, `format`, `force_generate`, `not_regenerate_finished_record`, `plugin`, `handover_direction`, `is_portal`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
  (NULL, 'Report', '220', 0, 0, 0, 0, 1, 0, 0, NULL, 'left_top', 0, '[current_date]_[name]', 'pdf', 0, 0, 0, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
INSERT INTO `patterns_i18n` (`parent_id`, `name`, `description`, `content`, `lang`, `translated`) VALUES
  (LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС', '', '<p>\r\n [period_expenses_and_incomes]</p>\r\n<p>\r\n    [due_or_recoverable_vat]</p>\r\n', 'bg', NOW()),
  (LAST_INSERT_ID(), 'Due/recoverable VAT', '', '<p>\r\n  [period_expenses_and_incomes]</p>\r\n<p>\r\n    [due_or_recoverable_vat]</p>\r\n', 'en', NOW());

# Added new report - 'globaltest_available_tsii_list' - for Globaltest installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('221', 'globaltest_available_tsii_list', 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_tsii := 6\r\nnomenclature_type_standard := 20\r\ndocument_type_certificate := 13\r\ndocument_type_protocol := 35\r\npattern_id := 0', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('221', 'Списък на наличните ТСИИ', NULL, NULL, 'bg'),
  ('221', 'List of available TMMT',   NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '221', '0', '1'),
  ('reports', 'export',          '221', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports'
    AND `action` IN ('generate_report', 'export')
    AND `model_type` = '221';

# Add placeholders for 'globaltest_available_tsii_list' report templates
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'available_tsii_list_tables', 'Report', 'send', 'all', ',221,', 'available_tsii_list_tables', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Списък на наличните ТСИИ (таблици)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'List of available TMMT (tables)',    NULL, 'en');

######################################################################################
# 2012-07-10 - Change the settings of the 'globaltest_calibration_plan' report

# Change the settings of the 'globaltest_calibration_plan' report
UPDATE `reports`
  SET `settings` = CONCAT(`settings`, '\r\ndocument_type_repair_protocol := 14')
  WHERE `id` = '197'
    AND `settings` NOT LIKE '%document_type_repair_protocol%';

######################################################################################
# 2012-07-12 - Change the settings of the reports: 'globaltest_qms_documents_changelog', 'globaltest_qms_documents_common_list' and 'globaltest_available_tsii_list'

# Change the settings of the reports: 'globaltest_qms_documents_changelog', 'globaltest_qms_documents_common_list' and 'globaltest_available_tsii_list'
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\ndocument_type_id := 24\r\nnomenclature_type_id := 18'
  WHERE `id` = '217';
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_id := 18'
  WHERE `id` = '219';
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_tsii := 6\r\nnomenclature_type_standard := 20\r\ndocument_type_certificate := 13\r\ndocument_type_protocol := 35'
  WHERE `id` = '221';

######################################################################################
# 2012-09-13 - Change the placeholders for 'globaltest_employee_cardboard' report templates

# Change the placeholders for 'globaltest_employee_cardboard' report templates
DELETE FROM `placeholders_i18n`
  WHERE `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model` = 'Report'
        AND `varname` IN ('secondary_education_school_name', 'higher_education_school_name'));
DELETE FROM `placeholders`
  WHERE `model` = 'Report'
    AND `varname` IN ('secondary_education_school_name', 'higher_education_school_name');

UPDATE `placeholders_i18n`
  SET `name` = 'Средно образование'
  WHERE `lang` = 'bg'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'secondary_education_year');
UPDATE `placeholders_i18n`
  SET `name` = 'Secondary education'
  WHERE `lang` = 'en'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'secondary_education_year');
UPDATE `placeholders`
  SET `varname` = 'secondary_education_table',
    `source`    = 'secondary_education_table'
  WHERE `model`   = 'Report'
    AND `varname` = 'secondary_education_year';

UPDATE `placeholders_i18n`
  SET `name` = 'Висше образование'
  WHERE `lang` = 'bg'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'higher_education_year');
UPDATE `placeholders_i18n`
  SET `name` = 'Higher education'
  WHERE `lang` = 'en'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'higher_education_year');
UPDATE `placeholders`
  SET `varname` = 'higher_education_table',
    `source`    = 'higher_education_table'
  WHERE `model`   = 'Report'
    AND `varname` = 'higher_education_year';

######################################################################################
# 2012-09-19 - Change settings for report 'globaltest_available_tsii_list'

# Change settings for report 'globaltest_available_tsii_list'
UPDATE `reports`
  SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\nnomenclature_type_tsii := 6\r\ndocument_type_certificate := 13\r\ndocument_type_protocol := 35'
  WHERE `type` = 'globaltest_available_tsii_list';

######################################################################################
# 2012-09-21 - Added and changed placeholders for report 'globaltest_employee_cardboard'

# Added and changed placeholders for report 'globaltest_employee_cardboard'
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'certificate_table', 'Report', 'send', 'all', ',209,', 'certificate_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Придобити сертификати (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Acquired certificates (table)',   NULL, 'en');

UPDATE `placeholders_i18n`
  SET `name` = 'Средно образование (таблица)'
  WHERE `lang` = 'bg'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'secondary_education_table');
UPDATE `placeholders_i18n`
  SET `name` = 'Secondary education (table)'
  WHERE `lang` = 'en'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'secondary_education_table');

UPDATE `placeholders_i18n`
  SET `name` = 'Висше образование (таблица)'
  WHERE `lang` = 'bg'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'higher_education_table');
UPDATE `placeholders_i18n`
  SET `name` = 'Higher education (table)'
  WHERE `lang` = 'en'
    AND `parent_id` IN (SELECT `id`
      FROM `placeholders`
      WHERE `model`   = 'Report'
        AND `varname` = 'higher_education_table');

######################################################################################
# 2012-09-26 - Moved the updateNomenclatureQMS method to separate QMS plugin (instead of one that is specific to the company)
#            - Moved the printHeaderFooter plugin method to separate QMS plugin (instead of one that is specific to the company)

# Moved the updateNomenclatureQMS method to separate QMS plugin (instead of one that is specific to the company)
UPDATE `automations` SET method = 'plugin := qms\r\nmethod := updateNomenclatureQMS' WHERE `method` LIKE '%method := updateNomenclatureQMS%';

# Moved the printHeaderFooter plugin method to separate QMS plugin (instead of one that is specific to the company)
UPDATE `patterns_plugins` SET folder = 'qms', method = 'prepareHeaderFooter', settings = 'type := header\r\nnom_type := 18\r\nnom_var_name := nzoom_id\r\n' WHERE `method` LIKE '%prepareHeader%';

########################################################################
# 2012-10-09 - Added new report - 'globaltest_days_in_absence' - for Globaltest installation (remote)

# Added new report - 'globaltest_days_in_absence' - for Globaltest installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (237, 'globaltest_days_in_absence', 'document_leave_request := 17\r\ndocument_leave_request_order := 37\r\ndocument_leave_working_place := 15\r\ndocument_working_contract := 34\r\n\r\nworking_contract_start_work := paid_leave\r\nworking_contract_employee := customer_id\r\n\r\nleave_request_employee := customer_id\r\nleave_request_leave_type := plr_leave_type\r\nleave_request_year := plr_leave_year\r\nleave_request_count_days := plr_leave_days\r\n\r\navailable_student_days := 25\r\n\r\nleave_paid_request := plr_leave_type1\r\nleave_unpaid_request := plr_leave_type2\r\nleave_student_request := plr_leave_type3\r\nleave_mother_request := plr_leave_type4\r\nleave_child_request := plr_leave_type5\r\n\r\ndocument_absence_reason := absence_reason\r\ndocument_absence_from := absence_to\r\ndocument_absence_to := absence_do\r\n\r\ndocument_absence_personal_reason := absence_reason_two\r\n\r\nworking_hours_per_day := 8', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (237, 'Дни в отсъствие', NULL, NULL, 'bg'),
  (237, 'Days in abscence', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 237, 0, 1),
  ('reports', 'export', 237, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = 237;

########################################################################
# 2012-10-15 - Modified placeholders for 'due_or_recoverable_vat' report for Globaltest installation (remote)

# Modified placeholders for 'due_or_recoverable_vat' report for Globaltest installation (remote)
SELECT @ph_id := id FROM `placeholders` WHERE `varname`='period_expenses_and_incomes' AND `model`='Report' AND `pattern_id`=',220,';

UPDATE `placeholders`
SET `varname`='invoice_expenses_and_incomes', `source`='invoice_expenses_and_incomes'
WHERE id=@ph_id;

UPDATE `placeholders` p, `placeholders_i18n` pi
SET pi.`name`='Разходи/приходи по фактури (таблица)'
WHERE p.id=pi.parent_id AND pi.lang="bg" AND p.id=@ph_id;

UPDATE `placeholders` p, `placeholders_i18n` pi
SET pi.`name`='Expenses/revenues by invoices (table)'
WHERE p.id=pi.parent_id AND pi.lang="en" AND p.id=@ph_id;

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'due_or_recoverable_vat_documents', 'Report', 'send', 'all', ',220,', 'due_or_recoverable_vat_documents', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Документи, генериращи ДДС за внасяне/възстановяване (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Documents generating due/recoverable VAT (table)', NULL, 'en');

UPDATE `patterns` p, `patterns_i18n` pi
SET pi.`content`='<p>[invoice_expenses_and_incomes]</p>\r\n<p>[due_or_recoverable_vat_documents]</p>\r\n<p>[due_or_recoverable_vat]</p>\r\n'
WHERE p.id=pi.parent_id AND p.model='Report' AND p.model_type=220;

########################################################################
# 2013-06-28 - Changed settings for automation 'updateNomenclatureNextDate'

# Changed settings for automation 'updateNomenclatureNextDate'
UPDATE `automations`
  SET `settings` = CONCAT(`settings`, '\r\nnomenclature_type_tsii := 6\r\nfield_tsii_next_date := next_date')
  WHERE `method` LIKE '%updateNomenclatureNextDate%'
    AND `start_model_type` = '22'
    AND `settings` NOT LIKE '%repair_protocol_add_days%';

######################################################################################
# 2015-03-17 - Added additional settings for the 'globaltest_days_in_absence' report

# Added additional settings for the 'globaltest_days_in_absence' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ndocument_absence_approved_status := 58\r\ndocument_absence_rejected_status := 59')
  WHERE `type`='globaltest_days_in_absence' AND `settings` NOT LIKE '%document_absence_approved_status%';

######################################################################################
# 2015-12-21 - Added report for sending emails for debt collection

# Added report for sending emails for debt collection
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(334, 'bgs_debt_collection_email_sender', '# settings for report filters\r\nfin_documents_types := 1,2,3,102\r\ncustomers_types := 3\r\nupcoming_num_days := 2\r\n\r\n# created document types per company\r\ndocument_type1 := 38\r\ndocument_type2 := \r\ndocument_type3 := \r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(334, 'Писма за дължими суми', '01. Финанси', NULL, 'bg'),
(334, 'Debt collection emails', '01. Financial', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 334, 0, 1);

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` = 'generate_report' AND `model_type` = 334;
