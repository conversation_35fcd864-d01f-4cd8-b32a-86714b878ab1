############################################################################
### SQL nZoom Specific Updates 1797 (http://mall.demo.n-zoom.com/) ###
############################################################################

######################################################################################
# 2009-11-26 - Added new report - 'colliers_payments_report' - Colliers Installation

# Added new report - 'colliers_payments_report' - Colliers Installation
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(103 , 'colliers_payments_report', NULL , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(103, 'Плащания и фактури за период', NULL, 'bg'),
(103, 'Payments and Invoices for a period', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 103, 0, 1),
  ('reports', 'export', 103, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=103;

######################################################################################
# 2009-11-27 - Added data for table `imports` and `imports_i18n` for colliers

#Added data for table `imports` and `imports_i18n` for colliers
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(1, 'colliers_invoices', NULL, 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(1, 'Импортиране на фактури', NULL, 'bg'),
(1, 'Invoices Import', NULL, 'en');
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(2, 'colliers_payments', NULL, 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(2, 'Импортиране на плащания', NULL, 'bg'),
(2, 'Payment Import', NULL, 'en');

######################################################################################
# 2009-12-21 - Added new import plugin 'colliers_csv_cameras' for the installation of Colliers

# Added new import plugin 'colliers_csv_cameras' for the installation of Colliers
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(3, 'colliers_csv_cameras', 'document_type_id := 27', 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(3, 'Импортиране на данни от камери', NULL, 'bg'),
(3, 'Import of footfall data', NULL, 'en');

######################################################################################
# 2009-12-01 - Added new report - 'colliers_attendance' - Colliers Installation & 1797 (demo server)
#            - Added new report - 'colliers_claims_report' - Colliers Installation

# Added new report - 'colliers_attendance' - Colliers Installation & 1797 (demo server)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(106 , 'colliers_attendance', NULL , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(106, 'Посещаемост', NULL, 'bg'),
(106, 'Аttendance', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 106, 0, 1),
  ('reports', 'export', 106, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=106;

#Added new report - 'colliers_claims_report' - Colliers Installation
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(105, 'colliers_claims_report', NULL , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(105, 'Вземания до дата', NULL, 'bg'),
(105, 'Claims till date', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 105, 0, 1),
  ('reports', 'export', 105, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=105;

######################################################################################
# 2009-12-02 - Added new report - 'colliers_completed_clearing_activities' - Colliers Installation & 1797 (demo server)

# Added new report - 'colliers_completed_clearing_activities' - Colliers Installation & 1797 (demo server)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(107 , 'colliers_completed_clearing_activities', NULL , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(107, 'Извършени дейности по почистване', NULL, 'bg'),
(107, 'Completed clearing activities', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 107, 0, 1),
('reports', 'export', 107, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=107;

######################################################################################
# 2010-02-08 - Change the name of the existing Colliers Attandance report
#            - Added new report - 'colliers_attendance' - Colliers Installation & 1730 (demo server)

# Change the name of the existing Colliers Attandance report
UPDATE `reports` SET `type`='colliers_attendance_without_error' WHERE `id`=106;
UPDATE `reports_i18n` SET `name` = 'Посещаемост (без отчетена грешка)' WHERE `parent_id`=106 AND `lang`='bg';
UPDATE `reports_i18n` SET `name` = 'Аttendance (without statistic error)' WHERE `parent_id` =106 AND `lang`='en';

# Added new report - 'colliers_attendance' - Colliers Installation & 1730 (demo server)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(114 , 'colliers_attendance', 'document_type_id := 27' , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(114, 'Посещаемост', NULL, 'bg'),
(114, 'Аttendance', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 114, 0, 1),
('reports', 'export', 114, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=114;

######################################################################################
# 2010-03-24 - Added new report - 'colliers_contracts_changes' - for Colliers installation (remote)

# Added new report - 'colliers_contracts_changes' - for Colliers installation (remote)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(116 , 'colliers_contracts_changes', NULL , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(116, 'Изменения по договори', NULL, 'bg'),
(116, 'Contracts changes', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 116, 0, 1),
('reports', 'export', 116, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=116;

######################################################################################
# 2010-03-25 - Added new report - 'colliers_units_unavailability' - for Colliers installation (remote)

# Added new report - 'colliers_units_unavailability' - for Colliers installation (remote)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(117 , 'colliers_units_unavailability', NULL , 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(117, 'Заетост на търговски площи', NULL, 'bg'),
(117, 'Commercial properties unavailability', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 117, 0, 1),
('reports', 'export', 117, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=117;

######################################################################################
# 2010-03-26 - Added new report - 'colliers_renters_retainment' - for Colliers installation (remote)
#            - Added new report - 'colliers_tenant_turnovers' - for Colliers installation (remote)

# Added new report - 'colliers_renters_retainment' - for Colliers installation (remote)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(119 , 'colliers_renters_retainment', NULL , 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(119, 'Задържане на наематели', NULL, 'bg'),
(119, 'Renters retainment', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 119, 0, 1),
('reports', 'export', 119, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=119;

# Added new report - 'colliers_tenant_turnovers' - for Colliers installation (remote)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(120 , 'colliers_tenant_turnovers', NULL , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(120, 'Обороти на наематели', NULL, 'bg'),
(120, 'Tenant turnovers', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 120, 0, 1),
('reports', 'export', 120, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=120;

######################################################################################
# 2010-03-30 - Renamed 'colliers_renters_retainment' to 'colliers_tenants_retainment' - for Colliers installation (remote)
UPDATE `reports` SET `type` = 'colliers_tenants_retainment' WHERE `type` = 'colliers_renters_retainment';
UPDATE `reports_i18n` SET `name` = 'Tenants retainment' WHERE `name` = 'Renters retainment';

######################################################################################
# 2010-04-01 - Added new report - 'colliers_turnovers_by_square_meters_and_type_area' - for Colliers installation (remote)

# Added new report - 'colliers_turnovers_by_square_meters_and_type_area' - for Colliers installation (remote)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(121 , 'colliers_turnovers_by_square_meters_and_type_area', NULL , '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(121, 'Оборот на м² по тип магазин и размер', NULL, 'bg'),
(121, 'Тurnovers by m² and type area', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 121, 0, 1),
('reports', 'export', 121, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=121;

######################################################################################
# 2010-04-06 - Added new report - 'colliers_available_units' - for Colliers installation (remote)

# Added new report - 'colliers_available_units' - for Colliers installation (remote)
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(122, 'colliers_available_units', NULL , 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(122, 'Незаети площи', NULL, 'bg'),
(122, 'Available units', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 122, 0, 1),
('reports', 'export', 122, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=122;

######################################################################################
# 2010-04-15 - Added new report 'colliers_contracts_bank_deposits'

#Added new report 'colliers_contracts_bank_deposits'

UPDATE `_fields_meta` SET `source` =  'method := getCurrencies'
WHERE `model` = 'Document' AND `model_type` = 22 AND `name` = 'bankbg_currency';

UPDATE documents_cstm dc, _fields_meta fm SET dc.value = 'EUR'
WHERE fm.model = 'document' AND fm.model_type = 22 AND fm.name = 'bankbg_currency'
  AND fm.id = dc.var_id AND dc.value = 'bankbg_eur';
  
UPDATE documents_cstm dc, _fields_meta fm SET dc.value = 'BGN'
WHERE fm.model = 'document' AND fm.model_type = 22 AND fm.name = 'bankbg_currency'
  AND fm.id = dc.var_id AND dc.value = 'bankbg_lv';

UPDATE documents d, contracts c, _fields_meta fm1, _fields_meta fm2, documents_cstm dc, contracts_cstm cc
    SET d.trademark = c.trademark
WHERE d.customer = c.customer AND c.type = 1 AND d.type = 22 AND d.deleted_by = 0 AND c.deleted_by = 0
  AND fm1.model = 'Document' AND fm1.model_type = d.type AND fm1.name = 'code_unit'
  AND dc.model_id = d.id AND dc.var_id = fm1.id
  AND fm2.model = 'Contract' AND fm2.model_type = c.type AND fm2.name = 'object_code'
  AND cc.model_id = c.id AND cc.var_id = fm2.id
  AND dc.value = cc.value;

INSERT INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `height`) VALUES
(NULL, 'Document', '22', 'contract_num', 'dropdown', '', '0', 'method := getCustomDropdown\ntable := DB_TABLE_CONTRACTS\ntable_i18n := DB_TABLE_CONTRACTS_I18N\nlabel := t.num, ti18n.name\nvalue := t.id\nwhere := t.customer = <customer> AND t.trademark = \'<trademark>\'', '', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '373', '1', '200', NULL);
INSERT INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(LAST_INSERT_ID(), 'label', 'По договор', 'bg'),
(LAST_INSERT_ID(), 'label', 'For Contract', 'en');

INSERT IGNORE INTO `documents_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
SELECT d.id, LAST_INSERT_ID(), 1, c.id, NOW(), 1, NOW(), 1, ''
FROM documents AS d
JOIN contracts AS c
    ON d.customer = c.customer AND c.type=1 AND c.deleted_by = 0
JOIN _fields_meta AS fm
    ON fm.model = 'Document' AND fm.model_type = d.type AND fm.name = 'code_unit'
JOIN documents_cstm AS dcstm
    ON dcstm.model_id = d.id AND dcstm.var_id = fm.id
JOIN _fields_meta AS fm1
    ON fm1.model = 'Contract' AND fm1.model_type = c.type AND fm1.name = 'object_code'
JOIN contracts_cstm AS ccstm
    ON ccstm.model_id = c.id AND ccstm.var_id = fm1.id
WHERE d.type = 22 AND d.substatus != 18 AND d.deleted_by = 0 AND dcstm.value = ccstm.value;

INSERT INTO `reports` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES (118, 'colliers_contracts_bank_deposits', 'allows_email_sending := 1\r\nemail_origin := self\r\n\r\nallows_files_generation := 1\r\nfiles_origin := self\r\n\r\ncreate_model := document\r\ncreate_type := 12\r\n\r\nattach_file_to_model := 1\r\nattach_file_to_email := 1\r\n\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_contact_person := contact_person\r\ntransform_b_trademark := trademark\r\ntransform_b_name := custom_name\r\n', 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(118, 'Изтекли банкови гаранции', NULL, 'bg'),
(118, 'Expired bank deposits', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'generate_report', id, 0, 1 FROM `reports` WHERE id=118;
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'export', id, 0, 2 FROM `reports` WHERE id=118;
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=118;

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'trademark_name', 'Report', 'send', 'all', '118', 'trademark_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Търговска марка', NULL, 'bg'),
(LAST_INSERT_ID(), 'trademark', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_num', 'Report', 'send', 'all', '118', 'document_num', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Номер на банкова баранция', NULL, 'bg'),
(LAST_INSERT_ID(), 'Bank deposit number', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_validity', 'Report', 'send', 'all', '118', 'document_validity', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на падеж на банковата гарнация', NULL, 'bg'),
(LAST_INSERT_ID(), 'Bank deposit validity date', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_days_left', 'Report', 'send', 'all', '118', 'document_days_left', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Оставащи дни / изтекла преди', NULL, 'bg'),
(LAST_INSERT_ID(), 'Days left / Expired before', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_renew_date', 'Report', 'send', 'all', '118', 'document_renew_date', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на подновяване на гаранцията', NULL, 'bg'),
(LAST_INSERT_ID(), 'Bank deposit renew date', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_bank_deposit', 'Report', 'send', 'all', '118', 'document_bank_deposit', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Стойност на гаранцията', NULL, 'bg'),
(LAST_INSERT_ID(), 'Bank deposit value', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_bank_deposit_currency', 'Report', 'send', 'all', '118', 'document_bank_deposit_currency', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Валута на гаранцията', NULL, 'bg'),
(LAST_INSERT_ID(), 'Bank deposit currency', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_code_unit', 'Report', 'send', 'all', '118', 'document_code_unit', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Търговска площ', NULL, 'bg'),
(LAST_INSERT_ID(), 'Trade area', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_num', 'Report', 'send', 'all', '118', 'contract_num', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Номер на договор', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract number', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_name', 'Report', 'send', 'all', '118', 'contract_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Относно(договор)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Subject(contract)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_validity', 'Report', 'send', 'all', '118', 'contract_validity', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на валидност на договора', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract validity date', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_fixed_deposit', 'Report', 'send', 'all', '118', 'contract_fixed_deposit', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Фиксирано обезпечение по договора', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract fixed deposit', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_fixed_deposit_currency', 'Report', 'send', 'all', '118', 'contract_fixed_deposit_currency', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Валута на фиксираното обезпечението', NULL, 'bg'),
(LAST_INSERT_ID(), 'Fixed deposit currency', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_deposit_periods_count', 'Report', 'send', 'all', '118', 'contract_deposit_periods_count', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Брой на обезпеченията(наеми)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Deposits(rents) count', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_deposit_for_periods', 'Report', 'send', 'all', '118', 'contract_deposit_for_periods', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Стойност на обезпечението(за брой периоди)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract deposit(for periods count)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_deposit_for_periods_currency', 'Report', 'send', 'all', '118', 'contract_deposit_for_periods_currency', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Валута на обезпечението(за брой периоди)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract deposit currency(for periods count)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'created_model_num', 'Report', 'send', 'all', '118', 'created_model_num', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Номер на създадения документ(изходяща поща)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Created document number(outgoing)', NULL, 'en');

######################################################################################
# 2010-05-04 - Added new report - colliers_turnovers_rental_price

INSERT INTO `reports` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES 
(123, 'colliers_turnovers_rental_price', 'allows_email_sending := 1\nemail_origin := self\n\nallows_files_generation := 1\nfiles_origin := model\n\ncreate_model := finance_incomes_reason\ncreate_type := 1\n\nattach_file_to_model := 1\nattach_file_to_email := 1\n\ntransform_b_customer := customer\ntransform_b_branch := branch\ntransform_b_contact_person := contact_person\ntransform_b_trademark := trademark\ntransform_b_name := custom_name\ntransform_b_company := company\ntransform_b_office := office\ntransform_b_status := status\ntransform_b_link_to_model_name := link_to_model_name\ntransform_b_link_to := contract_id', 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(123, 'Дължим подоходен наем', NULL, 'bg'),
(123, 'Turnovers rental price due', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'generate_report', id, 0, 1 FROM `reports` WHERE id=123;
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'export', id, 0, 2 FROM `reports` WHERE id=123;
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=123;
  
UPDATE `placeholders` SET `pattern_id` = CONCAT(',', `pattern_id`, ',') WHERE `model` = 'Report';
  
UPDATE `placeholders` SET `pattern_id` = CONCAT(`pattern_id`, 123, ',') WHERE `model` = 'Report' AND `varname` IN ('trademark_name', 'contract_num', 'contract_name', 'contract_validity', 'created_model_num');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'company_name', 'Report', 'send', 'all', ',123,', 'company_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Фирма', NULL, 'bg'),
(LAST_INSERT_ID(), 'Company', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'office_name', 'Report', 'send', 'all', ',123,', 'office_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Офис', NULL, 'bg'),
(LAST_INSERT_ID(), 'Office', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'object_name', 'Report', 'send', 'all', ',123,', 'object_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Търговска площ', NULL, 'bg'),
(LAST_INSERT_ID(), 'Trade area', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'base_price', 'Report', 'send', 'all', ',123,', 'base_price', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Базова наемна цена', NULL, 'bg'),
(LAST_INSERT_ID(), 'Base rental price', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'incomes_percent', 'Report', 'send', 'all', ',123,', 'incomes_percent', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Подоходен наем(%)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Incomes rental price(%)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'turnovers_price', 'Report', 'send', 'all', ',123,', 'turnovers_price', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Подоходен наем(ст/ст)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Turnovers price(value)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'invoiced', 'Report', 'send', 'all', ',123,', 'invoiced', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Фактурирано до сега', NULL, 'bg'),
(LAST_INSERT_ID(), 'Invoiced', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'difference', 'Report', 'send', 'all', ',123,', 'difference', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Разлика', NULL, 'bg'),
(LAST_INSERT_ID(), 'Difference', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'currency', 'Report', 'send', 'all', ',123,', 'currency', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Валута', NULL, 'bg'),
(LAST_INSERT_ID(), 'Currency', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'from_date', 'Report', 'send', 'all', ',123,', 'from', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'От дата', NULL, 'bg'),
(LAST_INSERT_ID(), 'Date from', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'to_date', 'Report', 'send', 'all', ',123,', 'to', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'До дата', NULL, 'bg'),
(LAST_INSERT_ID(), 'Date to', NULL, 'en');

######################################################################################
# 2010-05-05 - Fixed source of document_code_unit for COLLIERS

#Fixed source of document_code_unit for COLLIERS
UPDATE `placeholders` SET `source` = 'document_code_unit' WHERE `source` = 'document_code_umit';


######################################################################################
# 2010-05-14 - Added new report 'colliers_uncovered_deposits'

INSERT INTO `reports` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES 
(124, 'colliers_uncovered_deposits', 'allows_email_sending := 1\r\nemail_origin := self\r\n\r\nallows_files_generation := 1\r\nfiles_origin := self\r\n\r\ncreate_model := document\r\ncreate_type := 12\r\n\r\nattach_file_to_model := 1\r\nattach_file_to_email := 1\r\n\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_contact_person := contact_person\r\ntransform_b_trademark := trademark\r\ntransform_b_name := custom_name', 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(124, 'Непокрити  обезпечения', NULL, 'bg'),
(124, 'Uncovered deposits', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'generate_report', id, 0, 1 FROM `reports` WHERE id=124;
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'export', id, 0, 2 FROM `reports` WHERE id=124;
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=124;
  
UPDATE `_fields_meta` SET `source` =  'method := getCurrencies'
WHERE `model` = 'Document' AND `model_type` = 25 AND `name` = 'deposit_currency';

UPDATE documents_cstm dc, _fields_meta fm SET dc.value = 'EUR'
WHERE fm.model = 'document' AND fm.model_type = 25 AND fm.name = 'deposit_currency'
  AND fm.id = dc.var_id AND dc.value = 'bankbg_eur';
  
UPDATE documents_cstm dc, _fields_meta fm SET dc.value = 'BGN'
WHERE fm.model = 'document' AND fm.model_type = 25 AND fm.name = 'deposit_currency'
  AND fm.id = dc.var_id AND dc.value = 'bankbg_lv';
  
UPDATE `_fields_meta` SET `source` =  'method := getCurrencies'
WHERE `model` = 'Document' AND `model_type` = 33 AND `name` = 'value_digest_currency';

UPDATE documents_cstm dc, _fields_meta fm SET dc.value = 'EUR'
WHERE fm.model = 'document' AND fm.model_type = 33 AND fm.name = 'value_digest_currency'
  AND fm.id = dc.var_id AND dc.value = 'bankbg_eur';
  
UPDATE documents_cstm dc, _fields_meta fm SET dc.value = 'BGN'
WHERE fm.model = 'document' AND fm.model_type = 33 AND fm.name = 'value_digest_currency'
  AND fm.id = dc.var_id AND dc.value = 'bankbg_lv';
  
UPDATE `placeholders` SET `pattern_id` = CONCAT(`pattern_id`, 124, ',') WHERE `model` = 'Report' AND `varname` IN ('trademark_name', 'contract_num', 'contract_name', 'contract_validity', 'company_name', 'office_name', 'difference', 'currency', 'object_name', 'document_renew_date', 'document_bank_deposit', 'created_model_num');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_sign', 'Report', 'send', 'all', ',124,', 'contract_sign', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на подписване', NULL, 'bg'),
(LAST_INSERT_ID(), 'Sign date', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'paid_deposit', 'Report', 'send', 'all', ',124,', 'paid_deposit', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Обезпечена сума', NULL, 'bg'),
(LAST_INSERT_ID(), 'Paid deposit', NULL, 'en');


######################################################################################
# 2010-06-04 - 'colliers_attendance' for Colliers installation (remote)

#  Added placeholders and options for export with charts in 'colliers_attendance_without_error' and
# 'colliers_attendance' for Colliers installation (remote)

UPDATE `reports` SET `settings` = CONCAT(`settings`, '\nallows_files_generation := 1\nfiles_origin := self') WHERE `id`=114;
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\nallows_files_generation := 1\nfiles_origin := self') WHERE `id`=106;

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'month_chart', 'Report', 'send', 'all', ',106,114,', 'month_chart', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Месечна справка', NULL, 'bg'),
(LAST_INSERT_ID(), 'Month chart', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'daily_chart', 'Report', 'send', 'all', ',106,114,', 'daily_chart', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дневна справка', NULL, 'bg'),
(LAST_INSERT_ID(), 'Daily chart', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'week_hour_report', 'Report', 'send', 'all', ',106,114,', 'week_hour_report', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Среден брой посещения по часове и дни от седмицата', NULL, 'bg'),
(LAST_INSERT_ID(), 'Avarage attandance per hours', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'week_day_report', 'Report', 'send', 'all', ',106,114,', 'week_day_report', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Среден брой посещения по дни от седмицата', NULL, 'bg'),
(LAST_INSERT_ID(), 'Avarage attandance per week days', NULL, 'en');

######################################################################################
# 2010-06-08 - Added report settings for graphic's width and heights for Colliers attandance reports 
#              ('colliers_attendance', 'colliers_attendance_without_error')

UPDATE `reports` SET `settings` = CONCAT(`settings`, '\nmonth_chart_width := 100%\nmonth_chart_height := 300\ndaily_chart_width := 100%\ndaily_chart_height := 300\nweek_hour_report_width := 100%\nweek_hour_report_height := 400\nweek_day_report_width := 100%\nweek_day_report_height := 300') WHERE `id`=114;
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\nmonth_chart_width := 100%\nmonth_chart_height := 300\ndaily_chart_width := 100%\ndaily_chart_height := 300\nweek_hour_report_width := 100%\nweek_hour_report_height := 400\nweek_day_report_width := 100%\nweek_day_report_height := 300') WHERE `id`=106;

######################################################################################
# 2010-06-15 - Added plugins for patterns
#            - Added special placeholders used by the plugins

INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `added`, `modified`) VALUES 
(1 , 'Finance_Incomes_Reason', '1', 'parkmall', 'prepareInvoices', NOW() , NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES 
(1, 'Подготовка на фактури (Park Mall Стара Загора)', 'Данните за фактурите се обработват по специфичен начин, за да се постигне желаният печат на фактурите.', 'bg', NOW()),
(1, 'Invoices Preparation (Park Mall Stara Zagora)', 'The invoice data is prepared in a specific manner to print the pattern in the desired mode.', 'en', NOW());

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'finance_incomes_reason_unit', 'Finance_Incomes_Reason', 'basic', 'patterns', NULL, '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Обект', NULL, 'bg'),
(LAST_INSERT_ID(), 'Unit', NULL, 'en');

######################################################################################
# 2010-06-16 - Added new report - 'rent_rolls'

# Added new report - 'rent_rolls' - for The Mall installation (remote) with option to be added for other installations
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(129, 'rent_rolls', 'contract_rent_id := 1\r\nbase_rent_price := 1034\r\nbase_rent_price_op := 1038\r\ntax_service := 1035\r\ntax_medial_price := 1036\r\nparking_place := 1041\r\nadvance_rrp := 1051\r\nincomes_rental_price := inc_rentalprice\r\nincomes_rental_date := irp_date_from', 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(129, 'Наемни нива', NULL, 'bg'),
(129, 'Rent rolls', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 129, 0, 1),
('reports', 'export', 129, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=129;


######################################################################################
# 2010-06-17 - Added new report - 'contracts_cashflow' - for The Mall installation (remote) with option to be added for other installations
#            - Added new automation plugin for invoices templates creation on contracts
#            - Added section and position of the reports
#            - Rename rent_rolls and contracts_cashflow
#            - Set positions to all the colliers (malls) reports
#            - Change the names of some of the colliers (malls) reports
#            - Add sections to colliers (malls) reports (Bulgarian)
#            - Add sections to colliers (malls) reports (English)
#            - Added new report - 'colliers_contracts_profit_and_loss' - for The Mall installation (remote) with option to be added for other installations

# Added new report - 'contracts_cashflow' - for The Mall installation (remote) with option to be added for other installations
INSERT INTO `reports` (`id` ,`type` ,`settings` ,`is_portal` ,`visible`) VALUES
(130, 'contracts_cashflow', 'contract_rent_id := 1\r\nbase_rent_price := 1034\r\nbase_rent_price_op := 1038\r\ntax_service := 1035', 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(130, 'Паричен поток', NULL, 'bg'),
(130, 'Cashflow', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 130, 0, 1),
('reports', 'export', 130, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=130;

# Added new automation plugin for invoices templates creation on contracts

INSERT INTO `automations` (`id`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
    (NULL, '0', NULL, '1', 'contracts', '', 'action', '1', 'condition := 1', 'plugin := colliers\nmethod := createInvoicesTemplates', NULL, '0', '9999');

DELETE vc.*, vi.*, vm.* 
FROM _variables_cstm AS vc, _variables_i18n AS vi, _variables_meta AS vm
WHERE vm.id = vi.parent_id AND vm.id = vc.var_id
  AND vm.name IN ('invoices_auto_send', 'invoices_auto_issue', 'invoices_container','invoices_employee','invoices_first_date','invoices_fiscal_event_date','invoices_generate_pattern','invoices_issue_date','invoices_observer','invoices_payment_date','invoices_payment_type','invoices_period_rows','invoices_proforma','invoices_recurrence','invoices_single_period','invoices_unique_key');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_unique_key', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Уникален ключ за артикулите в договор за наем', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Unique key for the articles in the contracts for rent', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'article_id\nfree_field1', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_auto_issue', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Автоматично издаване на фактури', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Invoices auto issue', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_auto_send', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Автоматично изпращане на фактури', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Invoices auto send', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '1', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_email_template', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Шаблон за e-mail', 'bg'),
    (LAST_INSERT_ID(), 'label', 'E-mail template', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_generate_pattern', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Шаблон за генериране', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Generate pattern', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_single_period', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Единичен период за фактуриране', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Invoice single period', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 1\nperiod := month\nlength := 0', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_payment_type', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Начин на плащане', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Payment type', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'bank', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_container', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Каса / Банкова сметка', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Cashbox / Bank account', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_observer', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Отговорник за издаване на фактури', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Invoices observer', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '[financial_contact_person]', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_proforma', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Проформа фактура', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Proforma invoice', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_period_rows', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Начин на фактуриране на единичните периоди', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Single periods invoicing', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'one_one\n#one_all\n#all_one', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_first_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Дата на първа фактура', 'bg'),
    (LAST_INSERT_ID(), 'label', 'First invoice date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 3\nperiod_type := working\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_issue_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Дата на издаване', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Issue date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 3\nperiod_type := working\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_fiscal_event_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Дата на данъчно събитие', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Fiscal event date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 3\nperiod_type := working\nperiod := day\ndirection := after\npoint := start', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_payment_date', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Дата на падеж', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Payment date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 3\nperiod_type := working\nperiod := day\ndirection := after\npoint := issue', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_recurrence', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Периодичност на фактурите', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Invoice recurrence', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), 'count := 1\nperiod := month', NULL, NULL, NOW(), '1', NOW(), '1', '');

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'invoices_employee', NULL, NULL, 'text', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Служител', 'bg'),
    (LAST_INSERT_ID(), 'label', 'Employee', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
('Contract', 0, LAST_INSERT_ID(), '', NULL, NULL, NOW(), '1', NOW(), '1', '');

#Rename rent_rolls and contracts_cashflow
UPDATE `reports` SET type='colliers_contracts_cashflow' WHERE type='contracts_cashflow';
UPDATE `reports` SET type='colliers_rent_rolls' WHERE type='rent_rolls';


#Set positions to all the colliers (malls) reports
UPDATE reports SET position=30  WHERE type='colliers_contracts_changes';
UPDATE reports SET position=10  WHERE type='colliers_rent_rolls';
UPDATE reports SET position=20  WHERE type='colliers_turnovers_rental_price';
UPDATE reports SET position=10  WHERE type='colliers_contracts_bank_deposits';
UPDATE reports SET position=20  WHERE type='colliers_uncovered_deposits';
UPDATE reports SET position=10  WHERE type='colliers_tenant_turnovers';
UPDATE reports SET position=20  WHERE type='colliers_turnovers_by_square_meters_and_type_area';
UPDATE reports SET position=10  WHERE type='colliers_registered_damages';
UPDATE reports SET position=20  WHERE type='colliers_completed_clearing_activities';
UPDATE reports SET position=10  WHERE type='colliers_attendance_without_error';
UPDATE reports SET position=20  WHERE type='colliers_attendance';
UPDATE reports SET position=10  WHERE type='colliers_units_unavailability';
UPDATE reports SET position=20  WHERE type='colliers_available_units';
UPDATE reports SET position=40  WHERE type='colliers_contracts_cashflow';
UPDATE reports SET position=20  WHERE type='colliers_tenants_retainment';
UPDATE reports SET position=30  WHERE type='colliers_payments_report';
UPDATE reports SET position=40  WHERE type='colliers_claims_report';
UPDATE reports SET position=100 WHERE type='employee_added_orders';
UPDATE reports SET position=110 WHERE type='documents_all';
UPDATE reports SET position=120 WHERE type='free_days_report';

#Change the names of some of the colliers (malls) reports
UPDATE reports_i18n, reports SET name='Contract Changes' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_changes' AND lang='en';
UPDATE reports_i18n, reports SET name='Rent Rolls' WHERE reports.id=reports_i18n.parent_id AND type='colliers_rent_rolls' AND lang='en';
UPDATE reports_i18n, reports SET name='Revenue Rental Price Invoice Calculation' WHERE reports.id=reports_i18n.parent_id AND type='colliers_turnovers_rental_price' AND lang='en';
UPDATE reports_i18n, reports SET name='Expired Bank Guarantees' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_bank_deposits' AND lang='en';
UPDATE reports_i18n, reports SET name='Insufficent Collateral' WHERE reports.id=reports_i18n.parent_id AND type='colliers_uncovered_deposits' AND lang='en';
UPDATE reports_i18n, reports SET name='Tenant Turnover' WHERE reports.id=reports_i18n.parent_id AND type='colliers_tenant_turnovers' AND lang='en';
UPDATE reports_i18n, reports SET name='Average Turnover by Unit Type & Size' WHERE reports.id=reports_i18n.parent_id AND type='colliers_turnovers_by_square_meters_and_type_area' AND lang='en';
UPDATE reports_i18n, reports SET name='Registered Damages' WHERE reports.id=reports_i18n.parent_id AND type='colliers_registered_damages' AND lang='en';
UPDATE reports_i18n, reports SET name='Cleaning Activities Report' WHERE reports.id=reports_i18n.parent_id AND type='colliers_completed_clearing_activities' AND lang='en';
UPDATE reports_i18n, reports SET name='Footfall Report (no statistical error)' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance_without_error' AND lang='en';
UPDATE reports_i18n, reports SET name='Footfall Report' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance' AND lang='en';
UPDATE reports_i18n, reports SET name='Occupancy Rate' WHERE reports.id=reports_i18n.parent_id AND type='colliers_units_unavailability' AND lang='en';
UPDATE reports_i18n, reports SET name='Vacant Units' WHERE reports.id=reports_i18n.parent_id AND type='colliers_available_units' AND lang='en';
UPDATE reports_i18n, reports SET name='Contracts CashFlow' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_cashflow' AND lang='en';
UPDATE reports_i18n, reports SET name='Tenant Retention' WHERE reports.id=reports_i18n.parent_id AND type='colliers_tenants_retainment' AND lang='en';
UPDATE reports_i18n, reports SET name='Payments & Invoices for a Period' WHERE reports.id=reports_i18n.parent_id AND type='colliers_payments_report' AND lang='en';
UPDATE reports_i18n, reports SET name='Receivables Overdue' WHERE reports.id=reports_i18n.parent_id AND type='colliers_claims_report' AND lang='en';

#Add sections to colliers (malls) reports (Bulgarian)
UPDATE reports_i18n, reports SET section='02. Договори' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_changes' AND lang='bg';
UPDATE reports_i18n, reports SET section='02. Договори' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_cashflow' AND lang='bg';
UPDATE reports_i18n, reports SET section='03. Наеми' WHERE reports.id=reports_i18n.parent_id AND type='colliers_rent_rolls' AND lang='bg';
UPDATE reports_i18n, reports SET section='03. Наеми' WHERE reports.id=reports_i18n.parent_id AND type='colliers_turnovers_rental_price' AND lang='bg';
UPDATE reports_i18n, reports SET section='04. Обезпечения' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_bank_deposits' AND lang='bg';
UPDATE reports_i18n, reports SET section='04. Обезпечения' WHERE reports.id=reports_i18n.parent_id AND type='colliers_uncovered_deposits' AND lang='bg';
UPDATE reports_i18n, reports SET section='05. Обороти' WHERE reports.id=reports_i18n.parent_id AND type='colliers_tenant_turnovers' AND lang='bg';
UPDATE reports_i18n, reports SET section='05. Обороти' WHERE reports.id=reports_i18n.parent_id AND type='colliers_turnovers_by_square_meters_and_type_area' AND lang='bg';
UPDATE reports_i18n, reports SET section='06. Поддръжка' WHERE reports.id=reports_i18n.parent_id AND type='colliers_registered_damages' AND lang='bg';
UPDATE reports_i18n, reports SET section='06. Поддръжка' WHERE reports.id=reports_i18n.parent_id AND type='colliers_completed_clearing_activities' AND lang='bg';
UPDATE reports_i18n, reports SET section='07. Посещаемост' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance_without_error' AND lang='bg';
UPDATE reports_i18n, reports SET section='07. Посещаемост' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance' AND lang='bg';
UPDATE reports_i18n, reports SET section='08. ТП' WHERE reports.id=reports_i18n.parent_id AND type='colliers_units_unavailability' AND lang='bg';
UPDATE reports_i18n, reports SET section='08. ТП' WHERE reports.id=reports_i18n.parent_id AND type='colliers_available_units' AND lang='bg';

#Add sections to colliers (malls) reports (English)
UPDATE reports_i18n, reports SET section='02. Contracts' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_changes' AND lang='en';
UPDATE reports_i18n, reports SET section='02. Contracts' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_cashflow' AND lang='en';
UPDATE reports_i18n, reports SET section='03. Rent' WHERE reports.id=reports_i18n.parent_id AND type='colliers_rent_rolls' AND lang='en';
UPDATE reports_i18n, reports SET section='03. Rent' WHERE reports.id=reports_i18n.parent_id AND type='colliers_turnovers_rental_price' AND lang='en';
UPDATE reports_i18n, reports SET section='04. Deposits' WHERE reports.id=reports_i18n.parent_id AND type='colliers_contracts_bank_deposits' AND lang='en';
UPDATE reports_i18n, reports SET section='04. Deposits' WHERE reports.id=reports_i18n.parent_id AND type='colliers_uncovered_deposits' AND lang='en';
UPDATE reports_i18n, reports SET section='05. Turnover' WHERE reports.id=reports_i18n.parent_id AND type='colliers_tenant_turnovers' AND lang='en';
UPDATE reports_i18n, reports SET section='05. Turnover' WHERE reports.id=reports_i18n.parent_id AND type='colliers_turnovers_by_square_meters_and_type_area' AND lang='en';
UPDATE reports_i18n, reports SET section='06. Support' WHERE reports.id=reports_i18n.parent_id AND type='colliers_registered_damages' AND lang='en';
UPDATE reports_i18n, reports SET section='06. Support' WHERE reports.id=reports_i18n.parent_id AND type='colliers_completed_clearing_activities' AND lang='en';
UPDATE reports_i18n, reports SET section='07. Attendance' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance_without_error' AND lang='en';
UPDATE reports_i18n, reports SET section='07. Attendance' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance' AND lang='en';
UPDATE reports_i18n, reports SET section='08. Units' WHERE reports.id=reports_i18n.parent_id AND type='colliers_units_unavailability' AND lang='en';
UPDATE reports_i18n, reports SET section='08. Units' WHERE reports.id=reports_i18n.parent_id AND type='colliers_available_units' AND lang='en';

# Added new report - 'colliers_contracts_profit_and_loss' - for The Mall installation (remote) with option to be added for other installations
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(131, 'colliers_contracts_profit_and_loss', 'contract_rent_id := 1\r\nbase_rent_price := 1034\r\nbase_rent_price_op := 1038\r\ntax_service := 1035', 10, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(131, 'Приходи и разходи по договори', '02. Договори', NULL, 'bg'),
(131, 'Profit & Loss', '02. Contracts', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 131, 0, 1),
('reports', 'export', 131, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=131;

######################################################################################
# 2010-06-21 - Added placeholder for administrative contact of the contract to two reports: colliers_contracts_bank_deposits and colliers_uncovered_deposits

#Added placeholder for administrative contact of the contract to two reports: colliers_contracts_bank_deposits and colliers_uncovered_deposits
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_administrative', 'Report', 'send', 'all', ',118,124,', 'contract_administrative', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Административен контакт (възложител)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Administrative Contact (principal)', NULL, 'en');

######################################################################################
# 2010-06-22 - Fixed problem for "colliers" automation plugin

#Fixed problem for "colliers" automation plugin
UPDATE `automations` SET `settings` = 'brp := 385, 389\nsch := 386\ninterim := 387\nbrp_steps := 156\nsch_steps := 160' WHERE `method` LIKE '%createInvoicesTemplates%';

#Grant permissions for the nzoom user for some of the reports
GRANT SELECT , INSERT , UPDATE , DELETE , CREATE , DROP ON `nzoom_1797`.`reports_colliers_contracts_bank_deposits` TO 'nzoom160'@'localhost';
GRANT SELECT , INSERT , UPDATE , DELETE , CREATE , DROP ON `nzoom_1797`.`reports_colliers_uncovered_deposits` TO 'nzoom160'@'localhost';
GRANT SELECT , INSERT , UPDATE , DELETE , CREATE , DROP ON `nzoom_1797`.`reports_colliers_turnovers_rental_price` TO 'nzoom160'@'localhost';

######################################################################################
# 2010-06-24 - Added plugin for max service charge index

UPDATE `_indexes` SET `settings` =  'plugin := colliers\nmethod := maxServiceCharge\nbehavior := execute_after_indexing\n\n#behavior := execute_before_indexing\n#behavior := cancel_indexing ' WHERE  `id` = 2;

######################################################################################
# 2010-06-29 - Added new report - 'colliers_contracts_chronology' - for The Mall installation (remote) with option to be added for other installations
#            - Added global variable 'open_date'

# Added new report - 'colliers_contracts_chronology' - for The Mall installation (remote) with option to be added for other installations
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(133, 'colliers_contracts_chronology', 'contract_type_lease := 1\r\nnom_type_unit := 2\r\nfield_type_unit := type_unit\r\nfield_quadrature_real := quadrature_real\r\nfield_open_date := open_date\r\nsvc_brp := 1034\r\nsvc_brp_ca := 1038\r\nsvc_advance_rrp := 1051\r\nsvc_sch := 1035', 20, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(133, 'Хронология договори', '02. Договори', NULL, 'bg'),
(133, 'Contracts Chronology', '02. Contracts', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 133, 0, 1),
('reports', 'export', 133, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=133;

# Added global variable 'open_date'
INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
(NULL, 'Contract', 0, 'open_date', NULL, NULL, 'date', 1, NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES 
(LAST_INSERT_ID(), 'label', 'Дата на откриване', 'bg'), 
(LAST_INSERT_ID(), 'label', 'Opening date', 'en');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES 
('Contract', '0', LAST_INSERT_ID(), '', NULL , NULL , NOW(), '1', NOW(), '1', '');

######################################################################################
# 2010-07-01 - Added tag for export and plugin for export

#Insert tag for exported invoices
INSERT INTO `tags` (`id`, `model`, `color`, `place`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
(NULL, 'finance_incomes_reasons', 'red', 1, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
SET @tag_id:= LAST_INSERT_ID();
INSERT INTO `tags_types` (`tag_id`, `type_id`) VALUES
(@tag_id, 1);
INSERT INTO `tags_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(@tag_id, 'Експортирана', '', 'bg', NOW()),
(@tag_id, 'Exported', '', 'en', NOW());

#Added plugin for export
INSERT INTO `exports` (`id`, `type`, `model`, `model_type`, `settings`, `visible`) VALUES
(NULL, 'colliers_invoices_to_workflow', 'Finance_Incomes_Reason', 1, CONCAT('default_file_name := invoices.csv\r\nexport_encoding := utf-8\r\nexport_delimiter := \\t\r\nexport_new_line := \\n\\r\r\nexport_tags := ', @tag_id, '\r\nexport_VAT_account := 4532\r\n'), 1);
INSERT INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Експорт на фактури към Workflow', 'Фактурите се експортират по специфичен начин в tab delimited file със специален формат', 'bg'),
(LAST_INSERT_ID(), 'Export of Invoices to Workflow', 'The invoices are exported in a tab delimited file with special format', 'en');

######################################################################################
# 2010-07-05 - Grant permissions for the nzoom user for some finance documents types table (alter is needed for the auto_increment change)
#            - Change the names of some of the colliers (malls) reports

#Grant permissions for the nzoom user for some finance documents types table (alter is needed for the auto_increment change)
GRANT SELECT , INSERT , UPDATE , DELETE , ALTER ON `nzoom_1797`.`fin_documents_types` TO 'nzoom160'@'localhost';

#Change the names of some of the colliers (malls) reports
UPDATE reports_i18n, reports SET name='Occupancy of Leasable Areas' WHERE reports.id=reports_i18n.parent_id AND type='colliers_units_unavailability' AND lang='en';
UPDATE reports_i18n, reports SET name='Completed Cleaning Activities' WHERE reports.id=reports_i18n.parent_id AND type='colliers_completed_clearing_activities' AND lang='en';
UPDATE reports_i18n, reports SET name='Footfall' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance' AND lang='en';
UPDATE reports_i18n, reports SET name='Footfall Without Statistical Error' WHERE reports.id=reports_i18n.parent_id AND type='colliers_attendance_without_error' AND lang='en';

######################################################################################
# 2010-07-06 - Translate/rename the import plugins

#Translate/rename the import plugins
UPDATE imports_i18n ii, imports i SET ii.name='Импортиране на фактури от Workflow' WHERE i.id=ii.parent_id AND ii.lang='bg' AND i.type='colliers_invoices';
UPDATE imports_i18n ii, imports i SET ii.name='Import of invoices from Workflow' WHERE i.id=ii.parent_id AND ii.lang='en' AND i.type='colliers_invoices';

UPDATE imports_i18n ii, imports i SET ii.name='Импортиране на данни от камери' WHERE i.id=ii.parent_id AND ii.lang='bg' AND i.type='colliers_csv_cameras';
UPDATE imports_i18n ii, imports i SET ii.name='Import of footfall data' WHERE i.id=ii.parent_id AND ii.lang='en' AND i.type='colliers_csv_cameras';

#Added settings for colliers_claims_report
UPDATE `reports` SET `settings` =  'reports_additional_types := 101' WHERE  `type` = 'colliers_claims_report';

######################################################################################
# 2010-07-08 - Added new report 'colliers_equipment_maintenance_schedule' - Colliers Installation
#            - Added new report 'colliers_cleaning_activities_schedule' - for The Mall installation (remote)

# Added new report - 'colliers_equipment_maintenance_schedule' - Colliers Installation
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(135, 'colliers_equipment_maintenance_schedule', 'document_type_maintenance_schedule_id := 45', 40, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(135, 'График за поддръжка по групи оборудване за период', '05. Оперативен модул', NULL, 'bg'),
(135, 'Equipment Groups Maintenance Plan', '05. Operations Module', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 135, 0, 1),
('reports', 'export', 135, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=135;

# Added new report 'colliers_cleaning_activities_schedule' - for The Mall installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(134, 'colliers_cleaning_activities_schedule', 'doc_type_cleaning_schedule := 38\r\ndoc_type_cleaning_checklist := 39\r\nnom_type_zone := 11\r\nnom_type_activity := 12', 30, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(134, 'График почистване за период', '05. Оперативен модул', NULL, 'bg'),
(134, 'Cleaning Activities Schedule', '05. Operations Module', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 134, 0, 1),
('reports', 'export', 134, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=134;

#Fixed some placeholders
UPDATE `placeholders` SET `pattern_id` =',118,124,123,' WHERE `pattern_id`=',118124,,123,' AND model='Report';
UPDATE `placeholders` SET `pattern_id` =',118,124,' WHERE `pattern_id`=',118124,,' AND model='Report';

######################################################################################
# 2010-07-12 - Updated settings of report 'colliers_cleaning_activities_schedule' - for The Mall installation (remote)

# Updated settings of report 'colliers_cleaning_activities_schedule' - for The Mall installation (remote)
UPDATE `reports` SET `settings`='doc_type_cleaning_schedule := 38\r\nnom_type_zone := 11\r\nnom_type_activity := 12\r\ncreate_model := document\r\ncreate_type := 39\r\ncreate_contains_additional_vars := 1'
WHERE `id`=134;

######################################################################################
# 2010-07-14 - Updated fiscal settings for contract types

#Updated fiscal settings for contract types
UPDATE `contracts_types` SET `fiscal` =  '0', `fiscal_start` =  '0';

######################################################################################
# 2010-07-20 - Added functionality for the colliers_revenue_rental_price_calculation report to issue credit notices
#            - Added new import plugin 'colliers_water_expenses' for Park Mall installation (remote)

UPDATE `reports` SET 
    `settings` = 'allows_email_sending := 1\nemail_origin := self\n\nallows_files_generation := 1\nfiles_origin := model\n\ncreate_model := finance_incomes_reason\ncreate_type := 1,3\n\nattach_file_to_model := 1\nattach_file_to_email := 1\n\ntransform_b_customer := customer\ntransform_b_branch := branch\ntransform_b_contact_person := contact_person\ntransform_b_trademark := trademark\ntransform_b_name := custom_name\ntransform_b_company := company\ntransform_b_office := office\ntransform_b_status := status\ntransform_b_link_to_model_name := link_to_model_name\ntransform_b_link_to := link_to\n\nreport_contract_type := 1\nreport_articles := 385, 389\nreport_articles_invoiced := 390, 391\nreport_vat_multiplier := 1.2\nreport_dependencies := 385 => 391, 389 => 390, 391 => 391, 390 => 390'
WHERE `type` = 'colliers_revenue_rental_price_calculation';

# Added new import plugin 'colliers_water_expenses' for Park Mall installation (remote)
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(5, 'colliers_water_expenses', 'document_protocol_expeses_id := 34\r\ndocument_protocol_change_id:=31\r\ndocument_protocol_start_exploatation_id:=32\r\nnomenclature_water_device_type:=9', 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(5, 'Разходи за вода', NULL, 'bg'),
(5, 'Import of water exspenses', NULL, 'en');

######################################################################################
# 2010-07-21 - Updated settings of report 'colliers_equipment_maintenance_schedule' - for The Mall installation (remote)
#            - Added new import plugin 'colliers_air_conditioning_expenses' for Park Mall installation (remote)

# Updated settings of report 'colliers_equipment_maintenance_schedule' - for The Mall installation (remote)
UPDATE `reports` SET `settings`='document_type_maintenance_schedule_id := 45\r\ncreate_model := document\r\ncreate_type := 40\r\ncreate_contains_additional_vars := 1'
WHERE `id`=135;

# Added new import plugin 'colliers_air_conditioning_expenses' for Park Mall installation (remote)
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(6, 'colliers_air_conditioning_expenses', 'document_protocol_air_conditioning_expeses_id := 35\r\ndocument_protocol_change_id:=31\r\ndocument_protocol_start_exploatation_id:=32\r\nnomenclature_water_device_type:=9', 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(6, 'Разходи за климатизация', NULL, 'bg'),
(6, 'Air conditioning expenses', NULL, 'en');

######################################################################################
# 2010-07-27 - Changed the ID of the finance document type "Interest account"

UPDATE `fin_documents_types_i18n` SET `parent_id` = '18' WHERE `parent_id` =101 AND `lang` = 'bg';
UPDATE `fin_documents_types_i18n` SET `parent_id` = '18' WHERE `parent_id` =101 AND `lang` = 'en';
UPDATE `fin_documents_types` SET `id` = '18' WHERE `id` = 101;

######################################################################################
# 2010-08-03 - Added options for creating graphics for 'colliers_tenant_turnovers'

# Added options for creating graphics for 'colliers_tenant_turnovers'
UPDATE `reports` SET `settings` = '' WHERE `id`=120;
UPDATE `reports` SET `settings` = CONCAT(`settings`, 'allows_files_generation := 1\nfiles_origin := self\nturnovers_chart_width := 100%\nturnovers_chart_height := 400') WHERE `id`=120;
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'tenant_turnovers_chart', 'Report', 'send', 'all', ',120,', 'tenant_turnovers_chart', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Обороти на наематели(графика)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Tenants turnovers(chart)', NULL, 'en');

######################################################################################
# 2010-09-10 - Added settings for 'colliers_vacant_units' report

# Added settings for 'colliers_vacant_units' report
UPDATE `reports` 
SET `settings`='contract_type_lease := 1\r\nnom_type_unit := 2\r\nfield_part_gla := part_gla\r\nfield_quadrature_real := quadrature_real\r\nfield_type_unit := type_unit\r\nfield_floor_unit := floor_unit\r\nfield_situated_unit := situated_unit\r\n'
WHERE `id`=122;

######################################################################################
# 2010-10-04 - Added placeholder for the table to be exported in 'tenant_turnovers_table' report
#            - Added options for creating graphics for 'units_unavailability_chart' and 'colliers_tenants_retainment'

# Added placeholder for the table to be exported in 'tenant_turnovers_table' report
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'tenant_turnovers_table', 'Report', 'send', 'all', ',120,', 'tenant_turnovers_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Обороти на наематели(таблица)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Tenants turnovers(table)', NULL, 'en');

# Added options for creating graphics for 'units_unavailability_chart' and 'colliers_tenants_retainment'
UPDATE `reports` SET `settings` = '' WHERE `id`=117;
UPDATE `reports` SET `settings` = CONCAT(`settings`, 'allows_files_generation := 1\nfiles_origin := self\nunits_unavailability_chart_width := 100%\nunits_unavailability_chart_height := 250') WHERE `id`=117;
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'units_unavailability_chart', 'Report', 'send', 'all', ',117,', 'units_unavailability_chart', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Заетост на търговски площи(графика)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Units unavailability(chart)', NULL, 'en');

UPDATE `reports` SET `settings` = '' WHERE `id`=119;
UPDATE `reports` SET `settings` = CONCAT(`settings`, 'allows_files_generation := 1\nfiles_origin := self\ntenants_retainment_tenants_chart_width := 100%\ntenants_retainment_tenants_chart_height := 250\ntenants_retainment_gla_chart_width := 100%\ntenants_retainment_gla_chart_height := 250') WHERE `id`=119;
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'tenants_retainment_tenants_chart', 'Report', 'send', 'all', ',119,', 'tenants_retainment_tenants_chart', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Задържане на наематели - наематели (графика)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Tenant retention - tenants (chart)', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'tenants_retainment_gla_chart', 'Report', 'send', 'all', ',119,', 'tenants_retainment_gla_chart', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Задържане на наематели - площ (графика)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Tenant retention - gla (chart)', NULL, 'en');

######################################################################################
# 2010-11-24 - Fixed the export plugins

UPDATE `exports` SET settings=REPLACE(settings, 'default_file_name', 'export_file_name');
UPDATE `exports` SET settings=CONCAT(settings, 'export_hide_filters := file_name, format, separator\r\n');

######################################################################################
# 2011-04-28 - Fixed periods_start for templates of temporary agreements and annexes

# Fixed periods_start for templates of temporary agreements and annexes
UPDATE contracts c, fin_invoices_templates fit LEFT JOIN fin_invoices_templates ft ON ft.contract_id = fit.contract_id AND ft.periods_start != fit.periods_start
SET fit.periods_start=c.date_start_temporary_agreement
 WHERE fit.contract_id=c.id AND c.subtype = 'temporary_agreement' AND fit.periods_start != c.date_start_temporary_agreement AND ft.id IS NULL;

UPDATE contracts c, fin_invoices_templates fit LEFT JOIN fin_invoices_templates ft ON ft.contract_id = fit.contract_id AND ft.periods_start != fit.periods_start
SET fit.periods_start=c.date_start_annex
 WHERE fit.contract_id=c.id AND c.subtype = 'annex' AND fit.periods_start != c.date_start_annex AND ft.id IS NULL;

CREATE TEMPORARY TABLE IF NOT EXISTS tt0
SELECT MIN(fit.periods_start) as min_periods_start, fit.contract_id, c.date_start_temporary_agreement FROM fin_invoices_templates fit, contracts c
 WHERE fit.contract_id=c.id AND c.subtype = 'temporary_agreement' AND fit.periods_start != c.date_start_temporary_agreement GROUP BY fit.contract_id HAVING COUNT(fit.id) > 1 ORDER BY c.parent_record, c.id, fit.periods_start;
UPDATE fin_invoices_templates fit, tt0
SET fit.periods_start=tt0.date_start_temporary_agreement
 WHERE fit.contract_id=tt0.contract_id AND fit.periods_start=tt0.min_periods_start;

CREATE TEMPORARY TABLE IF NOT EXISTS tt1
SELECT MIN(fit.periods_start) as min_periods_start, fit.contract_id, c.date_start_annex FROM fin_invoices_templates fit, contracts c
 WHERE fit.contract_id=c.id AND c.subtype = 'annex' AND fit.periods_start != c.date_start_annex GROUP BY fit.contract_id HAVING COUNT(fit.id) > 1 ORDER BY c.parent_record, c.id, fit.periods_start;
UPDATE fin_invoices_templates fit, tt1
SET fit.periods_start=tt1.date_start_annex
 WHERE fit.contract_id=tt1.contract_id AND fit.periods_start=tt1.min_periods_start;

######################################################################################
# 2012-01-17 - Added missing setting for 'colliers_leasable_area_occupancy' report

# Added missing setting for 'colliers_leasable_area_occupancy' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\nunits_unavailability_column_chart_height := 400') 
WHERE `id`=117 AND settings NOT LIKE '%units_unavailability_column_chart_height%';

######################################################################################
# 2013-01-18 - Changed settings for width of charts in reports

# Changed settings for width of charts in reports
UPDATE `reports` SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\nunits_unavailability_chart_width := 800\r\nunits_unavailability_chart_height := 250\r\nunits_unavailability_column_chart_height := 400\r\nunits_unavailability_column_chart_width := 1200' 
WHERE `type` = 'colliers_leasable_area_occupancy';
UPDATE `reports` SET `settings` = 'allows_files_generation := 1\r\nfiles_origin := self\r\ntenants_retainment_tenants_chart_width := 800\r\ntenants_retainment_tenants_chart_height := 250\r\ntenants_retainment_gla_chart_width := 800\r\ntenants_retainment_gla_chart_height := 250' 
WHERE `type` = 'colliers_tenants_retention';
UPDATE `reports` SET `settings` = 'allows_files_generation := 1\nfiles_origin := self\nturnovers_chart_width := 1200\nturnovers_chart_height := 400'
WHERE `type` = 'colliers_tenant_turnover';
UPDATE `reports` SET `settings` = 'document_type_id := 27\r\nallows_files_generation := 1\r\nfiles_origin := self\r\nmonth_chart_width := 1200\r\nmonth_chart_height := 300\r\ndaily_chart_width := 1200\r\ndaily_chart_height := 300\r\nweek_hour_report_width := 1200\r\nweek_hour_report_height := 400\r\nweek_day_report_width := 1200\r\nweek_day_report_height := 300'
WHERE `type` IN ('colliers_footfall', 'colliers_footfall_unaltered');
