-- #########################################
-- ### CONVERSION SQL FILE FOR NZOOM v23 ###
-- #########################################

#########################################################################################
# 2023-01-06 - Added new setting for setting labels of certain filters in address_budgets report
#            - Added new setting for setting labels of certain filters in address_expenses_and_incomes_for_period report
#            - Added new setting for different order of the results in deliverers_obligations report

# Added new setting for setting labels of certain filters in address_budgets report
UPDATE reports SET settings = CONCAT('filter_company_lbl_bg :=\r\nfilter_region_lbl_bg :=\r\nfilter_office_lbl_bg :=\r\n\r\n', `settings`)
WHERE type = 'address_budgets' AND settings NOT LIKE '%filter_company_lbl%';

# Added new setting for setting labels of certain filters in address_expenses_and_incomes_for_period report
UPDATE reports SET settings = CONCAT('filter_company_lbl_bg :=\r\nfilter_region_lbl_bg :=\r\nfilter_office_lbl_bg :=\r\n\r\n', `settings`)
WHERE type = 'address_expenses_and_incomes_for_period' AND settings NOT LIKE '%filter_company_lbl%';

# Added new setting for different order of the results in deliverers_obligations report
UPDATE reports SET settings = CONCAT(`settings`, '\r\n\r\nsort_by_customer_name :=')
WHERE type = 'deliverers_obligations' AND settings NOT LIKE '%sort_by_customer_name%';

#########################################################################################
# 2023-01-16 - Add new setting execute_as_original_user for automation gridToModels

# Add new setting execute_as_original_user for automation gridToModels
UPDATE  `automations`
 SET `settings` = CONCAT(`settings`, '\r\n\r\n# Изпълни автоматичното действие от името на оригиналния (текущия) потребител. По подразбиране е изключена със стойност 0, за да се включи задайте стойност 1.\r\nexecute_as_original_user := 0')
 WHERE `method` LIKE '%gridToModels%'
   AND `settings` NOT LIKE '%execute_as_original_user%';

#########################################################################################
# 2023-01-18 - Added new setting for biotrade_week_schedule dashlet plugin to mark which is the realted report to be used by the plugin

# Added new setting for biotrade_week_schedule dashlet plugin to mark which is the realted report to be used by the plugin
UPDATE dashlets_plugins SET settings = CONCAT('related_report := biotrade_visit_planning\r\n\r\n', `settings`)
WHERE type = 'biotrade_week_schedule' AND settings NOT LIKE '%related\_report%';

#########################################################################################
# 2023-01-31 - Added new settings for support of oauth2 authentication for bouncedEmails

# Added new settings for support of oauth2 authentication for bouncedEmails
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
  (NULL, 'emails', 'replyto_imap_oauth2_provider', ''),
  (NULL, 'emails', 'replyto_imap_oauth2_client_id', ''),
  (NULL, 'emails', 'replyto_imap_oauth2_client_secret', ''),
  (NULL, 'emails', 'replyto_imap_oauth2_tenant_id', ''),
  (NULL, 'emails', 'replyto_imap_oauth2_refresh_token', '');

INSERT IGNORE INTO `settings_i18n` VALUES
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_provider'),            'IMAP oAuth2 доставчик',       'Доставчик за oAuth2 аутентицазия <br /> (Възможни са: Azure и Google)',         'bg', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_provider'),            'IMAP oAuth2 provider',        'Provider of oAuth2 authentication <br /> (Posible values: Azure and Google)',   'en', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_client_id'),           'IMAP oAuth2 client id',       'Client_id <br /> (Взeма се от доставчика)',                                     'bg', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_client_id'),           'IMAP oAuth2 client id',       'Client_id <br /> (Get it from the provider)',                                   'en', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_client_secret'),       'IMAP oAuth2 client secret',   'Client_secret <br /> (Взeма се от доставчика)',                                 'bg', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_client_secret'),       'IMAP oAuth2 client secret',   'Client_secret <br /> (Get it from the provider)',                               'en', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_tenant_id'),           'IMAP oAuth2 tenant Id',       'Tenant_id <br /> (Взeма се от доставчика)',                                     'bg', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_tenant_id'),           'IMAP oAuth2 tenant Id',       'Tenant_id <br /> (Get it from the provider)',                                   'en', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_refresh_token'),       'IMAP oAuth2 refresh token',   'Refresh_token <br /> (Генерира се отделно)',                                    'bg', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_oauth2_refresh_token'),       'IMAP oAuth2 refresh token',   'Refresh_token <br /> (Should be generated in onother program)',                 'en', now());

#########################################################################################
# 2023-02-03 - Added settings column in bullets table

# Added settings column in bullets table
ALTER TABLE `bullets` ADD `settings` TEXT NOT NULL AFTER `description`;

#########################################################################################
# 2023-02-10 - Changed the character set and collation

# Changed the character set and collation
-- ALTER DATABASE <databasename> CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_fields_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_fields_meta` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_fields_options` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_fields_template_settings` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_fields_template_settings_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_formulas` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_formulas_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_indexes` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_indexes_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_indexes_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_indexes_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_locked_records` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_modules_fields` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_search_comparisons` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_search_defs` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_shorten_url_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_stopwatches` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_variables_cstm` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_variables_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `_variables_meta` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_acknowledgements` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_categories` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_categories_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `announcements_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_bb` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_comments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_configurator` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_documents` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_documents_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_documents_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_documents_cstm` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_documents_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_documents_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_documents_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_files` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_files_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_gt2_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_gt2_audit_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_gt2_details` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_gt2_details_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_gt2_indexes` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `archive_tags_models` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `assignments_configurator` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `automations` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `automations_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `bb` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `bullets` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `comments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `configurator` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `configurator_group` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_contacts_cc` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_cstm` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_statuses` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `contracts_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `country_list` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `country_nonworkdays` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `crontab` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `crontab_issue_results` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `crontab_lock` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `cstm_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_cstm` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_trademarks` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `customers_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `dashlets` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `dashlets_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `dashlets_plugins` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `dashlets_plugins_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `departments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `departments_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `diag_containers_change_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `diag_invoice_status_reasons_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `diag_payment_status_reasons_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `diag_payment_status_reasons_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_cstm` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_default_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_medias` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_statuses` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `documents_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_campaigns` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_campaigns_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_campaigns_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_campaigns_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_campaigns_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_sentbox` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_targetlists` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_targetlists_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_targetlists_data_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_targetlists_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `emails_targetlists_unsubscribed` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_categories` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_cats_assigned` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_default_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_recurrence` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_reminders` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `events_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `exports` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `exports_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `exports_log` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `files` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `files_contents` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `files_conversions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `files_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `filters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_centers` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_centers_distribution` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_centers_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_centers_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_centers_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_distribution_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_distribution_items` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_items` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_items_distribution` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_analysis_items_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_annulments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_annulments_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_balance` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_bank_accounts` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_bank_accounts_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_bank_accounts_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_batches` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_budgets` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_budgets_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_cashboxes` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_cashboxes_amounts` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_cashboxes_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_cashboxes_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_companies` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_companies_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_companies_offices` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_companies_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_counters_offices` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_currencies` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_currencies_available` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_currencies_available_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_default_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_documents_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_documents_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_documents_statuses` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_documents_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_documents_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_expenses_reasons` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_expenses_reasons_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_incomes_reasons` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_incomes_reasons_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_incomes_reasons_invoice_status` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_invoices_templates` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_invoices_templates_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_invoices_templates_info` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_offices_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_payments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_payments_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_payments_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_payments_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_payments_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_payslip` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_reasons_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_recurring_payments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_recurring_payments_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_repayment_plans` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_repayment_plans_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_repayment_plans_incomes` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_repayment_plans_statuses` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_transaction_expenses` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_transaction_expenses_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_transfers` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_transfers_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_vat_rates` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_violation_notice` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_warehouses` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_warehouses_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_warehouses_documents` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_warehouses_documents_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_warehouses_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_warehouses_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `fin_warehouses_quantities` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `groups` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `groups_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `gt2_allocated_costs` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `gt2_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `gt2_audit_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `gt2_batches_data` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `gt2_details` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `gt2_details_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `gt2_indexes` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `helps` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `helps_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `imports` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `imports_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `imports_log` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `imports_tables_configurator` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `layouts` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `layouts_assign_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `layouts_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `layouts_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `logs` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `minitasks` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_categories` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_categories_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_cats` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_cstm` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_distribution` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_measures` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_price_list` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_price_list_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_price_updates` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_prices` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_types_distribution` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `nom_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `notes` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `notes_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `offices` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `offices_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `outlooks` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `outlooks_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `outlooks_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `outlooks_settings` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `patterns` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `patterns_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `patterns_parts` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `patterns_parts_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `patterns_plugins` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `patterns_plugins_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `personal_settings` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `placeholders` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `placeholders_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_cstm` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_default_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `projects_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `reminders_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `reports` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `reports_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `roles` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `roles_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `roles_definitions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `roles_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `roles_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `settings` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `settings_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `side_panels` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `stages` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `stages_activities` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `stages_activities_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `stages_activities_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `stages_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `stages_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `status_settings` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tags` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tags_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tags_models` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tags_permissions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tags_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tags_sections_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tags_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_audit` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_configurator` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_counters` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_counters_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_default_assignments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_dependencies` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_history` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_notifications` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_relatives` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_sections` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_statuses` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_timesheets` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_timesheets_activities` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_timesheets_configurator` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_types` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `tasks_types_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `transformations` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `transformations_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `transitions` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `transitions_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `turnovers` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `types_relations` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `types_vat_options` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `users` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `users_departments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `users_groups` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `users_i18n` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `users_login_tokens` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `users_stats` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

#########################################################################################
# 2023-02-13 - Added new setting that decides whether to show or not the customer type in goods movement report
#

# Added new setting that decides whether to show or not the customer type in goods movement report
UPDATE `reports` SET `settings` = CONCAT(`settings`,'\r\n\r\ndisplay_customer_types :=')
WHERE `type` = 'goods_movement' AND `settings` NOT LIKE '%display_customer_types%';

#########################################################################################
# 2023-02-14 - Changed the character set and collation of routines and triggers

# Changed the character set and collation of routines and triggers
SET NAMES utf8mb4;
SET collation_connection=utf8mb4_unicode_ci;

# PROCEDURE TO WRITE LOG FOR THE CHANGES
DROP PROCEDURE IF EXISTS log_changes;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE log_changes(current_action varchar(255), current_model varchar(255), current_model_id int(11), current_container_type varchar(255), current_container_id int(11), previous_sum double(11,2), new_sum double(11,2), current_container_currency varchar(3), changed_data_vars text CHARACTER SET utf8mb4)
SQL SECURITY INVOKER
    BEGIN
      # write history
      INSERT INTO diag_containers_change_history
      SET action=current_action,
          changed_model=current_model,
          changed_model_id=current_model_id,
          changed_by=USER(),
          container_type=current_container_type,
          container_id=current_container_id,
          previous_amount=(IF (previous_sum IS NULL, 0, previous_sum)),
          changed_amount=new_sum,
          currency=current_container_currency,
          changed_data=changed_data_vars,
          change_date=NOW();
    END;//
delimiter ;

# FUNCTION TO WRITE HISTORY FOR CHANGES IN FIN_BALANCE TABLE
DROP FUNCTION IF EXISTS log_payment_status_history;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION log_payment_status_history(current_action varchar(255), row_id int(11), changed_data_vars text CHARACTER SET utf8mb4) RETURNS INT(11)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE history_id INT(11);
        # write history
        INSERT INTO diag_payment_status_reasons_history
        SET action=current_action,
            balance_row_id=row_id,
            changed_by=USER(),
            changed_data=changed_data_vars,
            change_date=NOW();
        SELECT LAST_INSERT_ID() INTO history_id;

        # Return the history id so it can be used later for audit
        return history_id;
    END;//
delimiter ;

# PROCEDURE TO UPDATE THE REASON PAYMENT STATUS DEPENDING ON THE CHANGE IN fin_balance TABLE
DROP PROCEDURE IF EXISTS update_reason_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_reason_payment_status(side_connection varchar(255), changed_model_name varchar(255), changed_model_id int(11), history_id int(11))
SQL SECURITY INVOKER
    BEGIN
        SET @current_model_total = NULL;
        SET @current_model_type = NULL;
        SET @current_model_payment_status = NULL;
        SET @amount_difference = NULL;
        SET @new_payment_status = NULL;
        SET @related_model_id = NULL;

        # Check what the model is. The trigger should do its job only in case the model is Finance_Expenses_Reason or Finance_Incomes_Reason
        IF (changed_model_name='Finance_Expenses_Reason') THEN
            # CASE FOR FINANCE_EXPENSES_REASON
            # Get the amount from fin_balance for this model, that is connected to the same side of the connection

            # Get the type and the total for this model
            SELECT fer.type, fer.total_with_vat, fer.payment_status INTO @current_model_type, @current_model_total, @current_model_payment_status
              FROM fin_expenses_reasons as fer
              WHERE fer.id=changed_model_id;

            # If the calculated amount is less than 0 then absolute value is taken
            IF (@current_model_total<0) THEN
                SET @current_model_total=@current_model_total*-1;
            END IF;

            SET @new_payment_status = define_new_payment_status(changed_model_name, changed_model_id, side_connection);

            # Update the status of the reason
            UPDATE fin_expenses_reasons SET payment_status=@new_payment_status WHERE id=changed_model_id;
            CALL log_payment_status_audit(history_id, changed_model_name, changed_model_id, @current_model_payment_status, @new_payment_status);

            # If the reason is expense invoice (ID 20) or proforma (ID 21) checks for parent model or models
            IF (@current_model_type=20 OR @current_model_type=21) THEN
                # Check if there is a direct expense reason parent
                SET @related_model_id = (SELECT fer.id
                  FROM fin_reasons_relatives as frr
                  JOIN fin_expenses_reasons as fer
                    ON (frr.link_to=fer.id AND fer.type>100)
                  WHERE frr.parent_model_name='Finance_Expenses_Reason' AND frr.link_to_model_name='Finance_Expenses_Reason' AND frr.parent_id=changed_model_id
                    AND fer.active=1 AND fer.annulled_by=0 AND fer.status="finished");

                # If parent reason exists, updates its status too
                IF (@related_model_id) THEN
                    # If reason parent is found, get its data
                    SELECT fer.payment_status INTO @current_model_payment_status
                      FROM fin_expenses_reasons AS fer
                      WHERE fer.id=@related_model_id;

                    # Update reason payment status
                    IF (@current_model_type=21) THEN
                        # If current model is a proforma, both reason and proforma could have distributed payments
                        SET @new_payment_status = define_new_payment_status(changed_model_name, @related_model_id, side_connection);
                    END IF;

                    UPDATE fin_expenses_reasons SET payment_status=@new_payment_status WHERE id=@related_model_id;
                    CALL log_payment_status_audit(history_id, 'Finance_Expenses_Reason', @related_model_id, @current_model_payment_status, @new_payment_status);
                ELSEIF (@current_model_type=20) THEN
                    # Updates payment status of reason parents of proforma parents of expense invoice
                    CALL update_multiple_reason_payment_status(changed_model_id, @new_payment_status, history_id);
                END IF;
            END IF;
        ELSEIF (changed_model_name='Finance_Incomes_Reason') THEN
            # CASE FOR FINANCE_INCOMES_REASON
            SELECT fir.type, fir.total_with_vat, fir.payment_status INTO @current_model_type, @current_model_total, @current_model_payment_status
              FROM fin_incomes_reasons as fir
              WHERE fir.id=changed_model_id;

            # Flag to show if an user defined incomes reason (with type greater than 100) has to be changed
            SET @incomes_reason_calculate=0;
            SET @incomes_reason_total=0;
            SET @incomes_reason_payment_status='';

            IF (@current_model_type>100) THEN
                # If the current changed reason has type greater than 100 then certain processing is skipped
                # and goes directly to processing the incomes reason status
                SET @incomes_reason_calculate=changed_model_id;
                SET @incomes_reason_total=@current_model_total;
                SET @incomes_reason_payment_status=@current_model_payment_status;
            ELSE
                # Case for the reason from system type
                # If the calculated amount is less than 0 then absolute value is taken
                IF (@current_model_total<0) THEN
                    SET @current_model_total=@current_model_total*-1;
                END IF;

                SET @new_payment_status = define_new_payment_status(changed_model_name, changed_model_id, side_connection);

                # Update the status of the reason
                UPDATE fin_incomes_reasons SET payment_status=@new_payment_status, payment_status_modified=(IF (@current_model_payment_status COLLATE utf8mb4_unicode_ci = @new_payment_status COLLATE utf8mb4_unicode_ci, payment_status_modified, NOW())) WHERE id=changed_model_id;
                CALL log_payment_status_audit(history_id, changed_model_name, changed_model_id, @current_model_payment_status, @new_payment_status);

                IF (@current_model_type=4 OR @current_model_type=3) THEN
                    # If the type is debit or credit notice then try to find the related incomes reason
                    # Search for related invoice
                    SET @related_model_id = (SELECT fir.id
                      FROM fin_reasons_relatives as frr
                      JOIN fin_incomes_reasons as fir
                        ON (frr.link_to=fir.id AND fir.type=1)
                      WHERE frr.parent_model_name='Finance_Incomes_Reason' AND frr.link_to_model_name='Finance_Incomes_Reason' AND frr.parent_id=changed_model_id
                        AND fir.annulled_by=0 AND fir.active=1 AND fir.status="finished");

                    IF (@related_model_id) THEN
                        # Search for the incomes reason related to the found invoice
                        SELECT fir.id, fir.total_with_vat, fir.payment_status INTO @incomes_reason_calculate, @incomes_reason_total, @incomes_reason_payment_status
                          FROM fin_reasons_relatives as frr
                          JOIN fin_incomes_reasons as fir
                            ON (frr.link_to=fir.id AND fir.type>100)
                          WHERE frr.parent_model_name='Finance_Incomes_Reason' AND frr.link_to_model_name='Finance_Incomes_Reason' AND frr.parent_id=@related_model_id;
                    END IF;
                ELSEIF (@current_model_type=1 OR @current_model_type=2) THEN
                    # Search for direct relation with user-defined incomes reason
                    SET @related_model_id = (SELECT fir.id
                      FROM fin_reasons_relatives as frr
                      INNER JOIN fin_incomes_reasons as fir
                        ON (frr.link_to=fir.id AND fir.type>100)
                      WHERE frr.parent_model_name='Finance_Incomes_Reason' AND frr.link_to_model_name='Finance_Incomes_Reason' AND frr.parent_id=changed_model_id
                        AND fir.annulled_by=0 AND fir.active=1 AND fir.status="finished");

                    # If reason parent is found, get its data
                    IF (@related_model_id) THEN
                        SELECT fir.id, fir.total_with_vat, fir.payment_status INTO @incomes_reason_calculate, @incomes_reason_total, @incomes_reason_payment_status
                          FROM fin_incomes_reasons AS fir
                          WHERE fir.id=@related_model_id;
                    END IF;
                END IF;
            END IF;

            # This part is dedicated to changing the status of a user-defined incomes reason (type>100)
            IF (@incomes_reason_calculate) THEN
                SET @new_payment_status = define_new_payment_status(changed_model_name, @incomes_reason_calculate, 'parent');

                # Update the status of the user defined reason
                UPDATE fin_incomes_reasons SET payment_status=@new_payment_status, payment_status_modified=(IF (@incomes_reason_payment_status COLLATE utf8mb4_unicode_ci = @new_payment_status COLLATE utf8mb4_unicode_ci, payment_status_modified, NOW())) WHERE id=@incomes_reason_calculate;
                CALL log_payment_status_audit(history_id, 'Finance_Incomes_Reason', @incomes_reason_calculate, @incomes_reason_payment_status, @new_payment_status);
            END IF;
        END IF;
    END;//
delimiter ;

# TRIGGER TO UPDATE PAYMENT STATUS AND INVOICE STATUS OF A REASON ON INSERT OF A ROW IN FIN_REASONS_RELATIVES TABLE
# ONLY IN CASE THE REASON IS CREDIT NOTICE, DEBIT NOTICE, A CORRECTING DOCUMENT OR AN ADVANCED INVOICE
DROP TRIGGER IF EXISTS insert_reasons_relatives_row;
delimiter $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER insert_reasons_relatives_row AFTER INSERT ON fin_reasons_relatives
FOR EACH ROW
BEGIN
    # parent_id
    SET @parent_id_reason_type = NULL;
    SET @parent_id_reason_type_advance = NULL;
    SET @link_to_reason_type = NULL;
    SET @reason_to_check_invoiced_status = NULL;
    SET @current_invoiced_status = NULL;
    SET @new_invoiced_status = NULL;

    # Check if this is a credit notice, debit notice or correcting document, related to other incomes reason
    SET @parent_id_reason_type = (SELECT `type` FROM fin_incomes_reasons WHERE `id`=NEW.parent_id AND `type` IN (4,5,3,1) AND `active`=1 AND `annulled_by`=0 AND `status`="finished");
    SET @link_to_reason_type = (SELECT `type` FROM fin_incomes_reasons WHERE `id`=NEW.link_to AND (`type`=1 OR `type`>100) AND `active`=1 AND `annulled_by`=0 AND `status`="finished");
    # Check if this is an advanced invoice related to other incomes reason
    SET @parent_id_reason_type_advance = (SELECT `type` FROM fin_incomes_reasons WHERE `id`=NEW.parent_id AND `type`=1 AND `advance`=@link_to_reason_type AND `active`=1 AND `annulled_by`=0 AND `status`="finished");

    IF (NEW.parent_model_name="Finance_Incomes_Reason" AND NEW.link_to_model_name="Finance_Incomes_Reason" AND (@parent_id_reason_type IN (3,4,5) OR @parent_id_reason_type_advance)) THEN
        # Get the data for the related incomes reason
        IF (@parent_id_reason_type IN (3,4,5)) THEN
            SELECT id, payment_status INTO @reason_id, @reason_old_payment_status FROM fin_incomes_reasons WHERE `id`=NEW.link_to AND (`type`=1 OR `type`>100) AND `active`=1 AND `annulled_by`=0 AND `status`="finished";
        ELSE
            SELECT id, payment_status INTO @reason_id, @reason_old_payment_status FROM fin_incomes_reasons WHERE `id`=NEW.link_to AND `type`>100 AND `active`=1 AND `annulled_by`=0 AND `status`="finished";
        END IF;

        IF (@reason_id) THEN
            # Find out the new payment status
            SET @reason_new_payment_status = define_new_payment_status(NEW.link_to_model_name, @reason_id, 'paid');
            IF (@reason_new_payment_status COLLATE utf8mb4_unicode_ci != @reason_old_payment_status COLLATE utf8mb4_unicode_ci) THEN
                # Update the payment status only if it is different than the previous payment status
                UPDATE fin_incomes_reasons SET payment_status=@reason_new_payment_status, payment_status_modified=NOW() WHERE id=@reason_id;

                # Writes history and audit
                SET @changed_data='';
                SET @changed_data=CONCAT(@changed_data, 'inserted_reason_id := ', NEW.parent_id, '\r\n');
                SET @history_id = log_payment_status_history('insert_fin_reasons_relatives_row', 0, @changed_data);
                CALL log_payment_status_audit(@history_id, "Finance_Incomes_Reason", @reason_id, @reason_old_payment_status, @reason_new_payment_status);
            END IF;
        END IF;
    END IF;

    IF (NEW.parent_model_name="Finance_Incomes_Reason" AND NEW.link_to_model_name="Finance_Incomes_Reason" AND @link_to_reason_type) THEN
        # the relation has to be checked if its invoiced_status has to be changed
        IF (@link_to_reason_type>100) THEN
            SET @reason_to_check_invoiced_status = NEW.link_to;
        ELSE
            SET @reason_to_check_invoiced_status = search_related_incomes_reason(NEW.link_to, @link_to_reason_type);
        END IF;
    END IF;

    IF (@reason_to_check_invoiced_status) THEN
        SELECT invoice_status INTO @current_invoiced_status FROM fin_incomes_reasons_invoice_status WHERE `parent_id`=@reason_to_check_invoiced_status;
        SET @new_invoiced_status = define_new_invoiced_status(@reason_to_check_invoiced_status);
        IF (@new_invoiced_status COLLATE utf8mb4_unicode_ci != @current_invoiced_status COLLATE utf8mb4_unicode_ci) THEN
            UPDATE fin_incomes_reasons_invoice_status SET `invoice_status`=@new_invoiced_status, `invoice_status_modified`=NOW() WHERE `parent_id`=@reason_to_check_invoiced_status;

            SET @changed_data='';
            SET @changed_data = CONCAT(@changed_data, 'parent_id := ', NEW.parent_id, '\r\n');
            SET @changed_data = CONCAT(@changed_data, 'parent_model_name := ', NEW.parent_model_name, '\r\n');
            SET @changed_data = CONCAT(@changed_data, 'link_to := ', NEW.link_to, '\r\n');
            SET @changed_data = CONCAT(@changed_data, 'link_to_model_name := ', NEW.link_to_model_name, '\r\n');

            CALL log_invoice_status_history_audit('insert_fin_reasons_relatives_row', @reason_to_check_invoiced_status, @changed_data, @current_invoiced_status, @new_invoiced_status);
        END IF;
    END IF;
END;$$$
delimiter ;


# TRIGGER TO UPDATE PAYMENT STATUS AND INVOICE STATUS OF A REASON ON DELETE OF A ROW IN FIN_REASONS_RELATIVES TABLE
# ONLY IN CASE THE REASON IS AN ADVANCED INVOICE
DROP TRIGGER IF EXISTS delete_reasons_relatives_row;
delimiter $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER delete_reasons_relatives_row AFTER DELETE ON fin_reasons_relatives
FOR EACH ROW
BEGIN
    # parent_id
    SET @parent_id_reason_type_advance = NULL;
    SET @link_to_reason_type = NULL;
    SET @reason_to_check_invoiced_status = NULL;
    SET @current_invoiced_status = NULL;
    SET @new_invoiced_status = NULL;

    SET @link_to_reason_type = (SELECT `type` FROM fin_incomes_reasons WHERE `id`=OLD.link_to AND `type`>100 AND `active`=1 AND `annulled_by`=0 AND `status`="finished");
    # Check if this is an advanced invoice related to other incomes reason
    SET @parent_id_reason_type_advance = (SELECT `type` FROM fin_incomes_reasons WHERE `id`=OLD.parent_id AND `type`=1 AND `advance`=@link_to_reason_type AND `active`=1 AND `annulled_by`=0 AND `status`="finished");

    IF (OLD.parent_model_name="Finance_Incomes_Reason" AND OLD.link_to_model_name="Finance_Incomes_Reason" AND @parent_id_reason_type_advance) THEN
        # Get the data for the related incomes reason
        SELECT id, payment_status INTO @reason_id, @reason_old_payment_status FROM fin_incomes_reasons WHERE `id`=OLD.link_to AND `type`>100 AND `active`=1 AND `annulled_by`=0 AND `status`="finished";
        IF (@reason_id) THEN
            # Find out the new payment status
            SET @reason_new_payment_status = define_new_payment_status(OLD.link_to_model_name, @reason_id, 'paid');
            IF (@reason_new_payment_status COLLATE utf8mb4_unicode_ci != @reason_old_payment_status COLLATE utf8mb4_unicode_ci) THEN
                # Update the payment status only if it is different from the previous payment status
                UPDATE fin_incomes_reasons SET payment_status=@reason_new_payment_status, payment_status_modified=NOW() WHERE id=@reason_id;

                # Write history and audit
                SET @changed_data='';
                SET @changed_data = CONCAT(@changed_data, 'deleted_reason_id := ', OLD.parent_id, '\r\n');
                SET @history_id = log_payment_status_history('delete_fin_reasons_relatives_row', 0, @changed_data);
                CALL log_payment_status_audit(@history_id, "Finance_Incomes_Reason", @reason_id, @reason_old_payment_status, @reason_new_payment_status);
            END IF;
        END IF;
    END IF;

    IF (OLD.parent_model_name="Finance_Incomes_Reason" AND OLD.link_to_model_name="Finance_Incomes_Reason" AND @link_to_reason_type) THEN
        # the relation has to be checked if its invoiced_status has to be changed
        SET @reason_to_check_invoiced_status = OLD.link_to;

        SELECT invoice_status INTO @current_invoiced_status FROM fin_incomes_reasons_invoice_status WHERE `parent_id`=@reason_to_check_invoiced_status;
        SET @new_invoiced_status = define_new_invoiced_status(@reason_to_check_invoiced_status);
        IF (@new_invoiced_status COLLATE utf8mb4_unicode_ci != @current_invoiced_status COLLATE utf8mb4_unicode_ci) THEN
            UPDATE fin_incomes_reasons_invoice_status SET `invoice_status`=@new_invoiced_status, `invoice_status_modified`=NOW() WHERE `parent_id`=@reason_to_check_invoiced_status;

            # Write history and audit
            SET @changed_data='';
            SET @changed_data = CONCAT(@changed_data, 'parent_id := ', OLD.parent_id, '\r\n');
            SET @changed_data = CONCAT(@changed_data, 'parent_model_name := ', OLD.parent_model_name, '\r\n');
            SET @changed_data = CONCAT(@changed_data, 'link_to := ', OLD.link_to, '\r\n');
            SET @changed_data = CONCAT(@changed_data, 'link_to_model_name := ', OLD.link_to_model_name, '\r\n');
            CALL log_invoice_status_history_audit('delete_fin_reasons_relatives_row', @reason_to_check_invoiced_status, @changed_data, @current_invoiced_status, @new_invoiced_status);
        END IF;
    END IF;
END;$$$
delimiter ;

# PROCEDURE TO WRITE HISTORY FOR CHANGES IN INVOICE STATUS OF THE INCOMES REASONS
DROP PROCEDURE IF EXISTS log_invoice_status_history_audit;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE log_invoice_status_history_audit(current_action varchar(255), model_id int(11), changed_data_vars text CHARACTER SET utf8mb4, old_invoice_status varchar(255), new_invoice_status varchar(255))
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        # write history
        INSERT INTO diag_invoice_status_reasons_history
        SET action=current_action,
            changed_row_id=model_id,
            changed_by=USER(),
            changed_data=changed_data_vars,
            previous_invoice_status=old_invoice_status,
            new_invoice_status=new_invoice_status,
            change_date=NOW();
    END;//
delimiter ;

# TRIGGER TO UPDATE INVOICE STATUS OF A REASON ON ROW UPDATE
DROP TRIGGER IF EXISTS update_fin_incomes_reason;
delimiter $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER update_fin_incomes_reason AFTER UPDATE ON fin_incomes_reasons
FOR EACH ROW
BEGIN
    SET @reason_to_check_invoiced_status = NULL;
    SET @current_invoiced_status = NULL;
    SET @new_invoiced_status = NULL;

    IF ((NEW.type>100 OR NEW.type IN (1,3,4)) AND ((NEW.status='finished' AND NEW.status!=OLD.status) OR (NEW.annulled_by!=OLD.annulled_by) OR (NEW.active!=OLD.active) OR (NEW.total_with_vat != OLD.total_with_vat))) THEN
        SET @reason_to_check_invoiced_status = search_related_incomes_reason(NEW.id, NEW.type);
        IF (@reason_to_check_invoiced_status) THEN
            SELECT invoice_status INTO @current_invoiced_status FROM `fin_incomes_reasons_invoice_status` WHERE `parent_id`=@reason_to_check_invoiced_status;
            SET @new_invoiced_status = define_new_invoiced_status(@reason_to_check_invoiced_status);
            IF (@new_invoiced_status COLLATE utf8mb4_unicode_ci != @current_invoiced_status COLLATE utf8mb4_unicode_ci) THEN
                UPDATE `fin_incomes_reasons_invoice_status` SET `invoice_status`=@new_invoiced_status, `invoice_status_modified`=NOW() WHERE `parent_id`=@reason_to_check_invoiced_status;

                SET @changed_data='';
                IF (OLD.status!=NEW.status) THEN
                    SET @changed_data = CONCAT(@changed_data, 'status := ', OLD.status, ' -> ', NEW.status, '\r\n');
                END IF;
                IF (OLD.annulled_by!=NEW.annulled_by) THEN
                    SET @changed_data = CONCAT(@changed_data, 'annulled_by := ', OLD.annulled_by, ' -> ', NEW.annulled_by, '\r\n');
                END IF;
                IF (OLD.active!=NEW.active) THEN
                    SET @changed_data = CONCAT(@changed_data, 'active := ', OLD.active, ' -> ', NEW.active, '\r\n');
                END IF;
                IF (OLD.total_with_vat!=NEW.total_with_vat) THEN
                    SET @changed_data = CONCAT(@changed_data, 'total_with_vat := ', OLD.total_with_vat, ' -> ', NEW.total_with_vat, '\r\n');
                END IF;

                CALL log_invoice_status_history_audit('update_fin_incomes_reason_row', NEW.id, @changed_data, @current_invoiced_status, @new_invoiced_status);
            END IF;
        END IF;
    END IF;
END;$$$
delimiter ;

# TRIGGER TO UPDATE INVOICE STATUS OF A REASON ON ROW DELETE
DROP TRIGGER IF EXISTS delete_fin_incomes_reason;
delimiter $$$
CREATE DEFINER = 'nzoomer'@'localhost' TRIGGER delete_fin_incomes_reason AFTER DELETE ON fin_incomes_reasons
FOR EACH ROW
BEGIN
    SET @reason_to_check_invoiced_status = NULL;
    SET @current_invoiced_status = NULL;
    SET @new_invoiced_status = NULL;

    IF (OLD.type IN (1,3,4) AND OLD.status='finished' AND OLD.active=1 AND OLD.annulled_by=0) THEN
        SET @reason_to_check_invoiced_status = search_related_incomes_reason(OLD.id, OLD.type);
        IF (@reason_to_check_invoiced_status) THEN
            SELECT invoice_status INTO @current_invoiced_status FROM `fin_incomes_reasons_invoice_status` WHERE `parent_id`=@reason_to_check_invoiced_status;
            SET @new_invoiced_status = define_new_invoiced_status(@reason_to_check_invoiced_status);
            IF (@new_invoiced_status COLLATE utf8mb4_unicode_ci != @current_invoiced_status COLLATE utf8mb4_unicode_ci) THEN
                UPDATE `fin_incomes_reasons_invoice_status` SET `invoice_status`=@new_invoiced_status, `invoice_status_modified`=NOW() WHERE `parent_id`=@reason_to_check_invoiced_status;

                SET @changed_data='';
                SET @changed_data = CONCAT(@changed_data, 'status := ', OLD.status, '\r\n');
                SET @changed_data = CONCAT(@changed_data, 'annulled_by := ', OLD.annulled_by, '\r\n');
                SET @changed_data = CONCAT(@changed_data, 'active := ', OLD.active, '\r\n');
                SET @changed_data = CONCAT(@changed_data, 'total_with_vat := ', OLD.total_with_vat, '\r\n');

                CALL log_invoice_status_history_audit('delete_fin_incomes_reason_row', OLD.id, @changed_data, @current_invoiced_status, @new_invoiced_status);
            END IF;
        END IF;
    END IF;

    DELETE FROM `fin_incomes_reasons_invoice_status` WHERE `parent_id`=OLD.id;
END;$$$
delimiter ;

DROP FUNCTION IF EXISTS strip_tags;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION strip_tags(x text CHARACTER SET utf8mb4) RETURNS text CHARACTER SET utf8mb4
  LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
  BEGIN
    DECLARE tag_start INT UNSIGNED;
    DECLARE tag_end INT UNSIGNED;
    DECLARE loop_num INT UNSIGNED;
    IF (x REGEXP '.*<[^ ].*>.*') THEN
      SET tag_start = LOCATE('<', x, 1);
      SET loop_num = 1;
      REPEAT
        IF (SUBSTRING(x, tag_start +1, 1) != ' ') THEN
          SET tag_end = LOCATE('>', x, tag_start);
          SET x = CONCAT(SUBSTRING(x, 1, tag_start -1), SUBSTRING(x, tag_end +1));
          SET tag_start = LOCATE('<', x, 1);
        ELSE
          SET tag_start = LOCATE('<', x, tag_start +1);
        END IF;
        SET loop_num = loop_num +1;
      UNTIL (x NOT REGEXP '.*<[^ ].*>.*' OR loop_num >= 1000) END REPEAT;
      return x;
    ELSE
      return x;
    END IF;
  END;//
delimiter ;

# FUNCTION TO DEFINE THE INVOICE STATUS OF AN INCOMES REASON
DROP FUNCTION IF EXISTS define_new_invoiced_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_invoiced_status(incomes_reason_id int(11)) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE reason_cursor_done INT DEFAULT FALSE;
        DECLARE new_invoiced_status VARCHAR(255);
        DECLARE incomes_reason_total DOUBLE(15,6);
        DECLARE calculated_invoiced_amount DOUBLE(15,6);
        DECLARE difference_invoiced_amount DOUBLE(15,6);
        DECLARE related_reason_id INT(11);
        DECLARE related_reason_total DOUBLE(15,6);
        DECLARE related_debit_credit_sum DOUBLE(15,6);
        DECLARE num_invoices INT DEFAULT 0;

        DECLARE reason_cursor CURSOR FOR
            SELECT fir.id, SUM(fir.total_with_vat)
            FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
            WHERE frr.link_to = incomes_reason_id
              AND fir.id = frr.parent_id
              AND fir.status = "finished"
              AND frr.parent_model_name = "Finance_Incomes_Reason"
              AND frr.link_to_model_name = "Finance_Incomes_Reason"
              AND fir.annulled_by=0 AND fir.type=1
              GROUP BY fir.id;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET reason_cursor_done = TRUE;

        IF (SELECT `id` FROM fin_incomes_reasons WHERE `id`=incomes_reason_id AND `type`>100 AND `active`=1 AND `status`='finished' AND `annulled_by`=0) THEN
            SELECT ROUND(total_with_vat, 6) INTO incomes_reason_total FROM fin_incomes_reasons WHERE `id`=incomes_reason_id;
            SET calculated_invoiced_amount=0;

            OPEN reason_cursor;
            reason_loop: LOOP
                FETCH reason_cursor INTO related_reason_id, related_reason_total;

                IF reason_cursor_done THEN
                    LEAVE reason_loop;
                END IF;

                SELECT SUM(total_with_vat) INTO related_debit_credit_sum
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
                WHERE frr.link_to = related_reason_id
                  AND fir.status = "finished"
                  AND fir.id = frr.parent_id
                  AND frr.parent_model_name = "Finance_Incomes_Reason"
                  AND frr.link_to_model_name = "Finance_Incomes_Reason"
                  AND fir.annulled_by=0 AND fir.type IN (3,4);

                SET calculated_invoiced_amount=ROUND((calculated_invoiced_amount+((IF (related_reason_total IS NULL, 0, related_reason_total)) + (IF (related_debit_credit_sum IS NULL, 0, related_debit_credit_sum)))), 6);

                SET num_invoices = num_invoices + 1;
            END LOOP;

            SET difference_invoiced_amount = incomes_reason_total - calculated_invoiced_amount;

            IF (difference_invoiced_amount<=0 AND num_invoices>0) THEN
                SET new_invoiced_status = 'invoiced';
            ELSEIF (difference_invoiced_amount<incomes_reason_total AND difference_invoiced_amount>0) THEN
                SET new_invoiced_status = 'partial';
            ELSEIF (difference_invoiced_amount>=incomes_reason_total) THEN
                SET new_invoiced_status = 'not_invoiced';
            END IF;
        ELSE
            SET new_invoiced_status = 'not_invoicable';
        END IF;

        return new_invoiced_status;
    END;//
delimiter ;

#########################################################################################
# 2023-03-10 - Added new setting for setting display_customer_type in Sales report

# Added new setting for setting display_customer_type in Sales report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ndisplay_customer_type := \r\n')
WHERE `type` = 'sales' AND settings NOT LIKE '%display_customer_type%';

#########################################################################################
# 2023-03-16 - Added new settings for support of oauth2 authentication for bouncedEmails
#            - Added new setting for setting display_customer_type in Sales report
#            - Removed service functions/procedure used only upon installation

# Added new settings for support of oauth2 authentication for bouncedEmails
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
  (NULL, 'emails', 'replyto_imap_authentication_method', 'IMAP');

INSERT IGNORE INTO `settings_i18n` VALUES
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_authentication_method'),   'IMAP метод на aутентикация (IMAP или oauth2)',  'IMAP - Традиционна аутентикация с парола<br/>oauth2 - Аутеентикация с oauth2 доставчик (Azure, Google)',  'bg', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'emails'       AND `name` = 'replyto_imap_authentication_method'),   'IMAP authentication method (IMAP or oauth2)',   'IMAP - traditional authentication with password<br/>oauth2 - authenticate with oauth2 provider',          'en', now());

# Added new setting for setting display_customer_type in Sales report
UPDATE `automations` SET `settings` = TRIM(CONCAT(`settings`, '\r\nget_company_data_by := user\r\n'))
WHERE `method` LIKE '%createSalesForOrders%' AND settings NOT LIKE '%get_company_data_by%';
UPDATE `automations` SET `settings` = TRIM(CONCAT(`settings`, 'default_cashbox := \r\n'))
WHERE `method` LIKE '%createSalesForOrders%' AND settings NOT LIKE '%default_cashbox%';
UPDATE `automations` SET `settings` = TRIM(CONCAT(`settings`, 'default_bank_account := \r\n'))
WHERE `method` LIKE '%createSalesForOrders%' AND settings NOT LIKE '%default_bank_account%';

# Removed service functions/procedure used only upon installation
DROP FUNCTION IF EXISTS `easter`;
DROP FUNCTION IF EXISTS `addHoliday`;
DROP PROCEDURE IF EXISTS `addAllHolidays`;



######################################################################################
# 2023-03-17 - Added field_name column to _search_defs

# Added field_name column to _search_defs
ALTER TABLE _search_defs
    add field_name varchar(255) null after controller;
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'eti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'ei18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'ei18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'location' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'ei18n.location' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'event_start' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'DATE_FORMAT(e.event_start, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'event_start' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.event_start' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'event_start' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'DATE_FORMAT(e.event_start + INTERVAL e.duration MINUTE, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'priority' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.priority' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'DATE_FORMAT(e.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'DATE_FORMAT(e.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'allday_events' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.allday_event' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'user_observer_title' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'ea.participant_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'user_participant_title' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'ea1.participant_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'customer_participant_title' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'ea2.participant_id' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'event_start' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'DATE_ADD(e.event_start, INTERVAL e.duration MINUTE)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'e.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'type_section' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'et.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'document_referer' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'erl1.link_to' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project_referer' WHERE `module` = 'events' AND `controller` = 'events' AND `var_name` = 'erl2.link_to' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'types_name' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'eti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_name_plural' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'eti18n.name_plural' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_description' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'eti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_count_events' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'count_events' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(et.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'et.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(et.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'et.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'et.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'et.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'et.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'et.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'et.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_section' WHERE `module` = 'events' AND `controller` = 'types' AND `var_name` = 'es.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_name' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'esi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'sections_description' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'esi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(es.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(es.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_position' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'events' AND `controller` = 'sections' AND `var_name` = 'es.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'module' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'CONCAT(f.module, \'_\', f.controller)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'module_from' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'CONCAT(f.module_from, \'_\', f.controller_from)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'DATE_FORMAT(f.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'f.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'DATE_FORMAT(f.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'f.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'f.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'f.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'filters' AND `controller` = 'filters' AND `var_name` = 'f.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'for' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'CONCAT(m.model, \'_\', IFNULL(t.type, 0))' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'deadline' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'DATE_FORMAT(m.deadline, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'deadline' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.deadline' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'assigned_to' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.assigned_to' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'assigned_to' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'CONCAT(ui18n0.firstname, \' \', ui18n0.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'DATE_FORMAT(m.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'DATE_FORMAT(m.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'severity' WHERE `module` = 'minitasks' AND `controller` = 'minitasks' AND `var_name` = 'm.severity' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'ni18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name_code' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'CONCAT(ni18n.name, \'\')' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sell_price' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.sell_price' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'sell_price' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.sell_price_currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'last_delivery_price' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.last_delivery_price' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'last_delivery_price' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.last_delivery_price_currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'average_weighted_delivery_price' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.average_weighted_delivery_price' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'average_weighted_delivery_price' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.average_weighted_delivery_price_currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'categories' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'nc.cat_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type_section' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'nt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'DATE_FORMAT(n.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'DATE_FORMAT(n.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'subtype' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.subtype' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'batch_options' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'n.has_batch' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouse' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'nq.warehouse_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'available' WHERE `module` = 'nomenclatures' AND `controller` = 'nomenclatures' AND `var_name` = 'nq.quantity' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'nomenclatures' AND `controller` = 'trademarks' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_name' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_description' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_types_used' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nt.id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(nc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(nc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'counters_count_nomenclatures' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'count_nomenclatures' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nc.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'nomenclatures' AND `controller` = 'counters' AND `var_name` = 'nc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_name' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_name_plural' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nti18n.name_plural' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_code' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.auto_code_suffix' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_description' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_count_nomenclatures' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'count_nomenclatures' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(nt.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(nt.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_section' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'ns.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_counter' WHERE `module` = 'nomenclatures' AND `controller` = 'types' AND `var_name` = 'nt.counter' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_name' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'nsi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'sections_description' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'nsi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ns.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ns.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_position' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'nomenclatures' AND `controller` = 'sections' AND `var_name` = 'ns.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fai18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fai18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'gt2_total' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat_rate' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_vat_rate' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_no_vat_reason_text' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fai18n.total_no_vat_reason_text' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_with_vat' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_with_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_value' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_discount_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_percentage' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_discount_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_value' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_surplus_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_percentage' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_surplus_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_without_discount' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.total_without_discount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'annulments_currency' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'annulments_fiscal_total_vat' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.fiscal_total_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'annulments_fiscal_total' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.fiscal_total' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'DATE_FORMAT(fa.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'annulments_modified' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'DATE_FORMAT(fa.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'annulments_modified' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'documents_type_section' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fdt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fdti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'issue_date' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.issue_date' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'annulments_invoice_code' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.invoice_code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'payment_status' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.payment_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'annulments_date_of_payment' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'DATE_FORMAT(fa.date_of_payment, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'annulments_date_of_payment' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.date_of_payment' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'annulments_payment_type' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.payment_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'fa.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'container_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'CONCAT(fa.payment_type, \'_\', fa.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'annulments' AND `var_name` = 'CONCAT(fa.payment_type, \'__\', fa.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'currencies_name' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.code' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'currencies_bank' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.bank' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'currencies_buys_bank' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.buys_bank' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'currencies_sells_bank' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.sells_bank' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'currencies_buys_cash' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.buys_cash' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'currencies_sells_cash' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.sells_cash' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'date' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'DATE_FORMAT(fcur.date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'DATE_FORMAT(fcur.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'DATE_FORMAT(fcur.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'currencies_fixing' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.fixing' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'currencies_code' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcur.code' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'currencies_name' WHERE `module` = 'finance' AND `controller` = 'currencies' AND `var_name` = 'fcurai18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'firi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'firi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fdti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'gt2_total' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat_rate' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_vat_rate' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_no_vat_reason_text' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'firi18n.total_no_vat_reason_text' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_with_vat' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_with_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_value' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_discount_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_percentage' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_discount_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_value' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_surplus_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_percentage' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_surplus_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_without_discount' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.total_without_discount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_currency' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_fiscal_total_vat' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.fiscal_total_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_fiscal_total' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.fiscal_total' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_status' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'DATE_FORMAT(fir.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_modified' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'DATE_FORMAT(fir.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_modified' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'payment_status' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.payment_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'issue_date' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.issue_date' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_distributed' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.distributed' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deactivated' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_date' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'DATE_FORMAT(fir.anNULLed, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_date' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.anNULLed' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_by' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.anNULLed_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_by' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'CONCAT(ui18n3.firstname, \' \', ui18n3.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_type_section' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fdt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_invoice_code' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.invoice_code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_date_of_payment' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'DATE_FORMAT(fir.date_of_payment, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_of_payment' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.date_of_payment' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.payment_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'assign_title' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fda.assigned_to' AND `field_type` = 'assignment';
UPDATE `_search_defs` SET `field_name` = 'invoice_status' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'firis.invoice_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'handovered_status' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.handovered_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_eik' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.eik' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_vat_num' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'fir.vat_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_customer_address' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'firi18n.customer_address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'incomes_reasons_received_by' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'firi18n.received_by' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'container_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'CONCAT(fir.payment_type, \'_\', fir.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `var_name` = 'CONCAT(fir.payment_type, \'__\', fir.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'feri18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'feri18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fdti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'gt2_total' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat_rate' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_vat_rate' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_no_vat_reason_text' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'feri18n.total_no_vat_reason_text' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_with_vat' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_with_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_value' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_discount_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_percentage' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_discount_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_value' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_surplus_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_percentage' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_surplus_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_without_discount' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.total_without_discount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'expenses_reasons_currency' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'expenses_reasons_status' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'DATE_FORMAT(fer.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'expenses_reasons_modified' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'DATE_FORMAT(fer.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'expenses_reasons_modified' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'issue_date' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.issue_date' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'expenses_reasons_distributed' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.distributed' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deactivated' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_date' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'DATE_FORMAT(fer.anNULLed, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_date' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.anNULLed' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_by' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.anNULLed_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_by' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'CONCAT(ui18n3.firstname, \' \', ui18n3.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_type_section' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fdt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'payment_status' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.payment_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'expenses_reasons_date_of_payment' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'DATE_FORMAT(fer.date_of_payment, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_of_payment' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.date_of_payment' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'invoice_num' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.invoice_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.payment_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'assign_title' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fda.assigned_to' AND `field_type` = 'assignment';
UPDATE `_search_defs` SET `field_name` = 'admit_VAT_credit' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.admit_VAT_credit' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'accounting_period' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.accounting_period' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'handovered_status' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.handovered_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'container_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'CONCAT(fer.payment_type, \'_\', fer.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'CONCAT(fer.payment_type, \'__\', fer.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'allocated_status' WHERE `module` = 'finance' AND `controller` = 'expenses_reasons' AND `var_name` = 'fer.allocated_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fdti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'DATE_FORMAT(fwd.date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.warehouse' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'gt2_total' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat_rate' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_vat_rate' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_vat' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_no_vat_reason_text' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.total_no_vat_reason_text' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_with_vat' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_with_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_value' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_discount_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_discount_percentage' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_discount_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_value' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_surplus_value' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_surplus_percentage' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_surplus_percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'gt2_total_without_discount' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.total_without_discount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'warehouses_documents_currency' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouses_documents_status' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'DATE_FORMAT(fwd.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'DATE_FORMAT(fwd.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'DATE_FORMAT(fwd.anNULLed, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.anNULLed' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_by' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.anNULLed_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_type_section' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fdt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwh.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'assign_title' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fda.assigned_to' AND `field_type` = 'assignment';
UPDATE `_search_defs` SET `field_name` = 'to_warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.to_company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'to_warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fci18n_to.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'to_description' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.to_description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'to_warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.to_office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'to_warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'oi18n_to.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'to_date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.to_date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'to_date' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'DATE_FORMAT(fwd.to_date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'to_warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.to_warehouse' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'to_warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwi18n_to.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'to_warehouse_data' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwh_to.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.department' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouses_documents_batch' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fb.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'warehouses_documents_serial' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'gbd.serial_number' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'warehouses_documents_custom' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'gbd.cstm_number' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'from' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.from' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'to' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.to' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'to_from' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.to_from' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'to_to' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.to_to' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'location' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwdi18n.location' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'fwd.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'warehouses_documents' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'note' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fpi18n.note' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'customer_num' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.customer_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'CONCAT(c1i18n.name, \' \', c1i18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'employee' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.employee' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'employee' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.cashbox' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fcbi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fcb.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.bank_account' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fbai18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'company_data' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fba.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'currency' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'amount' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'payments_status' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'DATE_FORMAT(fp.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'DATE_FORMAT(fp.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'anNULLed_state' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.anNULLed' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'issue_date' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.issue_date' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'reason' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fpi18n.reason' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.receive_flag' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'payments_distributed_amount' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.distributed_amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'payments_not_distributed_amount' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.not_distributed_amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'payments_distribution_status' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.distribution_status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'payments_invoices_only' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.invoices_only' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'fp.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'finance' AND `controller` = 'payments' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'payments_types_name' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'fpti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'payments_types_description' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'fpti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'DATE_FORMAT(fpt.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'fpt.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'DATE_FORMAT(fpt.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'fpt.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'fpt.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'fpt.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'payments_types' AND `var_name` = 'fpt.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'fti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'note' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'fti18n.note' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'transfer_date' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'DATE_FORMAT(ft.transfer_date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'transfer_date' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.transfer_date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'from_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.from_company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'from_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'from_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.from_office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'from_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'to_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.to_company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'to_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'fci18n1.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'to_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.to_office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'to_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'oi18n1.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'from_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.from_cashbox' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'from_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.from_bank_account' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'to_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.to_cashbox' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'to_company_data' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.to_bank_account' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'employee' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'employee' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.employee' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'currency' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'amount' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'transfers_status' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'DATE_FORMAT(ft.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'DATE_FORMAT(ft.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'ft.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'reason' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'fti18n.reason' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'has_files' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'file' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'finance' AND `controller` = 'transfers' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_name' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'ftei18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_description' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'ftei18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_operation' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.operation' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_type' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_from_amount' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.from_amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_to_amount' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.to_amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_fixed_transaction' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.fixed_transaction' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_free_amount' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.free_amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'transaction_expenses_percentage' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.percentage' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'DATE_FORMAT(fte.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'DATE_FORMAT(fte.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'transaction_expenses' AND `var_name` = 'fte.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'analysis_types_name' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fati18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'analysis_types_description' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fati18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'analysis_types_type' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.kind' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'DATE_FORMAT(fat.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'DATE_FORMAT(fat.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'analysis_types' AND `var_name` = 'fat.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_name' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fbai18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_code' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_description' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fbai18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_bank' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fbai18n.bank' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_type' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_currency' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.currency' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_start_amount' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_current_amount' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.current_amount' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_credit_limit' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.credit_limit' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_company' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_company' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_office' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_office' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_iban' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.iban' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_bic' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.bic' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'DATE_FORMAT(fba.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'DATE_FORMAT(fba.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'bank_accounts_cheque' WHERE `module` = 'finance' AND `controller` = 'bank_accounts' AND `var_name` = 'fba.cheque' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'budgets_method' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.method' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'budgets_company' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'budgets_company' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'budgets_year' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.year' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'budgets_status' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'DATE_FORMAT(fb.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'DATE_FORMAT(fb.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'budgets' AND `var_name` = 'fb.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_name' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcbi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_code' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_description' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcbi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_location' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcbi18n.location' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_company' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_company' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_office' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'cashboxes_office' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'DATE_FORMAT(fcb.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'DATE_FORMAT(fcb.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'cashboxes' AND `var_name` = 'fcb.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'companies_name' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_registration_address' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fci18n.registration_address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_mol' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fci18n.mol' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_country' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.country' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'cl.country_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'companies_city' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fci18n.city' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_correspondation_address' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fci18n.correspondation_address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_code' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_VAT_number' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.VAT_number' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_eik' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.eik' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_registration_file' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.registration_file' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_registration_number' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.registration_number' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_registration_volume' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.registration_volume' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'companies_post_code' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.post_code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'DATE_FORMAT(fc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'DATE_FORMAT(fc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'position' WHERE `module` = 'finance' AND `controller` = 'companies' AND `var_name` = 'fc.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'counters_name' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_description' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_company' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_company' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc1i18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'counters_office' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fco.office_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_office' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(fc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(fc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_model' WHERE `module` = 'finance' AND `controller` = 'counters' AND `var_name` = 'fc.model' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_sections_name' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fdsi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'documents_sections_description' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fdsi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'DATE_FORMAT(fds.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'DATE_FORMAT(fds.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_sections_position' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'documents_sections' AND `var_name` = 'fds.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_statuses_name' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'documents_statuses_description' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'documents_statuses_status' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_statuses_doc_type' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.doc_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_statuses_doc_type' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fdti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'documents_statuses_sequence' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.sequence' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'DATE_FORMAT(fds.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'DATE_FORMAT(fds.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'documents_statuses' AND `var_name` = 'fds.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_types_name' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'documents_types_description' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'documents_types_code' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'documents_types_add_invoice' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.add_invoice' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_types_credit' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.credit' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_types_payments' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.payments' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'DATE_FORMAT(fdt.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'DATE_FORMAT(fdt.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'documents_types_add_proforma' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.add_proforma' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_type_section' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fds.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'documents_types_for_model' WHERE `module` = 'finance' AND `controller` = 'documents_types' AND `var_name` = 'fdt.model' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'repayment_plans_company' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'repayment_plans_customer' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'repayment_plans_customer' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'repayment_plans_status' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'DATE_FORMAT(frp.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'DATE_FORMAT(frp.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'repayment_plans' AND `var_name` = 'frp.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouses_name' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwhi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'warehouses_description' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwhi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'warehouses_location' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwhi18n.location' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'warehouses_company' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouses_company' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'warehouses_office' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'warehouses_office' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'DATE_FORMAT(fwh.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'DATE_FORMAT(fwh.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'warehouses_code' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'responsible' WHERE `module` = 'finance' AND `controller` = 'warehouses' AND `var_name` = 'fwh.employees' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fiti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fdti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fit.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fit.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'container_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'CONCAT(fit.payment_type, \'_\', fit.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'CONCAT(fit.payment_type, \'__\', fit.container_id)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fiti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fit.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fit.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fit.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fit.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'finance' AND `controller` = 'invoices_templates' AND `var_name` = 'fit.observer' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 'DATE_FORMAT(t.day, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 't.day' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 't.total' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 't.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 't.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 't.customer' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'turnovers' AND `controller` = 'turnovers' AND `var_name` = 'total_with_vat' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.log' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.success' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'DATE(il.added)' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'i.id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.added_by' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.log' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.success' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'i.id' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.file_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'il.file_name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'imports' AND `controller` = 'imports' AND `var_name` = 'CONCAT(ui.firstname, \' \', ui.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'comments' AND `controller` = 'comments' AND `var_name` = 'c.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'comments' AND `controller` = 'comments' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'subject' WHERE `module` = 'comments' AND `controller` = 'comments' AND `var_name` = 'c.subject' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'comments' AND `controller` = 'comments' AND `var_name` = 'c.content' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'coti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'DATE_FORMAT(co.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'DATE_FORMAT(co.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date_sign' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'DATE_FORMAT(co.date_sign, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_sign' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.date_sign' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date_start' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'DATE_FORMAT(co.date_start, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_start' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.date_start' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date_validity' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'DATE_FORMAT(co.date_validity, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_validity' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.date_validity' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date_end' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'DATE_FORMAT(co.date_end, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_end' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.date_end' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'subtype' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.subtype' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'coi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'assign_title' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'coa.assigned_to' AND `field_type` = 'assignment';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'date_start_subtype' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.date_start_subtype' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_end_subtype' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.date_end_subtype' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'company' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'office' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'office' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'custom_num' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.custom_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'self_financial' WHERE `module` = 'contracts' AND `controller` = 'contracts' AND `var_name` = 'co.self_financial' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_name' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_description' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(coc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(coc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_company' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_company' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'fci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'counters_office' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_office' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'counters_model_type' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coc.model_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_model_type' WHERE `module` = 'contracts' AND `controller` = 'counters' AND `var_name` = 'coti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'statuses_name' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cosi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'statuses_description' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cosi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'statuses_status' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_contract_type' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.contract_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_contract_type' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'coti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'DATE_FORMAT(cos.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'DATE_FORMAT(cos.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_sequence' WHERE `module` = 'contracts' AND `controller` = 'statuses' AND `var_name` = 'cos.sequence' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'coti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'coti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(cot.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(cot.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'contracts' AND `controller` = 'types' AND `var_name` = 'cot.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'cti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'types_section' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ct.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name_code' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname, \'\')' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'is_company' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.is_company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.department' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'depi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'assigned' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.assigned' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'assigned' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'CONCAT(ui18n4.firstname, \' \', ui18n4.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'DATE_FORMAT(c.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'DATE_FORMAT(c.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'country' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.country' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'cl.country_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'postal_code' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.postal_code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'city' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.city' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'address' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'address_by_personal_id' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.address_by_personal_id' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'registration_address' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.registration_address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'mol' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.mol' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'email' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.email' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'phone' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.phone' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'fax' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.fax' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gsm' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.gsm' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'in_dds' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.in_dds' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'eik' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.eik' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'ucn' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.ucn' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'identity_num' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.identity_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'skype' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.skype' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'othercontact' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.othercontact' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'web' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.web' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'identity_date' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.identity_date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'identity_valid' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.identity_valid' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'identity_by' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.identity_by' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'registration_file' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.registration_file' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'registration_volume' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.registration_volume' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'registration_number' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.registration_number' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_department' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.company_department' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'position' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.position' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_name' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.company_name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'notes' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ci18n.notes' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'iban' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.iban' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'salutation' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.salutation' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ctm.trademark_id' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'main_trademark' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'admit_VAT_credit' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'c.admit_VAT_credit' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'customers' AND `controller` = 'customers' AND `var_name` = 'file' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'company_person' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.is_company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.department' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'assigned' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'CONCAT(ui18n4.firstname, \' \', ui18n4.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'country' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.country' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'country_name' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'cl.country_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'postal_code' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.postal_code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'city' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'ci18n.city' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'address' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'ci18n.address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'address_by_personal_id' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'ci18n.address_by_personal_id' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'registration_address' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'ci18n.registration_address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'mol' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'ci18n.mol' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'email' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.email' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'phone' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.phone' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'fax' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.fax' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gsm' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.gsm' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'in_dds' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.in_dds' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'eik' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.eik' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'ucn' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.ucn' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'identity_num' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.identity_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'contacts_count' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'contacts_count' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'is_main' WHERE `module` = 'customers' AND `controller` = 'branches' AND `var_name` = 'c.is_main' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'pc.id' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'admit_VAT_credit' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.admit_VAT_credit' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.department' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'assigned' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'CONCAT(ui18n4.firstname, \' \', ui18n4.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'country' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.country' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'postal_code' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.postal_code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'city' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'ci18n.city' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'address' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'ci18n.address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'address_by_personal_id' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'ci18n.address_by_personal_id' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'registration_address' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'ci18n.registration_address' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'mol' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'ci18n.mol' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'email' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.email' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'phone' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.phone' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'fax' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.fax' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'gsm' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.gsm' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'in_dds' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.in_dds' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'eik' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.eik' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'ucn' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.ucn' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'identity_num' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.identity_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'deleted' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'bni18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'is_main' WHERE `module` = 'customers' AND `controller` = 'contactpersons' AND `var_name` = 'c.is_main' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'ct.id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(cc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(cc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'firstname' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'firstname' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'count_customers' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'count_customers' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cc.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'deleted' WHERE `module` = 'customers' AND `controller` = 'counters' AND `var_name` = 'cc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_name' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'csi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'sections_description' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'csi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(cs.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(cs.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_position' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'customers' AND `controller` = 'sections' AND `var_name` = 'cs.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'cti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_section' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'cs.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_kind' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.kind' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(ct.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(ct.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'position' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'counter' WHERE `module` = 'customers' AND `controller` = 'types' AND `var_name` = 'ct.counter' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'di18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'di18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'for_module_report' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'CONCAT(d.module, \'_\', d.controller)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'DATE_FORMAT(d.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'DATE_FORMAT(d.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'di18n.description' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'full_width' WHERE `module` = 'dashlets' AND `controller` = 'dashlets' AND `var_name` = 'd.full_width' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'di18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'di18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'full_num' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.full_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'custom_num' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.custom_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'dti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'type_section' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'dt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'deadline' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'DATE_FORMAT(d.deadline, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'deadline' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.deadline' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'validity_term' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'DATE_FORMAT(d.validity_term, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'validity_term' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.validity_term' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'ownership' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.ownership' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'assign_title' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'da.assigned_to' AND `field_type` = 'assignment';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'employee' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.employee' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'employee' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'CONCAT(ei18n.name, \' \', ei18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.department' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'depi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'DATE_FORMAT(d.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'DATE_FORMAT(d.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'media' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.media' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'media' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'dmi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'office' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'office' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'notes' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'di18n.notes' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'gi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'di18n.description' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'notes' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'di18n.notes' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'archive_state' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.archive' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'archived' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'DATE_FORMAT(d.archived, \"%Y-%m-%d\")' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'archived' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.archived' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.date' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'contract' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'd.contract' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'documents' AND `controller` = 'documents' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_name' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_description' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_types_used' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dt.id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(dc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(dc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'counters_count_documents' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'count_documents' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dc.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'documents' AND `controller` = 'counters' AND `var_name` = 'dc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'medias_name' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dmi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'medias_description' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dmi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'DATE_FORMAT(dm.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'DATE_FORMAT(dm.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'medias_position' WHERE `module` = 'documents' AND `controller` = 'medias' AND `var_name` = 'dm.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'sections_name' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'dsi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'sections_description' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'dsi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ds.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ds.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_position' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'documents' AND `controller` = 'sections' AND `var_name` = 'ds.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_name' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'dsi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'statuses_description' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'dsi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'statuses_status' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_doc_type' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.doc_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_doc_type' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'dti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'DATE_FORMAT(ds.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'DATE_FORMAT(ds.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_sequence' WHERE `module` = 'documents' AND `controller` = 'statuses' AND `var_name` = 'ds.sequence' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'types_name' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_description' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_code' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type_section' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'ds.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_direction' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.direction' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_count_documents' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'count_documents' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'types_counter' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.counter' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(dt.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(dt.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'documents' AND `controller` = 'types' AND `var_name` = 'dt.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'subject' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'ei18n.subject' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'ei18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'body' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'ei18n.body' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'DATE_FORMAT(e.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'DATE_FORMAT(e.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'e.model' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model_type' WHERE `module` = 'emails' AND `controller` = 'emails' AND `var_name` = 'CONCAT(e.model, e.model_type)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'campaigns_name' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'eci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'campaigns_description' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'eci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'campaigns_sender_name' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'eci18n.sender_name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'campaigns_sender' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.sender' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'campaigns_status' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'DATE_FORMAT(ec.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'DATE_FORMAT(ec.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'ec.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'emails' AND `controller` = 'campaigns' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'targetlists_name' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'eti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'targetlists_description' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'eti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'et.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'et.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'et.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'DATE_FORMAT(et.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'et.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'DATE_FORMAT(et.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'et.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'et.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'et.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'emails' AND `controller` = 'targetlists' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'fi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'model_id' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.model_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'origin' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.origin' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'permissions' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.permission' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'DATE_FORMAT(f.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'DATE_FORMAT(f.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'files' AND `controller` = 'files' AND `var_name` = 'f.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'hi18n.content' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'module_name' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.module_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'action_name' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.action_name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'action_name' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'CONCAT(h.module_name, \'_\', h.action_name)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'DATE_FORMAT(h.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'DATE_FORMAT(h.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'helps' AND `controller` = 'helps' AND `var_name` = 'h.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'indexes' AND `controller` = 'indexes' AND `var_name` = 'ii18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'model_type' WHERE `module` = 'indexes' AND `controller` = 'indexes' AND `var_name` = 'cti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'indexes' AND `controller` = 'indexes' AND `var_name` = 'CONCAT( ui18n1.firstname, \" \", ui18n1.lastname )' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'li18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'li18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.system' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'position' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.place' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'document_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model1' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'customer_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model2' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'project_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model3' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'nomenclature_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model4' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'contract_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model5' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'task_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model6' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'event_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model7' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_incomes_reason_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model8' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_expenses_reason_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model9' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_annulment_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model10' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_payment_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model11' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_transfer_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model12' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model_type' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model_type' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'DATE_FORMAT(l.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'DATE_FORMAT(l.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'visible' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.visible' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'info_header_visibility' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.info_header_visibility' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model13' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model13' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'keyname' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.keyname' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'permissions_view' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'permissions_view' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'permissions_edit' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'permissions_edit' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'model14' WHERE `module` = 'layouts' AND `controller` = 'layouts' AND `var_name` = 'l.model14' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'subject' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'ni18n.subject' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'ni18n.content' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'origin' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.origin' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'DATE_FORMAT(n.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'DATE_FORMAT(n.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'n.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'notes' AND `controller` = 'notes' AND `var_name` = 'ni18n.content' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'oi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'oi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'DATE_FORMAT(o.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'DATE_FORMAT(o.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'offices' AND `controller` = 'offices' AND `var_name` = 'o.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'pi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'pi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'pi18n.content' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'model_type' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model_type' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'position' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'header' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'phi18n.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'footer' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'pfi18n.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'prefix' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.prefix' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'DATE_FORMAT(p.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'DATE_FORMAT(p.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'list' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.list' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'section_type' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'document_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model1' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'customer_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model2' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'event_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model3' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_incomes_reason_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model4' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_expenses_reason_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model5' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_warehouses_document_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model6' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'contract_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model7' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'report_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model8' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_annulment_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model9' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'nomenclature_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model10' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'project_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model11' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'task_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model12' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_payment_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model13' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finance_invoices_template_type_search' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.model14' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'company' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'p.company' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'patterns' AND `controller` = 'patterns' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'ppi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'ppi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'ppi18n.content' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'parts_type' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'DATE_FORMAT(pp.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'DATE_FORMAT(pp.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'patterns' AND `controller` = 'parts' AND `var_name` = 'pp.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'pi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'name_code' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'CONCAT(pi18n.name, \'\')' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'pi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'num' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'typee' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'pti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'type_section' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'pt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finished_project' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.finished' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'priority' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.priority' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'manager' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.manager' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'manager' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'CONCAT(ui18n0.firstname, \' \', ui18n0.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'notes' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'pi18n.notes' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'assign_title' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'pa.assigned_to' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'parent_project' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.parent_project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'date_start' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.date_start' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'date_end' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.date_end' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'work_period' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.work_period' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'budget' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.budget' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'count_documents' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'count_documents' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'count_tasks' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'count_tasks' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'DATE_FORMAT(p.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'DATE_FORMAT(p.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'p.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'tag_id' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'projects' AND `controller` = 'projects' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'sections_name' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'psi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'sections_description' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'psi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ps.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ps.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_position' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'projects' AND `controller` = 'sections' AND `var_name` = 'ps.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_name' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_description' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_section' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'ps.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(pt.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pt.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(pt.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pt.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pt.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pt.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pt.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pt.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'projects' AND `controller` = 'types' AND `var_name` = 'pt.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pt.id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(pc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(pc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'count_projects' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'count_projects' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pc.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'deleted' WHERE `module` = 'projects' AND `controller` = 'counters' AND `var_name` = 'pc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'ri18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'ri18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'DATE_FORMAT(r.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'r.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'DATE_FORMAT(r.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'r.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'r.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'r.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'r.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'r.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'roles' AND `controller` = 'roles' AND `var_name` = 'r.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 'si18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 'si18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model_type' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 'dti18n.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'charge_person' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.charge_person' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 'DATE_FORMAT(s.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 'DATE_FORMAT(s.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'stages' AND `controller` = 'stages' AND `var_name` = 's.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'spi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'spi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model_type' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'pti18n.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'charge_person' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.charge_person' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'DATE_FORMAT(sp.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'DATE_FORMAT(sp.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'stages' AND `controller` = 'phases' AND `var_name` = 'sp.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 'ti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 'ti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'color' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.color' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 'DATE_FORMAT(t.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 'DATE_FORMAT(t.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'ownership_group' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'model' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.model' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'place' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.place' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 'tsi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'section' WHERE `module` = 'tags' AND `controller` = 'tags' AND `var_name` = 't.section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'tsi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'tsi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'tag_limit' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.tag_limit' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'place' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.place' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ts.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ts.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'deleted' WHERE `module` = 'tags' AND `controller` = 'sections' AND `var_name` = 'ts.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'ti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'ti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'tti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'type_section' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'tt.type_section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.project' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'project' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'pi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'customer' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'CONCAT(ci18n.name, \' \', ci18n.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.trademark' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'trademark' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'ni18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'status' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'severity' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.severity' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'assign_title' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'ta.assigned_to' AND `field_type` = 'assignment';
UPDATE `_search_defs` SET `field_name` = 'planned_start_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'DATE_FORMAT(t.planned_start_date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'planned_start_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.planned_start_date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'planned_finish_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'DATE_FORMAT(t.planned_finish_date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'planned_finish_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.planned_finish_date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'start_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'DATE_FORMAT(t.start_date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'start_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.start_date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'finish_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'DATE_FORMAT(t.finish_date, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'finish_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.finish_date' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'progress' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.progress' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'planned_time' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.planned_time' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'spent_time' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.spent_time' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'ownership' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.ownership' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'started_by' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.started_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'finish_by' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.finish_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'full_num' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.full_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'notes' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'ti18n.notes' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.department' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'department' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'depi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'DATE_FORMAT(t.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'DATE_FORMAT(t.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'tags' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'tags.tag_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'description' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'ti18n.description' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'task_field' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'task_field' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'equipment' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 't.equipment' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'tasks' AND `controller` = 'tasks' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'tt.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'tt.subject' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'tt.content' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'tta.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'tt.startperiod' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'tt.endperiod' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = '' WHERE `module` = 'tasks' AND `controller` = 'timesheets' AND `var_name` = 'tt.duration' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'sections_name' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'tsi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'sections_description' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'tsi18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ts.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'DATE_FORMAT(ts.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'sections_position' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.position' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'tasks' AND `controller` = 'sections' AND `var_name` = 'ts.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_name' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'tsi18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'statuses_task_type' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.task_type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_task_type' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'tti18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'statuses_status' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.status' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'statuses_sequence' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.sequence' AND `field_type` = 'number';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'DATE_FORMAT(ts.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'DATE_FORMAT(ts.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'tasks' AND `controller` = 'statuses' AND `var_name` = 'ts.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_name' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tti18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_description' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tti18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_section' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'ts.name' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_counter' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.counter' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(tt.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(tt.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'tasks' AND `controller` = 'types' AND `var_name` = 'tt.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_name' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_description' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(tc.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tc.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(tc.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tc.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tc.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tc.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tc.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tc.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'tasks' AND `controller` = 'counters' AND `var_name` = 'tc.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'firstname' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'ui18n.firstname' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'lastname' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'ui18n.lastname' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'username' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.username' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'email' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.email' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'code' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'office' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.office' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'office' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'oi18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'DATE_FORMAT(u.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'DATE_FORMAT(u.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.default_group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'is_portal' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.is_portal' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'salutation' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.salutation' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'invoice_code' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.invoice_code' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'role' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.role' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'name' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'ri18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'default_customer' WHERE `module` = 'users' AND `controller` = 'users' AND `var_name` = 'u.default_customer' AND `field_type` = 'autocompleter';
UPDATE `_search_defs` SET `field_name` = 'subject' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'ai18n.subject' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'ai18n.content' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.type' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'ati18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'category' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.category' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'category' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'aci18n.name' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'read_by' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'acks.user_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'assignments' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'aa.assigned_to' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'priority' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.priority' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'DATE_FORMAT(a.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'DATE_FORMAT(a.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'archive' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.search_archive' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'content' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'ai18n.content' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'full_num' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'a.full_num' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'id' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'f.id IS NOT NULL' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'filename' WHERE `module` = 'announcements' AND `controller` = 'announcements' AND `var_name` = 'f.filename' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'categories_name' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'aci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'categories_description' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'aci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'ac.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'DATE_FORMAT(ac.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'ac.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'DATE_FORMAT(ac.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'ac.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'ac.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'ac.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'ac.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'announcements' AND `controller` = 'categories' AND `var_name` = 'ac.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'counters_name' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'aci18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'counters_description' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'aci18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(ac.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'ac.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'DATE_FORMAT(ac.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'ac.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'ac.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'ac.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'ac.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'ac.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'announcements' AND `controller` = 'counters' AND `var_name` = 'ac.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'types_name' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'ati18n.name' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_description' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'ati18n.description' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'types_color' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.color' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(at.added, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'added' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.added' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'DATE_FORMAT(at.modified, \'%Y-%m-%d\')' AND `field_type` = 'date';
UPDATE `_search_defs` SET `field_name` = 'modified_date' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.modified' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n1.firstname, \' \', ui18n1.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'added_by' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.added_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'CONCAT(ui18n2.firstname, \' \', ui18n2.lastname)' AND `field_type` = 'sort_only';
UPDATE `_search_defs` SET `field_name` = 'modified_by' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.modified_by' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'group' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.group' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'active_state' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.active' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'include_deleted' WHERE `module` = 'announcements' AND `controller` = 'types' AND `var_name` = 'at.deleted' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'title' WHERE `module` = 'outlooks' AND `controller` = 'outlooks' AND `var_name` = 'oi18n.title' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'subtitle' WHERE `module` = 'outlooks' AND `controller` = 'outlooks' AND `var_name` = 'oi18n.subtitle' AND `field_type` = 'text';
UPDATE `_search_defs` SET `field_name` = 'module' WHERE `module` = 'outlooks' AND `controller` = 'outlooks' AND `var_name` = 'CONCAT(o.module, \'|\', o.controller)' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'type' WHERE `module` = 'outlooks' AND `controller` = 'outlooks' AND `var_name` = 'o.model_id' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'section' WHERE `module` = 'outlooks' AND `controller` = 'outlooks' AND `var_name` = 'o.section' AND `field_type` = 'dropdown';
UPDATE `_search_defs` SET `field_name` = 'role_user' WHERE `module` = 'outlooks' AND `controller` = 'outlooks' AND `var_name` = 'oa.assigned_to' AND `field_type` = 'dropdown';

#########################################################################################
# 2023-03-17 - Add new setting Theme

# Add new setting Theme
INSERT IGNORE INTO `settings`
(SELECT null as `id`, `section`, 'theme' as `name`, `value` FROM `settings` WHERE `section` = 'themes' AND `name` = 'default_theme' LIMIT 1);
  INSERT IGNORE INTO `settings_i18n` VALUES
  ((SELECT `id` FROM `settings` WHERE `section` = 'themes' AND `name` = 'theme'), 'Тема ма интерфейса', 'Темата която се използва', 'bg', now()),
  ((SELECT `id` FROM `settings` WHERE `section` = 'themes' AND `name` = 'theme'), 'Тheme', 'The theme to be used', 'en', now());

# Update translations for default_theme
UPDATE settings_i18n SET label='Тема подразбиране', help = 'Резервна тема'
WHERE `parent_id` IN (SELECT `id` FROM `settings` WHERE `section` = 'themes' AND `name` = 'default_theme') and lang = 'bg';
UPDATE settings_i18n SET label='Default theme', help = 'The fallback theme'
WHERE `parent_id` IN (SELECT `id` FROM `settings` WHERE `section` = 'themes' AND `name` = 'default_theme') and lang = 'en';

########################################################################
# 2023-04-05 - Added translation of some items menu in the admin role

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:20:"Органайзер";s:2:"en";s:9:"Organizer";}',
    'a:4:{s:2:"bg";s:20:"Органайзер";s:2:"en";s:9:"Organizer";s:2:"de";s:9:"Organizer";s:2:"ro";s:9:"Organizer";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:20:"Органайзер";s:2:"en";s:9:"Organizer";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:13:"Шаблoни";s:2:"en";s:9:"Pattеrns";}',
    'a:4:{s:2:"bg";s:14:"Шаблони";s:2:"en";s:8:"Patterns";s:2:"de";s:9:"Schablone";s:2:"ro";s:9:"Șabloane";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:13:"Шаблoни";s:2:"en";s:9:"Pattеrns";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:36:"Презапис на изгледи";s:2:"en";s:15:"Resave outlooks";}',
    'a:4:{s:2:"bg";s:36:"Презапис на изгледи";s:2:"en";s:15:"Resave outlooks";s:2:"de";s:23:"Ausblicke neu speichern";s:2:"ro";s:24:"Resalvați perspectivele";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:36:"Презапис на изгледи";s:2:"en";s:15:"Resave outlooks";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:18:"Настройки";s:2:"en";s:8:"Settings";}',
    'a:4:{s:2:"bg";s:18:"Настройки";s:2:"en";s:8:"Settings";s:2:"de";s:13:"Einstellungen";s:2:"ro";s:7:"Setări";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:18:"Настройки";s:2:"en";s:8:"Settings";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:16:"Плащания";s:2:"en";s:8:"Payments";}',
    'a:4:{s:2:"bg";s:16:"Плащания";s:2:"en";s:8:"Payments";s:2:"de";s:9:"Zahlungen";s:2:"ro";s:7:"Plăți";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:16:"Плащания";s:2:"en";s:8:"Payments";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:8:"Пари";s:2:"en";s:5:"Money";}',
    'a:4:{s:2:"bg";s:8:"Пари";s:2:"en";s:5:"Money";s:2:"de";s:4:"Geld";s:2:"ro";s:4:"Bani";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:8:"Пари";s:2:"en";s:5:"Money";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:10:"Склад";s:2:"en";s:9:"Warehouse";}',
    'a:4:{s:2:"bg";s:10:"Склад";s:2:"en";s:9:"Warehouse";s:2:"de";s:9:"Lagerhaus";s:2:"ro";s:7:"Depozit";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:10:"Склад";s:2:"en";s:9:"Warehouse";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:12:"Анализ";s:2:"en";s:8:"Analysis";}',
    'a:4:{s:2:"bg";s:12:"Анализ";s:2:"en";s:8:"Analysis";s:2:"de";s:7:"Analyse";s:2:"ro";s:8:"Analiză";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:12:"Анализ";s:2:"en";s:8:"Analysis";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:14:"Обороти";s:2:"en";s:9:"Turnovers";}',
    'a:4:{s:2:"bg";s:14:"Обороти";s:2:"en";s:9:"Turnovers";s:2:"de";s:8:"Umsätze";s:2:"ro";s:16:"Cifra de afaceri";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:14:"Обороти";s:2:"en";s:9:"Turnovers";}%';

UPDATE roles
SET menu_settings=REPLACE(menu_settings,
    'a:2:{s:2:"bg";s:14:"Индекси";s:2:"en";s:7:"Indexes";}',
    'a:4:{s:2:"bg";s:14:"Индекси";s:2:"en";s:7:"Indexes";s:2:"de";s:7:"Indizes";s:2:"ro";s:6:"Indici";}')
WHERE menu_settings LIKE '%a:2:{s:2:"bg";s:14:"Индекси";s:2:"en";s:7:"Indexes";}%';

########################################################################
# 2023-04-24 - Added finance documents NAME placeholder
#            - Fixed the function that adds holiday and its subsequent non working day when the holiday is on Saturday or Sunday

# Added finance documents NAME placeholder
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('finance_incomes_reason_name', 'Finance_Incomes_Reason', 'basic', 'patterns', NULL, 'name', '1'),
  ('finance_expenses_reason_name', 'Finance_Expenses_Reason', 'basic', 'patterns', NULL, 'name', '1'),
  ('finance_warehouses_document_name', 'Finance_Warehouses_Document', 'basic', 'patterns', NULL, 'name', '1'),
  ('finance_annulment_name', 'Finance_Annulment', 'basic', 'patterns', NULL, 'name', '1');
INSERT IGNORE INTO `placeholders_i18n`
  SELECT id, 'Относно на документа', NULL, 'bg'
  FROM placeholders WHERE model LIKE 'finance_%' AND `source`='name' AND `usage`='patterns';
INSERT IGNORE INTO `placeholders_i18n`
  SELECT id, 'Document name', NULL, 'en'
  FROM placeholders WHERE model LIKE 'finance_%' AND `source`='name' AND `usage`='patterns';

# Fixed the function that adds holiday and its subsequent non working day when the holiday is on Saturday or Sunday
DROP FUNCTION IF EXISTS `addHoliday`;
DELIMITER $$$
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION `addHoliday`(d DATE, descr TEXT, additional INT) RETURNS boolean
    LANGUAGE SQL
    DETERMINISTIC
    MODIFIES SQL DATA
    SQL SECURITY INVOKER
BEGIN
    DECLARE cDate date;
    DECLARE tmpDescr text;
    DECLARE result BOOLEAN;
    DECLARE tmp int;
    SET result = true;

    SELECT COUNT(`date`) into tmp FROM country_nonworkdays WHERE `date`=d;
    SELECT `description` into tmpDescr FROM country_nonworkdays WHERE `date`=d;
    IF tmp = 0 THEN
        INSERT IGNORE INTO `country_nonworkdays` (`country`, `date`, `type`, `work_on`, `description`)
        VALUES ('BG', d, 'holiday', NULL, descr);

        -- add additional holiday the next working day when the holiday is on Saturday or Sunday
        IF additional AND DATE_FORMAT(d, '%w') IN (0, 6) THEN
            SET cDate = date_add(d, INTERVAL 1 DAY);
            SET tmp = 1;
            daysLoop:WHILE tmp = 1 DO
                    SELECT COUNT(`date`) into tmp FROM country_nonworkdays WHERE `date`=cDate;
                    IF tmp = 0 THEN
                        INSERT IGNORE INTO `country_nonworkdays` (`country`, `date`, `type`, `work_on`, `description`)
                        VALUES ('BG', cDate, 'holiday', NULL, CONCAT('Допълнителен почивен ден за ', descr));
                        SET result = true;
                    ELSE
                        SET cDate = date_add(cDate, INTERVAL 1 DAY);
                        SET result = false;
                    END IF;
                END WHILE;
        END IF;
    END IF;

    -- the holiday coincides with some other holiday (most possibly Easter)
    IF tmpDescr != descr THEN
        SET tmpDescr = CONCAT('Допълнителен почивен ден за ', descr);

        -- add additional holiday the next working day when the holiday is on Saturday or Sunday
        IF additional THEN
            SET cDate = date_add(d, INTERVAL 1 DAY);
            SET tmp = 1;
            daysLoop:WHILE tmp = 1 DO
                    SELECT COUNT(`date`) into tmp FROM country_nonworkdays WHERE `date`=cDate
                                                                             AND `description`COLLATE utf8mb4_unicode_ci != tmpDescr COLLATE utf8mb4_unicode_ci;
                    IF tmp = 0 THEN
                        INSERT IGNORE INTO `country_nonworkdays` (`country`, `date`, `type`, `work_on`, `description`)
                        VALUES ('BG', cDate, 'holiday', NULL, tmpDescr);
                        SET result = true;
                    ELSE
                        SET cDate = date_add(cDate, INTERVAL 1 DAY);
                        SET result = false;
                    END IF;
                END WHILE;
        END IF;
    END IF;

    RETURN result;
END$$$

DELIMITER ;

######################################################################################
# 2023-04-27 - Added system setting for the country

# Added system setting for the country
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES
  ('sys', 'country', 'BG');
UPDATE `settings` SET `value`='BG' WHERE `section`='sys' AND `name`='country';

DROP FUNCTION IF EXISTS `easter`;
DROP FUNCTION IF EXISTS `addHoliday`;
DROP PROCEDURE IF EXISTS `addAllHolidays`;
DROP PROCEDURE IF EXISTS `addAllHolidaysBG`;
DROP PROCEDURE IF EXISTS `addAllHolidaysRO`;

######################################################################################
# 2023-05-09 - Added new setting to the report 'export_finance_warehouse_documents'

# Added new setting to the report 'export_finance_warehouse_documents'
UPDATE reports SET settings = CONCAT('filter_articles_types :=\r\n\r\n', `settings`)
WHERE type = 'export_finance_warehouse_documents' AND settings NOT LIKE '%filter_articles_types%';

######################################################################################
# 2023-05-10 - Added new setting in 'blinds_orders_analysis' report

# Added new setting in 'blinds_orders_analysis' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nfield_dograma_gift_blinds :=', '\r\ninclude_available_bonus := 1\r\nfield_dograma_gift_blinds :=') WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%include_available_bonus%';

######################################################################################
# 2023-06-05 - Added new setting in 'blinds_orders_analysis' report for custom labels
#            - Added new setting in 'blinds_orders_analysis' report for displaying certain columns
#            - Added new setting in 'blinds_orders_analysis' report for displaying measures in the result table
#            - Added new setting in 'blinds_orders_analysis' report for displaying payment amounts in the result table
#            - Added new setting in 'present_form_76' report for including extra column with employee num

# Added new setting in 'blinds_orders_analysis' report for custom labels
UPDATE `reports` SET `settings`=CONCAT('# labels settings\r\nlabel_offices_measure_bg :=\r\nlabel_orders_measure_bg :=\r\n\r\n', `settings`) WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%label_offices_measure_bg%';

# Added new setting in 'blinds_orders_analysis' report for displaying certain columns
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\n# types of customers', '\r\ndisplay_measures :=\r\ndisplay_pay_total :=\r\ndisplay_paid_amount :=\r\ndisplay_unpaid_amount :=\r\n\r\n# types of customers') WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%display_measures%';

# Added new setting in 'blinds_orders_analysis' report for displaying measures in the result table
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\n# total amount to pay', '\r\n\r\n# measure for the items\r\nfield_horizontal_measure :=\r\nfield_vertical_measure :=\r\nfield_vrsht_measure :=\r\nfield_kom_measure :=\r\nfield_dograma_measure :=\r\nfield_str_measure :=\r\nfield_rolo_measure :=\r\nfield_roman_measure :=\r\nfield_plise_measure :=\r\nfield_accessories_measure :=\r\nfield_reclamations_measure :=\r\nfield_glass_panes_measure :=\r\n\r\n# total amount to pay') WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%field_horizontal_measure%';

# Added new setting in 'blinds_orders_analysis' report for displaying payment amounts in the result table
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\n# payment ids', '\r\n\r\n# payment amounts\r\nfield_horizontal_payment :=\r\nfield_vertical_payment :=\r\nfield_vrsht_payment :=\r\nfield_kom_payment :=\r\nfield_dograma_payment := link_pay_sum\r\nfield_str_payment :=\r\nfield_rolo_payment :=\r\nfield_roman_payment :=\r\nfield_plise_payment :=\r\nfield_accessories_payment := link_pay_sum\r\nfield_reclamations_payment :=\r\nfield_glass_panes_payment :=\r\n\r\n# payment ids') WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%field_horizontal_payment%';

# Added new setting in 'present_form_76' report for including extra column with employee num
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nwork_time_type_norm :=', '\r\ncontract_employee_num :=\r\n\r\nwork_time_type_norm :=') WHERE `type`='present_form_76' AND `settings` NOT LIKE '%contract_employee_num%';

######################################################################################
# 2023-06-14 - Added new setting in 'blinds_orders_analysis' report for displaying payment total in the result table
#            - Added new setting in 'blinds_orders_analysis' report for displaying measures in the result table

# Added new setting in 'blinds_orders_analysis' report for displaying payment total in the result table
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\n# total correction amount', '\r\n\r\n# total amount to pay\r\nfield_horizontal_pay_total := \r\nfield_vertical_pay_total := \r\nfield_vrsht_pay_total := \r\nfield_kom_pay_total := \r\nfield_dograma_pay_total :=\r\nfield_str_pay_total := \r\nfield_rolo_pay_total := \r\nfield_roman_pay_total := \r\nfield_plise_pay_total := \r\nfield_accessories_pay_total :=\r\nfield_reclamations_pay_total := \r\nfield_glass_panes_pay_total := \r\n\r\n# total correction amount') WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%field_horizontal_pay_total%';

# Added new setting in 'blinds_orders_analysis' report for displaying measures in the result table
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\n# total amount to pay', '\r\n\r\n# measure for the items\r\nfield_horizontal_measure :=\r\nfield_vertical_measure :=\r\nfield_vrsht_measure :=\r\nfield_kom_measure :=\r\nfield_dograma_measure :=\r\nfield_str_measure :=\r\nfield_rolo_measure :=\r\nfield_roman_measure :=\r\nfield_plise_measure :=\r\nfield_accessories_measure :=\r\nfield_reclamations_measure :=\r\nfield_glass_panes_measure :=\r\n\r\n# total amount to pay') WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%field_horizontal_measure%';

######################################################################################
# 2023-06-15 - Added placehoder for warehouse documents parent document num

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'finance_warehouses_document_parent_num', 'Finance_Warehouses_Document', 'basic', 'patterns', NULL, 'parent_num', 0);

INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Номер на родителския документ', NULL, 'bg'),
(LAST_INSERT_ID(), 'Parent document\'s number', NULL, 'en');

######################################################################################
# 2023-06-19 - Added placehoder for warehouse documents parent document invoice num

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'finance_warehouses_document_parent_invoice_num', 'Finance_Warehouses_Document', 'basic', 'patterns', NULL, 'parent_invoice_num', 0);

INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Номер на фактура на родителския документ', NULL, 'bg'),
(LAST_INSERT_ID(), 'Parent document invoice\'s number', NULL, 'en');

######################################################################################
# 2023-06-23 - Added setting for showing the new filter for produce date in 'production_schedule' report

UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\norder_types :=', '\r\ninclude_produce_date_filter :=\r\norder_types :=')
  WHERE `type` = 'production_schedule' AND `settings` NOT LIKE '%include_produce_date_filter%';

######################################################################################
# 2023-07-10 - Added new settings in 'production_schedule' report for capacities

# Added new settings in 'production_schedule' report for capacities
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\norder_windows_profile_type_var :=', '\r\n\r\ncapacity_type := 6\r\ncapacity_active_substatus := 28\r\ncapacity_var_produced_kind := kind_sht\r\ncapacity_var_produced_type := type_dograma\r\ncapacity_var_produced_article := production_article\r\ncapacity_var_produced_quantity := max_number\r\ncapacity_var_produced_size := max_size\r\n\r\norder_windows_profile_type_var :=') WHERE `type`='production_schedule' AND `settings` NOT LIKE '%capacity_type%';


#########################################################################################
# 2023-07-19 - Add new setting to allow users to change their theme

# Add new setting users_can_select_theme
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`)
VALUES
    (NULL, 'themes', 'users_can_select_theme', '');

INSERT IGNORE INTO `settings_i18n`
VALUES
    ((SELECT `id` FROM `settings` WHERE `section` = 'themes' AND `name` = 'users_can_select_theme'), 'Измежду кои теми могат да избират потребителите', 'Списък с теми разделени със запетайка. Празно за да не могат да избират. (Големи и малки букви имат значение)<br /> (Пример: Default, Evolution)',                 'bg', now()),
    ((SELECT `id` FROM `settings` WHERE `section` = 'themes' AND `name` = 'users_can_select_theme'), 'Which themes can users choose from',              'A comma separated list of themes. Leave the field blank to prevent users from choosing the theme themselvs. (Case sensitive)<br /> (Example: Default, Evolution)', 'en', now());

# Remove the obsolete setting 'theme'
DELETE FROM `settings_i18n` WHERE parent_id = (SELECT `id` FROM `settings` WHERE `section` = 'themes' AND `name` = 'theme');
DELETE FROM `settings` WHERE `section` = 'themes' AND `name` = 'theme';

#########################################################################################
# 2023-07-20 - Added auto_increment for events_types

# Add new setting users_can_select_theme
ALTER TABLE `events_types`
    CHANGE COLUMN `id` `id` INT NOT NULL AUTO_INCREMENT FIRST;
ALTER TABLE `events_types` AUTO_INCREMENT = 1;

#########################################################################################
# 2023-07-21 - Added setting for inactive clients and articles for sales report
#            - Added setting for inactive customers and nomenclatures for goods_movement report

# Added setting for inactive clients and articles for sales report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '
include_inactive_clients :=
include_inactive_articles := ')
WHERE `type`='sales' AND `settings` NOT LIKE '%include_inactive_clients%';

# Added setting for inactive customers and nomenclatures for goods_movement report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '
include_inactive_customers :=
include_inactive_nomenclatures := ')
WHERE `type`='goods_movement' AND `settings` NOT LIKE '%include_inactive_customers%';

######################################################################################
# 2023-08-24 - Added placehoder for trademark_name in documents emails

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'trademark_name', 'Document', 'send', 'emails', NULL, 'trademark_name', 0);

INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Търговска марка', NULL, 'bg'),
(LAST_INSERT_ID(), 'Trademark', NULL, 'en');

######################################################################################
# 2023-09-07 - Added settings for cashflow report columns for expenses/incomes

# Added settings for cashflow report columns for expenses/incomes
UPDATE `reports`
SET settings=CONCAT(settings, "
# КОЛОНИ:
#columns_expenses := customer_name, type_name_num, date, total, total_vat, total_with_vat, maturity_date, customer_iban, customer_bic, payment_by
#columns_incomes := customer_name, type_name_num, date, total, total_vat, total_with_vat, maturity_date, payment_by
") WHERE type="cash_flow" AND settings NOT LIKE "%columns_expenses%";

######################################################################################
# 2023-09-08 - Added new setting in 'blinds_orders_analysis' report for showing filters for tags and/or employees

# Added new setting in 'blinds_orders_analysis' report for showing filters for tags and/or employees
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\n# order types', '\r\n\r\nshow_filter_tags :=\r\nshow_filter_employee :=\r\n\r\n# order types') WHERE `type`='blinds_orders_analysis' AND `settings` NOT LIKE '%show_filter_tags%';

######################################################################################
# 2023-09-20 - Renamed the biotrade pattern plugin to gt2 and add default settings

UPDATE patterns_plugins
SET
 folder='gt2',
 settings='gt2_quantity := 0\r\ngt2_rows := 2\r\ngt2_total := 2\r\ngt2_total_vat := 2\r\ngt2_total_with_vat:= 2\r\ndecimal_separator := .\r\nthousands_separator :=  &nbsp;\r\n'
WHERE folder='biotrade' AND method='prepareModifiedGT2';

######################################################################################
# 2023-09-25 - Update search_defs. Sorting columns for dropdowns need have the proper '_name' suffix in the field_name
#              This is needed for matching basic vars with the sorting parameter

# Update existing search_def rows
UPDATE _search_defs t
    JOIN _search_defs t2 ON t2.module = t.module AND t2.controller = t.controller AND t2.field_name = t.field_name AND t2.field_type <> 'sort_only'
    SET t.field_name = CONCAT(t.field_name, '_name')
    WHERE t.controller <> 'types' && t.field_type = 'sort_only' AND t.field_name <> '' AND t.var_name LIKE '%name%';

# Add the missing type_lname row for nomenclatures
INSERT IGNORE INTO _search_defs (module, controller, field_name, var_name, label_param, layout_keyword, sortable, `key`, priority, field_type, field_compare, source)
VALUES
    ('nomenclatures', 'nomenclatures', 'type_name', 'nti18n.name', 'nomenclatures_type', null, 'nti18n.name', 0, 2, 'sort_only', 'none', null);

######################################################################################
# 2023-09-29 - Added new setting in 'budgets' report for initial budgets

# Added new setting in 'budgets' report for initial budgets
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_budget_item_var_parent_item :=', '\r\nnom_office_var_start_date := start_project\r\nnom_office_var_end_date := end_project\r\n\r\nnom_budget_item_var_parent_item :=') WHERE `type`='budgets' AND `settings` NOT LIKE '%nom_office_var_start_date%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndoc_type_incomes :=', '\r\ndoc_type_initial_budget_year :=\r\ndoc_type_initial_budget_period :=\r\ndoc_type_incomes :=') WHERE `type`='budgets' AND `settings` NOT LIKE '%doc_type_initial_budget_year%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndoc_var_year :=', '\r\ndoc_type_initial_budget_year_status_accepted :=\r\ndoc_type_initial_budget_period_status_accepted :=\r\n\r\ndoc_var_year :=') WHERE `type`='budgets' AND `settings` NOT LIKE '%doc_type_initial_budget_year_status_accepted%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndoc_type_budget_year :=', '\r\n\r\ninclude_initial_budgets :=\r\n\r\ndoc_type_budget_year :=') WHERE `type`='budgets' AND `settings` NOT LIKE '%include_initial_budgets%';

######################################################################################
# 2023-10-19 - Replaced DOUBLE fields with DECIMAL, increase the DECIMAL mantis 25,6/21.2 instead of 15.6/11.2

# Replaced DOUBLE fields with DECIMAL, increase the DECIMAL mantis 25,6/21.2 instead of 15.6/11.2
ALTER TABLE _indexes_data 
    MODIFY `index` DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
    MODIFY value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE archive_gt2_audit
  MODIFY article_height DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_width DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_weight DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_volume DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY available_quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY average_weighted_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY last_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY vat_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE archive_gt2_details
  MODIFY article_height DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_width DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_weight DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_volume DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY available_quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY average_weighted_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY last_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY vat_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE crontab_issue_results
  MODIFY total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE diag_containers_change_history
  MODIFY previous_amount DECIMAL(21,2) NOT NULL,
  MODIFY changed_amount DECIMAL(21,2) NOT NULL;
ALTER TABLE fin_analysis_centers_distribution
  MODIFY percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_analysis_distribution_data
  MODIFY element_amount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY center_amount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_analysis_distribution_items
  MODIFY item_amount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_analysis_items_distribution
  MODIFY percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_annulments
  MODIFY total_without_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY fiscal_total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY fiscal_total_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_balance
  MODIFY paid_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL COMMENT 'Distributed amount between financial documents and/or payments. Amount is always > 0.';
ALTER TABLE fin_bank_accounts
  MODIFY amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY current_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY credit_limit DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_budgets
  MODIFY income_amount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY expense_amount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_budgets_data
  MODIFY data_amount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_1 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_2 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_3 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_4 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_5 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_6 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_7 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_8 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_9 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_10 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_11 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY month_amount_12 DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_cashboxes_amounts
  MODIFY amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_currencies
  MODIFY rate DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY fixing DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY buys_bank DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY buys_cash DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY sells_bank DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY sells_cash DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_currencies_available
  MODIFY fixed DECIMAL(25,6) NOT NULL;
ALTER TABLE fin_expenses_reasons
  MODIFY currency_rate DECIMAL(25,6) DEFAULT 1.000000 NOT NULL COMMENT 'Conversion rate from currency of parent record to currency of current record. Value can be other than 1 only for an incoming invoice created from multiple proformas.',
  MODIFY total_without_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_incomes_reasons
  MODIFY total_without_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY fiscal_total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY fiscal_total_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_invoices_templates
  MODIFY total_without_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_payments
  MODIFY container_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY container_rate DECIMAL(25,6) DEFAULT 1.000000 NOT NULL,
  MODIFY amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY distributed_amount DECIMAL(21,2) NOT NULL,
  MODIFY not_distributed_amount DECIMAL(21,2) NOT NULL,
  MODIFY transaction_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_payslip
  MODIFY amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_recurring_payments
  MODIFY total DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY total_vat DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY total_with_vat DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_repayment_plans_data
  MODIFY amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY paid_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_transaction_expenses
  MODIFY from_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY to_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY fixed_transaction DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY free_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY percentage DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_transfers
  MODIFY from_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY from_rate DECIMAL(25,6) DEFAULT 1.000000 NOT NULL,
  MODIFY to_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY to_rate DECIMAL(25,6) DEFAULT 1.000000 NOT NULL,
  MODIFY amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL,
  MODIFY transaction_amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_violation_notice
  MODIFY amount DECIMAL(21,2) DEFAULT 0.00 NOT NULL;
ALTER TABLE fin_warehouses_documents
  MODIFY total_without_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY total_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE fin_warehouses_quantities
  MODIFY quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE gt2_allocated_costs
  MODIFY allocated_amount DECIMAL(25,6) NOT NULL COMMENT 'in currency of expense';
ALTER TABLE gt2_audit
  MODIFY article_height DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_width DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_weight DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_volume DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY available_quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY average_weighted_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY last_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY vat_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE gt2_batches_data
  MODIFY available_quantity decimal(25,6) NOT NULL DEFAULT '0.000000',
  MODIFY quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE gt2_details
  MODIFY article_height DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_width DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_weight DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY article_volume DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY available_quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY average_weighted_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY last_delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY discount_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY surplus_percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY price_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY vat_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_profit_no_final_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_discount_value DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY subtotal_with_vat_with_discount DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE nom
  MODIFY sell_price DECIMAL(25,6) NULL,
  MODIFY last_delivery_price DECIMAL(25,6) NULL,
  MODIFY average_weighted_delivery_price DECIMAL(25,6) NULL;
ALTER TABLE nom_distribution
  MODIFY percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE nom_measures
  MODIFY rate DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE nom_price_updates
  MODIFY old_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL COMMENT 'old AWDP',
  MODIFY old_quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL COMMENT 'old available quantity',
  MODIFY delivery_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL COMMENT 'in AWDP currency',
  MODIFY delivery_quantity DECIMAL(25,6) DEFAULT 0.000000 NOT NULL,
  MODIFY new_price DECIMAL(25,6) DEFAULT 0.000000 NOT NULL COMMENT 'new AWDP';
ALTER TABLE nom_prices
  MODIFY price DECIMAL(25,6) DEFAULT 0.00 NOT NULL;
ALTER TABLE nom_types_distribution
  MODIFY percentage DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE turnovers
  MODIFY total DECIMAL(25,6) DEFAULT 0.000000 NOT NULL;
ALTER TABLE users
  MODIFY working_hours DECIMAL(4,2) DEFAULT 0.00 NOT NULL;

# FUNCTION TO CALCULATE CONTAINER SUM
DROP FUNCTION IF EXISTS calculate_container_sum;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION calculate_container_sum(current_container_id int(11), current_container_type varchar(255), current_container_currency varchar(3)) RETURNS DECIMAL(21,2)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE transfer_sum DECIMAL(21,2);
        DECLARE payments_sum DECIMAL(21,2);
        DECLARE new_sum DECIMAL(21,2);
        DECLARE starting_amount DECIMAL(21,2);

        # If container is bank_account then the starting amount has to be taken
        IF (current_container_type = 'bank_account') THEN
            SET starting_amount = (SELECT amount FROM fin_bank_accounts WHERE id=current_container_id);
        END IF;

        # Get the transfers sum
        SELECT SUM(IF(from_container_id=current_container_id AND from_container_type=current_container_type, (-1*from_amount), to_amount)) INTO transfer_sum
        FROM fin_transfers
        WHERE ((from_container_id=current_container_id AND from_container_type=current_container_type) OR (to_container_id=current_container_id AND to_container_type=current_container_type))
              AND (current_container_type = 'bank_account' OR (current_container_type='cashbox' AND currency=current_container_currency))
              AND status='finished';

        # Get the payments sum
        SELECT SUM(receive_flag*container_amount) INTO payments_sum
        FROM fin_payments
        WHERE     container_id=current_container_id
              AND container_type=current_container_type
              AND annulled_by=0
              AND (current_container_type = 'bank_account' OR (current_container_type='cashbox' AND currency=current_container_currency))
              AND status='finished'
              AND `type`!='TR';

        # Calculates the current amount of the container
        SET new_sum = (IF (transfer_sum IS NULL, 0, transfer_sum)) + (IF (payments_sum IS NULL, 0, payments_sum)) + (IF (starting_amount IS NULL, 0, starting_amount));
        return new_sum;
    END;//
delimiter ;


# PROCEDURE TO UPDATE THE AMOUNT IN THE REQUIRED TABLE
DROP PROCEDURE IF EXISTS update_container_amounts;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_container_amounts(current_container_type varchar(255), current_container_id int(11), new_sum DECIMAL(21,2), current_container_currency varchar(3), user_correct int(11))
SQL SECURITY INVOKER
    BEGIN
        IF (current_container_type='cashbox') THEN
            # Case for cashboxes
            INSERT INTO fin_cashboxes_amounts
                SET amount=new_sum,
                    parent_id=current_container_id,
                    currency=current_container_currency,
                    revised=NOW(),
                    revised_by=user_correct
            ON DUPLICATE KEY UPDATE
                amount=new_sum,
                revised=NOW(),
                revised_by=user_correct;
        ELSE
            # Case for bank accounts
            UPDATE fin_bank_accounts
            SET current_amount = new_sum
            WHERE id=current_container_id;
        END IF;
    END;//
delimiter ;


# PROCEDURE TO WRITE LOG FOR THE CHANGES
DROP PROCEDURE IF EXISTS log_changes;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE log_changes(current_action varchar(255), current_model varchar(255), current_model_id int(11), current_container_type varchar(255), current_container_id int(11), previous_sum DECIMAL(21,2), new_sum DECIMAL(21,2), current_container_currency varchar(3), changed_data_vars text CHARACTER SET utf8mb4)
SQL SECURITY INVOKER
    BEGIN
      # write history
      INSERT INTO diag_containers_change_history
      SET action=current_action,
          changed_model=current_model,
          changed_model_id=current_model_id,
          changed_by=USER(),
          container_type=current_container_type,
          container_id=current_container_id,
          previous_amount=(IF (previous_sum IS NULL, 0, previous_sum)),
          changed_amount=new_sum,
          currency=current_container_currency,
          changed_data=changed_data_vars,
          change_date=NOW();
    END;//
delimiter ;

# PROCEDURE TO CALCULATE CONTAINER SUM
DROP PROCEDURE IF EXISTS administrative_update_container_amounts;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE administrative_update_container_amounts()
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE container_id int(11);
        DECLARE container_amount DECIMAL(21,2);
        DECLARE container_currency varchar(3);

        # Get all the bank accounts which does not have the same synthetic value and analytic value
        DECLARE bank_accounts_cursor CURSOR FOR SELECT id,current_amount,currency FROM `fin_bank_accounts` WHERE current_amount!=calculate_container_sum(id, 'bank_account', currency);

        # Get all the cashboxes data which does not have the same synthetic value and analytic value
        DECLARE cashboxes_cursor CURSOR FOR SELECT parent_id,amount,currency FROM `fin_cashboxes_amounts` WHERE amount!=calculate_container_sum(parent_id, 'cashbox', currency);
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        # Start looping through the results
        OPEN bank_accounts_cursor;
        read_loop: LOOP
            FETCH bank_accounts_cursor INTO container_id, container_amount, container_currency;
            IF done THEN
                LEAVE read_loop;
            END IF;
            SET @new_sum = calculate_container_sum(container_id, 'bank_account', container_currency);
            CALL update_container_amounts('bank_account', container_id, @new_sum, container_currency, 1);
            CALL log_changes('adminitrative_update', '', 0, 'bank_account', container_id, container_amount, @new_sum, container_currency, NULL);
        END LOOP;
        CLOSE bank_accounts_cursor;

        SET done = FALSE;

        # Start looping through the results
        OPEN cashboxes_cursor;
        read_loop: LOOP
            FETCH cashboxes_cursor INTO container_id, container_amount, container_currency;
            IF done THEN
                LEAVE read_loop;
            END IF;
            SET @new_sum = calculate_container_sum(container_id, 'cashbox', container_currency);
            CALL update_container_amounts('cashbox', container_id, @new_sum, container_currency, 1);
            CALL log_changes('adminitrative_update', '', 0, 'cashbox', container_id, container_amount, @new_sum, container_currency, NULL);
        END LOOP;
        CLOSE cashboxes_cursor;
    END;//
delimiter ;

# PROCEDURE TO UPDATE PAYMENT STATUS OF MULTIPLE EXPENSES REASONS WHICH ARE PARENTS OF PROFORMAS WHICH ARE PARENTS OF A SINGLE INVOICE
DROP PROCEDURE IF EXISTS update_multiple_reason_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_multiple_reason_payment_status(changed_model_id INT(11), new_payment_status VARCHAR(255), history_id INT(11))
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE reason_id INT(11);
        DECLARE reason_total DECIMAL(25,2);
        DECLARE reason_current_payment_status VARCHAR(255);
        DECLARE reason_payment_status VARCHAR(255);

        DECLARE reason_cursor CURSOR FOR
            SELECT fer2.id, ROUND(fer2.total_with_vat * fer.currency_rate, 2) AS total_with_vat, fer2.payment_status
            FROM fin_reasons_relatives AS frr
            INNER JOIN fin_expenses_reasons AS fer
              ON (fer.type=21 AND fer.annulled_by=0 AND fer.active=1
                AND frr.parent_model_name="Finance_Expenses_Reason"
                AND frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to=fer.id)
            INNER JOIN fin_reasons_relatives AS frr2
              ON (frr2.parent_model_name="Finance_Expenses_Reason" AND frr2.parent_id=frr.link_to
                AND frr2.link_to_model_name="Finance_Expenses_Reason")
            INNER JOIN  fin_expenses_reasons AS fer2
              ON (fer2.id=frr2.link_to)
            WHERE fer2.type>100 AND fer2.annulled_by=0 AND fer2.active=1 AND frr.parent_id=changed_model_id
            ORDER BY fer2.issue_date ASC, fer2.id ASC;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        # Paid amount to expense invoice
        SET @current_paid_amount = 0;
        IF (new_payment_status = 'partial') THEN
            SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE parent_model_name="Finance_Expenses_Reason" AND parent_id=changed_model_id);
        END IF;

        OPEN reason_cursor;

        reason_loop: LOOP
            FETCH reason_cursor INTO reason_id, reason_total, reason_current_payment_status;

            IF done THEN
                LEAVE reason_loop;
            END IF;

            IF (new_payment_status IN ('paid', 'unpaid')) THEN
                # Update all expenses reasons with the same payment status as invoice
                SET reason_payment_status = new_payment_status;
            ELSEIF (new_payment_status = 'partial') THEN
                # Specify payment status for each reason iteratively
                IF (@current_paid_amount <= 0 AND reason_total > 0) THEN
                    SET reason_payment_status = 'unpaid';
                ELSEIF (@current_paid_amount >= reason_total) THEN
                    SET reason_payment_status = 'paid';
                ELSE
                    SET reason_payment_status = 'partial';
                END IF;

                # Subtract the amount of reason from paid amount to get remaining amount
                IF (@current_paid_amount > 0) THEN
                    SET @current_paid_amount = ROUND((IF (@current_paid_amount IS NULL, 0, @current_paid_amount))-(IF (reason_total IS NULL, 0, reason_total)), 2);
                    IF (@current_paid_amount < 0.01) THEN
                        SET @current_paid_amount = 0;
                    END IF;
                END IF;
            END IF;

            IF (reason_payment_status IS NOT NULL) THEN
                UPDATE fin_expenses_reasons SET payment_status=reason_payment_status WHERE id=reason_id;
                CALL log_payment_status_audit(history_id, 'Finance_Expenses_Reason', reason_id, reason_current_payment_status, reason_payment_status);
            END IF;
        END LOOP;

        CLOSE reason_cursor;
    END;//
delimiter ;

# PROCEDURE TO UPDATE PAYMENT STATUS OF MULTIPLE EXPENSES REASONS WHICH ARE PARENTS OF PROFORMAS WHICH ARE PARENTS OF A SINGLE INVOICE
DROP PROCEDURE IF EXISTS update_multiple_reason_payment_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' PROCEDURE update_multiple_reason_payment_status(changed_model_id INT(11), new_payment_status VARCHAR(255), history_id INT(11))
SQL SECURITY INVOKER
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE reason_id INT(11);
        DECLARE reason_total DECIMAL(25,6);
        DECLARE reason_current_payment_status VARCHAR(255);
        DECLARE reason_payment_status VARCHAR(255);

        DECLARE reason_cursor CURSOR FOR
            SELECT fer2.id, ROUND(fer2.total_with_vat * fer.currency_rate, 2) AS total_with_vat, fer2.payment_status
            FROM fin_reasons_relatives AS frr
            INNER JOIN fin_expenses_reasons AS fer
              ON (fer.type=21 AND fer.annulled_by=0 AND fer.active=1
                AND frr.parent_model_name="Finance_Expenses_Reason"
                AND frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to=fer.id)
            INNER JOIN fin_reasons_relatives AS frr2
              ON (frr2.parent_model_name="Finance_Expenses_Reason" AND frr2.parent_id=frr.link_to
                AND frr2.link_to_model_name="Finance_Expenses_Reason")
            INNER JOIN  fin_expenses_reasons AS fer2
              ON (fer2.id=frr2.link_to)
            WHERE fer2.type>100 AND fer2.annulled_by=0 AND fer2.active=1 AND frr.parent_id=changed_model_id
            ORDER BY fer2.issue_date ASC, fer2.id ASC;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

        # Paid amount to expense invoice
        SET @current_paid_amount = 0;
        IF (new_payment_status = 'partial') THEN
            SET @current_paid_amount = (SELECT SUM(paid_amount) FROM fin_balance WHERE parent_model_name="Finance_Expenses_Reason" AND parent_id=changed_model_id);
        END IF;

        OPEN reason_cursor;

        reason_loop: LOOP
            FETCH reason_cursor INTO reason_id, reason_total, reason_current_payment_status;

            IF done THEN
                LEAVE reason_loop;
            END IF;

            IF (new_payment_status IN ('paid', 'unpaid')) THEN
                # Update all expenses reasons with the same payment status as invoice
                SET reason_payment_status = new_payment_status;
            ELSEIF (new_payment_status = 'partial') THEN
                # Specify payment status for each reason iteratively
                IF (@current_paid_amount <= 0 AND reason_total > 0) THEN
                    SET reason_payment_status = 'unpaid';
                ELSEIF (@current_paid_amount >= reason_total) THEN
                    SET reason_payment_status = 'paid';
                ELSE
                    SET reason_payment_status = 'partial';
                END IF;

                # Subtract the amount of reason from paid amount to get remaining amount
                IF (@current_paid_amount > 0) THEN
                    SET @current_paid_amount = ROUND((IF (@current_paid_amount IS NULL, 0, @current_paid_amount))-(IF (reason_total IS NULL, 0, reason_total)), 2);
                    IF (@current_paid_amount < 0.01) THEN
                        SET @current_paid_amount = 0;
                    END IF;
                END IF;
            END IF;

            IF (reason_payment_status IS NOT NULL) THEN
                UPDATE fin_expenses_reasons SET payment_status=reason_payment_status WHERE id=reason_id;
                CALL log_payment_status_audit(history_id, 'Finance_Expenses_Reason', reason_id, reason_current_payment_status, reason_payment_status);
            END IF;
        END LOOP;

        CLOSE reason_cursor;
    END;//
delimiter ;

DROP PROCEDURE IF EXISTS `update_payment_distribution_status`;
delimiter //
CREATE DEFINER=`nzoomer`@`localhost` PROCEDURE `update_payment_distribution_status`(IN `side_connection` VARCHAR(255), IN `changed_model_id` INT(11), IN amnt DECIMAL(21,2),
    OUT ds VARCHAR(255), OUT da DECIMAL(21,2), OUT nda DECIMAL(21,2), OUT io INT(11))
    SQL SECURITY INVOKER
BEGIN
    SET @amount = NULL;
    SET @diff = NULL;
    SET @distributed = NULL;
    SET @distribution_status = NULL;
    SET @official = NULL;

    IF (amnt IS NULL) THEN
        SELECT amount INTO @amount
        FROM fin_payments
        WHERE id=changed_model_id;
    ELSE
        SET @amount = amnt;
    END IF;

    IF (side_connection = 'parent') THEN
        SELECT ROUND(SUM(`paid_amount`),2) INTO @distributed
        FROM fin_balance
        WHERE parent_id=changed_model_id AND parent_model_name='Finance_Payment';

        SELECT fb.paid_to INTO @official
        FROM fin_balance fb
        JOIN fin_incomes_reasons fir
            ON fir.id = fb.paid_to AND (fir.type = 2 OR fir.type > 100)
        WHERE fb.parent_id=changed_model_id AND fb.parent_model_name='Finance_Payment' AND fb.paid_to_model_name = 'Finance_Incomes_Reason'
        LIMIT 1;
    END IF;
    IF (side_connection = 'paid') THEN
        SELECT ROUND(SUM(`paid_amount`),2) INTO @distributed
        FROM fin_balance
        WHERE paid_to=changed_model_id AND paid_to_model_name='Finance_Payment';

        SELECT fb.paid_to INTO @official
        FROM fin_balance fb
        JOIN fin_expenses_reasons fer
            ON fer.id = fb.paid_to AND (fer.type = 21 OR fer.type > 100)
        WHERE fb.parent_id=changed_model_id AND fb.paid_to_model_name ='Finance_Payment' AND fb.parent_model_name = 'Finance_Expenses_Reason'
        LIMIT 1;
    END IF;
    IF (@distributed IS NULL) THEN
        SET @distributed = 0;
    END IF;
    SET @diff = ROUND(@amount - @distributed, 2);
    IF (@distributed = 0) THEN
        SET @distribution_status = 'not_distributed';
    ELSEIF (@diff = 0) THEN
        SET @distribution_status = 'distributed';
    ELSE
        SET @distribution_status = 'partial';
    END IF;
    IF (@official IS NOT NULL AND @official != 0 OR @distributed = 0) THEN
        SET @official = 0;
    ELSE
        SET @official = 1;
    END IF;

    IF (amnt IS NULL) THEN
        UPDATE fin_payments SET
            distribution_status = @distribution_status,
            distributed_amount = @distributed,
            not_distributed_amount = @diff,
            invoices_only = @official
        WHERE id = changed_model_id;
    ELSE
        SET ds = @distribution_status;
        SET da = @distributed;
        SET nda = @diff;
        SET io = @official;
    END IF;
END;//
delimiter ;

# FUNCTION TO DEFINE THE INVOICE STATUS OF AN INCOMES REASON
DROP FUNCTION IF EXISTS define_new_invoiced_status;
delimiter //
CREATE DEFINER = 'nzoomer'@'localhost' FUNCTION define_new_invoiced_status(incomes_reason_id int(11)) RETURNS VARCHAR(255)
    LANGUAGE SQL NOT DETERMINISTIC READS SQL DATA SQL SECURITY INVOKER
    BEGIN
        DECLARE reason_cursor_done INT DEFAULT FALSE;
        DECLARE new_invoiced_status VARCHAR(255);
        DECLARE incomes_reason_total DECIMAL(25,6);
        DECLARE calculated_invoiced_amount DECIMAL(25,6);
        DECLARE difference_invoiced_amount DECIMAL(25,6);
        DECLARE related_reason_id INT(11);
        DECLARE related_reason_total DECIMAL(25,6);
        DECLARE related_debit_credit_sum DECIMAL(25,6);
        DECLARE num_invoices INT DEFAULT 0;

        DECLARE reason_cursor CURSOR FOR
            SELECT fir.id, SUM(fir.total_with_vat)
            FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
            WHERE frr.link_to = incomes_reason_id
              AND fir.id = frr.parent_id
              AND fir.status = "finished"
              AND frr.parent_model_name = "Finance_Incomes_Reason"
              AND frr.link_to_model_name = "Finance_Incomes_Reason"
              AND fir.annulled_by=0 AND fir.type=1
              GROUP BY fir.id;

        DECLARE CONTINUE HANDLER FOR NOT FOUND SET reason_cursor_done = TRUE;

        IF (SELECT `id` FROM fin_incomes_reasons WHERE `id`=incomes_reason_id AND `type`>100 AND `active`=1 AND `status`='finished' AND `annulled_by`=0) THEN
            SELECT ROUND(total_with_vat, 6) INTO incomes_reason_total FROM fin_incomes_reasons WHERE `id`=incomes_reason_id;
            SET calculated_invoiced_amount=0;

            OPEN reason_cursor;
            reason_loop: LOOP
                FETCH reason_cursor INTO related_reason_id, related_reason_total;

                IF reason_cursor_done THEN
                    LEAVE reason_loop;
                END IF;

                SELECT SUM(total_with_vat) INTO related_debit_credit_sum
                FROM fin_incomes_reasons AS fir, fin_reasons_relatives AS frr
                WHERE frr.link_to = related_reason_id
                  AND fir.status = "finished"
                  AND fir.id = frr.parent_id
                  AND frr.parent_model_name = "Finance_Incomes_Reason"
                  AND frr.link_to_model_name = "Finance_Incomes_Reason"
                  AND fir.annulled_by=0 AND fir.type IN (3,4);

                SET calculated_invoiced_amount=ROUND((calculated_invoiced_amount+((IF (related_reason_total IS NULL, 0, related_reason_total)) + (IF (related_debit_credit_sum IS NULL, 0, related_debit_credit_sum)))), 6);

                SET num_invoices = num_invoices + 1;
            END LOOP;

            SET difference_invoiced_amount = incomes_reason_total - calculated_invoiced_amount;

            IF (difference_invoiced_amount<=0 AND num_invoices>0) THEN
                SET new_invoiced_status = 'invoiced';
            ELSEIF (difference_invoiced_amount<incomes_reason_total AND difference_invoiced_amount>0) THEN
                SET new_invoiced_status = 'partial';
            ELSEIF (difference_invoiced_amount>=incomes_reason_total) THEN
                SET new_invoiced_status = 'not_invoiced';
            END IF;
        ELSE
            SET new_invoiced_status = 'not_invoicable';
        END IF;

        return new_invoiced_status;
    END;//
delimiter ;

######################################################################################
# 2023-10-24 - Settings for 'bgservice_feedback_analysis' report transffered from the code to the DB

# Settings for 'bgservice_feedback_analysis' report transffered from the code to the DB
UPDATE `reports` SET `settings`='doc_type_feedback := 100\r\nfeedback_date := feedback_date\r\nppp_worker := ppp_worker_id\r\nfeedback_people := feedback_people\r\nppp_type_service := activity_id\r\nfeedback_work := feedback_work\r\nfeedback_type := feedback_type'
WHERE `type`='bgservice_feedback_analysis' AND (`settings`='' OR `settings` IS NULL);

######################################################################################
# 2023-10-25 - Added new setting flag to mark if unfinished contracts will be used in 'hr_employee_file' report

# Added new setting flag to mark if unfinished contracts will be used in 'hr_employee_file' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nworking_contract_model :=', '\r\nuse_unfinished_contracts :=\r\nworking_contract_model :=') WHERE `type`='hr_employee_file' AND `settings` NOT LIKE '%use_unfinished_contracts%';

######################################################################################
# 2023-10-27 - Added new setting in 'issueProtocolFromOrder' automation for checking relations with certain incomes reasons
#            - Added new setting for the 'unite_invoices' report that allows the source documents which will be merged to be tagged

# Added new setting in 'issueProtocolFromOrder' automation for checking relations with certain incomes reasons
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\noutgoing_warehouse :=', '\r\nrelated_reason_types_prevent_ppp_edit :=\r\noutgoing_warehouse :=') WHERE `method` LIKE '%issueProtocolFromOrder%' AND `settings` NOT LIKE '%related_reason_types_prevent_ppp_edit%';

# Added new setting for the 'unite_invoices' report that allows the source documents which will be merged to be tagged
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ntag_source_reasons_after_merge :=') WHERE `type`='unite_invoices' AND `settings` NOT LIKE '%tag_source_reasons_after_merge%';

######################################################################################
# 2023-12-05 - Added new settings for the 'merge_orders' report that will prevent add of certain document if its total is over sum in the setting

# Added new settings for the 'merge_orders' report that will prevent add of certain document if its total is over sum in the setting
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nprevent_documents_add :=\r\nprevent_documents_add_sum :=') WHERE `type`='merge_orders' AND `settings` NOT LIKE '%prevent_documents_add%';

######################################################################################
# 2023-12-06 - Added new settings for home office documents in 'hr_who_is_resting' report

# Added new settings for home office documents in 'hr_who_is_resting' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nemployee_responsible_for_department :=', '\r\n\r\nhome_office_document_id :=\r\nhome_office_document_start_date :=\r\nhome_office_document_end_date :=\r\nhome_office_approve_status :=\r\n\r\nemployee_responsible_for_department :=') WHERE `type`='hr_who_is_resting' AND `settings` NOT LIKE '%home_office_document_id%';

######################################################################################
# 2023-12-11 - Added new settings for including profits in 'sales' report

# Added new settings for including profits in 'sales' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nfreeze_table_headers :=', '\r\ninclude_show_profits_filter :=\r\n\r\nfreeze_table_headers :=') WHERE `type`='sales' AND `settings` NOT LIKE '%include_show_profits_filter%';

######################################################################################
# 2023-12-12 - Added new settings for including profits in 'sales' report where the new setting is not already added

# Added new settings for including profits in 'sales' report where the new setting is not already added
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ninclude_show_profits_filter :=') WHERE `type`='sales' AND `settings` NOT LIKE '%include_show_profits_filter%';

######################################################################################
# 2023-12-14 - Removed 2024-05-07 as a holiday, it's a regular work day

# Removed 2024-05-07 as a holiday, it's a regular work day
DELETE FROM country_nonworkdays WHERE DATE='2024-05-07';

######################################################################################
# 2023-12-28 - Added new settings for damage commission in 'aon_upcoming_renewals' report

# Added new settings for damage commission in 'aon_upcoming_renewals' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncar_nom_type_id :=', '\r\ninsurance_damage_percent := commission_damage\r\ninsurance_damage_value := commission_damage__valuelocal\r\n\r\ncar_nom_type_id :=') WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%insurance_damage_percent%';
