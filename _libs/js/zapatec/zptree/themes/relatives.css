/* This is a copy of the default.css, edited according to the needs of the nZoom tree */
.zpTreeRelativesContainer .tree,
.zpTreeRelativesContainer .tree-item table.tree-table{
  font: 11px tahoma,verdana,sans-serif;
}

.zpTreeRelativesContainer .tree {
  padding-left: 15px;
}

.zpTreeRelativesContainer .tree-top {
  padding-left: 0;
}

.zpTreeRelativesContainer .tree-item { 
  padding-left: 17px;
}

.zpTreeRelativesContainer .tree-item-more {
  padding-left: 1px;
}

.zpTreeRelativesContainer .tree-item td.label {
  cursor: pointer;
  cursor: hand;
}

.zpTreeRelativesContainer .tree-item table.tree-table td.label{
  padding: 0 2px;
}

.zpTreeRelativesContainer .tree-item .tgb {
  width: 9px; 
  height: 9px; 
  cursor: default;
}

.zpTreeRelativesContainer .tree-item .plus {
  padding-left: 0px !important;
  padding-right: 7px;
}

.zpTreeRelativesContainer .tree-item .minus {
  padding-left: 0px !important;
  padding-right: 7px;
}

.zpTreeRelativesContainer .tree-item .fetching {
  padding-left: 0px !important;
  padding-right: 7px;
}

.zpTreeRelativesContainer .tree-item .icon {
  cursor: pointer; 
  cursor: hand;
  width: 18px; 
  height: 18px; 
  text-align: center; 
}

.zpTreeRelativesContainer .tree-item-selected table.tree-table td.label {
  background-color: #0A246A; 
  color: #fff;
}

.zpTreeRelativesContainer .checkboxContainer {
  cursor: pointer; 
  cursor: hand;
  width: 12px; 
  height: 12px; 
  text-align: center; 
  background-repeat: no-repeat;
  background-position: 1px 50%;
}


.zpTreeRelativesContainer .tree-lines-t .checkboxContainer,
.zpTreeRelativesContainer .tree-lines-t .tgb {
  background-position: 1px top !important;
  vertical-align: top;
} 

.zpTreeRelativesContainer .tree-lines-b .checkboxContainer,
.zpTreeRelativesContainer .tree-lines-b .tgb {
  background-position: 1px bottom !important;
  vertical-align: bottom;
}

.tree_container {
  padding-left: 0px !important;
}
