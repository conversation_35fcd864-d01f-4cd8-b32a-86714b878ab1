/*
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/
.cke_dialog {
    visibility: visible
}

.cke_dialog_body {
    z-index: 1;
    background: var(--background-color);
    border-radius: var(--border-radius);
    box-shadow: 0 0 1rem rgba(0, 0, 0, .3)
}

.cke_dialog strong {
    font-weight: bold
}

.cke_dialog_title {
    font-weight: bold;
    font-size: 0.875rem;
    cursor: move;
    position: relative;
    color: var(--text-color);
    padding: 1rem 1rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    background: var(--background-color);
}

.cke_dialog_contents {
    background-color: #fff;
    overflow: auto;
    padding: 1rem 1.25rem 0.5rem 1rem;
    margin-top: 3.1rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius)
}

.cke_dialog_contents_body {
    overflow: auto;
    padding: 1rem;
    margin-top: 1.5rem
}

.cke_dialog_footer {
    text-align: right;
    position: relative;
    border: 0;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    background: var(--background-color);

}

.cke_rtl .cke_dialog_footer {
    text-align: left
}

.cke_hc .cke_dialog_footer {
    outline: 0;
}

.cke_dialog .cke_resizer {
    margin-top: 2.5rem
}

.cke_dialog .cke_resizer_rtl {
    margin-left: 0.5rem
}

.cke_dialog .cke_resizer_ltr {
    margin-right: 0.5rem
}

.cke_dialog_tabs {
    height: 3.1rem;
    display: inline-block;
    margin: 0.5rem 0 0;
    position: absolute;
    z-index: 2;
    left: 0.75rem
}

.cke_rtl .cke_dialog_tabs {
    right: 0.75rem
}

a.cke_dialog_tab {
    height: 1rem;
    padding: 0.75rem 0.5rem;
    margin-right: 0.25rem;
    display: inline-block;
    cursor: pointer;
    line-height: 16px;
    outline: 0;
    color: var(--text-color);
    border: 1px solid var(--border-input-color);
    border-bottom: none;
    border-radius: 0.5rem 0.5rem 0 0;
    background: var(--altbackground-color);
}

.cke_rtl a.cke_dialog_tab {
    margin-right: 0;
    margin-left: 0.5rem
}

a.cke_dialog_tab:hover {
    background: var(--primary-color);
    color: var(--text-onprimary-color);
    border-color: var(--primary-color);
}

a.cke_dialog_tab_selected {
    background: var(--background-color);
    color: var(--primary-color);
    border-bottom-color: #fff;
    cursor: default;
    filter: none
}

a.cke_dialog_tab_selected:hover {
    border-color: var(--primary-color);
    background: var(--background-color);
    color: var(--primary-color);
}

.cke_hc a.cke_dialog_tab:hover, .cke_hc a.cke_dialog_tab_selected {
    border: 3px solid;
    padding: 2px 6px
}

a.cke_dialog_tab_disabled {
    color: #bababa;
    cursor: default
}

.cke_single_page .cke_dialog_tabs {
    display: none
}

.cke_single_page .cke_dialog_contents {
    padding-top: 1rem;
    margin-top: 0;
    border-top: 0
}

.cke_dialog_close_button {
    background-image: url(images/close.png);
    background-repeat: no-repeat;
    background-position: 50%;
    position: absolute;
    cursor: pointer;
    text-align: center;
    height: 20px;
    width: 20px;
    top: 5px;
    z-index: 5;
    opacity: .8;
    filter: alpha(opacity=80)
}

.cke_dialog_close_button:hover {
    opacity: 1;
    filter: alpha(opacity=100)
}

.cke_hidpi .cke_dialog_close_button {
    background-image: url(images/hidpi/close.png);
    background-size: 16px
}

.cke_dialog_close_button span {
    display: none
}

.cke_hc .cke_dialog_close_button span {
    display: inline;
    cursor: pointer;
    font-weight: bold;
    position: relative;
    top: 3px
}

.cke_ltr .cke_dialog_close_button {
    right: 5px
}

.cke_rtl .cke_dialog_close_button {
    left: 6px
}

.cke_dialog_close_button {
    top: 4px
}

.cke_dialog_ui_labeled_content {

}

div.cke_disabled .cke_dialog_ui_labeled_content div * {

    cursor: default;
    opacity: 0.7
}

.cke_dialog_ui_vbox table, .cke_dialog_ui_hbox table {
    margin: auto
}

.cke_dialog_ui_vbox_child {
    padding: 5px 0
}

.cke_dialog_ui_hbox {
    width: 100%
}

.cke_dialog_ui_hbox_first, .cke_dialog_ui_hbox_child, .cke_dialog_ui_hbox_last {
    vertical-align: top
}

.cke_ltr .cke_dialog_ui_hbox_first, .cke_ltr .cke_dialog_ui_hbox_child {
    padding-right: 10px
}

.cke_rtl .cke_dialog_ui_hbox_first, .cke_rtl .cke_dialog_ui_hbox_child {
    padding-left: 10px
}

.cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first, .cke_ltr .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child {
    padding-right: 5px
}

.cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_first, .cke_rtl .cke_dialog_footer_buttons .cke_dialog_ui_hbox_child {
    padding-left: 5px;
    padding-right: 0
}

.cke_hc div.cke_dialog_ui_input_text, .cke_hc div.cke_dialog_ui_input_password, .cke_hc div.cke_dialog_ui_input_textarea, .cke_hc div.cke_dialog_ui_input_select, .cke_hc div.cke_dialog_ui_input_file {
    border: 1px solid
}

textarea.cke_dialog_ui_input_textarea {
    overflow: auto;
    resize: none
}

input.cke_dialog_ui_input_text, input.cke_dialog_ui_input_password, textarea.cke_dialog_ui_input_textarea {
    background-color: var(--background-color);
    border: 1px solid var(--border-input-color);
    padding: 0.5rem;
    width: 100%;
    *width: 95%;
    box-sizing: border-box;
    border-radius: 0.25rem;
}

input.cke_dialog_ui_input_text:hover, input.cke_dialog_ui_input_password:hover, textarea.cke_dialog_ui_input_textarea:hover {
}

input.cke_dialog_ui_input_text:focus, input.cke_dialog_ui_input_password:focus, textarea.cke_dialog_ui_input_textarea:focus, select.cke_dialog_ui_input_select:focus {

}

a.cke_dialog_ui_button {
    display: inline-block;
    padding: 0.5rem 0.75rem;
    margin: 1px;
    text-align: center;
    color: var(--text-onprimary-color);
    vertical-align: middle;
    cursor: pointer;
    border-radius: 2rem;
    background: var(--primary-color);
    box-sizing: border-box;
}

span.cke_dialog_ui_button {
    color: var(--text-onprimary-color);
}

a.cke_dialog_ui_button:hover {
    box-shadow: 0px 0px 0px 0px rgb(0 0 0 / 20%), 0px 0px 0px 0px rgb(0 0 0 / 14%), 0px 0px 0px 0px rgb(0 0 0 / 12%);
}

a.cke_dialog_ui_button:focus,
a.cke_dialog_ui_button:active {
    outline: 0;
    box-shadow: 0 0 0.5rem rgba(0, 0, 0, .4)
}

.cke_hc a.cke_dialog_ui_button:hover, .cke_hc a.cke_dialog_ui_button:focus, .cke_hc a.cke_dialog_ui_button:active {
    border: 3px solid;
    padding-top: 1px;
    padding-bottom: 1px
}

.cke_hc a.cke_dialog_ui_button:hover span, .cke_hc a.cke_dialog_ui_button:focus span, .cke_hc a.cke_dialog_ui_button:active span {
    padding-left: 10px;
    padding-right: 10px
}

.cke_dialog_footer_buttons a.cke_dialog_ui_button span {
    color: inherit;
    font-size: 12px;
    font-weight: bold;
    line-height: 18px;
    padding: 0 12px
}

a.cke_dialog_ui_button_ok {
    color: var(--text-onaccent-color);;
    background: var(--accent-color);
}


a.cke_dialog_ui_button_ok:hover {
    box-shadow: 0px 0px 0px 0px rgb(0 0 0 / 20%), 0px 0px 0px 0px rgb(0 0 0 / 14%), 0px 0px 0px 0px rgb(0 0 0 / 12%);
}

a.cke_dialog_ui_button span {
}

a.cke_dialog_ui_button_ok span {
}

span.cke_dialog_ui_button {
    cursor: pointer
}

a.cke_dialog_ui_button_ok:focus, a.cke_dialog_ui_button_ok:active, a.cke_dialog_ui_button_cancel:focus, a.cke_dialog_ui_button_cancel:active {
    box-shadow: 0px 0px 0px 0px rgb(0 0 0 / 20%), 0px 0px 0px 0px rgb(0 0 0 / 14%), 0px 0px 0px 0px rgb(0 0 0 / 12%);
}

a.cke_dialog_ui_button_ok:focus, a.cke_dialog_ui_button_ok:active {
}

a.cke_dialog_ui_button_ok:focus span, a.cke_dialog_ui_button_ok:active span, a.cke_dialog_ui_button_cancel:focus span, a.cke_dialog_ui_button_cancel:active span {

}

.cke_dialog_footer_buttons {
    display: inline-table;
    margin: 1rem;
    width: auto;
    position: relative;
    vertical-align: middle
}

div.cke_dialog_ui_input_select {
    display: table
}

select.cke_dialog_ui_input_select {
    height: 25px;
    line-height: 25px;
    background-color: var(--background-color);
    border: 1px solid var(--border-input-color);
    padding: 0.25rem 0.5rem;
    outline: 0;
    border-radius: 0.25rem;
}

.cke_dialog_ui_input_file {
    width: 100%;
    height: 1.5rem
}

.cke_hc .cke_dialog_ui_labeled_content input:focus,
.cke_hc .cke_dialog_ui_labeled_content select:focus,
.cke_hc .cke_dialog_ui_labeled_content textarea:focus {
    outline: 1px dotted
}

.cke_dialog .cke_dark_background {
    background-color: #dedede
}

.cke_dialog .cke_light_background {
    background-color: #ebebeb
}

.cke_dialog .cke_centered {
    text-align: center
}

.cke_dialog a.cke_btn_reset {
    float: right;
    background: url(images/refresh.png) top left no-repeat;
    width: 16px;
    height: 16px;
    border: 1px none;
    font-size: 1px
}

.cke_hidpi .cke_dialog a.cke_btn_reset {
    background-size: 16px;
    background-image: url(images/hidpi/refresh.png)
}

.cke_rtl .cke_dialog a.cke_btn_reset {
    float: left
}

.cke_dialog a.cke_btn_locked, .cke_dialog a.cke_btn_unlocked {
    float: left;
    width: 16px;
    height: 16px;
    background-repeat: no-repeat;
    border: none 1px;
    font-size: 1px
}

.cke_dialog a.cke_btn_locked .cke_icon {
    display: none
}

.cke_rtl .cke_dialog a.cke_btn_locked, .cke_rtl .cke_dialog a.cke_btn_unlocked {
    float: right
}

.cke_dialog a.cke_btn_locked {
    background-image: url(images/lock.png)
}

.cke_dialog a.cke_btn_unlocked {
    background-image: url(images/lock-open.png)
}

.cke_hidpi .cke_dialog a.cke_btn_unlocked, .cke_hidpi .cke_dialog a.cke_btn_locked {
    background-size: 16px
}

.cke_hidpi .cke_dialog a.cke_btn_locked {
    background-image: url(images/hidpi/lock.png)
}

.cke_hidpi .cke_dialog a.cke_btn_unlocked {
    background-image: url(images/hidpi/lock-open.png)
}

.cke_dialog .cke_btn_over {
    border: outset 1px;
    cursor: pointer
}

.cke_dialog .ImagePreviewBox {
    border: 1px solid var(--border-input-color);
    overflow: scroll;
    height: 314px;
    width: 300px;
    padding: 2px;
    background-color: white
}

.cke_dialog .ImagePreviewBox table td {
    white-space: normal
}

.cke_dialog .ImagePreviewLoader {
    position: absolute;
    white-space: normal;
    overflow: hidden;
    height: 160px;
    width: 230px;
    margin: 2px;
    padding: 2px;
    opacity: .9;
    filter: alpha(opacity=90);
    background-color: #e4e4e4
}

.cke_dialog .FlashPreviewBox {
    white-space: normal;
    border: 2px ridge black;
    overflow: auto;
    height: 160px;
    width: 390px;
    padding: 2px;
    background-color: white
}

.cke_dialog .cke_pastetext {
    width: 346px;
    height: 170px
}

.cke_dialog .cke_pastetext textarea {
    width: 340px;
    height: 170px;
    resize: none
}

.cke_dialog iframe.cke_pasteframe {
    width: 346px;
    height: 130px;
    background-color: white;
    border: 1px solid #aeb3b9;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px
}

.cke_dialog .cke_hand {
    cursor: pointer
}

.cke_disabled {
    color: #a0a0a0
}

.cke_dialog_body .cke_label {
    display: none
}

.cke_dialog_body label {
    display: inline;
    margin-bottom: auto;
    cursor: default
}

.cke_dialog_body label.cke_required {
    font-weight: bold
}

a.cke_smile {
    overflow: hidden;
    display: block;
    text-align: center;
    padding: .3em 0
}

a.cke_smile img {
    vertical-align: middle
}

a.cke_specialchar {
    cursor: inherit;
    display: block;
    height: 1.25em;
    padding: .2em .3em;
    text-align: center
}

a.cke_smile, a.cke_specialchar {
    border: 1px solid transparent
}

a.cke_smile:hover, a.cke_smile:focus, a.cke_smile:active, a.cke_specialchar:hover, a.cke_specialchar:focus, a.cke_specialchar:active {
    background: #fff;
    outline: 0
}

a.cke_smile:hover, a.cke_specialchar:hover {
    border-color: #888
}

a.cke_smile:focus, a.cke_smile:active, a.cke_specialchar:focus, a.cke_specialchar:active {
    border-color: #139ff7
}

.cke_dialog_contents a.colorChooser {
    display: block;
    margin-top: 6px;
    margin-left: 10px;
    width: 80px
}

.cke_rtl .cke_dialog_contents a.colorChooser {
    margin-right: 10px
}

.cke_dialog_ui_checkbox_input:focus, .cke_dialog_ui_radio_input:focus, .cke_btn_over {
    outline: 1px dotted #696969
}

.cke_iframe_shim {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    filter: alpha(opacity=0);
    width: 100%;
    height: 100%
}
