<?php
$GLOBALS['g_unicode_glyphs'] = array(
0x0001 => 'controlSTX',
0x0002 => 'controlSOT',
0x0003 => 'controlETX',
0x0004 => 'controlEOT',
0x0005 => 'controlENQ',
0x0006 => 'controlACK',
0x0007 => 'controlBEL',
0x0008 => 'controlBS',
0x0009 => 'controlHT',
0x000A => 'controlLF',
0x000B => 'controlVT',
0x000C => 'controlFF',
0x000D => 'controlCR',
0x000E => 'controlSO',
0x000F => 'controlSI',
0x0010 => 'controlDLE',
0x0011 => 'controlDC1',
0x0012 => 'controlDC2',
0x0013 => 'controlDC3',
0x0014 => 'controlDC4',
0x0015 => 'controlNAK',
0x0016 => 'controlSYN',
0x0017 => 'controlETB',
0x0018 => 'controlCAN',
0x0019 => 'controlEM',
0x001A => 'controlSUB',
0x001B => 'controlESC',
0x001C => 'controlFS',
0x001D => 'controlGS',
0x001E => 'controlRS',
0x001F => 'controlUS',
0x0020 => 'space',
0x0021 => 'exclam',
0x0022 => 'quotedbl',
0x0023 => 'numbersign',
0x0024 => 'dollar',
0x0025 => 'percent',
0x0026 => 'ampersand',
0x0027 => 'quotesingle',
0x0028 => 'parenleft',
0x0029 => 'parenright',
0x002A => 'asterisk',
0x002B => 'plus',
0x002C => 'comma',
0x002D => 'hyphen',
0x002E => 'period',
0x002F => 'slash',
0x0030 => 'zero',
0x0031 => 'one',
0x0032 => 'two',
0x0033 => 'three',
0x0034 => 'four',
0x0035 => 'five',
0x0036 => 'six',
0x0037 => 'seven',
0x0038 => 'eight',
0x0039 => 'nine',
0x003A => 'colon',
0x003B => 'semicolon',
0x003C => 'less',
0x003D => 'equal',
0x003E => 'greater',
0x003F => 'question',
0x0040 => 'at',
0x0041 => 'A',
0x0042 => 'B',
0x0043 => 'C',
0x0044 => 'D',
0x0045 => 'E',
0x0046 => 'F',
0x0047 => 'G',
0x0048 => 'H',
0x0049 => 'I',
0X004A => 'J',
0X004B => 'K',
0X004C => 'L',
0X004D => 'M',
0X004E => 'N',
0X004F => 'O',
0x0050 => 'P',
0x0051 => 'Q',
0x0052 => 'R',
0x0053 => 'S',
0x0054 => 'T',
0x0055 => 'U',
0x0056 => 'V',
0x0057 => 'W',
0x0058 => 'X',
0x0059 => 'Y',
0X005A => 'Z',
0x005B => 'bracketleft',
0x005C => 'backslash',
0x005D => 'bracketright',
0x005E => 'asciicircum',
0x005F => 'underscore',
0x0060 => 'grave',
0x0061 => 'a',
0x0062 => 'b',
0x0063 => 'c',
0x0064 => 'd',
0x0065 => 'e',
0x0066 => 'f',
0x0067 => 'g',
0x0068 => 'h',
0x0069 => 'i',
0x006A => 'j',
0x006B => 'k',
0x006C => 'l',
0x006D => 'm',
0x006E => 'n',
0x006F => 'o',
0x0070 => 'p',
0x0071 => 'q',
0x0072 => 'r',
0x0073 => 's',
0x0074 => 't',
0x0075 => 'u',
0x0076 => 'v',
0x0077 => 'w',
0x0078 => 'x',
0x0079 => 'y',
0x007A => 'z',
0x007B => 'braceleft',
0x007C => 'bar',
0x007D => 'braceright',
0x007E => 'asciitilde',
0x007F => 'controlDEL',
0x00A0 => 'nonbreakingspace',
0x00A1 => 'exclamdown',
0x00A2 => 'cent',
0x00A3 => 'sterling',
0x00A4 => 'currency',
0x00A5 => 'yen',
0x00A6 => 'brokenbar',
0x00A7 => 'section',
0x00A8 => 'dieresis',
0x00A9 => 'copyright',
0x00AA => 'ordfeminine',
0x00AB => 'guillemotleft',
0x00AC => 'logicalnot',
0x00AD => 'softhyphen',
0x00AE => 'registered',
0x00AF => 'overscore',
0x00B0 => 'degree',
0x00B1 => 'plusminus',
0x00B2 => 'twosuperior',
0x00B3 => 'threesuperior',
0x00B4 => 'acute',
0x00B5 => 'mu',
0x00B6 => 'paragraph',
0x00B7 => 'middot',
0x00B8 => 'cedilla',
0x00B9 => 'onesuperior',
0x00BA => 'ordmasculine',
0x00BB => 'guillemotright',
0x00BC => 'onequarter',
0x00BD => 'onehalf',
0x00BE => 'threequarters',
0x00BF => 'questiondown',
0x00C0 => 'Agrave',
0x00C1 => 'Aacute',
0x00C2 => 'Acircumflex',
0x00C3 => 'Atilde',
0x00C4 => 'Adieresis',
0x00C5 => 'Aring',
0X00C6 => 'AE',
0x00C7 => 'Ccedilla',
0x00C8 => 'Egrave',
0x00C9 => 'Eacute',
0x00CA => 'Ecircumflex',
0x00CB => 'Edieresis',
0x00CC => 'Igrave',
0x00CD => 'Iacute',
0x00CE => 'Icircumflex',
0x00CF => 'Idieresis',
0x00D0 => 'Eth',
0x00D1 => 'Ntilde',
0x00D2 => 'Ograve',
0x00D3 => 'Oacute',
0x00D4 => 'Ocircumflex',
0x00D5 => 'Otilde',
0x00D6 => 'Odieresis',
0x00D7 => 'multiply',
0x00D8 => 'Oslash',
0x00D9 => 'Ugrave',
0x00DA => 'Uacute',
0x00DB => 'Ucircumflex',
0x00DC => 'Udieresis',
0x00DD => 'Yacute',
0x00DE => 'Thorn',
0x00DF => 'germandbls',
0x00E0 => 'agrave',
0x00E1 => 'aacute',
0x00E2 => 'acircumflex',
0x00E3 => 'atilde',
0x00E4 => 'adieresis',
0x00E5 => 'aring',
0x00E6 => 'ae',
0x00E7 => 'ccedilla',
0x00E8 => 'egrave',
0x00E9 => 'eacute',
0x00EA => 'ecircumflex',
0x00EB => 'edieresis',
0x00EC => 'igrave',
0x00ED => 'iacute',
0x00EE => 'icircumflex',
0x00EF => 'idieresis',
0x00F0 => 'eth',
0x00F1 => 'ntilde',
0x00F2 => 'ograve',
0x00F3 => 'oacute',
0x00F4 => 'ocircumflex',
0x00F5 => 'otilde',
0x00F6 => 'odieresis',
0x00F7 => 'divide',
0x00F8 => 'oslash',
0x00F9 => 'ugrave',
0x00FA => 'uacute',
0x00FB => 'ucircumflex',
0x00FC => 'udieresis',
0x00FD => 'yacute',
0x00FE => 'thorn',
0x00FF => 'ydieresis',
0x0100 => 'Amacron',
0x0101 => 'amacron',
0x0102 => 'Abreve',
0x0103 => 'abreve',
0x0104 => 'Aogonek',
0x0105 => 'aogonek',
0x0106 => 'Cacute',
0x0107 => 'cacute',
0x0108 => 'Ccircumflex',
0x0109 => 'ccircumflex',
0x010A => 'Cdot',
0x010B => 'cdot',
0x010C => 'Ccaron',
0x010D => 'ccaron',
0x010E => 'Dcaron',
0x010F => 'dcaron',
0x0110 => 'Dcroat',
0x0110 => 'Dslash',
0x0111 => 'dcroat',
0x0111 => 'dmacron',
0x0112 => 'Emacron',
0x0113 => 'emacron',
0x0114 => 'Ebreve',
0x0115 => 'ebreve',
0x0116 => 'Edot',
0x0117 => 'edot',
0x0118 => 'Eogonek',
0x0119 => 'eogonek',
0x011A => 'Ecaron',
0x011B => 'ecaron',
0x011C => 'Gcircumflex',
0x011D => 'gcircumflex',
0x011E => 'Gbreve',
0x011F => 'gbreve',
0x0120 => 'Gdot',
0x0121 => 'gdot',
0x0122 => 'Gcedilla',
0x0123 => 'gcedilla',
0x0124 => 'Hcircumflex',
0x0125 => 'hcircumflex',
0x0126 => 'Hbar',
0x0127 => 'hbar',
0x0128 => 'Itilde',
0x0129 => 'itilde',
0x012A => 'Imacron',
0x012B => 'imacron',
0x012C => 'Ibreve',
0x012D => 'ibreve',
0x012E => 'Iogonek',
0x012F => 'iogonek',
0x0130 => 'Idot',
0x0131 => 'dotlessi',
0X0132 => 'IJ',
0x0133 => 'ij',
0x0134 => 'Jcircumflex',
0x0135 => 'jcircumflex',
0x0136 => 'Kcedilla',
0x0137 => 'kcedilla',
0x0138 => 'kgreenlandic',
0x0139 => 'Lacute',
0x013A => 'lacute',
0x013B => 'Lcedilla',
0x013C => 'lcedilla',
0x013D => 'Lcaron',
0x013E => 'lcaron',
0x013F => 'Ldot',
0x0140 => 'ldot',
0x0141 => 'Lslash',
0x0142 => 'lslash',
0x0143 => 'Nacute',
0x0144 => 'nacute',
0x0145 => 'Ncedilla',
0x0146 => 'ncedilla',
0x0147 => 'Ncaron',
0x0148 => 'ncaron',
0x0149 => 'napostrophe',
0x014A => 'Eng',
0x014B => 'eng',
0x014C => 'Omacron',
0x014D => 'omacron',
0x014E => 'Obreve',
0x014F => 'obreve',
0x0150 => 'Odblacute',
0x0151 => 'odblacute',
0X0152 => 'OE',
0x0153 => 'oe',
0x0154 => 'Racute',
0x0155 => 'racute',
0x0156 => 'Rcedilla',
0x0157 => 'rcedilla',
0x0158 => 'Rcaron',
0x0159 => 'rcaron',
0x015A => 'Sacute',
0x015B => 'sacute',
0x015C => 'Scircumflex',
0x015D => 'scircumflex',
0x015E => 'Scedilla',
0x015F => 'scedilla',
0x0160 => 'Scaron',
0x0161 => 'scaron',
0x0162 => 'Tcedilla',
0x0163 => 'tcedilla',
0x0164 => 'Tcaron',
0x0165 => 'tcaron',
0x0166 => 'Tbar',
0x0167 => 'tbar',
0x0168 => 'Utilde',
0x0169 => 'utilde',
0x016A => 'Umacron',
0x016B => 'umacron',
0x016C => 'Ubreve',
0x016D => 'ubreve',
0x016E => 'Uring',
0x016F => 'uring',
0x0170 => 'Udblacute',
0x0171 => 'udblacute',
0x0172 => 'Uogonek',
0x0173 => 'uogonek',
0x0174 => 'Wcircumflex',
0x0175 => 'wcircumflex',
0x0176 => 'Ycircumflex',
0x0177 => 'ycircumflex',
0x0178 => 'Ydieresis',
0x0179 => 'Zacute',
0x017A => 'zacute',
0x017B => 'Zdot',
0x017C => 'zdot',
0x017D => 'Zcaron',
0x017E => 'zcaron',
0x017F => 'slong',
0x0180 => 'bstroke',
0x0181 => 'Bhook',
0x0182 => 'Btopbar',
0x0183 => 'btopbar',
0x0184 => 'Tonesix',
0x0185 => 'tonesix',
0x0186 => 'Oopen',
0x0187 => 'Chook',
0x0188 => 'chook',
0x0189 => 'Dafrican',
0x018A => 'Dhook',
0x018B => 'Dtopbar',
0x018C => 'dtopbar',
0x018D => 'deltaturned',
0x018E => 'Ereversed',
0x018F => 'Schwa',
0x0190 => 'Eopen',
0x0191 => 'Fhook',
0x0192 => 'florin',
0x0193 => 'Ghook',
0x0194 => 'Gammaafrican',
0x0195 => 'hv',
0x0196 => 'Iotaafrican',
0x0197 => 'Istroke',
0x0198 => 'Khook',
0x0199 => 'khook',
0x019A => 'lbar',
0x019B => 'lambdastroke',
0x019C => 'Mturned',
0x019D => 'Nhookleft',
0x019E => 'nlegrightlong',
0x019F => 'Ocenteredtilde',
0x01A0 => 'Ohorn',
0x01A1 => 'ohorn',
0x01A2 => 'Oi',
0x01A3 => 'oi',
0x01A4 => 'Phook',
0x01A5 => 'phook',
0x01A6 => 'yr',
0x01A7 => 'Tonetwo',
0x01A8 => 'tonetwo',
0x01A9 => 'Esh',
0x01AA => 'eshreversedloop',
0x01AB => 'tpalatalhook',
0x01AC => 'Thook',
0x01AD => 'thook',
0x01AE => 'Tretroflexhook',
0x01AF => 'Uhorn',
0x01B0 => 'uhorn',
0x01B1 => 'Upsilonafrican',
0x01B2 => 'Vhook',
0x01B3 => 'Yhook',
0x01B4 => 'yhook',
0x01B5 => 'Zstroke',
0x01B6 => 'zstroke',
0x01B7 => 'Ezh',
0x01B8 => 'Ezhreversed',
0x01B9 => 'ezhreversed',
0x01BA => 'ezhtail',
0x01BB => 'twostroke',
0x01BC => 'Tonefive',
0x01BD => 'tonefive',
0x01BE => 'glottalinvertedstroke',
0x01BF => 'wynn',
0x01C0 => 'clickdental',
0x01C1 => 'clicklateral',
0x01C2 => 'clickalveolar',
0x01C3 => 'clickretroflex',
0x01C4 => 'DZcaron',
0x01C5 => 'Dzcaron',
0x01C6 => 'dzcaron',
0X01C7 => 'LJ',
0x01C8 => 'Lj',
0x01C9 => 'lj',
0X01CA => 'NJ',
0x01CB => 'Nj',
0x01CC => 'nj',
0x01CD => 'Acaron',
0x01CE => 'acaron',
0x01CF => 'Icaron',
0x01D0 => 'icaron',
0x01D1 => 'Ocaron',
0x01D2 => 'ocaron',
0x01D3 => 'Ucaron',
0x01D4 => 'ucaron',
0x01D5 => 'Udieresismacron',
0x01D6 => 'udieresismacron',
0x01D7 => 'Udieresisacute',
0x01D8 => 'udieresisacute',
0x01D9 => 'Udieresiscaron',
0x01DA => 'udieresiscaron',
0x01DB => 'Udieresisgrave',
0x01DC => 'udieresisgrave',
0x01DD => 'eturned',
0x01DE => 'Adieresismacron',
0x01DF => 'adieresismacron',
0x01E0 => 'Adotmacron',
0x01E1 => 'adotmacron',
0x01E2 => 'AEmacron',
0x01E3 => 'aemacron',
0x01E4 => 'Gstroke',
0x01E5 => 'gstroke',
0x01E6 => 'Gcaron',
0x01E7 => 'gcaron',
0x01E8 => 'Kcaron',
0x01E9 => 'kcaron',
0x01EA => 'Oogonek',
0x01EB => 'oogonek',
0x01EC => 'Oogonekmacron',
0x01ED => 'oogonekmacron',
0x01EE => 'Ezhcaron',
0x01EF => 'ezhcaron',
0x01F0 => 'jcaron',
0X01F1 => 'DZ',
0x01F2 => 'Dz',
0x01F3 => 'dz',
0x01F4 => 'Gacute',
0x01F5 => 'gacute',
0x01FA => 'Aringacute',
0x01FB => 'aringacute',
0x01FC => 'AEacute',
0x01FD => 'aeacute',
0x01FE => 'Oslashacute',
0x01FF => 'oslashacute',
0x0200 => 'Adblgrave',
0x0201 => 'adblgrave',
0x0202 => 'Ainvertedbreve',
0x0203 => 'ainvertedbreve',
0x0204 => 'Edblgrave',
0x0205 => 'edblgrave',
0x0206 => 'Einvertedbreve',
0x0207 => 'einvertedbreve',
0x0208 => 'Idblgrave',
0x0209 => 'idblgrave',
0x020A => 'Iinvertedbreve',
0x020B => 'iinvertedbreve',
0x020C => 'Odblgrave',
0x020D => 'odblgrave',
0x020E => 'Oinvertedbreve',
0x020F => 'oinvertedbreve',
0x0210 => 'Rdblgrave',
0x0211 => 'rdblgrave',
0x0212 => 'Rinvertedbreve',
0x0213 => 'rinvertedbreve',
0x0214 => 'Udblgrave',
0x0215 => 'udblgrave',
0x0216 => 'Uinvertedbreve',
0x0217 => 'uinvertedbreve',
0x0218 => 'Scommaaccent',
0x0219 => 'scommaaccent',
0x0250 => 'aturned',
0x0251 => 'ascript',
0x0252 => 'ascriptturned',
0x0253 => 'bhook',
0x0254 => 'oopen',
0x0255 => 'ccurl',
0x0256 => 'dtail',
0x0257 => 'dhook',
0x0258 => 'ereversed',
0x0259 => 'schwa',
0x025A => 'schwahook',
0x025B => 'eopen',
0x025C => 'eopenreversed',
0x025D => 'eopenreversedhook',
0x025E => 'eopenreversedclosed',
0x025F => 'jdotlessstroke',
0x0260 => 'ghook',
0x0261 => 'gscript',
0x0263 => 'gammalatinsmall',
0x0264 => 'ramshorn',
0x0265 => 'hturned',
0x0266 => 'hhook',
0x0267 => 'henghook',
0x0268 => 'istroke',
0x0269 => 'iotalatin',
0x026B => 'lmiddletilde',
0x026C => 'lbelt',
0x026D => 'lhookretroflex',
0x026E => 'lezh',
0x026F => 'mturned',
0x0270 => 'mlonglegturned',
0x0271 => 'mhook',
0x0272 => 'nhookleft',
0x0273 => 'nhookretroflex',
0x0275 => 'obarred',
0x0277 => 'omegalatinclosed',
0x0278 => 'philatin',
0x0279 => 'rturned',
0x027A => 'rlonglegturned',
0x027B => 'rhookturned',
0x027C => 'rlongleg',
0x027D => 'rhook',
0x027E => 'rfishhook',
0x027F => 'rfishhookreversed',
0x0281 => 'Rsmallinverted',
0x0282 => 'shook',
0x0283 => 'esh',
0x0284 => 'dotlessjstrokehook',
0x0285 => 'eshsquatreversed',
0x0286 => 'eshcurl',
0x0287 => 'tturned',
0x0288 => 'tretroflexhook',
0x0289 => 'ubar',
0x028A => 'upsilonlatin',
0x028B => 'vhook',
0x028C => 'vturned',
0x028D => 'wturned',
0x028E => 'yturned',
0x0290 => 'zretroflexhook',
0x0291 => 'zcurl',
0x0292 => 'ezh',
0x0293 => 'ezhcurl',
0x0294 => 'glottalstop',
0x0295 => 'glottalstopreversed',
0x0296 => 'glottalstopinverted',
0x0297 => 'cstretched',
0x0298 => 'bilabialclick',
0x029A => 'eopenclosed',
0x029B => 'Gsmallhook',
0x029D => 'jcrossedtail',
0x029E => 'kturned',
0x02A0 => 'qhook',
0x02A1 => 'glottalstopstroke',
0x02A2 => 'glottalstopstrokereversed',
0x02A3 => 'dzaltone',
0x02A4 => 'dezh',
0x02A5 => 'dzcurl',
0x02A6 => 'ts',
0x02A7 => 'tesh',
0x02A8 => 'tccurl',
0x02B0 => 'hsuperior',
0x02B1 => 'hhooksuperior',
0x02B2 => 'jsuperior',
0x02B4 => 'rturnedsuperior',
0x02B5 => 'rhookturnedsuperior',
0x02B6 => 'Rsmallinvertedsuperior',
0x02B7 => 'wsuperior',
0x02B8 => 'ysuperior',
0x02B9 => 'primemod',
0x02BA => 'dblprimemod',
0x02BB => 'commaturnedmod',
0x02BC => 'afii57929',
0x02BD => 'afii64937',
0x02BE => 'ringhalfright',
0x02BF => 'ringhalfleft',
0x02C0 => 'glottalstopmod',
0x02C1 => 'glottalstopreversedmod',
0x02C2 => 'arrowheadleftmod',
0x02C3 => 'arrowheadrightmod',
0x02C4 => 'arrowheadupmod',
0x02C5 => 'arrowheaddownmod',
0x02C6 => 'circumflex',
0x02C7 => 'caron',
0x02C8 => 'verticallinemod',
0x02C9 => 'firsttonechinese',
0x02CA => 'secondtonechinese',
0x02CB => 'fourthtonechinese',
0x02CC => 'verticallinelowmod',
0x02CD => 'macronlowmod',
0x02CE => 'gravelowmod',
0x02CF => 'acutelowmod',
0x02D0 => 'colontriangularmod',
0x02D1 => 'colontriangularhalfmod',
0x02D2 => 'ringhalfrightcentered',
0x02D3 => 'ringhalfleftcentered',
0x02D4 => 'uptackmod',
0x02D5 => 'downtackmod',
0x02D6 => 'plusmod',
0x02D7 => 'minusmod',
0x02D8 => 'breve',
0x02D9 => 'dotaccent',
0x02DA => 'ring',
0x02DB => 'ogonek',
0x02DC => 'tilde',
0x02DD => 'hungarumlaut',
0x02DE => 'rhotichookmod',
0x02E0 => 'gammasuperior',
0x02E3 => 'xsuperior',
0x02E4 => 'glottalstopreversedsuperior',
0x02E5 => 'tonebarextrahighmod',
0x02E6 => 'tonebarhighmod',
0x02E7 => 'tonebarmidmod',
0x02E8 => 'tonebarlowmod',
0x02E9 => 'tonebarextralowmod',
0x0300 => 'gravecmb',
0x0301 => 'acutecmb',
0x0302 => 'circumflexcmb',
0x0303 => 'tildecmb',
0x0304 => 'macroncmb',
0x0305 => 'overlinecmb',
0x0306 => 'brevecmb',
0x0307 => 'dotaccentcmb',
0x0308 => 'dieresiscmb',
0x0309 => 'hookcmb',
0x030A => 'ringcmb',
0x030B => 'hungarumlautcmb',
0x030C => 'caroncmb',
0x030D => 'verticallineabovecmb',
0x030E => 'dblverticallineabovecmb',
0x030F => 'dblgravecmb',
0x0310 => 'candrabinducmb',
0x0311 => 'breveinvertedcmb',
0x0312 => 'commaturnedabovecmb',
0x0313 => 'commaabovecmb',
0x0314 => 'commareversedabovecmb',
0x0315 => 'commaaboverightcmb',
0x0316 => 'gravebelowcmb',
0x0317 => 'acutebelowcmb',
0x0318 => 'lefttackbelowcmb',
0x0319 => 'righttackbelowcmb',
0x031A => 'leftangleabovecmb',
0x031B => 'horncmb',
0x031C => 'ringhalfleftbelowcmb',
0x031D => 'uptackbelowcmb',
0x031E => 'downtackbelowcmb',
0x031F => 'plusbelowcmb',
0x0320 => 'minusbelowcmb',
0x0321 => 'hookpalatalizedbelowcmb',
0x0322 => 'hookretroflexbelowcmb',
0x0323 => 'dotbelowcmb',
0x0324 => 'dieresisbelowcmb',
0x0325 => 'ringbelowcmb',
0x0327 => 'cedillacmb',
0x0328 => 'ogonekcmb',
0x0329 => 'verticallinebelowcmb',
0x032A => 'bridgebelowcmb',
0x032B => 'dblarchinvertedbelowcmb',
0x032C => 'caronbelowcmb',
0x032D => 'circumflexbelowcmb',
0x032E => 'brevebelowcmb',
0x032F => 'breveinvertedbelowcmb',
0x0330 => 'tildebelowcmb',
0x0331 => 'macronbelowcmb',
0x0332 => 'lowlinecmb',
0x0333 => 'dbllowlinecmb',
0x0334 => 'tildeoverlaycmb',
0x0335 => 'strokeshortoverlaycmb',
0x0336 => 'strokelongoverlaycmb',
0x0337 => 'solidusshortoverlaycmb',
0x0338 => 'soliduslongoverlaycmb',
0x0339 => 'ringhalfrightbelowcmb',
0x033A => 'bridgeinvertedbelowcmb',
0x033B => 'squarebelowcmb',
0x033C => 'seagullbelowcmb',
0x033D => 'xabovecmb',
0x033E => 'tildeverticalcmb',
0x033F => 'dbloverlinecmb',
0x0340 => 'gravetonecmb',
0x0341 => 'acutetonecmb',
0x0342 => 'perispomenigreekcmb',
0x0343 => 'koroniscmb',
0x0344 => 'dialytikatonoscmb',
0x0345 => 'ypogegrammenigreekcmb',
0x0360 => 'tildedoublecmb',
0x0361 => 'breveinverteddoublecmb',
0x0374 => 'numeralsigngreek',
0x0375 => 'numeralsignlowergreek',
0x037A => 'ypogegrammeni',
0x037E => 'questiongreek',
0x0384 => 'tonos',
0x0385 => 'dieresistonos',
0x0386 => 'Alphatonos',
0x0387 => 'anoteleia',
0x0388 => 'Epsilontonos',
0x0389 => 'Etatonos',
0x038A => 'Iotatonos',
0x038C => 'Omicrontonos',
0x038E => 'Upsilontonos',
0x038F => 'Omegatonos',
0x0390 => 'iotadieresistonos',
0x0391 => 'Alpha',
0x0392 => 'Beta',
0x0393 => 'Gamma',
0x0394 => 'Deltagreek',
0x0395 => 'Epsilon',
0x0396 => 'Zeta',
0x0397 => 'Eta',
0x0398 => 'Theta',
0x0399 => 'Iota',
0x039A => 'Kappa',
0x039B => 'Lambda',
0x039C => 'Mu',
0x039D => 'Nu',
0x039E => 'Xi',
0x039F => 'Omicron',
0x03A0 => 'Pi',
0x03A1 => 'Rho',
0x03A3 => 'Sigma',
0x03A4 => 'Tau',
0x03A5 => 'Upsilon',
0x03A6 => 'Phi',
0x03A7 => 'Chi',
0x03A8 => 'Psi',
0x03A9 => 'Omega',
0x03AA => 'Iotadieresis',
0x03AB => 'Upsilondieresis',
0x03AC => 'alphatonos',
0x03AD => 'epsilontonos',
0x03AE => 'etatonos',
0x03AF => 'iotatonos',
0x03B0 => 'upsilondieresistonos',
0x03B1 => 'alpha',
0x03B2 => 'beta',
0x03B3 => 'gamma',
0x03B4 => 'delta',
0x03B5 => 'epsilon',
0x03B6 => 'zeta',
0x03B7 => 'eta',
0x03B8 => 'theta',
0x03B9 => 'iota',
0x03BA => 'kappa',
0x03BB => 'lambda',
0x03BC => 'mugreek',
0x03BD => 'nu',
0x03BE => 'xi',
0x03BF => 'omicron',
0x03C0 => 'pi',
0x03C1 => 'rho',
0x03C2 => 'sigma1',
0x03C3 => 'sigma',
0x03C4 => 'tau',
0x03C5 => 'upsilon',
0x03C6 => 'phi',
0x03C7 => 'chi',
0x03C8 => 'psi',
0x03C9 => 'omega',
0x03CA => 'iotadieresis',
0x03CB => 'upsilondieresis',
0x03CC => 'omicrontonos',
0x03CD => 'upsilontonos',
0x03CE => 'omegatonos',
0x03D0 => 'betasymbolgreek',
0x03D1 => 'theta1',
0x03D2 => 'Upsilon1',
0x03D3 => 'Upsilonacutehooksymbolgreek',
0x03D4 => 'Upsilondieresishooksymbolgreek',
0x03D5 => 'phi1',
0x03D6 => 'omega1',
0x03DA => 'Stigmagreek',
0x03DC => 'Digammagreek',
0x03DE => 'Koppagreek',
0x03E0 => 'Sampigreek',
0x03E2 => 'Sheicoptic',
0x03E3 => 'sheicoptic',
0x03E4 => 'Feicoptic',
0x03E5 => 'feicoptic',
0x03E6 => 'Kheicoptic',
0x03E7 => 'kheicoptic',
0x03E8 => 'Horicoptic',
0x03E9 => 'horicoptic',
0x03EA => 'Gangiacoptic',
0x03EB => 'gangiacoptic',
0x03EC => 'Shimacoptic',
0x03ED => 'shimacoptic',
0x03EE => 'Deicoptic',
0x03EF => 'deicoptic',
0x03F0 => 'kappasymbolgreek',
0x03F1 => 'rhosymbolgreek',
0x03F2 => 'sigmalunatesymbolgreek',
0x03F3 => 'yotgreek',
0x0401 => 'afii10023',
0x0402 => 'afii10051',
0x0403 => 'afii10052',
0x0404 => 'afii10053',
0x0405 => 'afii10054',
0x0406 => 'afii10055',
0x0407 => 'afii10056',
0x0408 => 'afii10057',
0x0409 => 'afii10058',
0x040A => 'afii10059',
0x040B => 'afii10060',
0x040C => 'afii10061',
0x040E => 'afii10062',
0x040F => 'afii10145',
0x0410 => 'afii10017',
0x0411 => 'afii10018',
0x0412 => 'afii10019',
0x0413 => 'afii10020',
0x0414 => 'afii10021',
0x0415 => 'afii10022',
0x0416 => 'afii10024',
0x0417 => 'afii10025',
0x0418 => 'afii10026',
0x0419 => 'afii10027',
0x041A => 'afii10028',
0x041B => 'afii10029',
0x041C => 'afii10030',
0x041D => 'afii10031',
0x041E => 'afii10032',
0x041F => 'afii10033',
0x0420 => 'afii10034',
0x0421 => 'afii10035',
0x0422 => 'afii10036',
0x0423 => 'afii10037',
0x0424 => 'afii10038',
0x0425 => 'afii10039',
0x0426 => 'afii10040',
0x0427 => 'afii10041',
0x0428 => 'afii10042',
0x0429 => 'afii10043',
0x042A => 'afii10044',
0x042B => 'afii10045',
0x042C => 'afii10046',
0x042D => 'afii10047',
0x042E => 'afii10048',
0x042F => 'afii10049',
0x0430 => 'afii10065',
0x0431 => 'afii10066',
0x0432 => 'afii10067',
0x0433 => 'afii10068',
0x0434 => 'afii10069',
0x0435 => 'afii10070',
0x0436 => 'afii10072',
0x0437 => 'afii10073',
0x0438 => 'afii10074',
0x0439 => 'afii10075',
0x043A => 'afii10076',
0x043B => 'afii10077',
0x043C => 'afii10078',
0x043D => 'afii10079',
0x043E => 'afii10080',
0x043F => 'afii10081',
0x0440 => 'afii10082',
0x0441 => 'afii10083',
0x0442 => 'afii10084',
0x0443 => 'afii10085',
0x0444 => 'afii10086',
0x0445 => 'afii10087',
0x0446 => 'afii10088',
0x0447 => 'afii10089',
0x0448 => 'afii10090',
0x0449 => 'afii10091',
0x044A => 'afii10092',
0x044B => 'afii10093',
0x044C => 'afii10094',
0x044D => 'afii10095',
0x044E => 'afii10096',
0x044F => 'afii10097',
0x0451 => 'afii10071',
0x0452 => 'afii10099',
0x0453 => 'afii10100',
0x0454 => 'afii10101',
0x0455 => 'afii10102',
0x0456 => 'afii10103',
0x0457 => 'afii10104',
0x0458 => 'afii10105',
0x0459 => 'afii10106',
0x045A => 'afii10107',
0x045B => 'afii10108',
0x045C => 'afii10109',
0x045E => 'afii10110',
0x045F => 'afii10193',
0x0460 => 'Omegacyrillic',
0x0461 => 'omegacyrillic',
0x0462 => 'Yatcyrillic',
0x0462 => 'afii10146',
0x0463 => 'afii10194',
0x0464 => 'Eiotifiedcyrillic',
0x0465 => 'eiotifiedcyrillic',
0x0466 => 'Yuslittlecyrillic',
0x0467 => 'yuslittlecyrillic',
0x0468 => 'Yuslittleiotifiedcyrillic',
0x0469 => 'yuslittleiotifiedcyrillic',
0x046A => 'Yusbigcyrillic',
0x046B => 'yusbigcyrillic',
0x046C => 'Yusbigiotifiedcyrillic',
0x046D => 'yusbigiotifiedcyrillic',
0x046E => 'Ksicyrillic',
0x046F => 'ksicyrillic',
0x0470 => 'Psicyrillic',
0x0471 => 'psicyrillic',
0x0472 => 'afii10147',
0x0473 => 'afii10195',
0x0474 => 'afii10148',
0x0475 => 'afii10196',
0x0476 => 'Izhitsadblgravecyrillic',
0x0477 => 'izhitsadblgravecyrillic',
0x0478 => 'Ukcyrillic',
0x0479 => 'ukcyrillic',
0x047A => 'Omegaroundcyrillic',
0x047B => 'omegaroundcyrillic',
0x047C => 'Omegatitlocyrillic',
0x047D => 'omegatitlocyrillic',
0x047E => 'Otcyrillic',
0x047F => 'otcyrillic',
0x0480 => 'Koppacyrillic',
0x0481 => 'koppacyrillic',
0x0482 => 'thousandcyrillic',
0x0483 => 'titlocyrilliccmb',
0x0484 => 'palatalizationcyrilliccmb',
0x0485 => 'dasiapneumatacyrilliccmb',
0x0486 => 'psilipneumatacyrilliccmb',
0x0490 => 'Gheupturncyrillic',
0x0490 => 'afii10050',
0x0491 => 'afii10098',
0x0491 => 'gheupturncyrillic',
0x0492 => 'Ghestrokecyrillic',
0x0493 => 'ghestrokecyrillic',
0x0494 => 'Ghemiddlehookcyrillic',
0x0495 => 'ghemiddlehookcyrillic',
0x0496 => 'Zhedescendercyrillic',
0x0497 => 'zhedescendercyrillic',
0x0498 => 'Zedescendercyrillic',
0x0499 => 'zedescendercyrillic',
0x049A => 'Kadescendercyrillic',
0x049B => 'kadescendercyrillic',
0x049C => 'Kaverticalstrokecyrillic',
0x049D => 'kaverticalstrokecyrillic',
0x049E => 'Kastrokecyrillic',
0x049F => 'kastrokecyrillic',
0x04A0 => 'Kabashkircyrillic',
0x04A1 => 'kabashkircyrillic',
0x04A2 => 'Endescendercyrillic',
0x04A3 => 'endescendercyrillic',
0x04A4 => 'Enghecyrillic',
0x04A5 => 'enghecyrillic',
0x04A6 => 'Pemiddlehookcyrillic',
0x04A7 => 'pemiddlehookcyrillic',
0x04A8 => 'Haabkhasiancyrillic',
0x04A9 => 'haabkhasiancyrillic',
0x04AA => 'Esdescendercyrillic',
0x04AB => 'esdescendercyrillic',
0x04AC => 'Tedescendercyrillic',
0x04AD => 'tedescendercyrillic',
0x04AE => 'Ustraightcyrillic',
0x04AF => 'ustraightcyrillic',
0x04B0 => 'Ustraightstrokecyrillic',
0x04B1 => 'ustraightstrokecyrillic',
0x04B2 => 'Hadescendercyrillic',
0x04B3 => 'hadescendercyrillic',
0x04B4 => 'Tetsecyrillic',
0x04B5 => 'tetsecyrillic',
0x04B6 => 'Chedescendercyrillic',
0x04B7 => 'chedescendercyrillic',
0x04B8 => 'Cheverticalstrokecyrillic',
0x04B9 => 'cheverticalstrokecyrillic',
0x04BA => 'Shhacyrillic',
0x04BB => 'shhacyrillic',
0x04BC => 'Cheabkhasiancyrillic',
0x04BD => 'cheabkhasiancyrillic',
0x04BE => 'Chedescenderabkhasiancyrillic',
0x04BF => 'chedescenderabkhasiancyrillic',
0x04C0 => 'palochkacyrillic',
0x04C1 => 'Zhebrevecyrillic',
0x04C2 => 'zhebrevecyrillic',
0x04C3 => 'Kahookcyrillic',
0x04C4 => 'kahookcyrillic',
0x04C7 => 'Enhookcyrillic',
0x04C8 => 'enhookcyrillic',
0x04CB => 'Chekhakassiancyrillic',
0x04CC => 'chekhakassiancyrillic',
0x04D0 => 'Abrevecyrillic',
0x04D1 => 'abrevecyrillic',
0x04D2 => 'Adieresiscyrillic',
0x04D3 => 'adieresiscyrillic',
0x04D4 => 'Aiecyrillic',
0x04D5 => 'aiecyrillic',
0x04D6 => 'Iebrevecyrillic',
0x04D7 => 'iebrevecyrillic',
0x04D8 => 'Schwacyrillic',
0x04D9 => 'afii10846',
0x04D9 => 'schwacyrillic',
0x04DA => 'Schwadieresiscyrillic',
0x04DB => 'schwadieresiscyrillic',
0x04DC => 'Zhedieresiscyrillic',
0x04DD => 'zhedieresiscyrillic',
0x04DE => 'Zedieresiscyrillic',
0x04DF => 'zedieresiscyrillic',
0x04E0 => 'Dzeabkhasiancyrillic',
0x04E1 => 'dzeabkhasiancyrillic',
0x04E2 => 'Imacroncyrillic',
0x04E3 => 'imacroncyrillic',
0x04E4 => 'Idieresiscyrillic',
0x04E5 => 'idieresiscyrillic',
0x04E6 => 'Odieresiscyrillic',
0x04E7 => 'odieresiscyrillic',
0x04E8 => 'Obarredcyrillic',
0x04E9 => 'obarredcyrillic',
0x04EA => 'Obarreddieresiscyrillic',
0x04EB => 'obarreddieresiscyrillic',
0x04EE => 'Umacroncyrillic',
0x04EF => 'umacroncyrillic',
0x04F0 => 'Udieresiscyrillic',
0x04F1 => 'udieresiscyrillic',
0x04F2 => 'Uhungarumlautcyrillic',
0x04F3 => 'uhungarumlautcyrillic',
0x04F4 => 'Chedieresiscyrillic',
0x04F5 => 'chedieresiscyrillic',
0x04F8 => 'Yerudieresiscyrillic',
0x04F9 => 'yerudieresiscyrillic',
0x0531 => 'Aybarmenian',
0x0532 => 'Benarmenian',
0x0533 => 'Gimarmenian',
0x0534 => 'Daarmenian',
0x0535 => 'Echarmenian',
0x0536 => 'Zaarmenian',
0x0537 => 'Eharmenian',
0x0538 => 'Etarmenian',
0x0539 => 'Toarmenian',
0x053A => 'Zhearmenian',
0x053B => 'Iniarmenian',
0x053C => 'Liwnarmenian',
0x053D => 'Xeharmenian',
0x053E => 'Caarmenian',
0x053F => 'Kenarmenian',
0x0540 => 'Hoarmenian',
0x0541 => 'Jaarmenian',
0x0542 => 'Ghadarmenian',
0x0543 => 'Cheharmenian',
0x0544 => 'Menarmenian',
0x0545 => 'Yiarmenian',
0x0546 => 'Nowarmenian',
0x0547 => 'Shaarmenian',
0x0548 => 'Voarmenian',
0x0549 => 'Chaarmenian',
0x054A => 'Peharmenian',
0x054B => 'Jheharmenian',
0x054C => 'Raarmenian',
0x054D => 'Seharmenian',
0x054E => 'Vewarmenian',
0x054F => 'Tiwnarmenian',
0x0550 => 'Reharmenian',
0x0551 => 'Coarmenian',
0x0552 => 'Yiwnarmenian',
0x0553 => 'Piwrarmenian',
0x0554 => 'Keharmenian',
0x0555 => 'Oharmenian',
0x0556 => 'Feharmenian',
0x0559 => 'ringhalfleftarmenian',
0x055A => 'apostrophearmenian',
0x055B => 'emphasismarkarmenian',
0x055C => 'exclamarmenian',
0x055D => 'commaarmenian',
0x055E => 'questionarmenian',
0x055F => 'abbreviationmarkarmenian',
0x0561 => 'aybarmenian',
0x0562 => 'benarmenian',
0x0563 => 'gimarmenian',
0x0564 => 'daarmenian',
0x0565 => 'echarmenian',
0x0566 => 'zaarmenian',
0x0567 => 'eharmenian',
0x0568 => 'etarmenian',
0x0569 => 'toarmenian',
0x056A => 'zhearmenian',
0x056B => 'iniarmenian',
0x056C => 'liwnarmenian',
0x056D => 'xeharmenian',
0x056E => 'caarmenian',
0x056F => 'kenarmenian',
0x0570 => 'hoarmenian',
0x0571 => 'jaarmenian',
0x0572 => 'ghadarmenian',
0x0573 => 'cheharmenian',
0x0574 => 'menarmenian',
0x0575 => 'yiarmenian',
0x0576 => 'nowarmenian',
0x0577 => 'shaarmenian',
0x0578 => 'voarmenian',
0x0579 => 'chaarmenian',
0x057A => 'peharmenian',
0x057B => 'jheharmenian',
0x057C => 'raarmenian',
0x057D => 'seharmenian',
0x057E => 'vewarmenian',
0x057F => 'tiwnarmenian',
0x0580 => 'reharmenian',
0x0581 => 'coarmenian',
0x0582 => 'yiwnarmenian',
0x0583 => 'piwrarmenian',
0x0584 => 'keharmenian',
0x0585 => 'oharmenian',
0x0586 => 'feharmenian',
0x0587 => 'echyiwnarmenian',
0x0589 => 'periodarmenian',
0x0591 => 'etnahtafoukhhebrew',
0x0591 => 'etnahtafoukhlefthebrew',
0x0591 => 'etnahtahebrew',
0x0591 => 'etnahtalefthebrew',
0x0592 => 'segoltahebrew',
0x0593 => 'shalshelethebrew',
0x0594 => 'zaqefqatanhebrew',
0x0595 => 'zaqefgadolhebrew',
0x0596 => 'tipehahebrew',
0x0596 => 'tipehalefthebrew',
0x0597 => 'reviahebrew',
0x0597 => 'reviamugrashhebrew',
0x0598 => 'zarqahebrew',
0x0599 => 'pashtahebrew',
0x059A => 'yetivhebrew',
0x059B => 'tevirhebrew',
0x059B => 'tevirlefthebrew',
0x059C => 'gereshaccenthebrew',
0x059D => 'gereshmuqdamhebrew',
0x059E => 'gershayimaccenthebrew',
0x059F => 'qarneyparahebrew',
0x05A0 => 'telishagedolahebrew',
0x05A1 => 'pazerhebrew',
0x05A3 => 'munahhebrew',
0x05A3 => 'munahlefthebrew',
0x05A4 => 'mahapakhhebrew',
0x05A4 => 'mahapakhlefthebrew',
0x05A5 => 'merkhahebrew',
0x05A5 => 'merkhalefthebrew',
0x05A6 => 'merkhakefulahebrew',
0x05A6 => 'merkhakefulalefthebrew',
0x05A7 => 'dargahebrew',
0x05A7 => 'dargalefthebrew',
0x05A8 => 'qadmahebrew',
0x05A9 => 'telishaqetanahebrew',
0x05AA => 'yerahbenyomohebrew',
0x05AA => 'yerahbenyomolefthebrew',
0x05AB => 'olehebrew',
0x05AC => 'iluyhebrew',
0x05AD => 'dehihebrew',
0x05AE => 'zinorhebrew',
0x05AF => 'masoracirclehebrew',
0x05B0 => 'afii57799',
0x05B0 => 'sheva',
0x05B0 => 'sheva115',
0x05B0 => 'sheva15',
0x05B0 => 'sheva22',
0x05B0 => 'sheva2e',
0x05B0 => 'shevahebrew',
0x05B0 => 'shevanarrowhebrew',
0x05B0 => 'shevaquarterhebrew',
0x05B0 => 'shevawidehebrew',
0x05B1 => 'afii57801',
0x05B1 => 'hatafsegol',
0x05B1 => 'hatafsegol17',
0x05B1 => 'hatafsegol24',
0x05B1 => 'hatafsegol30',
0x05B1 => 'hatafsegolhebrew',
0x05B1 => 'hatafsegolnarrowhebrew',
0x05B1 => 'hatafsegolquarterhebrew',
0x05B1 => 'hatafsegolwidehebrew',
0x05B2 => 'afii57800',
0x05B2 => 'hatafpatah',
0x05B2 => 'hatafpatah16',
0x05B2 => 'hatafpatah23',
0x05B2 => 'hatafpatah2f',
0x05B2 => 'hatafpatahhebrew',
0x05B2 => 'hatafpatahnarrowhebrew',
0x05B2 => 'hatafpatahquarterhebrew',
0x05B2 => 'hatafpatahwidehebrew',
0x05B3 => 'afii57802',
0x05B3 => 'hatafqamats',
0x05B3 => 'hatafqamats1b',
0x05B3 => 'hatafqamats28',
0x05B3 => 'hatafqamats34',
0x05B3 => 'hatafqamatshebrew',
0x05B3 => 'hatafqamatsnarrowhebrew',
0x05B3 => 'hatafqamatsquarterhebrew',
0x05B3 => 'hatafqamatswidehebrew',
0x05B4 => 'afii57793',
0x05B4 => 'hiriq',
0x05B4 => 'hiriq14',
0x05B4 => 'hiriq21',
0x05B4 => 'hiriq2d',
0x05B4 => 'hiriqhebrew',
0x05B4 => 'hiriqnarrowhebrew',
0x05B4 => 'hiriqquarterhebrew',
0x05B4 => 'hiriqwidehebrew',
0x05B5 => 'afii57794',
0x05B5 => 'tsere',
0x05B5 => 'tsere12',
0x05B5 => 'tsere1e',
0x05B5 => 'tsere2b',
0x05B5 => 'tserehebrew',
0x05B5 => 'tserenarrowhebrew',
0x05B5 => 'tserequarterhebrew',
0x05B5 => 'tserewidehebrew',
0x05B6 => 'afii57795',
0x05B6 => 'segol',
0x05B6 => 'segol13',
0x05B6 => 'segol1f',
0x05B6 => 'segol2c',
0x05B6 => 'segolhebrew',
0x05B6 => 'segolnarrowhebrew',
0x05B6 => 'segolquarterhebrew',
0x05B6 => 'segolwidehebrew',
0x05B7 => 'afii57798',
0x05B7 => 'patah',
0x05B7 => 'patah11',
0x05B7 => 'patah1d',
0x05B7 => 'patah2a',
0x05B7 => 'patahhebrew',
0x05B7 => 'patahnarrowhebrew',
0x05B7 => 'patahquarterhebrew',
0x05B7 => 'patahwidehebrew',
0x05B8 => 'afii57797',
0x05B8 => 'qamats',
0x05B8 => 'qamats10',
0x05B8 => 'qamats1a',
0x05B8 => 'qamats1c',
0x05B8 => 'qamats27',
0x05B8 => 'qamats29',
0x05B8 => 'qamats33',
0x05B8 => 'qamatsde',
0x05B8 => 'qamatshebrew',
0x05B8 => 'qamatsnarrowhebrew',
0x05B8 => 'qamatsqatanhebrew',
0x05B8 => 'qamatsqatannarrowhebrew',
0x05B8 => 'qamatsqatanquarterhebrew',
0x05B8 => 'qamatsqatanwidehebrew',
0x05B8 => 'qamatsquarterhebrew',
0x05B8 => 'qamatswidehebrew',
0x05B9 => 'afii57806',
0x05B9 => 'holam',
0x05B9 => 'holam19',
0x05B9 => 'holam26',
0x05B9 => 'holam32',
0x05B9 => 'holamhebrew',
0x05B9 => 'holamnarrowhebrew',
0x05B9 => 'holamquarterhebrew',
0x05B9 => 'holamwidehebrew',
0x05BB => 'afii57796',
0x05BB => 'qubuts',
0x05BB => 'qubuts18',
0x05BB => 'qubuts25',
0x05BB => 'qubuts31',
0x05BB => 'qubutshebrew',
0x05BB => 'qubutsnarrowhebrew',
0x05BB => 'qubutsquarterhebrew',
0x05BB => 'qubutswidehebrew',
0x05BC => 'afii57807',
0x05BC => 'dagesh',
0x05BC => 'dageshhebrew',
0x05BD => 'afii57839',
0x05BD => 'siluqhebrew',
0x05BD => 'siluqlefthebrew',
0x05BE => 'afii57645',
0x05BE => 'maqafhebrew',
0x05BF => 'afii57841',
0x05BF => 'rafe',
0x05BF => 'rafehebrew',
0x05C0 => 'afii57842',
0x05C0 => 'paseqhebrew',
0x05C1 => 'afii57804',
0x05C1 => 'shindothebrew',
0x05C2 => 'afii57803',
0x05C2 => 'sindothebrew',
0x05C3 => 'afii57658',
0x05C3 => 'sofpasuqhebrew',
0x05C4 => 'upperdothebrew',
0x05D0 => 'afii57664',
0x05D0 => 'alef',
0x05D0 => 'alefhebrew',
0x05D1 => 'afii57665',
0x05D1 => 'bet',
0x05D1 => 'bethebrew',
0x05D2 => 'afii57666',
0x05D2 => 'gimel',
0x05D2 => 'gimelhebrew',
0x05D3 => 'afii57667',
0x05D3 => 'dalet',
0x05D3 => 'dalethebrew',
0x05D4 => 'afii57668',
0x05D4 => 'he',
0x05D4 => 'hehebrew',
0x05D5 => 'afii57669',
0x05D5 => 'vav',
0x05D5 => 'vavhebrew',
0x05D6 => 'afii57670',
0x05D6 => 'zayin',
0x05D6 => 'zayinhebrew',
0x05D7 => 'afii57671',
0x05D7 => 'het',
0x05D7 => 'hethebrew',
0x05D8 => 'afii57672',
0x05D8 => 'tet',
0x05D8 => 'tethebrew',
0x05D9 => 'afii57673',
0x05D9 => 'yod',
0x05D9 => 'yodhebrew',
0x05DA => 'afii57674',
0x05DA => 'finalkaf',
0x05DA => 'finalkafhebrew',
0x05DB => 'afii57675',
0x05DB => 'kaf',
0x05DB => 'kafhebrew',
0x05DC => 'afii57676',
0x05DC => 'lamed',
0x05DC => 'lamedhebrew',
0x05DD => 'afii57677',
0x05DD => 'finalmem',
0x05DD => 'finalmemhebrew',
0x05DE => 'afii57678',
0x05DE => 'mem',
0x05DE => 'memhebrew',
0x05DF => 'afii57679',
0x05DF => 'finalnun',
0x05DF => 'finalnunhebrew',
0x05E0 => 'afii57680',
0x05E0 => 'nun',
0x05E0 => 'nunhebrew',
0x05E1 => 'afii57681',
0x05E1 => 'samekh',
0x05E1 => 'samekhhebrew',
0x05E2 => 'afii57682',
0x05E2 => 'ayin',
0x05E2 => 'ayinhebrew',
0x05E3 => 'afii57683',
0x05E3 => 'finalpe',
0x05E3 => 'finalpehebrew',
0x05E4 => 'afii57684',
0x05E4 => 'pe',
0x05E4 => 'pehebrew',
0x05E5 => 'afii57685',
0x05E5 => 'finaltsadi',
0x05E5 => 'finaltsadihebrew',
0x05E6 => 'afii57686',
0x05E6 => 'tsadi',
0x05E6 => 'tsadihebrew',
0x05E7 => 'afii57687',
0x05E7 => 'qof',
0x05E7 => 'qofhebrew',
0x05E8 => 'afii57688',
0x05E8 => 'resh',
0x05E8 => 'reshhebrew',
0x05E9 => 'afii57689',
0x05E9 => 'shin',
0x05E9 => 'shinhebrew',
0x05EA => 'afii57690',
0x05EA => 'tav',
0x05EA => 'tavhebrew',
0x05F0 => 'afii57716',
0x05F0 => 'vavvavhebrew',
0x05F1 => 'afii57717',
0x05F1 => 'vavyodhebrew',
0x05F2 => 'afii57718',
0x05F2 => 'yodyodhebrew',
0x05F3 => 'gereshhebrew',
0x05F4 => 'gershayimhebrew',
0x060C => 'afii57388',
0x060C => 'commaarabic',
0x061B => 'afii57403',
0x061B => 'semicolonarabic',
0x061F => 'afii57407',
0x061F => 'questionarabic',
0x0621 => 'afii57409',
0x0621 => 'hamzaarabic',
0x0621 => 'hamzalowarabic',
0x0622 => 'afii57410',
0x0622 => 'alefmaddaabovearabic',
0x0623 => 'afii57411',
0x0623 => 'alefhamzaabovearabic',
0x0624 => 'afii57412',
0x0624 => 'wawhamzaabovearabic',
0x0625 => 'afii57413',
0x0625 => 'alefhamzabelowarabic',
0x0626 => 'afii57414',
0x0626 => 'yehhamzaabovearabic',
0x0627 => 'afii57415',
0x0627 => 'alefarabic',
0x0628 => 'afii57416',
0x0628 => 'beharabic',
0x0629 => 'afii57417',
0x0629 => 'tehmarbutaarabic',
0x062A => 'afii57418',
0x062A => 'teharabic',
0x062B => 'afii57419',
0x062B => 'theharabic',
0x062C => 'afii57420',
0x062C => 'jeemarabic',
0x062D => 'afii57421',
0x062D => 'haharabic',
0x062E => 'afii57422',
0x062E => 'khaharabic',
0x062F => 'afii57423',
0x062F => 'dalarabic',
0x0630 => 'afii57424',
0x0630 => 'thalarabic',
0x0631 => 'afii57425',
0x0631 => 'reharabic',
0x0632 => 'afii57426',
0x0632 => 'zainarabic',
0x0633 => 'afii57427',
0x0633 => 'seenarabic',
0x0634 => 'afii57428',
0x0634 => 'sheenarabic',
0x0635 => 'afii57429',
0x0635 => 'sadarabic',
0x0636 => 'afii57430',
0x0636 => 'dadarabic',
0x0637 => 'afii57431',
0x0637 => 'taharabic',
0x0638 => 'afii57432',
0x0638 => 'zaharabic',
0x0639 => 'afii57433',
0x0639 => 'ainarabic',
0x063A => 'afii57434',
0x063A => 'ghainarabic',
0x0640 => 'afii57440',
0x0640 => 'kashidaautoarabic',
0x0640 => 'kashidaautonosidebearingarabic',
0x0640 => 'tatweelarabic',
0x0641 => 'afii57441',
0x0641 => 'feharabic',
0x0642 => 'afii57442',
0x0642 => 'qafarabic',
0x0643 => 'afii57443',
0x0643 => 'kafarabic',
0x0644 => 'afii57444',
0x0644 => 'lamarabic',
0x0645 => 'afii57445',
0x0645 => 'meemarabic',
0x0646 => 'afii57446',
0x0646 => 'noonarabic',
0x0647 => 'afii57470',
0x0647 => 'heharabic',
0x0648 => 'afii57448',
0x0648 => 'wawarabic',
0x0649 => 'afii57449',
0x0649 => 'alefmaksuraarabic',
0x064A => 'afii57450',
0x064A => 'yeharabic',
0x064B => 'afii57451',
0x064B => 'fathatanarabic',
0x064C => 'afii57452',
0x064C => 'dammatanaltonearabic',
0x064C => 'dammatanarabic',
0x064D => 'afii57453',
0x064D => 'kasratanarabic',
0x064E => 'afii57454',
0x064E => 'fathaarabic',
0x064E => 'fathalowarabic',
0x064F => 'afii57455',
0x064F => 'dammaarabic',
0x064F => 'dammalowarabic',
0x0650 => 'afii57456',
0x0650 => 'kasraarabic',
0x0651 => '064B shaddafathatanarabic',
0x0651 => 'afii57457',
0x0651 => 'shaddaarabic',
0x0652 => 'afii57458',
0x0652 => 'sukunarabic',
0x0660 => 'afii57392',
0x0660 => 'zeroarabic',
0x0660 => 'zerohackarabic',
0x0661 => 'afii57393',
0x0661 => 'onearabic',
0x0661 => 'onehackarabic',
0x0662 => 'afii57394',
0x0662 => 'twoarabic',
0x0662 => 'twohackarabic',
0x0663 => 'afii57395',
0x0663 => 'threearabic',
0x0663 => 'threehackarabic',
0x0664 => 'afii57396',
0x0664 => 'fourarabic',
0x0664 => 'fourhackarabic',
0x0665 => 'afii57397',
0x0665 => 'fivearabic',
0x0665 => 'fivehackarabic',
0x0666 => 'afii57398',
0x0666 => 'sixarabic',
0x0666 => 'sixhackarabic',
0x0667 => 'afii57399',
0x0667 => 'sevenarabic',
0x0667 => 'sevenhackarabic',
0x0668 => 'afii57400',
0x0668 => 'eightarabic',
0x0668 => 'eighthackarabic',
0x0669 => 'afii57401',
0x0669 => 'ninearabic',
0x0669 => 'ninehackarabic',
0x066A => 'afii57381',
0x066A => 'percentarabic',
0x066B => 'decimalseparatorarabic',
0x066B => 'decimalseparatorpersian',
0x066C => 'thousandsseparatorarabic',
0x066C => 'thousandsseparatorpersian',
0x066D => 'afii63167',
0x066D => 'asteriskaltonearabic',
0x066D => 'asteriskarabic',
0x0679 => 'afii57511',
0x0679 => 'tteharabic',
0x067E => 'afii57506',
0x067E => 'peharabic',
0x0686 => 'afii57507',
0x0686 => 'tcheharabic',
0x0688 => 'afii57512',
0x0688 => 'ddalarabic',
0x0691 => 'afii57513',
0x0691 => 'rreharabic',
0x0698 => 'afii57508',
0x0698 => 'jeharabic',
0x06A4 => 'afii57505',
0x06A4 => 'veharabic',
0x06AF => 'afii57509',
0x06AF => 'gafarabic',
0x06BA => 'afii57514',
0x06BA => 'noonghunnaarabic',
0x06C1 => 'haaltonearabic',
0x06C1 => 'hehaltonearabic',
0x06D1 => 'yehthreedotsbelowarabic',
0x06D2 => 'afii57519',
0x06D2 => 'yehbarreearabic',
0x06D5 => 'afii57534',
0x06F0 => 'zeropersian',
0x06F1 => 'onepersian',
0x06F2 => 'twopersian',
0x06F3 => 'threepersian',
0x06F4 => 'fourpersian',
0x06F5 => 'fivepersian',
0x06F6 => 'sixpersian',
0x06F7 => 'sevenpersian',
0x06F8 => 'eightpersian',
0x06F9 => 'ninepersian',
0x0901 => 'candrabindudeva',
0x0902 => 'anusvaradeva',
0x0903 => 'visargadeva',
0x0905 => 'adeva',
0x0906 => 'aadeva',
0x0907 => 'ideva',
0x0908 => 'iideva',
0x0909 => 'udeva',
0x090A => 'uudeva',
0x090B => 'rvocalicdeva',
0x090C => 'lvocalicdeva',
0x090D => 'ecandradeva',
0x090E => 'eshortdeva',
0x090F => 'edeva',
0x0910 => 'aideva',
0x0911 => 'ocandradeva',
0x0912 => 'oshortdeva',
0x0913 => 'odeva',
0x0914 => 'audeva',
0x0915 => 'kadeva',
0x0916 => 'khadeva',
0x0917 => 'gadeva',
0x0918 => 'ghadeva',
0x0919 => 'ngadeva',
0x091A => 'cadeva',
0x091B => 'chadeva',
0x091C => 'jadeva',
0x091D => 'jhadeva',
0x091E => 'nyadeva',
0x091F => 'ttadeva',
0x0920 => 'tthadeva',
0x0921 => 'ddadeva',
0x0922 => 'ddhadeva',
0x0923 => 'nnadeva',
0x0924 => 'tadeva',
0x0925 => 'thadeva',
0x0926 => 'dadeva',
0x0927 => 'dhadeva',
0x0928 => 'nadeva',
0x0929 => 'nnnadeva',
0x092A => 'padeva',
0x092B => 'phadeva',
0x092C => 'badeva',
0x092D => 'bhadeva',
0x092E => 'madeva',
0x092F => 'yadeva',
0x0930 => 'radeva',
0x0931 => 'rradeva',
0x0932 => 'ladeva',
0x0933 => 'lladeva',
0x0934 => 'llladeva',
0x0935 => 'vadeva',
0x0936 => 'shadeva',
0x0937 => 'ssadeva',
0x0938 => 'sadeva',
0x0939 => 'hadeva',
0x093C => 'nuktadeva',
0x093D => 'avagrahadeva',
0x093E => 'aavowelsigndeva',
0x093F => 'ivowelsigndeva',
0x0940 => 'iivowelsigndeva',
0x0941 => 'uvowelsigndeva',
0x0942 => 'uuvowelsigndeva',
0x0943 => 'rvocalicvowelsigndeva',
0x0944 => 'rrvocalicvowelsigndeva',
0x0945 => 'ecandravowelsigndeva',
0x0946 => 'eshortvowelsigndeva',
0x0947 => 'evowelsigndeva',
0x0948 => 'aivowelsigndeva',
0x0949 => 'ocandravowelsigndeva',
0x094A => 'oshortvowelsigndeva',
0x094B => 'ovowelsigndeva',
0x094C => 'auvowelsigndeva',
0x094D => 'viramadeva',
0x0950 => 'omdeva',
0x0951 => 'udattadeva',
0x0952 => 'anudattadeva',
0x0953 => 'gravedeva',
0x0954 => 'acutedeva',
0x0958 => 'qadeva',
0x0959 => 'khhadeva',
0x095A => 'ghhadeva',
0x095B => 'zadeva',
0x095C => 'dddhadeva',
0x095D => 'rhadeva',
0x095E => 'fadeva',
0x095F => 'yyadeva',
0x0960 => 'rrvocalicdeva',
0x0961 => 'llvocalicdeva',
0x0962 => 'lvocalicvowelsigndeva',
0x0963 => 'llvocalicvowelsigndeva',
0x0964 => 'danda',
0x0965 => 'dbldanda',
0x0966 => 'zerodeva',
0x0967 => 'onedeva',
0x0968 => 'twodeva',
0x0969 => 'threedeva',
0x096A => 'fourdeva',
0x096B => 'fivedeva',
0x096C => 'sixdeva',
0x096D => 'sevendeva',
0x096E => 'eightdeva',
0x096F => 'ninedeva',
0x0970 => 'abbreviationsigndeva',
0x0981 => 'candrabindubengali',
0x0982 => 'anusvarabengali',
0x0983 => 'visargabengali',
0x0985 => 'abengali',
0x0986 => 'aabengali',
0x0987 => 'ibengali',
0x0988 => 'iibengali',
0x0989 => 'ubengali',
0x098A => 'uubengali',
0x098B => 'rvocalicbengali',
0x098C => 'lvocalicbengali',
0x098F => 'ebengali',
0x0990 => 'aibengali',
0x0993 => 'obengali',
0x0994 => 'aubengali',
0x0995 => 'kabengali',
0x0996 => 'khabengali',
0x0997 => 'gabengali',
0x0998 => 'ghabengali',
0x0999 => 'ngabengali',
0x099A => 'cabengali',
0x099B => 'chabengali',
0x099C => 'jabengali',
0x099D => 'jhabengali',
0x099E => 'nyabengali',
0x099F => 'ttabengali',
0x09A0 => 'tthabengali',
0x09A1 => 'ddabengali',
0x09A2 => 'ddhabengali',
0x09A3 => 'nnabengali',
0x09A4 => 'tabengali',
0x09A5 => 'thabengali',
0x09A6 => 'dabengali',
0x09A7 => 'dhabengali',
0x09A8 => 'nabengali',
0x09AA => 'pabengali',
0x09AB => 'phabengali',
0x09AC => 'babengali',
0x09AD => 'bhabengali',
0x09AE => 'mabengali',
0x09AF => 'yabengali',
0x09B0 => 'rabengali',
0x09B2 => 'labengali',
0x09B6 => 'shabengali',
0x09B7 => 'ssabengali',
0x09B8 => 'sabengali',
0x09B9 => 'habengali',
0x09BC => 'nuktabengali',
0x09BE => 'aavowelsignbengali',
0x09BF => 'ivowelsignbengali',
0x09C0 => 'iivowelsignbengali',
0x09C1 => 'uvowelsignbengali',
0x09C2 => 'uuvowelsignbengali',
0x09C3 => 'rvocalicvowelsignbengali',
0x09C4 => 'rrvocalicvowelsignbengali',
0x09C7 => 'evowelsignbengali',
0x09C8 => 'aivowelsignbengali',
0x09CB => 'ovowelsignbengali',
0x09CC => 'auvowelsignbengali',
0x09CD => 'viramabengali',
0x09D7 => 'aulengthmarkbengali',
0x09DC => 'rrabengali',
0x09DD => 'rhabengali',
0x09DF => 'yyabengali',
0x09E0 => 'rrvocalicbengali',
0x09E1 => 'llvocalicbengali',
0x09E2 => 'lvocalicvowelsignbengali',
0x09E3 => 'llvocalicvowelsignbengali',
0x09E6 => 'zerobengali',
0x09E7 => 'onebengali',
0x09E8 => 'twobengali',
0x09E9 => 'threebengali',
0x09EA => 'fourbengali',
0x09EB => 'fivebengali',
0x09EC => 'sixbengali',
0x09ED => 'sevenbengali',
0x09EE => 'eightbengali',
0x09EF => 'ninebengali',
0x09F0 => 'ramiddlediagonalbengali',
0x09F1 => 'ralowerdiagonalbengali',
0x09F2 => 'rupeemarkbengali',
0x09F3 => 'rupeesignbengali',
0x09F4 => 'onenumeratorbengali',
0x09F5 => 'twonumeratorbengali',
0x09F6 => 'threenumeratorbengali',
0x09F7 => 'fournumeratorbengali',
0x09F8 => 'denominatorminusonenumeratorbengali',
0x09F9 => 'sixteencurrencydenominatorbengali',
0x09FA => 'issharbengali',
0x0A02 => 'bindigurmukhi',
0x0A05 => 'agurmukhi',
0x0A06 => 'aagurmukhi',
0x0A07 => 'igurmukhi',
0x0A08 => 'iigurmukhi',
0x0A09 => 'ugurmukhi',
0x0A0A => 'uugurmukhi',
0x0A0F => 'eegurmukhi',
0x0A10 => 'aigurmukhi',
0x0A13 => 'oogurmukhi',
0x0A14 => 'augurmukhi',
0x0A15 => 'kagurmukhi',
0x0A16 => 'khagurmukhi',
0x0A17 => 'gagurmukhi',
0x0A18 => 'ghagurmukhi',
0x0A19 => 'ngagurmukhi',
0x0A1A => 'cagurmukhi',
0x0A1B => 'chagurmukhi',
0x0A1C => 'jagurmukhi',
0x0A1D => 'jhagurmukhi',
0x0A1E => 'nyagurmukhi',
0x0A1F => 'ttagurmukhi',
0x0A20 => 'tthagurmukhi',
0x0A21 => 'ddagurmukhi',
0x0A22 => 'ddhagurmukhi',
0x0A23 => 'nnagurmukhi',
0x0A24 => 'tagurmukhi',
0x0A25 => 'thagurmukhi',
0x0A26 => 'dagurmukhi',
0x0A27 => 'dhagurmukhi',
0x0A28 => 'nagurmukhi',
0x0A2A => 'pagurmukhi',
0x0A2B => 'phagurmukhi',
0x0A2C => 'bagurmukhi',
0x0A2D => 'bhagurmukhi',
0x0A2E => 'magurmukhi',
0x0A2F => 'yagurmukhi',
0x0A30 => 'ragurmukhi',
0x0A32 => 'lagurmukhi',
0x0A35 => 'vagurmukhi',
0x0A36 => 'shagurmukhi',
0x0A38 => 'sagurmukhi',
0x0A39 => 'hagurmukhi',
0x0A3C => 'nuktagurmukhi',
0x0A3E => 'aamatragurmukhi',
0x0A3F => 'imatragurmukhi',
0x0A40 => 'iimatragurmukhi',
0x0A41 => 'umatragurmukhi',
0x0A42 => 'uumatragurmukhi',
0x0A47 => 'eematragurmukhi',
0x0A48 => 'aimatragurmukhi',
0x0A4B => 'oomatragurmukhi',
0x0A4C => 'aumatragurmukhi',
0x0A4D => 'halantgurmukhi',
0x0A59 => 'khhagurmukhi',
0x0A5A => 'ghhagurmukhi',
0x0A5B => 'zagurmukhi',
0x0A5C => 'rragurmukhi',
0x0A5E => 'fagurmukhi',
0x0A66 => 'zerogurmukhi',
0x0A67 => 'onegurmukhi',
0x0A68 => 'twogurmukhi',
0x0A69 => 'threegurmukhi',
0x0A6A => 'fourgurmukhi',
0x0A6B => 'fivegurmukhi',
0x0A6C => 'sixgurmukhi',
0x0A6D => 'sevengurmukhi',
0x0A6E => 'eightgurmukhi',
0x0A6F => 'ninegurmukhi',
0x0A70 => 'tippigurmukhi',
0x0A71 => 'addakgurmukhi',
0x0A72 => 'irigurmukhi',
0x0A73 => 'uragurmukhi',
0x0A74 => 'ekonkargurmukhi',
0x0A81 => 'candrabindugujarati',
0x0A82 => 'anusvaragujarati',
0x0A83 => 'visargagujarati',
0x0A85 => 'agujarati',
0x0A86 => 'aagujarati',
0x0A87 => 'igujarati',
0x0A88 => 'iigujarati',
0x0A89 => 'ugujarati',
0x0A8A => 'uugujarati',
0x0A8B => 'rvocalicgujarati',
0x0A8D => 'ecandragujarati',
0x0A8F => 'egujarati',
0x0A90 => 'aigujarati',
0x0A91 => 'ocandragujarati',
0x0A93 => 'ogujarati',
0x0A94 => 'augujarati',
0x0A95 => 'kagujarati',
0x0A96 => 'khagujarati',
0x0A97 => 'gagujarati',
0x0A98 => 'ghagujarati',
0x0A99 => 'ngagujarati',
0x0A9A => 'cagujarati',
0x0A9B => 'chagujarati',
0x0A9C => 'jagujarati',
0x0A9D => 'jhagujarati',
0x0A9E => 'nyagujarati',
0x0A9F => 'ttagujarati',
0x0AA0 => 'tthagujarati',
0x0AA1 => 'ddagujarati',
0x0AA2 => 'ddhagujarati',
0x0AA3 => 'nnagujarati',
0x0AA4 => 'tagujarati',
0x0AA5 => 'thagujarati',
0x0AA6 => 'dagujarati',
0x0AA7 => 'dhagujarati',
0x0AA8 => 'nagujarati',
0x0AAA => 'pagujarati',
0x0AAB => 'phagujarati',
0x0AAC => 'bagujarati',
0x0AAD => 'bhagujarati',
0x0AAE => 'magujarati',
0x0AAF => 'yagujarati',
0x0AB0 => 'ragujarati',
0x0AB2 => 'lagujarati',
0x0AB3 => 'llagujarati',
0x0AB5 => 'vagujarati',
0x0AB6 => 'shagujarati',
0x0AB7 => 'ssagujarati',
0x0AB8 => 'sagujarati',
0x0AB9 => 'hagujarati',
0x0ABC => 'nuktagujarati',
0x0ABE => 'aavowelsigngujarati',
0x0ABF => 'ivowelsigngujarati',
0x0AC0 => 'iivowelsigngujarati',
0x0AC1 => 'uvowelsigngujarati',
0x0AC2 => 'uuvowelsigngujarati',
0x0AC3 => 'rvocalicvowelsigngujarati',
0x0AC4 => 'rrvocalicvowelsigngujarati',
0x0AC5 => 'ecandravowelsigngujarati',
0x0AC7 => 'evowelsigngujarati',
0x0AC8 => 'aivowelsigngujarati',
0x0AC9 => 'ocandravowelsigngujarati',
0x0ACB => 'ovowelsigngujarati',
0x0ACC => 'auvowelsigngujarati',
0x0ACD => 'viramagujarati',
0x0AD0 => 'omgujarati',
0x0AE0 => 'rrvocalicgujarati',
0x0AE6 => 'zerogujarati',
0x0AE7 => 'onegujarati',
0x0AE8 => 'twogujarati',
0x0AE9 => 'threegujarati',
0x0AEA => 'fourgujarati',
0x0AEB => 'fivegujarati',
0x0AEC => 'sixgujarati',
0x0AED => 'sevengujarati',
0x0AEE => 'eightgujarati',
0x0AEF => 'ninegujarati',
0x0E01 => 'kokaithai',
0x0E02 => 'khokhaithai',
0x0E03 => 'khokhuatthai',
0x0E04 => 'khokhwaithai',
0x0E05 => 'khokhonthai',
0x0E06 => 'khorakhangthai',
0x0E07 => 'ngonguthai',
0x0E08 => 'chochanthai',
0x0E09 => 'chochingthai',
0x0E0A => 'chochangthai',
0x0E0B => 'sosothai',
0x0E0C => 'chochoethai',
0x0E0D => 'yoyingthai',
0x0E0E => 'dochadathai',
0x0E0F => 'topatakthai',
0x0E10 => 'thothanthai',
0x0E11 => 'thonangmonthothai',
0x0E12 => 'thophuthaothai',
0x0E13 => 'nonenthai',
0x0E14 => 'dodekthai',
0x0E15 => 'totaothai',
0x0E16 => 'thothungthai',
0x0E17 => 'thothahanthai',
0x0E18 => 'thothongthai',
0x0E19 => 'nonuthai',
0x0E1A => 'bobaimaithai',
0x0E1B => 'poplathai',
0x0E1C => 'phophungthai',
0x0E1D => 'fofathai',
0x0E1E => 'phophanthai',
0x0E1F => 'fofanthai',
0x0E20 => 'phosamphaothai',
0x0E21 => 'momathai',
0x0E22 => 'yoyakthai',
0x0E23 => 'roruathai',
0x0E24 => 'ruthai',
0x0E25 => 'lolingthai',
0x0E26 => 'luthai',
0x0E27 => 'wowaenthai',
0x0E28 => 'sosalathai',
0x0E29 => 'sorusithai',
0x0E2A => 'sosuathai',
0x0E2B => 'hohipthai',
0x0E2C => 'lochulathai',
0x0E2D => 'oangthai',
0x0E2E => 'honokhukthai',
0x0E2F => 'paiyannoithai',
0x0E30 => 'saraathai',
0x0E31 => 'maihanakatthai',
0x0E32 => 'saraaathai',
0x0E33 => 'saraamthai',
0x0E34 => 'saraithai',
0x0E35 => 'saraiithai',
0x0E36 => 'sarauethai',
0x0E37 => 'saraueethai',
0x0E38 => 'sarauthai',
0x0E39 => 'sarauuthai',
0x0E3A => 'phinthuthai',
0x0E3F => 'bahtthai',
0x0E40 => 'saraethai',
0x0E41 => 'saraaethai',
0x0E42 => 'saraothai',
0x0E43 => 'saraaimaimuanthai',
0x0E44 => 'saraaimaimalaithai',
0x0E45 => 'lakkhangyaothai',
0x0E46 => 'maiyamokthai',
0x0E47 => 'maitaikhuthai',
0x0E48 => 'maiekthai',
0x0E49 => 'maithothai',
0x0E4A => 'maitrithai',
0x0E4B => 'maichattawathai',
0x0E4C => 'thanthakhatthai',
0x0E4D => 'nikhahitthai',
0x0E4E => 'yamakkanthai',
0x0E4F => 'fongmanthai',
0x0E50 => 'zerothai',
0x0E51 => 'onethai',
0x0E52 => 'twothai',
0x0E53 => 'threethai',
0x0E54 => 'fourthai',
0x0E55 => 'fivethai',
0x0E56 => 'sixthai',
0x0E57 => 'seventhai',
0x0E58 => 'eightthai',
0x0E59 => 'ninethai',
0x0E5A => 'angkhankhuthai',
0x0E5B => 'khomutthai',
0x1E00 => 'Aringbelow',
0x1E01 => 'aringbelow',
0x1E02 => 'Bdotaccent',
0x1E03 => 'bdotaccent',
0x1E04 => 'Bdotbelow',
0x1E05 => 'bdotbelow',
0x1E06 => 'Blinebelow',
0x1E07 => 'blinebelow',
0x1E08 => 'Ccedillaacute',
0x1E09 => 'ccedillaacute',
0x1E0A => 'Ddotaccent',
0x1E0B => 'ddotaccent',
0x1E0C => 'Ddotbelow',
0x1E0D => 'ddotbelow',
0x1E0E => 'Dlinebelow',
0x1E0F => 'dlinebelow',
0x1E10 => 'Dcedilla',
0x1E11 => 'dcedilla',
0x1E12 => 'Dcircumflexbelow',
0x1E13 => 'dcircumflexbelow',
0x1E14 => 'Emacrongrave',
0x1E15 => 'emacrongrave',
0x1E16 => 'Emacronacute',
0x1E17 => 'emacronacute',
0x1E18 => 'Ecircumflexbelow',
0x1E19 => 'ecircumflexbelow',
0x1E1A => 'Etildebelow',
0x1E1B => 'etildebelow',
0x1E1C => 'Ecedillabreve',
0x1E1D => 'ecedillabreve',
0x1E1E => 'Fdotaccent',
0x1E1F => 'fdotaccent',
0x1E20 => 'Gmacron',
0x1E21 => 'gmacron',
0x1E22 => 'Hdotaccent',
0x1E23 => 'hdotaccent',
0x1E24 => 'Hdotbelow',
0x1E25 => 'hdotbelow',
0x1E26 => 'Hdieresis',
0x1E27 => 'hdieresis',
0x1E28 => 'Hcedilla',
0x1E29 => 'hcedilla',
0x1E2A => 'Hbrevebelow',
0x1E2B => 'hbrevebelow',
0x1E2C => 'Itildebelow',
0x1E2D => 'itildebelow',
0x1E2E => 'Idieresisacute',
0x1E2F => 'idieresisacute',
0x1E30 => 'Kacute',
0x1E31 => 'kacute',
0x1E32 => 'Kdotbelow',
0x1E33 => 'kdotbelow',
0x1E34 => 'Klinebelow',
0x1E35 => 'klinebelow',
0x1E36 => 'Ldotbelow',
0x1E37 => 'ldotbelow',
0x1E38 => 'Ldotbelowmacron',
0x1E39 => 'ldotbelowmacron',
0x1E3A => 'Llinebelow',
0x1E3B => 'llinebelow',
0x1E3C => 'Lcircumflexbelow',
0x1E3D => 'lcircumflexbelow',
0x1E3E => 'Macute',
0x1E3F => 'macute',
0x1E40 => 'Mdotaccent',
0x1E41 => 'mdotaccent',
0x1E42 => 'Mdotbelow',
0x1E43 => 'mdotbelow',
0x1E44 => 'Ndotaccent',
0x1E45 => 'ndotaccent',
0x1E46 => 'Ndotbelow',
0x1E47 => 'ndotbelow',
0x1E48 => 'Nlinebelow',
0x1E49 => 'nlinebelow',
0x1E4A => 'Ncircumflexbelow',
0x1E4B => 'ncircumflexbelow',
0x1E4C => 'Otildeacute',
0x1E4D => 'otildeacute',
0x1E4E => 'Otildedieresis',
0x1E4F => 'otildedieresis',
0x1E50 => 'Omacrongrave',
0x1E51 => 'omacrongrave',
0x1E52 => 'Omacronacute',
0x1E53 => 'omacronacute',
0x1E54 => 'Pacute',
0x1E55 => 'pacute',
0x1E56 => 'Pdotaccent',
0x1E57 => 'pdotaccent',
0x1E58 => 'Rdotaccent',
0x1E59 => 'rdotaccent',
0x1E5A => 'Rdotbelow',
0x1E5B => 'rdotbelow',
0x1E5C => 'Rdotbelowmacron',
0x1E5D => 'rdotbelowmacron',
0x1E5E => 'Rlinebelow',
0x1E5F => 'rlinebelow',
0x1E60 => 'Sdotaccent',
0x1E61 => 'sdotaccent',
0x1E62 => 'Sdotbelow',
0x1E63 => 'sdotbelow',
0x1E64 => 'Sacutedotaccent',
0x1E65 => 'sacutedotaccent',
0x1E66 => 'Scarondotaccent',
0x1E67 => 'scarondotaccent',
0x1E68 => 'Sdotbelowdotaccent',
0x1E69 => 'sdotbelowdotaccent',
0x1E6A => 'Tdotaccent',
0x1E6B => 'tdotaccent',
0x1E6C => 'Tdotbelow',
0x1E6D => 'tdotbelow',
0x1E6E => 'Tlinebelow',
0x1E6F => 'tlinebelow',
0x1E70 => 'Tcircumflexbelow',
0x1E71 => 'tcircumflexbelow',
0x1E72 => 'Udieresisbelow',
0x1E73 => 'udieresisbelow',
0x1E74 => 'Utildebelow',
0x1E75 => 'utildebelow',
0x1E76 => 'Ucircumflexbelow',
0x1E77 => 'ucircumflexbelow',
0x1E78 => 'Utildeacute',
0x1E79 => 'utildeacute',
0x1E7A => 'Umacrondieresis',
0x1E7B => 'umacrondieresis',
0x1E7C => 'Vtilde',
0x1E7D => 'vtilde',
0x1E7E => 'Vdotbelow',
0x1E7F => 'vdotbelow',
0x1E80 => 'Wgrave',
0x1E81 => 'wgrave',
0x1E82 => 'Wacute',
0x1E83 => 'wacute',
0x1E84 => 'Wdieresis',
0x1E85 => 'wdieresis',
0x1E86 => 'Wdotaccent',
0x1E87 => 'wdotaccent',
0x1E88 => 'Wdotbelow',
0x1E89 => 'wdotbelow',
0x1E8A => 'Xdotaccent',
0x1E8B => 'xdotaccent',
0x1E8C => 'Xdieresis',
0x1E8D => 'xdieresis',
0x1E8E => 'Ydotaccent',
0x1E8F => 'ydotaccent',
0x1E90 => 'Zcircumflex',
0x1E91 => 'zcircumflex',
0x1E92 => 'Zdotbelow',
0x1E93 => 'zdotbelow',
0x1E94 => 'Zlinebelow',
0x1E95 => 'zlinebelow',
0x1E96 => 'hlinebelow',
0x1E97 => 'tdieresis',
0x1E98 => 'wring',
0x1E99 => 'yring',
0x1E9A => 'arighthalfring',
0x1E9B => 'slongdotaccent',
0x1EA0 => 'Adotbelow',
0x1EA1 => 'adotbelow',
0x1EA2 => 'Ahookabove',
0x1EA3 => 'ahookabove',
0x1EA4 => 'Acircumflexacute',
0x1EA5 => 'acircumflexacute',
0x1EA6 => 'Acircumflexgrave',
0x1EA7 => 'acircumflexgrave',
0x1EA8 => 'Acircumflexhookabove',
0x1EA9 => 'acircumflexhookabove',
0x1EAA => 'Acircumflextilde',
0x1EAB => 'acircumflextilde',
0x1EAC => 'Acircumflexdotbelow',
0x1EAD => 'acircumflexdotbelow',
0x1EAE => 'Abreveacute',
0x1EAF => 'abreveacute',
0x1EB0 => 'Abrevegrave',
0x1EB1 => 'abrevegrave',
0x1EB2 => 'Abrevehookabove',
0x1EB3 => 'abrevehookabove',
0x1EB4 => 'Abrevetilde',
0x1EB5 => 'abrevetilde',
0x1EB6 => 'Abrevedotbelow',
0x1EB7 => 'abrevedotbelow',
0x1EB8 => 'Edotbelow',
0x1EB9 => 'edotbelow',
0x1EBA => 'Ehookabove',
0x1EBB => 'ehookabove',
0x1EBC => 'Etilde',
0x1EBD => 'etilde',
0x1EBE => 'Ecircumflexacute',
0x1EBF => 'ecircumflexacute',
0x1EC0 => 'Ecircumflexgrave',
0x1EC1 => 'ecircumflexgrave',
0x1EC2 => 'Ecircumflexhookabove',
0x1EC3 => 'ecircumflexhookabove',
0x1EC4 => 'Ecircumflextilde',
0x1EC5 => 'ecircumflextilde',
0x1EC6 => 'Ecircumflexdotbelow',
0x1EC7 => 'ecircumflexdotbelow',
0x1EC8 => 'Ihookabove',
0x1EC9 => 'ihookabove',
0x1ECA => 'Idotbelow',
0x1ECB => 'idotbelow',
0x1ECC => 'Odotbelow',
0x1ECD => 'odotbelow',
0x1ECE => 'Ohookabove',
0x1ECF => 'ohookabove',
0x1ED0 => 'Ocircumflexacute',
0x1ED1 => 'ocircumflexacute',
0x1ED2 => 'Ocircumflexgrave',
0x1ED3 => 'ocircumflexgrave',
0x1ED4 => 'Ocircumflexhookabove',
0x1ED5 => 'ocircumflexhookabove',
0x1ED6 => 'Ocircumflextilde',
0x1ED7 => 'ocircumflextilde',
0x1ED8 => 'Ocircumflexdotbelow',
0x1ED9 => 'ocircumflexdotbelow',
0x1EDA => 'Ohornacute',
0x1EDB => 'ohornacute',
0x1EDC => 'Ohorngrave',
0x1EDD => 'ohorngrave',
0x1EDE => 'Ohornhookabove',
0x1EDF => 'ohornhookabove',
0x1EE0 => 'Ohorntilde',
0x1EE1 => 'ohorntilde',
0x1EE2 => 'Ohorndotbelow',
0x1EE3 => 'ohorndotbelow',
0x1EE4 => 'Udotbelow',
0x1EE5 => 'udotbelow',
0x1EE6 => 'Uhookabove',
0x1EE7 => 'uhookabove',
0x1EE8 => 'Uhornacute',
0x1EE9 => 'uhornacute',
0x1EEA => 'Uhorngrave',
0x1EEB => 'uhorngrave',
0x1EEC => 'Uhornhookabove',
0x1EED => 'uhornhookabove',
0x1EEE => 'Uhorntilde',
0x1EEF => 'uhorntilde',
0x1EF0 => 'Uhorndotbelow',
0x1EF1 => 'uhorndotbelow',
0x1EF2 => 'Ygrave',
0x1EF3 => 'ygrave',
0x1EF4 => 'Ydotbelow',
0x1EF5 => 'ydotbelow',
0x1EF6 => 'Yhookabove',
0x1EF7 => 'yhookabove',
0x1EF8 => 'Ytilde',
0x1EF9 => 'ytilde',
0x2002 => 'enspace',
0x200B => 'zerowidthspace',
0x200C => 'afii61664',
0x200C => 'zerowidthnonjoiner',
0x200D => 'afii301',
0x200E => 'afii299',
0x200F => 'afii300',
0x2010 => 'hyphentwo',
0x2012 => 'figuredash',
0x2013 => 'endash',
0x2014 => 'emdash',
0x2015 => 'afii00208',
0x2015 => 'horizontalbar',
0x2016 => 'dblverticalbar',
0x2017 => 'dbllowline',
0x2017 => 'underscoredbl',
0x2018 => 'quoteleft',
0x2019 => 'quoteright',
0x201A => 'quotesinglbase',
0x201B => 'quoteleftreversed',
0x201B => 'quotereversed',
0x201C => 'quotedblleft',
0x201D => 'quotedblright',
0x201E => 'quotedblbase',
0x2020 => 'dagger',
0x2021 => 'daggerdbl',
0x2022 => 'bullet',
0x2024 => 'onedotenleader',
0x2025 => 'twodotenleader',
0x2025 => 'twodotleader',
0x2026 => 'ellipsis',
0x202C => 'afii61573',
0x202D => 'afii61574',
0x202E => 'afii61575',
0x2030 => 'perthousand',
0x2032 => 'minute',
0x2033 => 'second',
0x2035 => 'primereversed',
0x2039 => 'guilsinglleft',
0x203A => 'guilsinglright',
0x203B => 'referencemark',
0x203C => 'exclamdbl',
0x203E => 'overline',
0x2042 => 'asterism',
0x2044 => 'fraction',
0x2070 => 'zerosuperior',
0x2074 => 'foursuperior',
0x2075 => 'fivesuperior',
0x2076 => 'sixsuperior',
0x2077 => 'sevensuperior',
0x2078 => 'eightsuperior',
0x2079 => 'ninesuperior',
0x207A => 'plussuperior',
0x207C => 'equalsuperior',
0x207D => 'parenleftsuperior',
0x207E => 'parenrightsuperior',
0x207F => 'nsuperior',
0x2080 => 'zeroinferior',
0x2081 => 'oneinferior',
0x2082 => 'twoinferior',
0x2083 => 'threeinferior',
0x2084 => 'fourinferior',
0x2085 => 'fiveinferior',
0x2086 => 'sixinferior',
0x2087 => 'seveninferior',
0x2088 => 'eightinferior',
0x2089 => 'nineinferior',
0x208D => 'parenleftinferior',
0x208E => 'parenrightinferior',
0x20A1 => 'colonmonetary',
0x20A1 => 'colonsign',
0x20A2 => 'cruzeiro',
0x20A3 => 'franc',
0x20A4 => 'afii08941',
0x20A4 => 'lira',
0x20A7 => 'peseta',
0x20A9 => 'won',
0x20AA => 'afii57636',
0x20AA => 'newsheqelsign',
0x20AA => 'sheqel',
0x20AA => 'sheqelhebrew',
0x20AB => 'dong',
0x20AC => 'Euro',
// 0x20AC => 'euro',
0x2103 => 'centigrade',
0x2105 => 'afii61248',
0x2105 => 'careof',
0x2109 => 'fahrenheit',
0x2111 => 'Ifraktur',
0x2113 => 'afii61289',
0x2113 => 'lsquare',
0x2116 => 'afii61352',
0x2116 => 'numero',
0x2118 => 'weierstrass',
0x211C => 'Rfraktur',
0x211E => 'prescription',
0x2121 => 'telephone',
0x2122 => 'trademark',
0x2126 => 'Ohm',
0x2126 => 'Omega',
0x212B => 'angstrom',
0x212E => 'estimated',
0x2135 => 'aleph',
0x2153 => 'onethird',
0x2154 => 'twothirds',
0x215B => 'oneeighth',
0x215C => 'threeeighths',
0x215D => 'fiveeighths',
0x215E => 'seveneighths',
0x2160 => 'Oneroman',
0x2161 => 'Tworoman',
0x2162 => 'Threeroman',
0x2163 => 'Fourroman',
0x2164 => 'Fiveroman',
0x2165 => 'Sixroman',
0x2166 => 'Sevenroman',
0x2167 => 'Eightroman',
0x2168 => 'Nineroman',
0x2169 => 'Tenroman',
0x216A => 'Elevenroman',
0x216B => 'Twelveroman',
0x2170 => 'oneroman',
0x2171 => 'tworoman',
0x2172 => 'threeroman',
0x2173 => 'fourroman',
0x2174 => 'fiveroman',
0x2175 => 'sixroman',
0x2176 => 'sevenroman',
0x2177 => 'eightroman',
0x2178 => 'nineroman',
0x2179 => 'tenroman',
0x217A => 'elevenroman',
0x217B => 'twelveroman',
0x2190 => 'arrowleft',
0x2191 => 'arrowup',
0x2192 => 'arrowright',
0x2193 => 'arrowdown',
0x2194 => 'arrowboth',
0x2195 => 'arrowupdn',
0x2196 => 'arrowupleft',
0x2197 => 'arrowupright',
0x2198 => 'arrowdownright',
0x2199 => 'arrowdownleft',
0x21A8 => 'arrowupdnbse',
0x21A8 => 'arrowupdownbase',
0x21B5 => 'carriagereturn',
0x21BC => 'harpoonleftbarbup',
0x21C0 => 'harpoonrightbarbup',
0x21C4 => 'arrowrightoverleft',
0x21C5 => 'arrowupleftofdown',
0x21C6 => 'arrowleftoverright',
0x21CD => 'arrowleftdblstroke',
0x21CF => 'arrowrightdblstroke',
0x21D0 => 'arrowdblleft',
0x21D0 => 'arrowleftdbl',
0x21D1 => 'arrowdblup',
0x21D2 => 'arrowdblright',
0x21D2 => 'dblarrowright',
0x21D3 => 'arrowdbldown',
0x21D4 => 'arrowdblboth',
0x21D4 => 'dblarrowleft',
0x21DE => 'pageup',
0x21DF => 'pagedown',
0x21E0 => 'arrowdashleft',
0x21E1 => 'arrowdashup',
0x21E2 => 'arrowdashright',
0x21E3 => 'arrowdashdown',
0x21E4 => 'arrowtableft',
0x21E5 => 'arrowtabright',
0x21E6 => 'arrowleftwhite',
0x21E7 => 'arrowupwhite',
0x21E8 => 'arrowrightwhite',
0x21E9 => 'arrowdownwhite',
0x21EA => 'capslock',
0x2200 => 'forall',
0x2200 => 'universal',
0x2202 => 'partialdiff',
0x2203 => 'existential',
0x2203 => 'thereexists',
0x2205 => 'emptyset',
0x2206 => 'Delta',
0x2206 => 'increment',
0x2207 => 'gradient',
0x2207 => 'nabla',
0x2208 => 'element',
0x2209 => 'notelement',
0x2209 => 'notelementof',
0x220B => 'suchthat',
0x220C => 'notcontains',
0x220F => 'product',
0x2211 => 'summation',
0x2212 => 'minus',
0x2213 => 'minusplus',
0x2215 => 'divisionslash',
0x2217 => 'asteriskmath',
0x2219 => 'bulletoperator',
0x221A => 'radical',
0x221D => 'proportional',
0x221E => 'infinity',
0x221F => 'orthogonal',
0x221F => 'rightangle',
0x2220 => 'angle',
0x2223 => 'divides',
0x2225 => 'parallel',
0x2226 => 'notparallel',
0x2227 => 'logicaland',
0x2228 => 'logicalor',
0x2229 => 'intersection',
0x222A => 'union',
0x222B => 'integral',
0x222C => 'dblintegral',
0x222E => 'contourintegral',
0x2234 => 'therefore',
0x2235 => 'because',
0x2236 => 'ratio',
0x2237 => 'proportion',
0x223C => 'similar',
0x223C => 'tildeoperator',
0x223D => 'reversedtilde',
0x2243 => 'asymptoticallyequal',
0x2245 => 'approximatelyequal',
0x2245 => 'congruent',
0x2248 => 'approxequal',
0x224C => 'allequal',
0x2250 => 'approaches',
0x2251 => 'geometricallyequal',
0x2252 => 'approxequalorimage',
0x2253 => 'imageorapproximatelyequal',
0x2260 => 'notequal',
0x2261 => 'equivalence',
0x2262 => 'notidentical',
0x2264 => 'lessequal',
0x2265 => 'greaterequal',
0x2266 => 'lessoverequal',
0x2267 => 'greateroverequal',
0x226A => 'muchless',
0x226B => 'muchgreater',
0x226E => 'notless',
0x226F => 'notgreater',
0x2270 => 'notlessnorequal',
0x2271 => 'notgreaternorequal',
0x2272 => 'lessorequivalent',
0x2273 => 'greaterorequivalent',
0x2276 => 'lessorgreater',
0x2277 => 'greaterorless',
0x2279 => 'notgreaternorless',
0x227A => 'precedes',
0x227B => 'succeeds',
0x2280 => 'notprecedes',
0x2281 => 'notsucceeds',
0x2282 => 'propersubset',
0x2282 => 'subset',
0x2283 => 'propersuperset',
0x2283 => 'superset',
0x2284 => 'notsubset',
0x2285 => 'notsuperset',
0x2286 => 'reflexsubset',
0x2286 => 'subsetorequal',
0x2287 => 'reflexsuperset',
0x2287 => 'supersetorequal',
0x228A => 'subsetnotequal',
0x228B => 'supersetnotequal',
0x2295 => 'circleplus',
0x2295 => 'pluscircle',
0x2296 => 'minuscircle',
0x2297 => 'circlemultiply',
0x2297 => 'timescircle',
0x2299 => 'circleot',
0x22A3 => 'tackleft',
0x22A4 => 'tackdown',
0x22A5 => 'perpendicular',
0x22BF => 'righttriangle',
0x22C5 => 'dotmath',
0x22CE => 'curlyor',
0x22CF => 'curlyand',
0x22DA => 'lessequalorgreater',
0x22DB => 'greaterequalorless',
0x22EE => 'ellipsisvertical',
0x2302 => 'house',
0x2303 => 'control',
0x2305 => 'projective',
0x2310 => 'logicalnotreversed',
0x2310 => 'revlogicalnot',
0x2312 => 'arc',
0x2318 => 'propellor',
0x2320 => 'integraltop',
0x2320 => 'integraltp',
0x2321 => 'integralbottom',
0x2321 => 'integralbt',
0x2325 => 'option',
0x2326 => 'deleteright',
0x2327 => 'clear',
0x2329 => 'angleleft',
0x232A => 'angleright',
0x232B => 'deleteleft',
0x2423 => 'blank',
0x2460 => 'onecircle',
0x2461 => 'twocircle',
0x2462 => 'threecircle',
0x2463 => 'fourcircle',
0x2464 => 'fivecircle',
0x2465 => 'sixcircle',
0x2466 => 'sevencircle',
0x2467 => 'eightcircle',
0x2468 => 'ninecircle',
0x2469 => 'tencircle',
0x246A => 'elevencircle',
0x246B => 'twelvecircle',
0x246C => 'thirteencircle',
0x246D => 'fourteencircle',
0x246E => 'fifteencircle',
0x246F => 'sixteencircle',
0x2470 => 'seventeencircle',
0x2471 => 'eighteencircle',
0x2472 => 'nineteencircle',
0x2473 => 'twentycircle',
0x2474 => 'oneparen',
0x2475 => 'twoparen',
0x2476 => 'threeparen',
0x2477 => 'fourparen',
0x2478 => 'fiveparen',
0x2479 => 'sixparen',
0x247A => 'sevenparen',
0x247B => 'eightparen',
0x247C => 'nineparen',
0x247D => 'tenparen',
0x247E => 'elevenparen',
0x247F => 'twelveparen',
0x2480 => 'thirteenparen',
0x2481 => 'fourteenparen',
0x2482 => 'fifteenparen',
0x2483 => 'sixteenparen',
0x2484 => 'seventeenparen',
0x2485 => 'eighteenparen',
0x2486 => 'nineteenparen',
0x2487 => 'twentyparen',
0x2488 => 'oneperiod',
0x2489 => 'twoperiod',
0x248A => 'threeperiod',
0x248B => 'fourperiod',
0x248C => 'fiveperiod',
0x248D => 'sixperiod',
0x248E => 'sevenperiod',
0x248F => 'eightperiod',
0x2490 => 'nineperiod',
0x2491 => 'tenperiod',
0x2492 => 'elevenperiod',
0x2493 => 'twelveperiod',
0x2494 => 'thirteenperiod',
0x2495 => 'fourteenperiod',
0x2496 => 'fifteenperiod',
0x2497 => 'sixteenperiod',
0x2498 => 'seventeenperiod',
0x2499 => 'eighteenperiod',
0x249A => 'nineteenperiod',
0x249B => 'twentyperiod',
0x249C => 'aparen',
0x249D => 'bparen',
0x249E => 'cparen',
0x249F => 'dparen',
0x24A0 => 'eparen',
0x24A1 => 'fparen',
0x24A2 => 'gparen',
0x24A3 => 'hparen',
0x24A4 => 'iparen',
0x24A5 => 'jparen',
0x24A6 => 'kparen',
0x24A7 => 'lparen',
0x24A8 => 'mparen',
0x24A9 => 'nparen',
0x24AA => 'oparen',
0x24AB => 'pparen',
0x24AC => 'qparen',
0x24AD => 'rparen',
0x24AE => 'sparen',
0x24AF => 'tparen',
0x24B0 => 'uparen',
0x24B1 => 'vparen',
0x24B2 => 'wparen',
0x24B3 => 'xparen',
0x24B4 => 'yparen',
0x24B5 => 'zparen',
0x24B6 => 'Acircle',
0x24B7 => 'Bcircle',
0x24B8 => 'Ccircle',
0x24B9 => 'Dcircle',
0x24BA => 'Ecircle',
0x24BB => 'Fcircle',
0x24BC => 'Gcircle',
0x24BD => 'Hcircle',
0x24BE => 'Icircle',
0x24BF => 'Jcircle',
0x24C0 => 'Kcircle',
0x24C1 => 'Lcircle',
0x24C2 => 'Mcircle',
0x24C3 => 'Ncircle',
0x24C4 => 'Ocircle',
0x24C5 => 'Pcircle',
0x24C6 => 'Qcircle',
0x24C7 => 'Rcircle',
0x24C8 => 'Scircle',
0x24C9 => 'Tcircle',
0x24CA => 'Ucircle',
0x24CB => 'Vcircle',
0x24CC => 'Wcircle',
0x24CD => 'Xcircle',
0x24CE => 'Ycircle',
0x24CF => 'Zcircle',
0x24D0 => 'acircle',
0x24D1 => 'bcircle',
0x24D2 => 'ccircle',
0x24D3 => 'dcircle',
0x24D4 => 'ecircle',
0x24D5 => 'fcircle',
0x24D6 => 'gcircle',
0x24D7 => 'hcircle',
0x24D8 => 'icircle',
0x24D9 => 'jcircle',
0x24DA => 'kcircle',
0x24DB => 'lcircle',
0x24DC => 'mcircle',
0x24DD => 'ncircle',
0x24DE => 'ocircle',
0x24DF => 'pcircle',
0x24E0 => 'qcircle',
0x24E1 => 'rcircle',
0x24E2 => 'scircle',
0x24E3 => 'tcircle',
0x24E4 => 'ucircle',
0x24E5 => 'vcircle',
0x24E6 => 'wcircle',
0x24E7 => 'xcircle',
0x24E8 => 'ycircle',
0x24E9 => 'zcircle',
0X2500 => 'SF100000',
0X2502 => 'SF110000',
0X250C => 'SF010000',
0X2510 => 'SF030000',
0X2514 => 'SF020000',
0X2518 => 'SF040000',
0X251C => 'SF080000',
0X2524 => 'SF090000',
0X252C => 'SF060000',
0X2534 => 'SF070000',
0X253C => 'SF050000',
0X2550 => 'SF430000',
0X2551 => 'SF240000',
0X2552 => 'SF510000',
0X2553 => 'SF520000',
0X2554 => 'SF390000',
0X2555 => 'SF220000',
0X2556 => 'SF210000',
0X2557 => 'SF250000',
0X2558 => 'SF500000',
0X2559 => 'SF490000',
0X255A => 'SF380000',
0X255B => 'SF280000',
0X255C => 'SF270000',
0X255D => 'SF260000',
0X255E => 'SF360000',
0X255F => 'SF370000',
0X2560 => 'SF420000',
0X2561 => 'SF190000',
0X2562 => 'SF200000',
0X2563 => 'SF230000',
0X2564 => 'SF470000',
0X2565 => 'SF480000',
0X2566 => 'SF410000',
0X2567 => 'SF450000',
0X2568 => 'SF460000',
0X2569 => 'SF400000',
0X256A => 'SF540000',
0X256B => 'SF530000',
0X256C => 'SF440000',
0x2580 => 'upblock',
0x2584 => 'dnblock',
0x2588 => 'block',
0x258C => 'lfblock',
0x2590 => 'rtblock',
0x2591 => 'ltshade',
0x2591 => 'shadelight',
0x2592 => 'shade',
0x2592 => 'shademedium',
0x2593 => 'dkshade',
0x2593 => 'shadedark',
0x25A0 => 'blacksquare',
0x25A0 => 'filledbox',
0X25A1 => 'H22073',
0x25A1 => 'whitesquare',
0x25A3 => 'squarewhitewithsmallblack',
0x25A4 => 'squarehorizontalfill',
0x25A5 => 'squareverticalfill',
0x25A6 => 'squareorthogonalcrosshatchfill',
0x25A7 => 'squareupperlefttolowerrightfill',
0x25A8 => 'squareupperrighttolowerleftfill',
0x25A9 => 'squarediagonalcrosshatchfill',
0X25AA => 'H18543',
0x25AA => 'blacksmallsquare',
0X25AB => 'H18551',
0x25AB => 'whitesmallsquare',
0x25AC => 'blackrectangle',
0x25AC => 'filledrect',
0x25B2 => 'blackuppointingtriangle',
0x25B2 => 'triagup',
0x25B3 => 'whiteuppointingtriangle',
0x25B4 => 'blackuppointingsmalltriangle',
0x25B5 => 'whiteuppointingsmalltriangle',
0x25B6 => 'blackrightpointingtriangle',
0x25B7 => 'whiterightpointingtriangle',
0x25B9 => 'whiterightpointingsmalltriangle',
0x25BA => 'blackrightpointingpointer',
0x25BA => 'triagrt',
0x25BC => 'blackdownpointingtriangle',
0x25BC => 'triagdn',
0x25BD => 'whitedownpointingtriangle',
0x25BF => 'whitedownpointingsmalltriangle',
0x25C0 => 'blackleftpointingtriangle',
0x25C1 => 'whiteleftpointingtriangle',
0x25C3 => 'whiteleftpointingsmalltriangle',
0x25C4 => 'blackleftpointingpointer',
0x25C4 => 'triaglf',
0x25C6 => 'blackdiamond',
0x25C7 => 'whitediamond',
0x25C8 => 'whitediamondcontainingblacksmalldiamond',
0x25C9 => 'fisheye',
0x25CA => 'lozenge',
0x25CB => 'circle',
0x25CB => 'whitecircle',
0x25CC => 'dottedcircle',
0x25CE => 'bullseye',
0X25CF => 'H18533',
0x25CF => 'blackcircle',
0x25D0 => 'circlewithlefthalfblack',
0x25D1 => 'circlewithrighthalfblack',
0x25D8 => 'bulletinverse',
0x25D8 => 'invbullet',
0x25D9 => 'invcircle',
0x25D9 => 'whitecircleinverse',
0x25E2 => 'blacklowerrighttriangle',
0x25E3 => 'blacklowerlefttriangle',
0x25E4 => 'blackupperlefttriangle',
0x25E5 => 'blackupperrighttriangle',
0x25E6 => 'openbullet',
0x25E6 => 'whitebullet',
0x25EF => 'largecircle',
0x2605 => 'blackstar',
0x2606 => 'whitestar',
0x260E => 'telephoneblack',
0x260F => 'whitetelephone',
0x261C => 'pointingindexleftwhite',
0x261D => 'pointingindexupwhite',
0x261E => 'pointingindexrightwhite',
0x261F => 'pointingindexdownwhite',
0x262F => 'yinyang',
0x263A => 'smileface',
0x263A => 'whitesmilingface',
0x263B => 'blacksmilingface',
0x263B => 'invsmileface',
0x263C => 'compass',
0x263C => 'sun',
0x2640 => 'female',
0x2640 => 'venus',
0x2641 => 'earth',
0x2642 => 'male',
0x2642 => 'mars',
0x2660 => 'spade',
0x2660 => 'spadesuitblack',
0x2661 => 'heartsuitwhite',
0x2662 => 'diamondsuitwhite',
0x2663 => 'club',
0x2663 => 'clubsuitblack',
0x2664 => 'spadesuitwhite',
0x2665 => 'heart',
0x2665 => 'heartsuitblack',
0x2666 => 'diamond',
0x2667 => 'clubsuitwhite',
0x2668 => 'hotsprings',
0x2669 => 'quarternote',
0x266A => 'musicalnote',
0x266B => 'eighthnotebeamed',
0x266B => 'musicalnotedbl',
0x266C => 'beamedsixteenthnotes',
0x266D => 'musicflatsign',
0x266F => 'musicsharpsign',
0x2713 => 'checkmark',
0x278A => 'onecircleinversesansserif',
0x278B => 'twocircleinversesansserif',
0x278C => 'threecircleinversesansserif',
0x278D => 'fourcircleinversesansserif',
0x278E => 'fivecircleinversesansserif',
0x278F => 'sixcircleinversesansserif',
0x2790 => 'sevencircleinversesansserif',
0x2791 => 'eightcircleinversesansserif',
0x2792 => 'ninecircleinversesansserif',
0x279E => 'arrowrightheavy',
0x3000 => 'ideographicspace',
0x3001 => 'ideographiccomma',
0x3002 => 'ideographicperiod',
0x3003 => 'dittomark',
0x3004 => 'jis',
0x3005 => 'ideographiciterationmark',
0x3006 => 'ideographicclose',
0x3007 => 'ideographiczero',
0x3008 => 'anglebracketleft',
0x3009 => 'anglebracketright',
0x300A => 'dblanglebracketleft',
0x300B => 'dblanglebracketright',
0x300C => 'cornerbracketleft',
0x300D => 'cornerbracketright',
0x300E => 'whitecornerbracketleft',
0x300F => 'whitecornerbracketright',
0x3010 => 'blacklenticularbracketleft',
0x3011 => 'blacklenticularbracketright',
0x3012 => 'postalmark',
0x3013 => 'getamark',
0x3014 => 'tortoiseshellbracketleft',
0x3015 => 'tortoiseshellbracketright',
0x3016 => 'whitelenticularbracketleft',
0x3017 => 'whitelenticularbracketright',
0x3018 => 'whitetortoiseshellbracketleft',
0x3019 => 'whitetortoiseshellbracketright',
0x301C => 'wavedash',
0x301D => 'quotedblprimereversed',
0x301E => 'quotedblprime',
0x3020 => 'postalmarkface',
0x3021 => 'onehangzhou',
0x3022 => 'twohangzhou',
0x3023 => 'threehangzhou',
0x3024 => 'fourhangzhou',
0x3025 => 'fivehangzhou',
0x3026 => 'sixhangzhou',
0x3027 => 'sevenhangzhou',
0x3028 => 'eighthangzhou',
0x3029 => 'ninehangzhou',
0x3036 => 'circlepostalmark',
0x3041 => 'asmallhiragana',
0x3042 => 'ahiragana',
0x3043 => 'ismallhiragana',
0x3044 => 'ihiragana',
0x3045 => 'usmallhiragana',
0x3046 => 'uhiragana',
0x3047 => 'esmallhiragana',
0x3048 => 'ehiragana',
0x3049 => 'osmallhiragana',
0x304A => 'ohiragana',
0x304B => 'kahiragana',
0x304C => 'gahiragana',
0x304D => 'kihiragana',
0x304E => 'gihiragana',
0x304F => 'kuhiragana',
0x3050 => 'guhiragana',
0x3051 => 'kehiragana',
0x3052 => 'gehiragana',
0x3053 => 'kohiragana',
0x3054 => 'gohiragana',
0x3055 => 'sahiragana',
0x3056 => 'zahiragana',
0x3057 => 'sihiragana',
0x3058 => 'zihiragana',
0x3059 => 'suhiragana',
0x305A => 'zuhiragana',
0x305B => 'sehiragana',
0x305C => 'zehiragana',
0x305D => 'sohiragana',
0x305E => 'zohiragana',
0x305F => 'tahiragana',
0x3060 => 'dahiragana',
0x3061 => 'tihiragana',
0x3062 => 'dihiragana',
0x3063 => 'tusmallhiragana',
0x3064 => 'tuhiragana',
0x3065 => 'duhiragana',
0x3066 => 'tehiragana',
0x3067 => 'dehiragana',
0x3068 => 'tohiragana',
0x3069 => 'dohiragana',
0x306A => 'nahiragana',
0x306B => 'nihiragana',
0x306C => 'nuhiragana',
0x306D => 'nehiragana',
0x306E => 'nohiragana',
0x306F => 'hahiragana',
0x3070 => 'bahiragana',
0x3071 => 'pahiragana',
0x3072 => 'hihiragana',
0x3073 => 'bihiragana',
0x3074 => 'pihiragana',
0x3075 => 'huhiragana',
0x3076 => 'buhiragana',
0x3077 => 'puhiragana',
0x3078 => 'hehiragana',
0x3079 => 'behiragana',
0x307A => 'pehiragana',
0x307B => 'hohiragana',
0x307C => 'bohiragana',
0x307D => 'pohiragana',
0x307E => 'mahiragana',
0x307F => 'mihiragana',
0x3080 => 'muhiragana',
0x3081 => 'mehiragana',
0x3082 => 'mohiragana',
0x3083 => 'yasmallhiragana',
0x3084 => 'yahiragana',
0x3085 => 'yusmallhiragana',
0x3086 => 'yuhiragana',
0x3087 => 'yosmallhiragana',
0x3088 => 'yohiragana',
0x3089 => 'rahiragana',
0x308A => 'rihiragana',
0x308B => 'ruhiragana',
0x308C => 'rehiragana',
0x308D => 'rohiragana',
0x308E => 'wasmallhiragana',
0x308F => 'wahiragana',
0x3090 => 'wihiragana',
0x3091 => 'wehiragana',
0x3092 => 'wohiragana',
0x3093 => 'nhiragana',
0x3094 => 'vuhiragana',
0x309B => 'voicedmarkkana',
0x309C => 'semivoicedmarkkana',
0x309D => 'iterationhiragana',
0x309E => 'voicediterationhiragana',
0x30A1 => 'asmallkatakana',
0x30A2 => 'akatakana',
0x30A3 => 'ismallkatakana',
0x30A4 => 'ikatakana',
0x30A5 => 'usmallkatakana',
0x30A6 => 'ukatakana',
0x30A7 => 'esmallkatakana',
0x30A8 => 'ekatakana',
0x30A9 => 'osmallkatakana',
0x30AA => 'okatakana',
0x30AB => 'kakatakana',
0x30AC => 'gakatakana',
0x30AD => 'kikatakana',
0x30AE => 'gikatakana',
0x30AF => 'kukatakana',
0x30B0 => 'gukatakana',
0x30B1 => 'kekatakana',
0x30B2 => 'gekatakana',
0x30B3 => 'kokatakana',
0x30B4 => 'gokatakana',
0x30B5 => 'sakatakana',
0x30B6 => 'zakatakana',
0x30B7 => 'sikatakana',
0x30B8 => 'zikatakana',
0x30B9 => 'sukatakana',
0x30BA => 'zukatakana',
0x30BB => 'sekatakana',
0x30BC => 'zekatakana',
0x30BD => 'sokatakana',
0x30BE => 'zokatakana',
0x30BF => 'takatakana',
0x30C0 => 'dakatakana',
0x30C1 => 'tikatakana',
0x30C2 => 'dikatakana',
0x30C3 => 'tusmallkatakana',
0x30C4 => 'tukatakana',
0x30C5 => 'dukatakana',
0x30C6 => 'tekatakana',
0x30C7 => 'dekatakana',
0x30C8 => 'tokatakana',
0x30C9 => 'dokatakana',
0x30CA => 'nakatakana',
0x30CB => 'nikatakana',
0x30CC => 'nukatakana',
0x30CD => 'nekatakana',
0x30CE => 'nokatakana',
0x30CF => 'hakatakana',
0x30D0 => 'bakatakana',
0x30D1 => 'pakatakana',
0x30D2 => 'hikatakana',
0x30D3 => 'bikatakana',
0x30D4 => 'pikatakana',
0x30D5 => 'hukatakana',
0x30D6 => 'bukatakana',
0x30D7 => 'pukatakana',
0x30D8 => 'hekatakana',
0x30D9 => 'bekatakana',
0x30DA => 'pekatakana',
0x30DB => 'hokatakana',
0x30DC => 'bokatakana',
0x30DD => 'pokatakana',
0x30DE => 'makatakana',
0x30DF => 'mikatakana',
0x30E0 => 'mukatakana',
0x30E1 => 'mekatakana',
0x30E2 => 'mokatakana',
0x30E3 => 'yasmallkatakana',
0x30E4 => 'yakatakana',
0x30E5 => 'yusmallkatakana',
0x30E6 => 'yukatakana',
0x30E7 => 'yosmallkatakana',
0x30E8 => 'yokatakana',
0x30E9 => 'rakatakana',
0x30EA => 'rikatakana',
0x30EB => 'rukatakana',
0x30EC => 'rekatakana',
0x30ED => 'rokatakana',
0x30EE => 'wasmallkatakana',
0x30EF => 'wakatakana',
0x30F0 => 'wikatakana',
0x30F1 => 'wekatakana',
0x30F2 => 'wokatakana',
0x30F3 => 'nkatakana',
0x30F4 => 'vukatakana',
0x30F5 => 'kasmallkatakana',
0x30F6 => 'kesmallkatakana',
0x30F7 => 'vakatakana',
0x30F8 => 'vikatakana',
0x30F9 => 'vekatakana',
0x30FA => 'vokatakana',
0x30FB => 'dotkatakana',
0x30FC => 'prolongedkana',
0x30FD => 'iterationkatakana',
0x30FE => 'voicediterationkatakana',
0x3105 => 'bbopomofo',
0x3106 => 'pbopomofo',
0x3107 => 'mbopomofo',
0x3108 => 'fbopomofo',
0x3109 => 'dbopomofo',
0x310A => 'tbopomofo',
0x310B => 'nbopomofo',
0x310C => 'lbopomofo',
0x310D => 'gbopomofo',
0x310E => 'kbopomofo',
0x310F => 'hbopomofo',
0x3110 => 'jbopomofo',
0x3111 => 'qbopomofo',
0x3112 => 'xbopomofo',
0x3113 => 'zhbopomofo',
0x3114 => 'chbopomofo',
0x3115 => 'shbopomofo',
0x3116 => 'rbopomofo',
0x3117 => 'zbopomofo',
0x3118 => 'cbopomofo',
0x3119 => 'sbopomofo',
0x311A => 'abopomofo',
0x311B => 'obopomofo',
0x311C => 'ebopomofo',
0x311D => 'ehbopomofo',
0x311E => 'aibopomofo',
0x311F => 'eibopomofo',
0x3120 => 'aubopomofo',
0x3121 => 'oubopomofo',
0x3122 => 'anbopomofo',
0x3123 => 'enbopomofo',
0x3124 => 'angbopomofo',
0x3125 => 'engbopomofo',
0x3126 => 'erbopomofo',
0x3127 => 'ibopomofo',
0x3128 => 'ubopomofo',
0x3129 => 'iubopomofo',
0x3131 => 'kiyeokkorean',
0x3132 => 'ssangkiyeokkorean',
0x3133 => 'kiyeoksioskorean',
0x3134 => 'nieunkorean',
0x3135 => 'nieuncieuckorean',
0x3136 => 'nieunhieuhkorean',
0x3137 => 'tikeutkorean',
0x3138 => 'ssangtikeutkorean',
0x3139 => 'rieulkorean',
0x313A => 'rieulkiyeokkorean',
0x313B => 'rieulmieumkorean',
0x313C => 'rieulpieupkorean',
0x313D => 'rieulsioskorean',
0x313E => 'rieulthieuthkorean',
0x313F => 'rieulphieuphkorean',
0x3140 => 'rieulhieuhkorean',
0x3141 => 'mieumkorean',
0x3142 => 'pieupkorean',
0x3143 => 'ssangpieupkorean',
0x3144 => 'pieupsioskorean',
0x3145 => 'sioskorean',
0x3146 => 'ssangsioskorean',
0x3147 => 'ieungkorean',
0x3148 => 'cieuckorean',
0x3149 => 'ssangcieuckorean',
0x314A => 'chieuchkorean',
0x314B => 'khieukhkorean',
0x314C => 'thieuthkorean',
0x314D => 'phieuphkorean',
0x314E => 'hieuhkorean',
0x314F => 'akorean',
0x3150 => 'aekorean',
0x3151 => 'yakorean',
0x3152 => 'yaekorean',
0x3153 => 'eokorean',
0x3154 => 'ekorean',
0x3155 => 'yeokorean',
0x3156 => 'yekorean',
0x3157 => 'okorean',
0x3158 => 'wakorean',
0x3159 => 'waekorean',
0x315A => 'oekorean',
0x315B => 'yokorean',
0x315C => 'ukorean',
0x315D => 'weokorean',
0x315E => 'wekorean',
0x315F => 'wikorean',
0x3160 => 'yukorean',
0x3161 => 'eukorean',
0x3162 => 'yikorean',
0x3163 => 'ikorean',
0x3164 => 'hangulfiller',
0x3165 => 'ssangnieunkorean',
0x3166 => 'nieuntikeutkorean',
0x3167 => 'nieunsioskorean',
0x3168 => 'nieunpansioskorean',
0x3169 => 'rieulkiyeoksioskorean',
0x316A => 'rieultikeutkorean',
0x316B => 'rieulpieupsioskorean',
0x316C => 'rieulpansioskorean',
0x316D => 'rieulyeorinhieuhkorean',
0x316E => 'mieumpieupkorean',
0x316F => 'mieumsioskorean',
0x3170 => 'mieumpansioskorean',
0x3171 => 'kapyeounmieumkorean',
0x3172 => 'pieupkiyeokkorean',
0x3173 => 'pieuptikeutkorean',
0x3174 => 'pieupsioskiyeokkorean',
0x3175 => 'pieupsiostikeutkorean',
0x3176 => 'pieupcieuckorean',
0x3177 => 'pieupthieuthkorean',
0x3178 => 'kapyeounpieupkorean',
0x3179 => 'kapyeounssangpieupkorean',
0x317A => 'sioskiyeokkorean',
0x317B => 'siosnieunkorean',
0x317C => 'siostikeutkorean',
0x317D => 'siospieupkorean',
0x317E => 'sioscieuckorean',
0x317F => 'pansioskorean',
0x3180 => 'ssangieungkorean',
0x3181 => 'yesieungkorean',
0x3182 => 'yesieungsioskorean',
0x3183 => 'yesieungpansioskorean',
0x3184 => 'kapyeounphieuphkorean',
0x3185 => 'ssanghieuhkorean',
0x3186 => 'yeorinhieuhkorean',
0x3187 => 'yoyakorean',
0x3188 => 'yoyaekorean',
0x3189 => 'yoikorean',
0x318A => 'yuyeokorean',
0x318B => 'yuyekorean',
0x318C => 'yuikorean',
0x318D => 'araeakorean',
0x318E => 'araeaekorean',
0x3200 => 'kiyeokparenkorean',
0x3201 => 'nieunparenkorean',
0x3202 => 'tikeutparenkorean',
0x3203 => 'rieulparenkorean',
0x3204 => 'mieumparenkorean',
0x3205 => 'pieupparenkorean',
0x3206 => 'siosparenkorean',
0x3207 => 'ieungparenkorean',
0x3208 => 'cieucparenkorean',
0x3209 => 'chieuchparenkorean',
0x320A => 'khieukhparenkorean',
0x320B => 'thieuthparenkorean',
0x320C => 'phieuphparenkorean',
0x320D => 'hieuhparenkorean',
0x320E => 'kiyeokaparenkorean',
0x320F => 'nieunaparenkorean',
0x3210 => 'tikeutaparenkorean',
0x3211 => 'rieulaparenkorean',
0x3212 => 'mieumaparenkorean',
0x3213 => 'pieupaparenkorean',
0x3214 => 'siosaparenkorean',
0x3215 => 'ieungaparenkorean',
0x3216 => 'cieucaparenkorean',
0x3217 => 'chieuchaparenkorean',
0x3218 => 'khieukhaparenkorean',
0x3219 => 'thieuthaparenkorean',
0x321A => 'phieuphaparenkorean',
0x321B => 'hieuhaparenkorean',
0x321C => 'cieucuparenkorean',
0x3220 => 'oneideographicparen',
0x3221 => 'twoideographicparen',
0x3222 => 'threeideographicparen',
0x3223 => 'fourideographicparen',
0x3224 => 'fiveideographicparen',
0x3225 => 'sixideographicparen',
0x3226 => 'sevenideographicparen',
0x3227 => 'eightideographicparen',
0x3228 => 'nineideographicparen',
0x3229 => 'tenideographicparen',
0x322A => 'ideographicmoonparen',
0x322B => 'ideographicfireparen',
0x322C => 'ideographicwaterparen',
0x322D => 'ideographicwoodparen',
0x322E => 'ideographicmetalparen',
0x322F => 'ideographicearthparen',
0x3230 => 'ideographicsunparen',
0x3231 => 'ideographicstockparen',
0x3232 => 'ideographichaveparen',
0x3233 => 'ideographicsocietyparen',
0x3234 => 'ideographicnameparen',
0x3235 => 'ideographicspecialparen',
0x3236 => 'ideographicfinancialparen',
0x3237 => 'ideographiccongratulationparen',
0x3238 => 'ideographiclaborparen',
0x3239 => 'ideographicrepresentparen',
0x323A => 'ideographiccallparen',
0x323B => 'ideographicstudyparen',
0x323C => 'ideographicsuperviseparen',
0x323D => 'ideographicenterpriseparen',
0x323E => 'ideographicresourceparen',
0x323F => 'ideographicallianceparen',
0x3240 => 'ideographicfestivalparen',
0x3242 => 'ideographicselfparen',
0x3243 => 'ideographicreachparen',
0x3260 => 'kiyeokcirclekorean',
0x3261 => 'nieuncirclekorean',
0x3262 => 'tikeutcirclekorean',
0x3263 => 'rieulcirclekorean',
0x3264 => 'mieumcirclekorean',
0x3265 => 'pieupcirclekorean',
0x3266 => 'sioscirclekorean',
0x3267 => 'ieungcirclekorean',
0x3268 => 'cieuccirclekorean',
0x3269 => 'chieuchcirclekorean',
0x326A => 'khieukhcirclekorean',
0x326B => 'thieuthcirclekorean',
0x326C => 'phieuphcirclekorean',
0x326D => 'hieuhcirclekorean',
0x326E => 'kiyeokacirclekorean',
0x326F => 'nieunacirclekorean',
0x3270 => 'tikeutacirclekorean',
0x3271 => 'rieulacirclekorean',
0x3272 => 'mieumacirclekorean',
0x3273 => 'pieupacirclekorean',
0x3274 => 'siosacirclekorean',
0x3275 => 'ieungacirclekorean',
0x3276 => 'cieucacirclekorean',
0x3277 => 'chieuchacirclekorean',
0x3278 => 'khieukhacirclekorean',
0x3279 => 'thieuthacirclekorean',
0x327A => 'phieuphacirclekorean',
0x327B => 'hieuhacirclekorean',
0x327F => 'koreanstandardsymbol',
0x328A => 'ideographmooncircle',
0x328B => 'ideographfirecircle',
0x328C => 'ideographwatercircle',
0x328D => 'ideographwoodcircle',
0x328E => 'ideographmetalcircle',
0x328F => 'ideographearthcircle',
0x3290 => 'ideographsuncircle',
0x3294 => 'ideographnamecircle',
0x3296 => 'ideographicfinancialcircle',
0x3298 => 'ideographiclaborcircle',
0x3299 => 'ideographicsecretcircle',
0x329D => 'ideographicexcellentcircle',
0x329E => 'ideographicprintcircle',
0x32A3 => 'ideographiccorrectcircle',
0x32A4 => 'ideographichighcircle',
0x32A5 => 'ideographiccentrecircle',
0x32A6 => 'ideographiclowcircle',
0x32A7 => 'ideographicleftcircle',
0x32A8 => 'ideographicrightcircle',
0x32A9 => 'ideographicmedicinecircle',
0x3300 => 'apaatosquare',
0x3303 => 'aarusquare',
0x3305 => 'intisquare',
0x330D => 'karoriisquare',
0x3314 => 'kirosquare',
0x3315 => 'kiroguramusquare',
0x3316 => 'kiromeetorusquare',
0x3318 => 'guramusquare',
0x331E => 'kooposquare',
0x3322 => 'sentisquare',
0x3323 => 'sentosquare',
0x3326 => 'dorusquare',
0x3327 => 'tonsquare',
0x332A => 'haitusquare',
0x332B => 'paasentosquare',
0x3331 => 'birusquare',
0x3333 => 'huiitosquare',
0x3336 => 'hekutaarusquare',
0x3339 => 'herutusquare',
0x333B => 'peezisquare',
0x3342 => 'hoonsquare',
0x3347 => 'mansyonsquare',
0x3349 => 'mirisquare',
0x334A => 'miribaarusquare',
0x334D => 'meetorusquare',
0x334E => 'yaadosquare',
0x3351 => 'rittorusquare',
0x3357 => 'wattosquare',
0x337B => 'heiseierasquare',
0x337C => 'syouwaerasquare',
0x337D => 'taisyouerasquare',
0x337E => 'meizierasquare',
0x337F => 'corporationsquare',
0x3380 => 'paampssquare',
0x3381 => 'nasquare',
0x3382 => 'muasquare',
0x3383 => 'masquare',
0x3384 => 'kasquare',
0x3385 => 'KBsquare',
0x3386 => 'MBsquare',
0x3387 => 'GBsquare',
0x3388 => 'calsquare',
0x3389 => 'kcalsquare',
0x338A => 'pfsquare',
0x338B => 'nfsquare',
0x338C => 'mufsquare',
0x338D => 'mugsquare',
0x338E => 'squaremg',
0x338F => 'squarekg',
0x3390 => 'Hzsquare',
0x3391 => 'khzsquare',
0x3392 => 'mhzsquare',
0x3393 => 'ghzsquare',
0x3394 => 'thzsquare',
0x3395 => 'mulsquare',
0x3396 => 'mlsquare',
0x3397 => 'dlsquare',
0x3398 => 'klsquare',
0x3399 => 'fmsquare',
0x339A => 'nmsquare',
0x339B => 'mumsquare',
0x339C => 'squaremm',
0x339D => 'squarecm',
0x339E => 'squarekm',
0x339F => 'mmsquaredsquare',
0x33A0 => 'cmsquaredsquare',
0x33A1 => 'squaremsquared',
0x33A2 => 'kmsquaredsquare',
0x33A3 => 'mmcubedsquare',
0x33A4 => 'cmcubedsquare',
0x33A5 => 'mcubedsquare',
0x33A6 => 'kmcubedsquare',
0x33A7 => 'moverssquare',
0x33A8 => 'moverssquaredsquare',
0x33A9 => 'pasquare',
0x33AA => 'kpasquare',
0x33AB => 'mpasquare',
0x33AC => 'gpasquare',
0x33AD => 'radsquare',
0x33AE => 'radoverssquare',
0x33AF => 'radoverssquaredsquare',
0x33B0 => 'pssquare',
0x33B1 => 'nssquare',
0x33B2 => 'mussquare',
0x33B3 => 'mssquare',
0x33B4 => 'pvsquare',
0x33B5 => 'nvsquare',
0x33B6 => 'muvsquare',
0x33B7 => 'mvsquare',
0x33B8 => 'kvsquare',
0x33B9 => 'mvmegasquare',
0x33BA => 'pwsquare',
0x33BB => 'nwsquare',
0x33BC => 'muwsquare',
0x33BD => 'mwsquare',
0x33BE => 'kwsquare',
0x33BF => 'mwmegasquare',
0x33C0 => 'kohmsquare',
0x33C1 => 'mohmsquare',
0x33C2 => 'amsquare',
0x33C3 => 'bqsquare',
0x33C4 => 'squarecc',
0x33C5 => 'cdsquare',
0x33C6 => 'coverkgsquare',
0x33C7 => 'cosquare',
0x33C8 => 'dbsquare',
0x33C9 => 'gysquare',
0x33CA => 'hasquare',
0x33CB => 'HPsquare',
0x33CD => 'KKsquare',
0x33CE => 'squarekmcapital',
0x33CF => 'ktsquare',
0x33D0 => 'lmsquare',
0x33D1 => 'squareln',
0x33D2 => 'squarelog',
0x33D3 => 'lxsquare',
0x33D4 => 'mbsquare',
0x33D5 => 'squaremil',
0x33D6 => 'molsquare',
0x33D8 => 'pmsquare',
0x33DB => 'srsquare',
0x33DC => 'svsquare',
0x33DD => 'wbsquare',
0x5344 => 'twentyhangzhou',
0xF6BE => 'dotlessj',
0XF6BF => 'LL',
0xF6C0 => 'll',
0xF6C3 => 'commaaccent',
0xF6C4 => 'afii10063',
0xF6C5 => 'afii10064',
0xF6C6 => 'afii10192',
0xF6C7 => 'afii10831',
0xF6C8 => 'afii10832',
0xF6C9 => 'Acute',
0xF6CA => 'Caron',
0xF6CB => 'Dieresis',
0xF6CC => 'DieresisAcute',
0xF6CD => 'DieresisGrave',
0xF6CE => 'Grave',
0xF6CF => 'Hungarumlaut',
0xF6D0 => 'Macron',
0xF6D1 => 'cyrBreve',
0xF6D2 => 'cyrFlex',
0xF6D3 => 'dblGrave',
0xF6D4 => 'cyrbreve',
0xF6D5 => 'cyrflex',
0xF6D6 => 'dblgrave',
0xF6D7 => 'dieresisacute',
0xF6D8 => 'dieresisgrave',
0xF6D9 => 'copyrightserif',
0xF6DA => 'registerserif',
0xF6DB => 'trademarkserif',
0xF6DC => 'onefitted',
0xF6DD => 'rupiah',
0xF6DE => 'threequartersemdash',
0xF6DF => 'centinferior',
0xF6E0 => 'centsuperior',
0xF6E1 => 'commainferior',
0xF6E2 => 'commasuperior',
0xF6E3 => 'dollarinferior',
0xF6E4 => 'dollarsuperior',
0xF6E5 => 'hypheninferior',
0xF6E6 => 'hyphensuperior',
0xF6E7 => 'periodinferior',
0xF6E8 => 'periodsuperior',
0xF6E9 => 'asuperior',
0xF6EA => 'bsuperior',
0xF6EB => 'dsuperior',
0xF6EC => 'esuperior',
0xF6ED => 'isuperior',
0xF6EE => 'lsuperior',
0xF6EF => 'msuperior',
0xF6F0 => 'osuperior',
0xF6F1 => 'rsuperior',
0xF6F2 => 'ssuperior',
0xF6F3 => 'tsuperior',
0xF6F4 => 'Brevesmall',
0xF6F5 => 'Caronsmall',
0xF6F6 => 'Circumflexsmall',
0xF6F7 => 'Dotaccentsmall',
0xF6F8 => 'Hungarumlautsmall',
0xF6F9 => 'Lslashsmall',
0xF6FA => 'OEsmall',
0xF6FB => 'Ogoneksmall',
0xF6FC => 'Ringsmall',
0xF6FD => 'Scaronsmall',
0xF6FE => 'Tildesmall',
0xF6FF => 'Zcaronsmall',
0xF721 => 'exclamsmall',
0xF724 => 'dollaroldstyle',
0xF726 => 'ampersandsmall',
0xF730 => 'zerooldstyle',
0xF731 => 'oneoldstyle',
0xF732 => 'twooldstyle',
0xF733 => 'threeoldstyle',
0xF734 => 'fouroldstyle',
0xF735 => 'fiveoldstyle',
0xF736 => 'sixoldstyle',
0xF737 => 'sevenoldstyle',
0xF738 => 'eightoldstyle',
0xF739 => 'nineoldstyle',
0xF73F => 'questionsmall',
0xF760 => 'Gravesmall',
0xF761 => 'Asmall',
0xF762 => 'Bsmall',
0xF763 => 'Csmall',
0xF764 => 'Dsmall',
0xF765 => 'Esmall',
0xF766 => 'Fsmall',
0xF767 => 'Gsmall',
0xF768 => 'Hsmall',
0xF769 => 'Ismall',
0xF76A => 'Jsmall',
0xF76B => 'Ksmall',
0xF76C => 'Lsmall',
0xF76D => 'Msmall',
0xF76E => 'Nsmall',
0xF76F => 'Osmall',
0xF770 => 'Psmall',
0xF771 => 'Qsmall',
0xF772 => 'Rsmall',
0xF773 => 'Ssmall',
0xF774 => 'Tsmall',
0xF775 => 'Usmall',
0xF776 => 'Vsmall',
0xF777 => 'Wsmall',
0xF778 => 'Xsmall',
0xF779 => 'Ysmall',
0xF77A => 'Zsmall',
0xF7A1 => 'exclamdownsmall',
0xF7A2 => 'centoldstyle',
0xF7A8 => 'Dieresissmall',
0xF7AF => 'Macronsmall',
0xF7B4 => 'Acutesmall',
0xF7B8 => 'Cedillasmall',
0xF7BF => 'questiondownsmall',
0xF7E0 => 'Agravesmall',
0xF7E1 => 'Aacutesmall',
0xF7E2 => 'Acircumflexsmall',
0xF7E3 => 'Atildesmall',
0xF7E4 => 'Adieresissmall',
0xF7E5 => 'Aringsmall',
0xF7E6 => 'AEsmall',
0xF7E7 => 'Ccedillasmall',
0xF7E8 => 'Egravesmall',
0xF7E9 => 'Eacutesmall',
0xF7EA => 'Ecircumflexsmall',
0xF7EB => 'Edieresissmall',
0xF7EC => 'Igravesmall',
0xF7ED => 'Iacutesmall',
0xF7EE => 'Icircumflexsmall',
0xF7EF => 'Idieresissmall',
0xF7F0 => 'Ethsmall',
0xF7F1 => 'Ntildesmall',
0xF7F2 => 'Ogravesmall',
0xF7F3 => 'Oacutesmall',
0xF7F4 => 'Ocircumflexsmall',
0xF7F5 => 'Otildesmall',
0xF7F6 => 'Odieresissmall',
0xF7F8 => 'Oslashsmall',
0xF7F9 => 'Ugravesmall',
0xF7FA => 'Uacutesmall',
0xF7FB => 'Ucircumflexsmall',
0xF7FC => 'Udieresissmall',
0xF7FD => 'Yacutesmall',
0xF7FE => 'Thornsmall',
0xF7FF => 'Ydieresissmall',
0xF884 => 'maihanakatleftthai',
0xF885 => 'saraileftthai',
0xF886 => 'saraiileftthai',
0xF887 => 'saraueleftthai',
0xF888 => 'saraueeleftthai',
0xF889 => 'maitaikhuleftthai',
0xF88A => 'maiekupperleftthai',
0xF88B => 'maieklowrightthai',
0xF88C => 'maieklowleftthai',
0xF88D => 'maithoupperleftthai',
0xF88E => 'maitholowrightthai',
0xF88F => 'maitholowleftthai',
0xF890 => 'maitriupperleftthai',
0xF891 => 'maitrilowrightthai',
0xF892 => 'maitrilowleftthai',
0xF893 => 'maichattawaupperleftthai',
0xF894 => 'maichattawalowrightthai',
0xF895 => 'maichattawalowleftthai',
0xF896 => 'thanthakhatupperleftthai',
0xF897 => 'thanthakhatlowrightthai',
0xF898 => 'thanthakhatlowleftthai',
0xF899 => 'nikhahitleftthai',
0xF8E5 => 'radicalex',
0xF8E6 => 'arrowvertex',
0xF8E7 => 'arrowhorizex',
0xF8E8 => 'registersans',
0xF8E9 => 'copyrightsans',
0xF8EA => 'trademarksans',
0xF8EB => 'parenlefttp',
0xF8EC => 'parenleftex',
0xF8ED => 'parenleftbt',
0xF8EE => 'bracketlefttp',
0xF8EF => 'bracketleftex',
0xF8F0 => 'bracketleftbt',
0xF8F1 => 'bracelefttp',
0xF8F2 => 'braceleftmid',
0xF8F3 => 'braceleftbt',
0xF8F4 => 'braceex',
0xF8F5 => 'integralex',
0xF8F6 => 'parenrighttp',
0xF8F7 => 'parenrightex',
0xF8F8 => 'parenrightbt',
0xF8F9 => 'bracketrighttp',
0xF8FA => 'bracketrightex',
0xF8FB => 'bracketrightbt',
0xF8FC => 'bracerighttp',
0xF8FD => 'bracerightmid',
0xF8FE => 'bracerightbt',
0xF8FF => 'apple',
0xFB00 => 'ff',
0xFB01 => 'fi',
0xFB02 => 'fl',
0xFB03 => 'ffi',
0xFB04 => 'ffl',
0xFB1F => 'afii57705',
0xFB1F => 'doubleyodpatah',
0xFB1F => 'doubleyodpatahhebrew',
0xFB1F => 'yodyodpatahhebrew',
0xFB20 => 'ayinaltonehebrew',
0xFB2A => 'afii57694',
0xFB2A => 'shinshindot',
0xFB2A => 'shinshindothebrew',
0xFB2B => 'afii57695',
0xFB2B => 'shinsindot',
0xFB2B => 'shinsindothebrew',
0xFB2C => 'shindageshshindot',
0xFB2C => 'shindageshshindothebrew',
0xFB2D => 'shindageshsindot',
0xFB2D => 'shindageshsindothebrew',
0xFB2E => 'alefpatahhebrew',
0xFB2F => 'alefqamatshebrew',
0xFB30 => 'alefdageshhebrew',
0xFB31 => 'betdagesh',
0xFB31 => 'betdageshhebrew',
0xFB32 => 'gimeldagesh',
0xFB32 => 'gimeldageshhebrew',
0xFB33 => 'daletdagesh',
0xFB33 => 'daletdageshhebrew',
0xFB34 => 'hedagesh',
0xFB34 => 'hedageshhebrew',
0xFB35 => 'afii57723',
0xFB35 => 'vavdagesh',
0xFB35 => 'vavdagesh65',
0xFB35 => 'vavdageshhebrew',
0xFB36 => 'zayindagesh',
0xFB36 => 'zayindageshhebrew',
0xFB38 => 'tetdagesh',
0xFB38 => 'tetdageshhebrew',
0xFB39 => 'yoddagesh',
0xFB39 => 'yoddageshhebrew',
0xFB3A => 'finalkafdagesh',
0xFB3A => 'finalkafdageshhebrew',
0xFB3B => 'kafdagesh',
0xFB3B => 'kafdageshhebrew',
0xFB3C => 'lameddagesh',
0xFB3C => 'lameddageshhebrew',
0xFB3E => 'memdagesh',
0xFB3E => 'memdageshhebrew',
0xFB40 => 'nundagesh',
0xFB40 => 'nundageshhebrew',
0xFB41 => 'samekhdagesh',
0xFB41 => 'samekhdageshhebrew',
0xFB43 => 'pefinaldageshhebrew',
0xFB44 => 'pedagesh',
0xFB44 => 'pedageshhebrew',
0xFB46 => 'tsadidagesh',
0xFB46 => 'tsadidageshhebrew',
0xFB47 => 'qofdagesh',
0xFB47 => 'qofdageshhebrew',
0xFB48 => 'reshdageshhebrew',
0xFB49 => 'shindagesh',
0xFB49 => 'shindageshhebrew',
0xFB4A => 'tavdages',
0xFB4A => 'tavdagesh',
0xFB4A => 'tavdageshhebrew',
0xFB4B => 'afii57700',
0xFB4B => 'vavholam',
0xFB4B => 'vavholamhebrew',
0xFB4C => 'betrafehebrew',
0xFB4D => 'kafrafehebrew',
0xFB4E => 'perafehebrew',
0xFB4F => 'aleflamedhebrew',
0xFB57 => 'pehfinalarabic',
0xFB58 => 'pehinitialarabic',
0xFB59 => 'pehmedialarabic',
0xFB67 => 'ttehfinalarabic',
0xFB68 => 'ttehinitialarabic',
0xFB69 => 'ttehmedialarabic',
0xFB6B => 'vehfinalarabic',
0xFB6C => 'vehinitialarabic',
0xFB6D => 'vehmedialarabic',
0xFB7B => 'tchehfinalarabic',
0xFB7C => 'FEE4 tchehmeeminitialarabic',
0xFB7C => 'tchehinitialarabic',
0xFB7D => 'tchehmedialarabic',
0xFB89 => 'ddalfinalarabic',
0xFB8B => 'jehfinalarabic',
0xFB8D => 'rrehfinalarabic',
0xFB93 => 'gaffinalarabic',
0xFB94 => 'gafinitialarabic',
0xFB95 => 'gafmedialarabic',
0xFB9F => 'noonghunnafinalarabic',
0xFBA4 => 'hehhamzaaboveisolatedarabic',
0xFBA5 => 'hehhamzaabovefinalarabic',
0xFBA7 => 'hehfinalaltonearabic',
0xFBA8 => 'hehinitialaltonearabic',
0xFBA9 => 'hehmedialaltonearabic',
0xFBAF => 'yehbarreefinalarabic',
0xFC08 => 'behmeemisolatedarabic',
0xFC0B => 'tehjeemisolatedarabic',
0xFC0C => 'tehhahisolatedarabic',
0xFC0E => 'tehmeemisolatedarabic',
0xFC48 => 'meemmeemisolatedarabic',
0xFC4B => 'noonjeemisolatedarabic',
0xFC4E => 'noonmeemisolatedarabic',
0xFC58 => 'yehmeemisolatedarabic',
0xFC5E => 'shaddadammatanarabic',
0xFC5F => 'shaddakasratanarabic',
0xFC60 => 'shaddafathaarabic',
0xFC61 => 'shaddadammaarabic',
0xFC62 => 'shaddakasraarabic',
0xFC6D => 'behnoonfinalarabic',
0xFC73 => 'tehnoonfinalarabic',
0xFC8D => 'noonnoonfinalarabic',
0xFC94 => 'yehnoonfinalarabic',
0xFC9F => 'behmeeminitialarabic',
0xFCA1 => 'tehjeeminitialarabic',
0xFCA2 => 'tehhahinitialarabic',
0xFCA4 => 'tehmeeminitialarabic',
0xFCC9 => 'lamjeeminitialarabic',
0xFCCA => 'lamhahinitialarabic',
0xFCCB => 'lamkhahinitialarabic',
0xFCCC => 'lammeeminitialarabic',
0xFCD1 => 'meemmeeminitialarabic',
0xFCD2 => 'noonjeeminitialarabic',
0xFCD5 => 'noonmeeminitialarabic',
0xFCDD => 'yehmeeminitialarabic',
0xFD3E => 'parenleftaltonearabic',
0xFD3F => 'parenrightaltonearabic',
0xFD88 => 'lammeemhahinitialarabic',
0xFDF2 => 'lamlamhehisolatedarabic',
0xFDFA => 'sallallahoualayhewasallamarabic',
0xFE30 => 'twodotleadervertical',
0xFE31 => 'emdashvertical',
0xFE32 => 'endashvertical',
0xFE33 => 'underscorevertical',
0xFE34 => 'wavyunderscorevertical',
0xFE35 => 'parenleftvertical',
0xFE36 => 'parenrightvertical',
0xFE37 => 'braceleftvertical',
0xFE38 => 'bracerightvertical',
0xFE39 => 'tortoiseshellbracketleftvertical',
0xFE3A => 'tortoiseshellbracketrightvertical',
0xFE3B => 'blacklenticularbracketleftvertical',
0xFE3C => 'blacklenticularbracketrightvertical',
0xFE3D => 'dblanglebracketleftvertical',
0xFE3E => 'dblanglebracketrightvertical',
0xFE3F => 'anglebracketleftvertical',
0xFE40 => 'anglebracketrightvertical',
0xFE41 => 'cornerbracketleftvertical',
0xFE42 => 'cornerbracketrightvertical',
0xFE43 => 'whitecornerbracketleftvertical',
0xFE44 => 'whitecornerbracketrightvertical',
0xFE49 => 'overlinedashed',
0xFE4A => 'overlinecenterline',
0xFE4B => 'overlinewavy',
0xFE4C => 'overlinedblwavy',
0xFE4D => 'lowlinedashed',
0xFE4E => 'lowlinecenterline',
0xFE4F => 'underscorewavy',
0xFE50 => 'commasmall',
0xFE52 => 'periodsmall',
0xFE54 => 'semicolonsmall',
0xFE55 => 'colonsmall',
0xFE59 => 'parenleftsmall',
0xFE5A => 'parenrightsmall',
0xFE5B => 'braceleftsmall',
0xFE5C => 'bracerightsmall',
0xFE5D => 'tortoiseshellbracketleftsmall',
0xFE5E => 'tortoiseshellbracketrightsmall',
0xFE5F => 'numbersignsmall',
0xFE61 => 'asterisksmall',
0xFE62 => 'plussmall',
0xFE63 => 'hyphensmall',
0xFE64 => 'lesssmall',
0xFE65 => 'greatersmall',
0xFE66 => 'equalsmall',
0xFE69 => 'dollarsmall',
0xFE6A => 'percentsmall',
0xFE6B => 'atsmall',
0xFE82 => 'alefmaddaabovefinalarabic',
0xFE84 => 'alefhamzaabovefinalarabic',
0xFE86 => 'wawhamzaabovefinalarabic',
0xFE88 => 'alefhamzabelowfinalarabic',
0xFE8A => 'yehhamzaabovefinalarabic',
0xFE8B => 'yehhamzaaboveinitialarabic',
0xFE8C => 'yehhamzaabovemedialarabic',
0xFE8E => 'aleffinalarabic',
0xFE90 => 'behfinalarabic',
0xFE91 => 'behinitialarabic',
0xFE92 => 'behmedialarabic',
0xFE94 => 'tehmarbutafinalarabic',
0xFE96 => 'tehfinalarabic',
0xFE97 => 'tehinitialarabic',
0xFE98 => 'tehmedialarabic',
0xFE9A => 'thehfinalarabic',
0xFE9B => 'thehinitialarabic',
0xFE9C => 'thehmedialarabic',
0xFE9E => 'jeemfinalarabic',
0xFE9F => 'jeeminitialarabic',
0xFEA0 => 'jeemmedialarabic',
0xFEA2 => 'hahfinalarabic',
0xFEA3 => 'hahinitialarabic',
0xFEA4 => 'hahmedialarabic',
0xFEA6 => 'khahfinalarabic',
0xFEA7 => 'khahinitialarabic',
0xFEA8 => 'khahmedialarabic',
0xFEAA => 'dalfinalarabic',
0xFEAC => 'thalfinalarabic',
0xFEAE => 'rehfinalarabic',
0xFEB0 => 'zainfinalarabic',
0xFEB2 => 'seenfinalarabic',
0xFEB3 => 'seeninitialarabic',
0xFEB4 => 'seenmedialarabic',
0xFEB6 => 'sheenfinalarabic',
0xFEB7 => 'sheeninitialarabic',
0xFEB8 => 'sheenmedialarabic',
0xFEBA => 'sadfinalarabic',
0xFEBB => 'sadinitialarabic',
0xFEBC => 'sadmedialarabic',
0xFEBE => 'dadfinalarabic',
0xFEBF => 'dadinitialarabic',
0xFEC0 => 'dadmedialarabic',
0xFEC2 => 'tahfinalarabic',
0xFEC3 => 'tahinitialarabic',
0xFEC4 => 'tahmedialarabic',
0xFEC6 => 'zahfinalarabic',
0xFEC7 => 'zahinitialarabic',
0xFEC8 => 'zahmedialarabic',
0xFECA => 'ainfinalarabic',
0xFECB => 'aininitialarabic',
0xFECC => 'ainmedialarabic',
0xFECE => 'ghainfinalarabic',
0xFECF => 'ghaininitialarabic',
0xFED0 => 'ghainmedialarabic',
0xFED2 => 'fehfinalarabic',
0xFED3 => 'fehinitialarabic',
0xFED4 => 'fehmedialarabic',
0xFED6 => 'qaffinalarabic',
0xFED7 => 'qafinitialarabic',
0xFED8 => 'qafmedialarabic',
0xFEDA => 'kaffinalarabic',
0xFEDB => 'kafinitialarabic',
0xFEDC => 'kafmedialarabic',
0xFEDE => 'lamfinalarabic',
0xFEDF => 'FEE4 FEA0 lammeemjeeminitialarabic',
0xFEDF => 'FEE4 FEA8 lammeemkhahinitialarabic',
0xFEDF => 'laminitialarabic',
0xFEE0 => 'lammedialarabic',
0xFEE2 => 'meemfinalarabic',
0xFEE3 => 'meeminitialarabic',
0xFEE4 => 'meemmedialarabic',
0xFEE6 => 'noonfinalarabic',
0xFEE7 => 'FEEC noonhehinitialarabic',
0xFEE7 => 'nooninitialarabic',
0xFEE8 => 'noonmedialarabic',
0xFEEA => 'hehfinalalttwoarabic',
0xFEEA => 'hehfinalarabic',
0xFEEB => 'hehinitialarabic',
0xFEEC => 'hehmedialarabic',
0xFEEE => 'wawfinalarabic',
0xFEF0 => 'alefmaksurafinalarabic',
0xFEF2 => 'yehfinalarabic',
0xFEF3 => 'alefmaksurainitialarabic',
0xFEF3 => 'yehinitialarabic',
0xFEF4 => 'alefmaksuramedialarabic',
0xFEF4 => 'yehmedialarabic',
0xFEF5 => 'lamalefmaddaaboveisolatedarabic',
0xFEF6 => 'lamalefmaddaabovefinalarabic',
0xFEF7 => 'lamalefhamzaaboveisolatedarabic',
0xFEF8 => 'lamalefhamzaabovefinalarabic',
0xFEF9 => 'lamalefhamzabelowisolatedarabic',
0xFEFA => 'lamalefhamzabelowfinalarabic',
0xFEFB => 'lamalefisolatedarabic',
0xFEFC => 'lamaleffinalarabic',
0xFEFF => 'zerowidthjoiner',
0xFF01 => 'exclammonospace',
0xFF02 => 'quotedblmonospace',
0xFF03 => 'numbersignmonospace',
0xFF04 => 'dollarmonospace',
0xFF05 => 'percentmonospace',
0xFF06 => 'ampersandmonospace',
0xFF07 => 'quotesinglemonospace',
0xFF08 => 'parenleftmonospace',
0xFF09 => 'parenrightmonospace',
0xFF0A => 'asteriskmonospace',
0xFF0B => 'plusmonospace',
0xFF0C => 'commamonospace',
0xFF0D => 'hyphenmonospace',
0xFF0E => 'periodmonospace',
0xFF0F => 'slashmonospace',
0xFF10 => 'zeromonospace',
0xFF11 => 'onemonospace',
0xFF12 => 'twomonospace',
0xFF13 => 'threemonospace',
0xFF14 => 'fourmonospace',
0xFF15 => 'fivemonospace',
0xFF16 => 'sixmonospace',
0xFF17 => 'sevenmonospace',
0xFF18 => 'eightmonospace',
0xFF19 => 'ninemonospace',
0xFF1A => 'colonmonospace',
0xFF1B => 'semicolonmonospace',
0xFF1C => 'lessmonospace',
0xFF1D => 'equalmonospace',
0xFF1E => 'greatermonospace',
0xFF1F => 'questionmonospace',
0xFF20 => 'atmonospace',
0xFF21 => 'Amonospace',
0xFF22 => 'Bmonospace',
0xFF23 => 'Cmonospace',
0xFF24 => 'Dmonospace',
0xFF25 => 'Emonospace',
0xFF26 => 'Fmonospace',
0xFF27 => 'Gmonospace',
0xFF28 => 'Hmonospace',
0xFF29 => 'Imonospace',
0xFF2A => 'Jmonospace',
0xFF2B => 'Kmonospace',
0xFF2C => 'Lmonospace',
0xFF2D => 'Mmonospace',
0xFF2E => 'Nmonospace',
0xFF2F => 'Omonospace',
0xFF30 => 'Pmonospace',
0xFF31 => 'Qmonospace',
0xFF32 => 'Rmonospace',
0xFF33 => 'Smonospace',
0xFF34 => 'Tmonospace',
0xFF35 => 'Umonospace',
0xFF36 => 'Vmonospace',
0xFF37 => 'Wmonospace',
0xFF38 => 'Xmonospace',
0xFF39 => 'Ymonospace',
0xFF3A => 'Zmonospace',
0xFF3B => 'bracketleftmonospace',
0xFF3C => 'backslashmonospace',
0xFF3D => 'bracketrightmonospace',
0xFF3E => 'asciicircummonospace',
0xFF3F => 'underscoremonospace',
0xFF40 => 'gravemonospace',
0xFF41 => 'amonospace',
0xFF42 => 'bmonospace',
0xFF43 => 'cmonospace',
0xFF44 => 'dmonospace',
0xFF45 => 'emonospace',
0xFF46 => 'fmonospace',
0xFF47 => 'gmonospace',
0xFF48 => 'hmonospace',
0xFF49 => 'imonospace',
0xFF4A => 'jmonospace',
0xFF4B => 'kmonospace',
0xFF4C => 'lmonospace',
0xFF4D => 'mmonospace',
0xFF4E => 'nmonospace',
0xFF4F => 'omonospace',
0xFF50 => 'pmonospace',
0xFF51 => 'qmonospace',
0xFF52 => 'rmonospace',
0xFF53 => 'smonospace',
0xFF54 => 'tmonospace',
0xFF55 => 'umonospace',
0xFF56 => 'vmonospace',
0xFF57 => 'wmonospace',
0xFF58 => 'xmonospace',
0xFF59 => 'ymonospace',
0xFF5A => 'zmonospace',
0xFF5B => 'braceleftmonospace',
0xFF5C => 'barmonospace',
0xFF5D => 'bracerightmonospace',
0xFF5E => 'asciitildemonospace',
0xFF61 => 'periodhalfwidth',
0xFF62 => 'cornerbracketlefthalfwidth',
0xFF63 => 'cornerbracketrighthalfwidth',
0xFF64 => 'ideographiccommaleft',
0xFF65 => 'middledotkatakanahalfwidth',
0xFF66 => 'wokatakanahalfwidth',
0xFF67 => 'asmallkatakanahalfwidth',
0xFF68 => 'ismallkatakanahalfwidth',
0xFF69 => 'usmallkatakanahalfwidth',
0xFF6A => 'esmallkatakanahalfwidth',
0xFF6B => 'osmallkatakanahalfwidth',
0xFF6C => 'yasmallkatakanahalfwidth',
0xFF6D => 'yusmallkatakanahalfwidth',
0xFF6E => 'yosmallkatakanahalfwidth',
0xFF6F => 'tusmallkatakanahalfwidth',
0xFF70 => 'katahiraprolongmarkhalfwidth',
0xFF71 => 'akatakanahalfwidth',
0xFF72 => 'ikatakanahalfwidth',
0xFF73 => 'ukatakanahalfwidth',
0xFF74 => 'ekatakanahalfwidth',
0xFF75 => 'okatakanahalfwidth',
0xFF76 => 'kakatakanahalfwidth',
0xFF77 => 'kikatakanahalfwidth',
0xFF78 => 'kukatakanahalfwidth',
0xFF79 => 'kekatakanahalfwidth',
0xFF7A => 'kokatakanahalfwidth',
0xFF7B => 'sakatakanahalfwidth',
0xFF7C => 'sikatakanahalfwidth',
0xFF7D => 'sukatakanahalfwidth',
0xFF7E => 'sekatakanahalfwidth',
0xFF7F => 'sokatakanahalfwidth',
0xFF80 => 'takatakanahalfwidth',
0xFF81 => 'tikatakanahalfwidth',
0xFF82 => 'tukatakanahalfwidth',
0xFF83 => 'tekatakanahalfwidth',
0xFF84 => 'tokatakanahalfwidth',
0xFF85 => 'nakatakanahalfwidth',
0xFF86 => 'nikatakanahalfwidth',
0xFF87 => 'nukatakanahalfwidth',
0xFF88 => 'nekatakanahalfwidth',
0xFF89 => 'nokatakanahalfwidth',
0xFF8A => 'hakatakanahalfwidth',
0xFF8B => 'hikatakanahalfwidth',
0xFF8C => 'hukatakanahalfwidth',
0xFF8D => 'hekatakanahalfwidth',
0xFF8E => 'hokatakanahalfwidth',
0xFF8F => 'makatakanahalfwidth',
0xFF90 => 'mikatakanahalfwidth',
0xFF91 => 'mukatakanahalfwidth',
0xFF92 => 'mekatakanahalfwidth',
0xFF93 => 'mokatakanahalfwidth',
0xFF94 => 'yakatakanahalfwidth',
0xFF95 => 'yukatakanahalfwidth',
0xFF96 => 'yokatakanahalfwidth',
0xFF97 => 'rakatakanahalfwidth',
0xFF98 => 'rikatakanahalfwidth',
0xFF99 => 'rukatakanahalfwidth',
0xFF9A => 'rekatakanahalfwidth',
0xFF9B => 'rokatakanahalfwidth',
0xFF9C => 'wakatakanahalfwidth',
0xFF9D => 'nkatakanahalfwidth',
0xFF9E => 'voicedmarkkanahalfwidth',
0xFF9F => 'semivoicedmarkkanahalfwidth',
0xFFE0 => 'centmonospace',
0xFFE1 => 'sterlingmonospace',
0xFFE3 => 'macronmonospace',
0xFFE5 => 'yenmonospace',
0xFFE6 => 'wonmonospace'
);
?>