<?php

class Audit {
    /**
     * Basic auditable vars
     * @var array
     */
    public static $basicAuditVars = array();

    /**
     * Variable types that hold no real values and such variables don't need to be audited
     * @var array
     */
    protected static $no_audit_var_types = array('group', 'gt2', 'config', 'button', 'table', 'bb', 'map');

    /**
     * Holds merged audit of GT2 rows in BB for partial actions done to a
     * single bb row prior to save action of the whole model
     * @var array
     */
    public static $bb_gt2_audit = array();

    /**
     * Name of action for partial audit of a single bb row
     * @var string
     */
    const BB_AUDIT_ACTION = 'managevars';

    /**
     * Artificial variable to be audited at the beginning of each bb variant
     * @var string
     */
    const BB_DELIMITER_VAR = 'bb_delimiter';

    /**
     * Save audit data
     *
     * @param Registry $registry - the main registry
     * @param array $params - audit data
     * @return bool - result of the operation
     */
    public static function saveData(&$registry, $params) {
        $db = $registry['db'];
        if (preg_match('#^finance_#i', $params['model_name']) && !preg_match('#^finance_warehouse$#i', $params['model_name'])) {
            $table_name = DB_TABLE_FINANCE_AUDIT;
        } elseif (preg_match('#_(contactperson|branch)#i', $params['model_name'])) {
            $table_name = DB_TABLE_CUSTOMERS_AUDIT;
        } else {
            $table_name = constant('DB_TABLE_' . strtoupper(General::singular2plural($params['model_name']) . '_AUDIT'));
        }
        $data = General::slashesEscape($params['data']);
        $parent_id = $params['parent_id'];

        $insert = array();
        foreach ($data as $idx => $row) {
            $label_col = '';
            if (!isset($row['label']) || is_array($row['label'])) {
                $row['label'] = '';
            }
            if (!isset($row['old_value'])) {
                $row['old_value'] = '';
            } elseif (is_object($row['old_value'])) {
                switch (get_class($row['old_value'])) {
                    case 'File':
                        $row['old_value'] = $row['old_value']->get('id');
                        break;
                    default:
                        //ToDo extend this switch with more classes (if necessary)
                        $row['old_value'] = 'Object';
                        break;
                }
            }
            if (is_object($row['field_value'])) {
                switch (get_class($row['field_value'])) {
                    case 'File':
                        $row['field_value'] = $row['field_value']->get('id');
                        break;
                    default:
                        //ToDo extend this switch with more classes (if necessary)
                        $row['field_value'] = 'Object';
                        break;
                }
            }
            if (empty($row['var_type'])) {
                $row['var_type'] = PH_VAR_BASIC;
            }
            // arrays should be first serialized, then escaped
            if (is_array($row['field_value'])) {
                $row['field_value'] = General::slashesEscape(serialize(General::slashesStrip($row['field_value'])));
                $row['is_array'] = 1;
            }
            if (is_array($row['old_value'])) {
                $row['old_value'] = General::slashesEscape(serialize(General::slashesStrip($row['old_value'])));
                $row['is_array'] = 1;
            }

            $insert[$idx] = array($parent_id,
                                  "'" . $row['field_name'] . "'",
                                  "'" . $row['field_value'] . "'",
                                  "'" . $row['old_value'] . "'",
                                  $row['var_type']
                            );

            if (!preg_match('#^finance_#i', $params['model_name'])) {
                if (is_object($row['label'])) {
                    switch (get_class($row['label'])) {
                        case 'File':
                            $label = sprintf('%s (version %d)',
                                             $row['label']->get('name'), $row['label']->get('revision'));
                            // General::slashesEscape does not escape internal objects
                            if (empty($row['label']->slashesEscaped)) {
                                $label = General::slashesEscape($label);
                            }
                            break;
                        default:
                            //ToDo extend this switch with more classes (if necessary)
                            $label = $row['label']->get('name');
                            break;
                    }
                } else {
                    $label = $row['label'];
                }

                $label_col = ', label';
                $insert[$idx][] = "'" . $label . "'";
            }
            $insert[$idx][] = (!empty($row['is_array'])) ? $row['is_array'] : 0;
            $additional_col = '';
            if ($params['model_name'] == 'Finance_Warehouse') {
                $insert[$idx][] = isset($row['batch_id']) ? $row['batch_id'] : 0;
                $additional_col = ', batch_id';
            }
            $insert[$idx] = '(' . implode(', ' , $insert[$idx]) . ')';
        }

        if (count($insert)) {
            $query = "INSERT INTO " . $table_name . "\n" .
                     " (parent_id, field_name, field_value, old_value, var_type" . $label_col . ", is_array" . $additional_col . ") VALUES " . "\n" .
                     implode(",\n" , $insert);
            $db->Execute($query);
        }

        if ($db->ErrorMsg()) {
            $registry['logger']->dbError('saving audit', $db, $query);
            return false;
        } else {
            return true;
        }
    }

    /**
     * Save 2nd type grouping table data
     *
     * @param Registry $registry - the main registry
     * @param array $params - audit data
     * @return bool - result of the operation
     */
    public static function saveGT2Data(&$registry, $params) {

        if (isset($params['new_model']) && $params['new_model']) {
            $params['new_model']->slashesEscape(true);
            $table = $params['new_model']->get('grouping_table_2');
            $translations = $params['new_model']->getTranslations();
            $model_lang = $params['new_model']->get('model_lang');
            $bb_id = $params['new_model']->get('bb_id');
        } else {
            $params['model']->slashesEscape(true);
            $table = $params['model']->get('grouping_table_2');
            $translations = $params['model']->getTranslations();
            $model_lang = $params['model']->get('model_lang');
            $bb_id = $params['model']->get('bb_id');
        }
        // inner bb data has no language, GT2 rows are audited only in current
        // model language and no other translations are saved
        if ($bb_id) {
            $translations = array($model_lang);
        }

        $params['old_model']->slashesEscape(true);
        $old_table = $params['old_model']->get('grouping_table_2');
        if (is_array($old_table) && !empty($old_table['vars'])) {
            foreach ($old_table['vars'] as $var_name => $var_info) {
                if ($var_info['type'] == 'file_upload') {
                    foreach ($old_table['values'] as $old_idx => $old_values) {
                        if (isset($old_values[$var_name]) && is_object($old_values[$var_name])) {
                            $old_table['values'][$old_idx][$var_name] = $old_values[$var_name]->get('id');
                        }
                    }
                }
            }
        }

        $db = &$registry['db'];

        $audit_rows = array();
        if (is_array($table) && !empty($table['values'])) {
            foreach ($table['values'] as $idx => $values) {
                foreach ($values as $var => $value) {
                    if (!array_key_exists($var, $table['vars'])) {
                        unset($values[$var]);
                    } elseif ($table['vars'][$var]['type'] == 'file_upload' && is_object($values[$var])) {
                        $values[$var] = $values[$var]->get('id');
                    }
                }

                if (isset($old_table['values'][$idx])) {
                    $diff = array_diff_assoc($values, $old_table['values'][$idx]);
                    if (!empty($diff)) {
                        //check if the row is really_updated
                        foreach ($diff as $k => $v) {
                            if (array_key_exists($k, $old_table['values'][$idx]) && $values[$k] == $old_table['values'][$idx][$k]) {
                                unset($diff[$k]);
                            }
                        }
                        if (!empty($diff)) {
                            $audit_rows[$idx] = 'updated';
                        }
                    }
                    unset($old_table['values'][$idx]);
                } else {
                    $audit_rows[$idx] = 'added';
                }
            }
        }

        if (is_array($old_table) && !empty($old_table['values'])) {
            $deleted = array_keys($old_table['values']);
            foreach ($deleted as $idx) {
                $audit_rows[$idx] = 'deleted';
            }
        }

        if (empty($audit_rows)) {
            return false;
        }

        $trans_values = array();
        if (isset($params['new_model']) && $params['new_model']) {
            $model = clone $params['new_model'];
        } else {
            $model = clone $params['model'];
        }
        $gov = $registry['get_old_vars'];
        if (!$gov) {
            $registry->set('get_old_vars', true, true);
        }
        foreach ($translations as $lang) {
            if ($lang != $model_lang) {
                $model->set('model_lang', $lang, true);
                $model->getGT2Vars();
                $model->slashesEscape(true);
                $trans_values[$lang] = $model->get('grouping_table_2');
                $trans_values[$lang] = $trans_values[$lang]['values'];
            }
        }
        if (!$gov) {
            $registry->remove('get_old_vars');
        }
        unset($model);

        foreach ($audit_rows as $row => $action) {
            if ($action != 'deleted') {
                $values = $table['values'][$row];
                $val = array();
                $val_i18n = array();
                $vars_i18n = array();
                if (empty($values['article_id']) && empty($values['price']) &&
                    empty($values['average_weighted_delivery_price']) && empty($values['last_delivery_price']) &&
                    empty($values['quantity']) && empty($values['subtotal'])) {
                    //skip empty rows
                    continue;
                }
            } else {
                $values = array();
            }
            $val = array();
            $val['h_id'] = sprintf('`%s` = \'%s\'', 'h_id', $params['parent_id']);
            $val['model'] = sprintf('`%s` = \'%s\'', 'model', $params['model_name']);
            // rows of GT2 in BB have no unique row numbers so they are formed
            // from id of bb row and GT2 row number (which is negative)
            $val['bb_id'] = sprintf('`%s` = \'%d\'', 'bb_id', $bb_id);
            $val['row_id'] = sprintf('`%s` = \'%s\'', 'row_id', $row);
            $val['action'] = sprintf('`%s` = \'%s\'', 'action', $action);
            foreach ($values as $var => $value) {
                if (!array_key_exists($var, $table['vars']) || empty($value) && !($value === '0') && (!$table['vars'][$var]['multilang'] || count($translations) == 1)) {
                    continue;
                }
                //ToDo - exclude indexes
                if (preg_match('#index#i', $var)) {
                    continue;
                }
                if ($table['vars'][$var]['type'] == 'file_upload' && is_object($value)) {
                    $value = $value->get('id');
                }
                if (!$table['vars'][$var]['multilang']) {
                    $val[$var] = sprintf('`%s` = \'%s\'', $var, $value);
                } else {
                    $val_i18n[$model_lang][$var] = "'" . $value . "'";
                    $vars_i18n[] = '`' . $var . '`';
                    foreach ($translations as $lang) {
                        if ($lang != $model_lang) {
                            if (!empty($trans_values[$lang][$row][$var])) {
                                $val_i18n[$lang][$var] = "'" . $trans_values[$lang][$row][$var] . "'";
                            } else {
                                $val_i18n[$lang][$var] = "''";
                            }
                        }
                    }
                }
            }

            $query = 'INSERT INTO ' . DB_TABLE_GT2_AUDIT . "\n" .
                     'SET ' . implode(",\n", $val);
            $db->Execute($query);

            if ($action != 'deleted') {
                $vars_i18n[] = '`parent_id`';
                $vars_i18n[] = '`lang`';
                $val_i18n[$model_lang]['parent_id'] = $db->Insert_Id();
                $val_i18n[$model_lang]['lang'] = "'" . $model_lang . "'";
                $val_i18n[$model_lang] = '(' . implode(', ', $val_i18n[$model_lang]) . ')';
                foreach ($translations as $lang) {
                    if ($lang != $model_lang) {
                        $val_i18n[$lang]['parent_id'] = $db->Insert_Id();
                        $val_i18n[$lang]['lang'] = "'" . $lang . "'";
                        $val_i18n[$lang] = '(' . implode(', ', $val_i18n[$lang]) . ')';
                    }
                }
                $vars_i18n = ' (' . implode(', ', $vars_i18n) . ')';
                //ToDo - check duplicate key to remove IGNORE
                $query = 'INSERT IGNORE INTO ' . DB_TABLE_GT2_AUDIT_I18N . $vars_i18n . "\n" .
                         'VALUES ' . "\n" . implode(",\n", $val_i18n);
                $db->Execute($query);
            }
        }

        return true;
    }

    /**
     * Get 2nd type grouping table data
     *
     * @param Registry $registry - the main registry
     * @param array $params - filtering parameters
     * @return array - audit data
     */
    public static function getGT2Data(&$registry, $params) {

        $db = $registry['db'];

        //use custom model name of model to get gt2 vars (only vars, not values!!!)
        if (!empty($params['gt2_model_name'])) {
            $model_name = $params['gt2_model_name'];
        } else {
            $model_name = $params['model_name'];
        }

        //get source field of the GT2 variable
        $query = 'SELECT source FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 'WHERE type = \'gt2\' AND model = \'' . $model_name . "'\n" .
                 '  AND gt2 > 0 AND model_type = \'' . $params['model_type'] . '\'' . "\n";
        $source = $db->GetOne($query);

        //parse main source field
        $source = General::parseSettings($source, '#^use_as_plain$#');
        $use_as_plain = !empty($source['use_as_plain']) ? preg_split('#\s*,\s*#', $source['use_as_plain']) : array();

        //get var names
        $query = 'SELECT fm.name AS idx, fm.*,' . "\n" .
                 '       IF(fi18n.content NOT LIKE \'\', fi18n.content, fm.name) AS label' . "\n" .
                 'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FIELDS_I18N .' AS fi18n' . "\n" .
                 '  ON fm.id=fi18n.parent_id AND fi18n.content_type="label" AND fi18n.lang="' . $registry['lang'] . '"' . "\n" .
                 'WHERE fm.type != \'gt2\' AND fm.model = \'' . $model_name . "'\n" .
                 '  AND fm.gt2 > 0 AND fm.model_type = \'' . $params['model_type'] . "'\n" .
                 'ORDER BY fm.position, fm.id';
        $records = $db->GetAssoc($query);

        $labels = $plain_labels = array();
        $dropdowns = array();
        $file_uploads = array();
        $date_formats = array();

        if ($records) {
            foreach ($records as $var => $record) {
                $permissions_view = array();
                preg_match('#permissions_view\s*:=\s*([^\n]*)#', $record['source'], $permissions_view);
                if (isset($permissions_view[1]) && !$record['hidden']) {
                    $permissions_view = preg_split('#\s*,\s*#', trim($permissions_view[1]));
                    $permissions_view = array_intersect($permissions_view, $registry['currentUser']->get('groups'));
                    if (!empty($permissions_view)) {
                        if (!in_array($var, $use_as_plain)) {
                            $labels[$var] = $record['label'];
                        } else {
                            $plain_labels[$var] = $record['label'];
                        }
                    }
                }
                if ($record['type'] == 'dropdown' || $record['type'] == 'radio') {
                    $dropdowns[$var] = $record;
                } elseif ($record['type'] == 'file_upload') {
                    $file_uploads[] = $var;
                } elseif ($record['type'] == 'date' || $record['type'] == 'datetime') {
                    $date_formats[$var] = $registry['translater']->translate($record['type'] == 'datetime' ? 'date_mid' : 'date_short');
                }
            }
        }

        if (!empty($dropdowns)) {
            //get options
            $tmp_model = new $params['model_name']($registry);
            $tmp_model->set('model_type', $params['model_type'], true);
            $tmp_model->processFields($dropdowns);
        }

        if (empty($labels)) {
            return array();
        }

        $prec = $registry['config']->getSectionParams('precision');

        if (!empty($params['archive'])) {
            $a_table = DB_TABLE_ARCHIVE_GT2_AUDIT;
            $a_i18n = DB_TABLE_ARCHIVE_GT2_AUDIT_I18N;
        } else {
            $a_table = DB_TABLE_GT2_AUDIT;
            $a_i18n = DB_TABLE_GT2_AUDIT_I18N;
        }

        $prQuan = $prec['gt2_quantity'];
        $prRows = $prec['gt2_rows'];
        $prAWDP = $prec['gt2_average_weighted_delivery_price'] ?? 6;

        //get new values
        $query = "SELECT fda.*, fdai18n.*,
                  CAST(ROUND(fda.quantity, {$prQuan}) AS DECIMAL(25,{$prQuan})) AS quantity,
                  CAST(ROUND(fda.available_quantity, {$prQuan}) AS DECIMAL(25,{$prQuan})) AS available_quantity,
                  CAST(ROUND(fda.last_delivery_price, {$prRows}) AS DECIMAL(25,{$prRows})) AS last_delivery_price,
                  CAST(ROUND(fda.price, {$prRows}) AS DECIMAL(25,{$prRows})) AS price,
                  CAST(ROUND(fda.discount_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS discount_value,
                  CAST(ROUND(fda.discount_percentage, {$prRows}) AS DECIMAL(25,{$prRows})) AS discount_percentage,
                  CAST(ROUND(fda.surplus_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS surplus_value,
                  CAST(ROUND(fda.surplus_percentage, {$prRows}) AS DECIMAL(25,{$prRows})) AS surplus_percentage,
                  CAST(ROUND(fda.price_with_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS price_with_discount,
                  CAST(ROUND(fda.vat_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS vat_value,
                  CAST(ROUND(fda.profit_no_final_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS profit_no_final_discount,
                  CAST(ROUND(fda.subtotal_profit_no_final_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_profit_no_final_discount,
                  CAST(ROUND(fda.subtotal, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal,
                  CAST(ROUND(fda.subtotal_with_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_with_discount,
                  CAST(ROUND(fda.subtotal_discount_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_discount_value,
                  CAST(ROUND(fda.subtotal_with_vat, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_with_vat,
                  CAST(ROUND(fda.subtotal_with_vat_with_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_with_vat_with_discount,
                  CAST(ROUND(fda.article_height, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_height,
                  CAST(ROUND(fda.article_width, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_width,
                  CAST(ROUND(fda.article_weight, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_weight,
                  CAST(ROUND(fda.article_volume, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_volume,
                  CAST(ROUND(fda.average_weighted_delivery_price, {$prAWDP}) AS DECIMAL(25,{$prAWDP})) AS average_weighted_delivery_price
                  FROM `{$a_table}` as fda
                  LEFT JOIN `{$a_i18n}` AS fdai18n
                    ON fda.id = fdai18n.parent_id AND fdai18n.lang " . (!empty($params['min_h_id']) ? 'IS NOT NULL' : '= \'' . $registry['lang'] . '\'') . "
                  WHERE fda.h_id = '{$params['parent_id']}' AND model='{$params['model_name']}'
                  ORDER BY fda.bb_id ASC, ABS(fda.row_id) ASC";
        $new_values = $db->GetAll($query);

        if (empty($new_values)) {
            return array('labels' => $labels, 'plain_labels' => $plain_labels);
        }

        if (!empty($file_uploads)) {
            $file_ids = array();
            foreach ($new_values as $row_index => $row_vars) {
                foreach ($row_vars as $row_var => $var_value) {
                    if (in_array($row_var, $file_uploads)) {
                        $file_ids[$var_value] = '';
                    }
                }
            }

            if (!empty($file_ids)) {
                require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                $filters = array(
                    'where' => array(
                        'f.id IN (\'' . implode('\', \'', array_keys($file_ids)) . '\')',
                        'f.deleted IS NOT NULL'
                    ),
                    'sanitize' => true,
                    'archive' => !empty($params['archive'])
                );
                $files = Files::search($registry, $filters);
                if ($files) {
                    foreach ($files as $file) {
                        $file_ids[$file->get('id')] = $file;
                    }
                }
            }

            foreach ($new_values as $row_index => $row_vars) {
                foreach ($row_vars as $row_var => $var_value) {
                    if (in_array($row_var, $file_uploads) && !empty($file_ids[$var_value])) {
                        $new_values[$row_index][$row_var] = $file_ids[$var_value]->getAsHTML();
                    }
                }
            }
        }

        if (preg_match('#^finance_#i', $params['model_name']) && !preg_match('#^finance_warehouse$#i', $params['model_name'])) {
            $table_name = DB_TABLE_FINANCE_HISTORY;
        } else {
            if (!empty($params['archive'])) {
                $table_name = constant('DB_TABLE_ARCHIVE_' . strtoupper(General::singular2plural($params['model_name'])) . '_HISTORY');
            } else {
                $table_name = constant('DB_TABLE_' . strtoupper(General::singular2plural($params['model_name'])) . '_HISTORY');
            }
        }
        $old_values = array();
        $query = 'SELECT h_date FROM ' . $table_name . "\n" .
                 'WHERE h_id=' . $params['parent_id'];
        $h_date = $db->GetOne($query);

        foreach ($new_values as $key => $row) {
            //get old values
            $query = "SELECT fda.*, fdai18n.*,
                      CAST(ROUND(fda.quantity, {$prQuan}) AS DECIMAL(25,{$prQuan})) AS quantity,
                      CAST(ROUND(fda.available_quantity, {$prQuan}) AS DECIMAL(25,{$prQuan})) AS available_quantity,
                      CAST(ROUND(fda.last_delivery_price, {$prRows}) AS DECIMAL(25,{$prRows})) AS last_delivery_price,
                      CAST(ROUND(fda.price, {$prRows}) AS DECIMAL(25,{$prRows})) AS price,
                      CAST(ROUND(fda.discount_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS discount_value,
                      CAST(ROUND(fda.discount_percentage, {$prRows}) AS DECIMAL(25,{$prRows})) AS discount_percentage,
                      CAST(ROUND(fda.surplus_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS surplus_value,
                      CAST(ROUND(fda.surplus_percentage, {$prRows}) AS DECIMAL(25,{$prRows})) AS surplus_percentage,
                      CAST(ROUND(fda.price_with_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS price_with_discount,
                      CAST(ROUND(fda.vat_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS vat_value,
                      CAST(ROUND(fda.profit_no_final_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS profit_no_final_discount,
                      CAST(ROUND(fda.subtotal_profit_no_final_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_profit_no_final_discount,
                      CAST(ROUND(fda.subtotal, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal,
                      CAST(ROUND(fda.subtotal_with_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_with_discount,
                      CAST(ROUND(fda.subtotal_discount_value, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_discount_value,
                      CAST(ROUND(fda.subtotal_with_vat, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_with_vat,
                      CAST(ROUND(fda.subtotal_with_vat_with_discount, {$prRows}) AS DECIMAL(25,{$prRows})) AS subtotal_with_vat_with_discount,
                      CAST(ROUND(fda.article_height, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_height,
                      CAST(ROUND(fda.article_width, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_width,
                      CAST(ROUND(fda.article_weight, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_weight,
                      CAST(ROUND(fda.article_volume, {$prRows}) AS DECIMAL(25,{$prRows})) AS article_volume,
                      CAST(ROUND(fda.average_weighted_delivery_price, {$prAWDP}) AS DECIMAL(25,{$prAWDP})) AS average_weighted_delivery_price
                      FROM `{$a_table}` as fda
                      LEFT JOIN `{$a_i18n}` AS fdai18n
                        ON fda.id = fdai18n.parent_id AND fdai18n.lang " . (!empty($params['min_h_id']) ? 'IS NOT NULL' : '= \'' . $registry['lang'] . '\'') . "
                      LEFT JOIN `{$table_name}` AS h
                        ON fda.h_id = h.h_id
                      WHERE fda.bb_id = '{$row['bb_id']}' AND fda.row_id = '{$row['row_id']}' AND h.h_date < '{$h_date}' AND fda.h_id != '{$params['parent_id']}'" .
                     (!empty($params['min_h_id']) ? "  AND fda.h_id < '{$params['min_h_id']}'" : "") . "
                      ORDER BY h_date DESC
                      LIMIT 0,1";
            $old_values[$key] = $db->GetAll($query);
            if (isset($old_values[$key][0])) {
                $old_values[$key] = $old_values[$key][0];
            }
            if (!empty($dropdowns) || !empty($date_formats)) {
                //replace dropdown values with option labels
                foreach ($row as $var_name => $var_value) {
                    if (isset($dropdowns[$var_name])) {
                        $found = false;
                        $optgroups = array();
                        if (array_key_exists('optgroups', $dropdowns[$var_name])) {
                            $optgroups = $dropdowns[$var_name]['optgroups'];
                        } elseif (array_key_exists('options', $dropdowns[$var_name])) {
                            $optgroups = array($dropdowns[$var_name]['options']);
                        }
                        foreach ($optgroups as $opt_options) {
                            foreach ($opt_options as $option) {
                                if ($option['option_value'] == $var_value) {
                                    $new_values[$key][$var_name] = $option['label'];
                                    $found = true;
                                    if (isset($old_values[$key][$var_name]) && $option['option_value'] == $old_values[$key][$var_name]) {
                                        $old_values[$key][$var_name] = $option['label'];
                                        // values match
                                        break 2;
                                    } elseif (!isset($old_values[$key][$var_name])) {
                                        // no old value
                                        break 2;
                                    }
                                }
                                if (isset($old_values[$key][$var_name]) && $option['option_value'] == $old_values[$key][$var_name]) {
                                    $old_values[$key][$var_name] = $option['label'];
                                    if ($found) {
                                        // both values found
                                        break 2;
                                    }
                                }
                            }
                        }
                    } elseif (isset($date_formats[$var_name])) {
                        if ($var_value) {
                            $new_values[$key][$var_name] = General::strftime($date_formats[$var_name], $var_value);
                        }
                        if (!empty($old_values[$key][$var_name])) {
                            $old_values[$key][$var_name] = General::strftime($date_formats[$var_name], $old_values[$key][$var_name]);
                        }
                    }
                }
            }
        }

        if (!empty($file_uploads)) {
            $file_ids = array();
            foreach ($old_values as $row_index => $row_vars) {
                foreach ($row_vars as $row_var => $var_value) {
                    if (in_array($row_var, $file_uploads)) {
                        $file_ids[$var_value] = '';
                    }
                }
            }

            if (!empty($file_ids)) {
                require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                $filters = array(
                    'where' => array(
                        'f.id IN (\'' . implode('\', \'', array_keys($file_ids)) . '\')',
                        'f.deleted IS NOT NULL'
                    ),
                    'sanitize' => true,
                    'archive' => !empty($params['archive'])
                );
                $files = Files::search($registry, $filters);
                if ($files) {
                    foreach ($files as $file) {
                        $file_ids[$file->get('id')] = $file;
                    }
                }
            }

            foreach ($old_values as $row_index => $row_vars) {
                foreach ($row_vars as $row_var => $var_value) {
                    if (in_array($row_var, $file_uploads) && !empty($file_ids[$var_value])) {
                        $old_values[$row_index][$row_var] = $file_ids[$var_value]->getAsHTML();
                    }
                }
            }
        }

        // if GT2 is in BB, add a caption row in the beginning of each variant
        if (array_filter($new_values, function($row) {
            return $row['bb_id'] > 0;
        })) {
            $idx = 0;
            $bb_id = 0;
            $new_values2 = $old_values2 = array();
            foreach ($new_values as $key => $row) {
                // add caption row
                if ($row['bb_id'] > 0 && $row['bb_id'] != $bb_id) {
                    $bb_id = $row['bb_id'];
                    if (!isset($blank_row)) {
                        $blank_row = array_fill_keys(array_keys($row), '');
                    }
                    if (!isset($model_id)) {
                        $model_id = $db->GetOne(
                            'SELECT model_id FROM ' . $table_name . "\n" .
                            'WHERE h_id=' . $params['parent_id']
                        );
                    }
                    if (!isset($bb_captions)) {
                        $bb_captions = self::getBBCaptions(
                            $registry,
                            array(
                                'model' => $params['model_name'],
                                'model_id' => $model_id,
                                'archive' => !empty($params['archive'])
                            ));
                        $bb_ids = array_keys($bb_captions);
                    }
                    $new_values2[$idx] = $old_values2[$idx] = array(
                        'field_name' => self::BB_DELIMITER_VAR,
                        'label' => self::prepareBBRowCaption($registry, $bb_id, $bb_captions),
                        'model_id' => $model_id,
                        'bb_id' => $bb_id,
                        'deleted' => !in_array($bb_id, $bb_ids)
                    ) + $blank_row;
                    $idx++;
                }
                // reindex old and new values
                $new_values2[$idx] = $row;
                if (array_key_exists($key, $old_values)) {
                    $old_values2[$idx] = $old_values[$key];
                }
                $idx++;
            }
            $new_values = $new_values2;
            $old_values = $old_values2;
        }

        return array('old_values' => $old_values, 'new_values' => $new_values, 'labels' => $labels, 'plain_labels' => $plain_labels);
    }

    /**
     * Get audit of all basic and additional variables other than those in GT2 rows
     *
     * @param Registry $registry - the main registry
     * @param array $params - filtering parameters
     * @return array - audit data
     */
    public static function getData(&$registry, $params) {
        $db = $registry['db'];
        $model_name_plural = strtoupper(General::singular2plural($params['model_name']));

        if (empty($params['archive'])) {
            $model_table_name = constant('DB_TABLE_' . $model_name_plural);
        } else {
            $model_table_name = constant('DB_TABLE_ARCHIVE_' . $model_name_plural);
        }
        if (preg_match('#^finance_#i', $params['model_name']) && !preg_match('#^finance_warehouse$#i', $params['model_name'])) {
            $table_name = DB_TABLE_FINANCE_AUDIT;
            $history_table_name = DB_TABLE_FINANCE_HISTORY;
        } else {
            if (!empty($params['archive'])) {
                $history_table_name = constant('DB_TABLE_ARCHIVE_' . $model_name_plural . '_HISTORY');
                $table_name = constant('DB_TABLE_ARCHIVE_' . $model_name_plural . '_AUDIT');
            } else {
                $history_table_name = constant('DB_TABLE_' . $model_name_plural . '_HISTORY');
                $table_name = constant('DB_TABLE_' . $model_name_plural . '_AUDIT');
            }
        }
        $prefix = strtolower($model_name_plural) . '_';
        $lang = $registry['lang'];

        $action_type = '';
        if (isset($params['action_type'])) {
            $action_type = $params['action_type'];
        } else {
            $query = 'SELECT h.action_type' . "\n" .
                     'FROM `' . $history_table_name . '` AS h' . "\n" .
                     'WHERE h.h_id=' . $params['parent_id'];
            $action_type = $db->GetOne($query);
        }

        $model_type = 0;
        if (isset($params['model_type'])) {
            $model_type = $params['model_type'];
        } elseif ($params['model_name'] != 'Emails_Campaign') {
            $query = 'SELECT m.type' . "\n" .
                     ' FROM `' . $history_table_name . '` AS h ' . "\n" .
                     ' LEFT JOIN `' . $model_table_name . '` as m ON m.id=h.model_id' . "\n" .
                     ' WHERE h.h_id=' . $params['parent_id'] . "\n";
            $model_type = $db->GetOne($query) ?: 0;
        }

        $tmp_model = null;
        if (class_exists($params['model_name'], false)) {
            $tmp_model = $params['model_name'];
            /** @var Model $tmp_model */
            $tmp_model = new $tmp_model($registry, array('type' => $model_type, 'archived_by' => !empty($params['archive'])));
        }
        if (strpos($params['model_name'], 'Finance_') !== 0 &&
        (!$tmp_model || $tmp_model->checkForVariables() || $params['model_name'] == 'Customer' && $action_type == 'transfer')) {
            $query =
                'SELECT a.*, fi18n.content, fm.type, "' . $action_type . '" AS `action`' . "\n" .
                'FROM `' . $table_name . '` AS a' . "\n" .
                'LEFT JOIN `' . DB_TABLE_FIELDS_META . '` AS fm ' . "\n" .
                '  ON a.var_type=' . PH_VAR_ADDITIONAL . ' AND fm.name=a.field_name' . "\n" .
                '    AND fm.model="' . $params['model_name'] . '"' . "\n" .
                '    AND fm.model_type=\'' . $model_type . '\'' . "\n" .
                'LEFT JOIN `' . DB_TABLE_FIELDS_I18N . '` AS fi18n ' . "\n" .
                '  ON fm.id=fi18n.parent_id AND fi18n.content_type="label" AND fi18n.lang="' . $lang . '"' . "\n" .
                'WHERE a.parent_id=\'' . $params['parent_id'] . '\'' . "\n" .
                'ORDER BY a.parent_id DESC, a.var_type ASC, a.a_id ASC';

            /**
             * Actions that can modify bb data (multi-actions cannot)
             * @var array $managevars_actions
             */
            $managevars_actions = array('add', 'edit', 'clone', 'transform', 'transfer',);

            // check if model has bb and get audit of partial bb edits
            if (!empty($params['get_merged_bb_audit']) &&
            ($tmp_model && $tmp_model->getMetaId('bb_group', true) || $params['model_name'] == 'Customer' && $action_type == 'transfer')) {
                $current_history = History::getData(
                    $registry,
                    array(
                        'model' => $params['model_name'],
                        'h_id' => $params['parent_id'],
                        'archive' => !empty($params['archive'])
                    ));
                if ($current_history) {
                    $current_history = reset($current_history);
                    $tmp_model->set('id', $current_history['model_id'], true);

                    $history = History::getData(
                        $registry,
                        array(
                            'model' => $tmp_model,
                            'archive' => !empty($params['archive']),
                            'limit_rows' => 1000
                        ));
                    $bb_history = array();
                    foreach ($history as $row) {
                        if ($row['h_id'] >= $params['parent_id']) {
                            continue;
                        } elseif ($row['action_type'] == self::BB_AUDIT_ACTION && $row['user_id'] == $current_history['user_id']) {
                            $bb_history[$row['h_id']] = $row;
                        } elseif (in_array($row['action_type'], $managevars_actions)) {
                            break;
                        }
                    }
                    if ($bb_history) {
                        // replacements for bb query
                        $search = array(
                            'a.parent_id=\'' . $params['parent_id'] . '\'',
                            '`action`'
                        );
                        $replace = array(
                            'a.parent_id IN (' . implode(',', array_keys($bb_history)) . ')',
                            '`action`, IF(fm.bb AND fm.multilang AND NOT(fm.configurator OR fm.grouping OR fm.gt2), 1, 0) AS bb_cstm_multilang'
                        );
                        // if action was transfer, type might have been different
                        $row = reset($bb_history);
                        if (!empty($row['data']['type']) && $row['data']['type'] != $model_type) {
                            $search[] = 'AND fm.model_type=\'' . $model_type . '\'';
                            $replace[] = 'AND fm.model_type=\'' . $row['data']['type'] . '\'';
                        }
                        $query_bb = str_replace($search, $replace, $query);

                        $bb_audit = array();
                        $bb_audit_records = $db->GetAll($query_bb);
                        foreach ($bb_audit_records as $arow) {
                            if (!empty($bb_history[$arow['parent_id']]['data']['bb_id']) &&
                            // in case of a multilang caption variable, only changes in the same lang
                            // as the main history row will be displayed in the merged audit
                            (empty($arow['bb_cstm_multilang']) || $current_history['lang'] == $bb_history[$arow['parent_id']]['lang'])) {
                                $bb_id = $bb_history[$arow['parent_id']]['data']['bb_id'];
                                if (!array_key_exists($bb_id, $bb_audit)) {
                                    $bb_audit[$bb_id] = array();
                                }
                                if (!array_key_exists($arow['field_name'], $bb_audit[$bb_id])) {
                                    $bb_audit[$bb_id][$arow['field_name']] = $arow;
                                } else {
                                    $bb_audit[$bb_id][$arow['field_name']]['old_value'] = $arow['old_value'];
                                }
                            }
                        }
                        ksort($bb_audit);

                        $bb_audit_flattened = array();
                        $bb_delimiter_var = self::BB_DELIMITER_VAR;
                        foreach ($bb_audit as $bb_data) {
                            // display only data where earliest and latest state differ
                            $bb_data = array_filter($bb_data, function($a) use ($bb_delimiter_var) {
                                return $a['field_name'] == $bb_delimiter_var || $a['label'] !== $a['old_value'];
                            });
                            if (array_diff_key($bb_data, array(self::BB_DELIMITER_VAR => ''))) {
                                $bb_audit_flattened = array_merge($bb_audit_flattened, array_values($bb_data));
                            }
                        }
                        $bb_audit = $bb_audit_flattened;

                        // check if any of the bb rows has GT2 audit and accumulate it
                        $bb_gt2 = $tmp_model->getMetaId('group_table_2', true);
                        if ($bb_gt2) {
                            $bb_gt2 = $tmp_model->getMetaInfo($bb_gt2);
                            if (!($bb_gt2 && $bb_gt2['type'] == 'gt2' && !empty($bb_gt2['bb']))) {
                                $bb_gt2 = false;
                            }
                        }
                        if ($bb_gt2) {
                            // hypothetically there might be GT2 audit and no regular audit
                            $min_h_id = min(array_keys($bb_history));

                            // collect gt2 audit per bb row (bb ids are keys)
                            $bb_gt2_audit = array();

                            foreach ($bb_history as $h_id => $row) {
                                if (!empty($row['data']['bb_id'])) {
                                    $bb_id = $row['data']['bb_id'];
                                    $bb_managevars_audit = self::getGT2Data(
                                        $registry,
                                        array('parent_id' => $h_id, 'min_h_id' => $min_h_id) + $params
                                    ) + array('h_id' => $h_id, 'parent_id' => $params['parent_id']);
                                    if (!empty($bb_managevars_audit['new_values'])) {
                                        if (!array_key_exists($bb_id, $bb_gt2_audit)) {
                                            $bb_gt2_audit[$bb_id] = array(
                                                'new_values' => array(),
                                                'old_values' => array(),
                                                'labels' => $bb_managevars_audit['labels']
                                            );
                                        }
                                        foreach ($bb_managevars_audit['new_values'] as $aidx => $arow) {
                                            // we get the latest audit per each row (even though it might not be the same row,
                                            // as they are reindexed on GT2 row deletion)
                                            // old values hold the state of the row before the earliest managevars action included in the merged audit
                                            if (!array_key_exists($arow['row_id'], $bb_gt2_audit[$bb_id]['new_values'])) {
                                                $bb_gt2_audit[$bb_id]['new_values'][$arow['row_id']] = $arow;
                                                $bb_gt2_audit[$bb_id]['old_values'][$arow['row_id']] =
                                                    isset($bb_managevars_audit['old_values'][$aidx]) ?
                                                    $bb_managevars_audit['old_values'][$aidx] :
                                                    array();
                                            }
                                        }
                                    }
                                }
                            }
                            ksort($bb_gt2_audit);

                            $bb_gt2_audit_flattened = array('new_values' => array(), 'old_values' => array(), 'labels' => array());
                            foreach ($bb_gt2_audit as $bb_id => $bb_gt2_audit_rows) {
                                foreach ($bb_gt2_audit_rows['new_values'] as $aidx => $arow) {
                                    // rows were added and then deleted, no need to display them
                                    if ($arow['action'] == 'deleted' && empty($bb_gt2_audit_rows['old_values'][$aidx]) ||
                                    // rows were updated but then reverted and there is no difference between earliest and latest state
                                    $arow['action'] == 'updated' && !empty($bb_gt2_audit_rows['old_values'][$aidx]) &&
                                    !array_diff_key(
                                        array_diff_assoc($arow, $bb_gt2_audit_rows['old_values'][$aidx]),
                                        array_flip(array('id', 'parent_id', 'h_id', 'action'))
                                    )) {
                                        unset($bb_gt2_audit_rows['new_values'][$aidx]);
                                        unset($bb_gt2_audit_rows['old_values'][$aidx]);
                                    }
                                }
                                if (count($bb_gt2_audit_rows['new_values']) > 1) {
                                    if (empty($bb_gt2_audit_flattened['labels'])) {
                                        $bb_gt2_audit_flattened['labels'] = $bb_gt2_audit_rows['labels'];
                                    }
                                    // row ids are negative, therefore sort in reverse order of keys
                                    krsort($bb_gt2_audit_rows['new_values']);
                                    $bb_gt2_audit_flattened['new_values'] = array_merge(
                                        $bb_gt2_audit_flattened['new_values'],
                                        array_values($bb_gt2_audit_rows['new_values'])
                                    );
                                    krsort($bb_gt2_audit_rows['old_values']);
                                    $bb_gt2_audit_flattened['old_values'] = array_merge(
                                        $bb_gt2_audit_flattened['old_values'],
                                        array_values($bb_gt2_audit_rows['old_values'])
                                    );
                                }
                            }

                            self::$bb_gt2_audit = $bb_gt2_audit_flattened;
                        }
                    }
                }
            }
        } else {
            $query =
                'SELECT a.*, "" AS content, "" AS type, "' . $action_type . '" as `action`' . "\n" .
                'FROM `' . $table_name . '` AS a' . "\n" .
                'WHERE a.parent_id=\'' . $params['parent_id'] . '\'' . "\n" .
                'ORDER BY a.var_type ASC, a.a_id ASC';
        }
        unset($tmp_model);

        $records = $db->GetAll($query);

        if (!empty($bb_audit)) {
            $records = array_merge($records, $bb_audit);
        }

        $date_formats = array(
            'date' => $registry['translater']->translate('date_short'),
            'datetime' => $registry['translater']->translate('date_mid')
        );

        foreach ($records as $key => $record) {
            if ($record['action'] == 'assign' && !in_array($params['model_name'], array('Event', 'Project'))) {
                $prefix = 'assignments_';
            } elseif ($record['action'] == 'email' || $record['action'] == 'receive_email') {
                $prefix = 'communications_';
                if (empty($communications_loaded)) {
                    $registry['translater']->loadFile(PH_MODULES_DIR . 'communications/i18n/' . $registry['lang'] . '/communications.ini');
                    $communications_loaded = true;
                }
                if (empty($params['history_activity']) && $record['field_name'] == 'mail_code') {
                    //hide the mail code, it is only used in history_activity
                    unset($records[$key]);
                    continue;
                }
            } elseif (in_array($record['action'], array('add_comment', 'edit_comment'))) {

                // do not show content of non-portal comments to portal users
                if ($registry['currentUser'] && $registry['currentUser']->get('is_portal') &&
                !array_filter($records, function($r) { return $r['field_name'] == 'is_portal' && !empty($r['field_value']); })) {
                    $records = array();
                    break;
                }

                // for now we won't display the is_portal property of comments in audit
                if ($record['field_name'] == 'is_portal') {
                    unset($records[$key]);
                    continue;
                }

                $prefix = 'comments_';
                if (empty($comments_loaded)) {
                    $registry['translater']->loadFile(PH_MODULES_DIR . 'comments/i18n/' . $registry['lang'] . '/comments.ini');
                    $comments_loaded = true;
                }
            } elseif (in_array($record['action'], array('add_timesheet', 'edit_timesheet'))) {
                $prefix = 'tasks_timesheets_';
                if (empty($tasks_loaded) && !in_array($params['model_name'], array('Task'))) {
                    $registry['translater']->loadFile(PH_MODULES_DIR . 'tasks/i18n/' . $registry['lang'] . '/tasks.ini');
                    $tasks_loaded = true;
                }
            } elseif ($record['field_name'] == self::BB_DELIMITER_VAR) {
                $bb_id = $record['field_value'];
                if (!isset($model_id)) {
                    $model_id = $db->GetOne(
                        'SELECT model_id FROM ' . $history_table_name . "\n" .
                        'WHERE h_id=' . $params['parent_id']
                    );
                }
                if (!isset($bb_captions)) {
                    $bb_captions = self::getBBCaptions(
                        $registry,
                        array(
                            'model' => $params['model_name'],
                            'model_id' => $model_id,
                            'archive' => !empty($params['archive'])
                        ));
                    $bb_ids = array_keys($bb_captions);
                }

                $records[$key] = array(
                    'label' => self::prepareBBRowCaption($registry, $bb_id, $bb_captions),
                    'model_id' => $model_id,
                    'deleted' => !in_array($bb_id, $bb_ids),
                ) + $records[$key];

                unset($records[$key]['type']);
                continue;
            }

            if ($record['var_type'] == PH_VAR_BASIC) {
                $records[$key]['var_label'] = $registry['translater']->translate($prefix . $record['field_name']) ?:
                                              $registry['translater']->translate($record['field_name']);
            } else {
                $records[$key]['var_label'] = $record['content'];
            }

            if ($record['var_type'] == PH_VAR_ADDITIONAL) {
                if ($record['is_array']) {
                    $field_values = !empty($record['field_value']) ? unserialize($record['field_value']) : array();
                    $old_values = !empty($record['old_value']) ? explode("\n", $record['old_value']) : array();
                } else {
                    $field_values = !empty($record['field_value']) ? array($record['field_value']) : array();
                    $old_values = !empty($record['old_value']) ? array($record['old_value']) : array();
                }
                switch ($record['type']) {
                    case 'file_upload':
                        //file_upload processing
                        foreach (array('label' => $field_values, 'old_value' => $old_values) as $audit_key => $audit_values) {
                            if (!empty($audit_values)) {
                                require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                                $records[$key][$audit_key] = '';
                                $filters = array(
                                    'where' => array(
                                        'f.id IN (\'' . implode('\', \'', $audit_values) . '\')',
                                        'f.deleted IS NOT NULL'
                                    ),
                                    'sanitize' => true,
                                    'archive' => !empty($params['archive'])
                                );
                                $files = Files::search($registry, $filters);
                                if ($files) {
                                    foreach ($files as $file) {
                                        $records[$key][$audit_key] .= $file->getAsHTML() . "\n";
                                    }
                                }
                            }
                        }
                        break;
                    case 'date':
                    case 'datetime':
                        $records[$key]['label'] = '';
                        foreach ($field_values as $value) {
                            $records[$key]['label'] .= ($value ? General::strftime($date_formats[$record['type']], $value) : '') . "\n";
                        }
                        $records[$key]['old_value'] = '';
                        foreach ($old_values as $value) {
                            $records[$key]['old_value'] .= ($value ? General::strftime($date_formats[$record['type']], $value) : '') . "\n";
                        }
                        break;
                }
            } elseif ($record['var_type'] == PH_VAR_BASIC) {
                if (!$record['is_array']) {
                    // data and datetime
                    if ($record['field_value'] && preg_match('#^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?$#', $record['field_value'])) {
                        if (strlen($record['field_value']) == 10 && Validator::validDate($record['field_value'])) {
                            $records[$key]['label'] = General::strftime($date_formats['date'], $record['field_value']);
                        } elseif (strlen($record['field_value']) == 19 && Validator::validDateTime($record['field_value'])) {
                            $records[$key]['label'] = General::strftime($date_formats['datetime'], $record['field_value']);
                        } elseif (preg_match('#^0{4}-0{2}-0{2}( 0{2}:0{2}:0{2})?$#', $record['field_value'])) {
                            $records[$key]['label'] = '-';
                        }
                    }
                    if ($record['old_value'] && preg_match('#^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}:\d{2})?$#', $record['old_value'])) {
                        if (strlen($record['old_value']) == 10 && Validator::validDate($record['old_value'])) {
                            $records[$key]['old_value'] = General::strftime($date_formats['date'], $record['old_value']);
                        } elseif (strlen($record['old_value']) == 19 && Validator::validDateTime($record['old_value'])) {
                            $records[$key]['old_value'] = General::strftime($date_formats['datetime'], $record['old_value']);
                        } elseif (preg_match('#^0{4}-0{2}-0{2}( 0{2}:0{2}:0{2})?$#', $record['old_value'])) {
                            $records[$key]['old_value'] = '-';
                        }
                    }
                } else {
                    // added/deleted attachments
                    if (in_array($record['field_name'], array('attached_files', 'added_attachments', 'deleted_attachments'))) {
                        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                        $records[$key]['label'] = '';
                        $filters = array(
                            'where' => array(
                                'f.id IN (\'' . implode('\', \'', unserialize($record['field_value'])) . '\')',
                                'f.deleted IS NOT NULL'
                            ),
                            'sanitize' => true,
                            'archive'  => !empty($params['archive'])
                        );
                        $files = Files::search($registry, $filters);
                        if ($files) {
                            foreach ($files as $file) {
                                if (!empty($params['history_activity'])) {
                                    $file->set('display_icon_image', true, true);
                                }
                                $records[$key]['label'] .= $file->getAsHTML() . "\n";
                            }
                        }
                    }
                }
            }
            unset($records[$key]['type']);
        }

        if ($db->ErrorMsg()) {
            $registry['logger']->dbError('get audit', $db, $query);
            return false;
        } else {
            return $records;
        }
    }

    /**
     * Prepares caption for bb row for display in history/audit.
     * Caption contains current position of bb row in bb table and label of inner grouping variable.
     *
     * @param Registry $registry - the main registry
     * @param int $bb_id - id of bb row
     * @param array $bb_captions - labels of inner grouping variables for all bb rows of model
     * @return string - formatted string for display
     */
    public static function prepareBBRowCaption(Registry &$registry, $bb_id, array $bb_captions = null) {
        if (!isset($bb_captions)) {
            $bb_captions = self::getBBCaptions($registry, array('bb_id' => $bb_id));
        }

        $var_label = array_search($bb_id, array_keys($bb_captions));
        $var_label = sprintf(
            '%s[%s]',
            !empty($bb_captions[$bb_id]) ? $bb_captions[$bb_id] . ' ' : '',
            $var_label === false ? $registry['translater']->translate('legend_deleted_row') : (++$var_label)
        );

        return $var_label;
    }

    /**
     * Gets labels of inner grouping variables for all bb rows of model
     *
     * @param Registry $registry - the main registry
     * @param array $model_params - parameters for model, model_id and archived flag OR id of bb row
     * @return array - labels of inner groping variables (GT, GT2 or config) for all bb rows of model
     */
    public static function getBBCaptions(Registry &$registry, array $model_params = array()) {
        $db = &$registry['db'];
        $table = empty($model_params['archive']) ? DB_TABLE_BB : DB_TABLE_ARCHIVE_BB;

        // try to get model params from bb row, if not specified
        if ((empty($model_params['model']) || empty($model_params['model_id'])) && !empty($model_params['bb_id'])) {
            $query = 'SELECT model, model_id FROM `' . $table . '` WHERE id = \'' . $model_params['bb_id'] . '\'';
            $model_params = $db->GetRow($query);
            if (!$model_params && empty($model_params['archive'])) {
                $model_params = $db->GetRow(preg_replace('#`' . $table . '`#', '`' . DB_TABLE_ARCHIVE_BB . '`', $query));
                $table = DB_TABLE_ARCHIVE_BB;
            }
        }

        $bb_captions = array();
        if ($model_params) {
            $bb_captions = $db->GetAssoc(
                'SELECT bb.id, fi.content' . "\n" .
                'FROM `'. $table . '` AS bb' . "\n" .
                'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fi' . "\n" .
                '  ON fi.parent_id = bb.meta_id AND fi.lang = \'' . $registry['lang'] . '\' AND fi.content_type = \'label\'' . "\n" .
                "WHERE bb.model = '{$model_params['model']}' AND bb.model_id = '{$model_params['model_id']}' \n" .
                'ORDER BY bb.id ASC'
            );
        }
        return $bb_captions;
    }

    /**
     * Get basic variables for audit of a module from config
     *
     * @param Registry $registry - the main registry
     * @param string $module - name of module to search for
     * @return array - variable names
     */
    public static function getBasicAuditVars(&$registry, $module) {
        //get main variables
        $config_vars = $registry['config']->getParamAsArray($module, 'audit');
        self::$basicAuditVars = $config_vars;

        return $config_vars;
    }
}

?>
