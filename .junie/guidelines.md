# Developer Guidelines for Nzoom

This guide provides essential information for new developers working on the Nzoom project.

## Project Overview

Nzoom is a web-based CRM system built with PHP 7.4 and MySQL. It follows an MVC architecture pattern and is organized in a modular structure.

## Tech Stack

- **PHP 7.4+**: Core programming language
- **MySQL 8.2**: Database
- **ADOdb**: Database abstraction layer

## Project Structure

```
/
├── _libs/                  # Core application code
│   ├── inc/                # Core libraries and MVC framework
│   │   ├── common/         # Common utilities
│   │   ├── ext/            # External libraries
│   │   └── mvc/            # MVC implementation
│   └── modules/            # Application modules
│       ├── customers/      # Customer management module
│       ├── documents/      # Document management module
│       └── ...             # Other functional modules
├── conf/                   # Configuration files
├── resources/              # Static resources
└── cache/                  # Cache storage
```

## Setting Up Development Environment

1. **Manual Setup**:
   - Configure a PHP 7.4+ environment
   - Set up a MySQL 8.2 database
   - Configure web server (Apache/Nginx) to point to the project root

## Running the Application

1. Default database credentials (for development):
   - Database: nzoom
   - Username: nzoom
   - Password: nzoompassword

## Code Organization

### MVC Pattern

- **Models**: Located in `_libs/modules/*/models/`
  - Handle data logic and business rules
  - Extend the base `Model` class from `_libs/inc/mvc/model.class.php`

- **Viewers**: Located in `_libs/modules/*/viewer/`
  - Handle presentation logic

- **Controllers**: Located in `_libs/modules/*/controllers/`
  - Handle request processing and routing

### Module Structure

Each module follows a consistent structure:
```
module_name/
├── controllers/     # Controller classes
├── i18n/            # Internationalization files
├── javascript/      # JavaScript files
├── models/          # Model classes
├── templates/       # Template files
└── viewer/          # Viewer classes
```

## Best Practices

1. **Follow MVC Pattern**: Keep business logic in models, presentation in views, and request handling in controllers
2. **Use Dependency Injection**: Utilize the registry pattern for dependencies
3. **Internationalization**: Use the i18n system for all user-facing text
4. **Error Handling**: Use the built-in error handling methods in the Model class
5. **Database Access**: Use the Model methods for database operations
6. **Security**: Always validate and sanitize input data

## Common Tasks

### Adding a New Module

1. Create a new directory in `_libs/modules/`
2. Create the standard module structure (controllers, models, views, etc.)
3. Implement the required classes extending the base framework classes

### Working with Database

- Models handle database operations through the ADOdb abstraction layer
- Use the Model methods for CRUD operations

### Debugging

- Check PHP error logs at `php_errors.log`
- Enable debugging in configuration files when needed

## Additional Resources

For more detailed documentation, refer to the comprehensive documentation outline available in the project.
