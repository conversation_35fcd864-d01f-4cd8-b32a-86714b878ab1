services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nzoom_app
    restart: unless-stopped
    volumes:
      - .:/var/www/html
      - ./docker/php/custom.ini:/usr/local/etc/php/conf.d/custom.ini
    ports:
      - "8080:80"
    depends_on:
      - db
    networks:
      - nzoom-network

  db:
    image: mysql:8.2
    container_name: nzoom_db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: nzoom
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_USER: nzoom
      MYSQL_PASSWORD: nzoompassword
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3316:3306"
    networks:
      - nzoom-network

networks:
  nzoom-network:
    driver: bridge

volumes:
  dbdata:
